#!/usr/bin/env python3
"""
Stage 3 修复验证测试
快速验证数据结构修复是否成功
"""

import sys
import traceback
from unittest.mock import Mock

def test_data_structures():
    """测试数据结构兼容性"""
    try:
        print("🔍 测试数据结构修复...")
        
        # 1. 测试 ResearchProblem
        from reasoning.data_models import ResearchProblem
        
        research_problem = ResearchProblem(
            title="Attention Mechanism for Classification",
            description="Investigate attention mechanisms for image classification",
            domain="computer_vision",
            question="How to improve attention mechanisms?",
            hypothesis=["Attention can improve accuracy"],
            background={"context": "test"},
            objectives=["Improve accuracy", "Reduce training time"],
            constraints=["Limited GPU resources"]
        )
        print("✅ ResearchProblem 创建成功")
        
        # 2. 测试 VisualizationChart
        from reasoning.data_models import VisualizationChart
        
        test_chart = VisualizationChart(
            chart_type='line',
            title='Test Chart',
            description='Test chart description',
            data_requirements=[],
            recommended_tool='matplotlib',
            data_source='data.csv',
            x_axis='x',
            y_axis='y'
        )
        print("✅ VisualizationChart 创建成功")
        
        # 3. 测试 EnhancedMultiAgentCollaborator
        from reasoning.enhanced_multi_agent_collaborator import EnhancedMultiAgentCollaborator
        from core.unified_api_client import UnifiedAPIClient
        from agents.agent_manager import AgentManager
        
        mock_client = Mock()
        mock_client.generate_response = Mock(return_value="Mock response")
        mock_client.generate_response_with_json = Mock(return_value=("Mock response", {"test": "data"}))
        mock_client.available_models = {"text": ["deepseek-chat"], "vision": ["qwen-vl"]}
        
        agent_manager = AgentManager(mock_client)
        collaborator = EnhancedMultiAgentCollaborator(mock_client, agent_manager)
        
        # 测试协作会话创建
        session = collaborator.create_collaboration_session(
            research_topic="Test topic",
            specific_questions=["Question 1", "Question 2"]
        )
        print("✅ EnhancedMultiAgentCollaborator 创建协作会话成功")
        
        print("\n🎉 所有数据结构修复验证通过!")
        return True
        
    except Exception as e:
        print(f"❌ 数据结构测试失败: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_data_structures()
    if not success:
        sys.exit(1)
