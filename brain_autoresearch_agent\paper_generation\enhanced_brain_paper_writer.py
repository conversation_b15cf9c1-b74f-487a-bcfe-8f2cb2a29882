"""
Enhanced Brain Paper Writer

实现集成多专家评审和自动修订功能的智能论文撰写系统
"""

import os
import sys
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
import logging

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from agents.agent_manager import AgentManager
from core.paper_workflow import PaperWorkflowExtractor
from paper_generation.review_system.multi_expert_review_system import (
    MultiExpertReviewSystem, PaperReviewResult
)
from paper_generation.review_system.auto_revision_engine import (
    AutoRevisionEngine, PaperRevisionSession
)
from paper_generation.latex_generator import LaTeXGenerator


@dataclass
class PaperGenerationConfig:
    """论文生成配置"""
    target_venue: str = "ICML"
    paper_type: str = "research"  # research, survey, position
    max_review_iterations: int = 3
    quality_threshold: float = 7.0
    enable_auto_revision: bool = True
    enable_multi_expert_review: bool = True
    latex_output: bool = True
    language: str = "english"  # 支持语言设置
    
    # Stage 4 specific settings
    enable_literature_integration: bool = True
    enable_experimental_validation: bool = True
    enable_visualization_generation: bool = True
    
    # 高级系统所需的额外配置
    enable_async_processing: bool = True
    max_pages: int = 8
    include_experiments: bool = True
    include_ablation_studies: bool = False
    enable_citation_validation: bool = True
    enable_latex_compilation: bool = False
    num_citation_rounds: int = 10


@dataclass
class PaperQualityMetrics:
    """论文质量指标"""
    overall_score: float
    novelty_score: float
    technical_quality_score: float
    clarity_score: float
    significance_score: float
    reproducibility_score: float
    expert_consensus: float
    improvement_history: List[float]


@dataclass
class PaperGenerationResult:
    """论文生成结果"""
    paper_content: Dict[str, Any]
    quality_metrics: PaperQualityMetrics
    review_history: List[PaperReviewResult]
    revision_history: List[PaperRevisionSession]
    latex_output: Optional[str]
    generation_metadata: Dict[str, Any]
    success: bool
    warnings: List[str]


class EnhancedBrainPaperWriter:
    """增强的大脑启发智能论文撰写系统"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None, 
                 config: Optional[PaperGenerationConfig] = None):
        """
        初始化增强论文撰写系统
        
        Args:
            llm_client: LLM客户端实例
            config: 论文生成配置
        """
        if llm_client is None:
            self.llm_client = LLMClient()
        else:
            self.llm_client = llm_client
            
        self.config = config or PaperGenerationConfig()
        self.agent_manager = AgentManager(self.llm_client)
        self.workflow_extractor = PaperWorkflowExtractor(self.llm_client)
        
        # 初始化评审和修订系统
        self._setup_multi_expert_review()
        self._setup_auto_revision_engine()
        
        # LaTeX生成器
        if self.config.latex_output:
            self.latex_generator = LaTeXGenerator()
        
        self.logger = self._setup_logger()
        
        # 论文模板和结构定义
        self.paper_structure = {
            'research': ['abstract', 'introduction', 'related_work', 'methodology', 
                        'experiments', 'results', 'discussion', 'conclusion', 'references'],
            'survey': ['abstract', 'introduction', 'background', 'survey_analysis', 
                      'taxonomy', 'discussion', 'future_work', 'conclusion', 'references'],
            'position': ['abstract', 'introduction', 'position_statement', 'arguments', 
                        'implications', 'discussion', 'conclusion', 'references']
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('EnhancedBrainPaperWriter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _setup_multi_expert_review(self) -> None:
        """设置多专家评审系统"""
        if self.config.enable_multi_expert_review:
            if not hasattr(self, 'review_system'):
                self.review_system = MultiExpertReviewSystem(self.llm_client)
                print(f"        ✅ 多专家评审系统已设置")
            else:
                print(f"        ✅ 多专家评审系统已存在")
        else:
            print(f"        ⏭️ 多专家评审已禁用")
    
    def _setup_auto_revision_engine(self) -> None:
        """设置自动修订引擎"""
        if self.config.enable_auto_revision:
            if not hasattr(self, 'revision_engine'):
                self.revision_engine = AutoRevisionEngine(self.llm_client)
                print(f"        ✅ 自动修订引擎已设置")
            else:
                print(f"        ✅ 自动修订引擎已存在")
        else:
            print(f"        ⏭️ 自动修订已禁用")
    
    def generate_paper_with_quality_control(self, research_topic: str, 
                                           research_context: Optional[Dict[str, Any]] = None) -> PaperGenerationResult:
        """
        生成带质量控制的完整论文
        
        Args:
            research_topic: 研究主题
            research_context: 研究上下文信息
            
        Returns:
            PaperGenerationResult: 论文生成结果
        """
        print(f"\\n🚀 开始增强论文生成流程")
        print(f"📋 研究主题: {research_topic}")
        print(f"🎯 目标会议: {self.config.target_venue}")
        print(f"📊 质量阈值: {self.config.quality_threshold}")
        print(f"🔄 最大评审轮次: {self.config.max_review_iterations}")
        
        generation_start_time = datetime.now()
        review_history = []
        revision_history = []
        warnings = []
        
        try:
            # 第1阶段: 初始论文生成
            print(f"\\n📝 第1阶段: 初始论文生成")
            paper_content = self._generate_initial_paper(research_topic, research_context)
            
            if not paper_content:
                return self._create_failure_result("Initial paper generation failed", warnings)
            
            print(f"  ✅ 初始论文生成完成，包含 {len(paper_content.get('sections', {}))} 个部分")
            
            # 第2阶段: 质量控制循环
            print(f"\\n🔍 第2阶段: 质量控制循环")
            final_paper, final_quality_metrics = self._quality_control_loop(
                paper_content, review_history, revision_history
            )
            
            # 第3阶段: LaTeX生成
            latex_output = None
            if self.config.latex_output:
                print(f"\\n📄 第3阶段: LaTeX格式生成")
                latex_output = self._generate_latex_output(final_paper)
                if latex_output:
                    print(f"  ✅ LaTeX生成完成")
                else:
                    warnings.append("LaTeX generation failed")
            
            # 第4阶段: 生成元数据
            generation_metadata = {
                'generation_time': (datetime.now() - generation_start_time).total_seconds(),
                'config': self.config.__dict__,
                'topic': research_topic,
                'total_review_iterations': len(review_history),
                'total_revision_iterations': len(revision_history),
                'timestamp': generation_start_time.isoformat()
            }
            
            print(f"\\n✅ 论文生成完成")
            print(f"📊 最终质量评分: {final_quality_metrics.overall_score:.2f}/10")
            print(f"🔄 评审轮次: {len(review_history)}")
            print(f"✏️ 修订轮次: {len(revision_history)}")
            print(f"⏱️ 总用时: {generation_metadata['generation_time']:.1f}秒")
            
            return PaperGenerationResult(
                paper_content=final_paper,
                quality_metrics=final_quality_metrics,
                review_history=review_history,
                revision_history=revision_history,
                latex_output=latex_output,
                generation_metadata=generation_metadata,
                success=True,
                warnings=warnings
            )
            
        except Exception as e:
            self.logger.error(f"Paper generation failed: {e}")
            warnings.append(f"Generation error: {e}")
            return self._create_failure_result(str(e), warnings)
    
    def _generate_initial_paper(self, research_topic: str, 
                               research_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """生成初始论文内容"""
        
        # 获取论文结构
        sections = self.paper_structure.get(self.config.paper_type, self.paper_structure['research'])
        
        print(f"  📋 生成论文结构: {len(sections)} 个部分")
        
        # 第1步: 研究分析和文献综述
        print(f"    🔍 步骤1: 研究分析和文献综述")
        research_analysis = self._conduct_research_analysis(research_topic, research_context)
        
        # 第2步: 生成论文大纲
        print(f"    📋 步骤2: 生成论文大纲")
        paper_outline = self._generate_paper_outline(research_topic, research_analysis, sections)
        
        # 第3步: 逐步生成各个部分
        print(f"    ✏️ 步骤3: 生成论文内容")
        paper_sections = {}
        
        for i, section in enumerate(sections[:-1]):  # 排除references
            print(f"      📝 生成 {section} ({i+1}/{len(sections)-1})")
            section_content = self._generate_section_content(
                section, research_topic, research_analysis, paper_outline, paper_sections
            )
            
            if section_content:
                paper_sections[section] = section_content
                print(f"        ✅ {section} 完成 ({len(section_content)} 字符)")
            else:
                print(f"        ❌ {section} 生成失败")
                paper_sections[section] = f"[{section} content to be generated]"
        
        # 生成标题
        title = self._generate_paper_title(research_topic, research_analysis)
        
        # 生成参考文献
        references = self._generate_references(research_analysis)
        paper_sections['references'] = references
        
        paper_content = {
            'title': title,
            'abstract': paper_sections.get('abstract', ''),
            'sections': paper_sections,
            'metadata': {
                'topic': research_topic,
                'venue': self.config.target_venue,
                'type': self.config.paper_type,
                'generated_at': datetime.now().isoformat()
            }
        }
        
        return paper_content
    
    def _conduct_research_analysis(self, research_topic: str, 
                                  research_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """执行研究分析"""
        
        analysis_results = {}
        
        try:
            # 获取AI技术专家分析
            ai_expert = self.agent_manager.get_agent('ai_technology')
            if ai_expert:
                ai_analysis = ai_expert.analyze({
                    "input_text": f"Analyze the research topic: {research_topic}",
                    "analysis_type": "research_analysis"
                })
                if ai_analysis and hasattr(ai_analysis, 'content'):
                    analysis_results['ai_perspective'] = ai_analysis.content
            
            # 获取神经科学专家分析
            neuro_expert = self.agent_manager.get_agent('neuroscience')
            if neuro_expert:
                neuro_analysis = neuro_expert.analyze({
                    "input_text": f"Provide neuroscience perspective on: {research_topic}",
                    "analysis_type": "neuroscience_analysis"
                })
                if neuro_analysis and hasattr(neuro_analysis, 'content'):
                    analysis_results['neuroscience_perspective'] = neuro_analysis.content
            
            # 获取实验设计分析
            exp_expert = self.agent_manager.get_agent('experiment_design')
            if exp_expert:
                exp_analysis = exp_expert.analyze({
                    "input_text": f"Design experiments for: {research_topic}",
                    "analysis_type": "experiment_design"
                })
                if exp_analysis and hasattr(exp_analysis, 'content'):
                    analysis_results['experiment_design'] = exp_analysis.content
            
            # 使用工作流提取器分析现有研究
            workflow_analysis = self.workflow_extractor.extract_research_workflow(research_topic)
            if workflow_analysis:
                analysis_results['workflow_analysis'] = workflow_analysis
            
        except Exception as e:
            self.logger.warning(f"Research analysis partially failed: {e}")
            analysis_results['error'] = str(e)
        
        return analysis_results
    
    def _generate_paper_outline(self, research_topic: str, research_analysis: Dict[str, Any], 
                               sections: List[str]) -> Dict[str, str]:
        """生成论文大纲"""
        # 安全转换 research_analysis，避免 MagicMock 序列化错误
        try:
            from unittest.mock import MagicMock
        except ImportError:
            MagicMock = None
        def safe_for_json(obj):
            if MagicMock and isinstance(obj, MagicMock):
                # 处理MagicMock对象的所有特殊属性
                mock_attrs = {}
                for attr in ['_mock_return_value', '_mock_name', '_mock_parent', '_mock_new_name']:
                    if hasattr(obj, attr):
                        val = getattr(obj, attr)
                        if val is not None:
                            mock_attrs[attr] = str(val)
                return f"<Mock:{mock_attrs}>"
            if hasattr(obj, 'to_dict'):
                try:
                    return safe_for_json(obj.to_dict())
                except Exception as e:
                    return str(obj)
            if isinstance(obj, (str, int, float, bool, type(None))):
                return obj
            if isinstance(obj, dict):
                return {str(k): safe_for_json(v) for k, v in obj.items()}
            if isinstance(obj, (list, tuple, set)):
                return [safe_for_json(v) for v in obj]
            try:
                # 尝试使用对象的__dict__
                if hasattr(obj, '__dict__'):
                    return safe_for_json(obj.__dict__)
                # 最后尝试直接字符串化
                return str(obj)
            except Exception:
                return f"<Unserializable:{type(obj).__name__}>"
        safe_analysis = safe_for_json(research_analysis)
        outline_prompt = f"""
        Generate a detailed outline for a {self.config.paper_type} paper on "{research_topic}" 
        for {self.config.target_venue}.

        Research Analysis:
        {json.dumps(safe_analysis, indent=2)[:2000]}...

        Required Sections: {', '.join(sections)}

        Please provide a structured outline with:
        1. Main points for each section
        2. Key arguments and contributions
        3. Logical flow between sections
        4. Specific technical details to include

        Format as JSON:
        {{
            "section_name": "detailed outline for this section",
            ...
        }}
        """
        try:
            response = self.llm_client.get_response(outline_prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            # 尝试解析JSON
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                outline = json.loads(json_text)
                return outline
            else:
                # 如果JSON解析失败，创建基本大纲
                return self._create_default_outline(sections)
        except Exception as e:
            self.logger.warning(f"Outline generation failed: {e}")
            return self._create_default_outline(sections)
    
    def _create_default_outline(self, sections: List[str]) -> Dict[str, str]:
        """创建默认大纲"""
        default_outlines = {
            'abstract': 'Concise summary of the research problem, methodology, key results, and contributions',
            'introduction': 'Background, motivation, problem statement, contributions, and paper structure',
            'related_work': 'Survey of existing literature, identification of gaps, and positioning of this work',
            'methodology': 'Detailed description of the proposed approach, algorithms, and theoretical foundations',
            'experiments': 'Experimental setup, datasets, evaluation metrics, and implementation details',
            'results': 'Experimental results, analysis, and comparison with baselines',
            'discussion': 'Interpretation of results, limitations, and broader implications',
            'conclusion': 'Summary of contributions, future work, and final remarks'
        }
        
        outline = {}
        for section in sections:
            if section in default_outlines:
                outline[section] = default_outlines[section]
            else:
                outline[section] = f"Content outline for {section} section"
        
        return outline
    
    def _generate_section_content(self, section_name: str, research_topic: str,
                                 research_analysis: Dict[str, Any], paper_outline: Dict[str, str],
                                 existing_sections: Dict[str, str]) -> str:
        """生成特定部分的内容"""
        
        # 获取合适的专家代理
        if section_name in ['methodology', 'experiments']:
            expert = self.agent_manager.get_agent('experiment_design')
        elif section_name in ['results', 'discussion']:
            expert = self.agent_manager.get_agent('data_analysis')
        elif section_name in ['related_work', 'introduction']:
            expert = self.agent_manager.get_agent('ai_technology')
        else:
            expert = self.agent_manager.get_agent('paper_writing')
        
        # 构建生成提示
        context_info = ""
        if existing_sections:
            for prev_section, content in existing_sections.items():
                context_info += f"\\n{prev_section.upper()}:\\n{content[:500]}...\\n"
        
        # 使用安全序列化
        safe_research_analysis = self._sanitize_for_json(research_analysis)
        section_prompt = f"""
        Write the {section_name} section for a {self.config.paper_type} paper on "{research_topic}" 
        for {self.config.target_venue}.

        Section Outline:
        {paper_outline.get(section_name, f'Generate {section_name} content')}

        Research Analysis Context:
        {json.dumps(safe_research_analysis, indent=2)[:1500]}...

        Previous Sections Context:
        {context_info}

        Requirements:
        1. Academic writing style appropriate for {self.config.target_venue}
        2. Proper technical depth and rigor
        3. Logical flow and clear presentation
        4. Appropriate length for the section (aim for 300-800 words)
        5. Include relevant technical details and citations

        Please generate comprehensive, high-quality content for the {section_name} section.
        """
        
        try:
            if expert:
                response = expert.analyze({
                    "input_text": section_prompt,
                    "analysis_type": "content_generation"
                })
                
                if response and hasattr(response, 'content') and response.content:
                    return response.content.strip()
            
            # 备用LLM生成
            response = self.llm_client.get_response(section_prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            return response_text.strip()
            
        except Exception as e:
            self.logger.warning(f"Section {section_name} generation failed: {e}")
            return f"[{section_name} content - generation failed: {e}]"
    
    def _generate_paper_title(self, research_topic, research_analysis):
        """生成论文标题"""
        try:
            # 使用安全序列化
            safe_research_analysis = self._sanitize_for_json(research_analysis)
            
            # 将研究分析转换为字符串
            research_analysis_str = ""
            if isinstance(safe_research_analysis, dict):
                research_analysis_str = json.dumps(safe_research_analysis, indent=2)[:1000]
            else:
                research_analysis_str = str(safe_research_analysis)[:1000]
            
            prompt = f"""
            Based on the following research topic and analysis, generate an academic paper title.
            
            RESEARCH TOPIC:
            {research_topic}
            
            RESEARCH ANALYSIS:
            {research_analysis_str}...
            
            The title should be:
            - Concise but informative (10-15 words)
            - Reflect the key contribution
            - Appropriate for an academic conference
            - Mention the core technology/approach
            
            Return ONLY the title text, with no additional commentary.
            """
            
            # 使用模拟模式生成标题
            title = self.llm_client.get_text_response(prompt)
            if not title or len(title.strip()) < 5:
                return f"Brain-Inspired Meta-Learning: {research_topic}"
            return title.strip()
        except Exception as e:
            print(f"⚠️ 标题生成失败: {e}")
            return f"Brain-Inspired Intelligence: A Novel Approach to {research_topic}"
    
    def _generate_references(self, research_analysis):
        """生成参考文献"""
        try:
            # 使用安全序列化
            safe_research_analysis = self._sanitize_for_json(research_analysis)
            
            # 将研究分析转换为字符串
            research_analysis_str = ""
            if isinstance(safe_research_analysis, dict):
                research_analysis_str = json.dumps(safe_research_analysis, indent=2)[:1500]
            else:
                research_analysis_str = str(safe_research_analysis)[:1500]
            
            prompt = f"""
            Based on the following research analysis, generate a list of academic references in BibTeX format.
            
            RESEARCH ANALYSIS:
            {research_analysis_str}...
            
            Generate 10-15 high-quality academic references that would be relevant for this research paper.
            Use proper BibTeX format with all required fields.
            Include a mix of recent papers (last 5 years) and seminal works.
            
            Return ONLY the BibTeX entries, with no additional commentary.
            """
            
            # 使用模拟模式生成参考文献
            references = self.llm_client.get_text_response(prompt)
            
            # 如果生成失败，返回默认参考文献
            if not references or len(references) < 100:
                return self._get_default_references()
            
            return references
        except Exception as e:
            print(f"⚠️ 参考文献生成失败: {e}")
            return self._get_default_references()
        
    def _get_default_references(self):
        """返回默认参考文献"""
        return """
@article{finn2017model,
  title={Model-agnostic meta-learning for fast adaptation of deep networks},
  author={Finn, Chelsea and Abbeel, Pieter and Levine, Sergey},
  journal={International Conference on Machine Learning},
  pages={1126--1135},
  year={2017}
}

@article{hassabis2017neuroscience,
  title={Neuroscience-inspired artificial intelligence},
  author={Hassabis, Demis and Kumaran, Dharshan and Summerfield, Christopher and Botvinick, Matthew},
  journal={Neuron},
  volume={95},
  number={2},
  pages={245--258},
  year={2017},
  publisher={Elsevier}
}

@article{lake2017building,
  title={Building machines that learn and think like people},
  author={Lake, Brenden M and Ullman, Tomer D and Tenenbaum, Joshua B and Gershman, Samuel J},
  journal={Behavioral and brain sciences},
  volume={40},
  year={2017},
  publisher={Cambridge University Press}
}
"""
    
    def _quality_control_loop(self, paper_content: Dict[str, Any], 
                             review_history: List[PaperReviewResult],
                             revision_history: List[PaperRevisionSession]) -> Tuple[Dict[str, Any], PaperQualityMetrics]:
        """质量控制循环"""
        
        current_paper = paper_content.copy()
        iteration = 0
        
        while iteration < self.config.max_review_iterations:
            print(f"\\n  🔍 质量评估轮次 {iteration + 1}/{self.config.max_review_iterations}")
            
            # 执行多专家评审
            if self.config.enable_multi_expert_review:
                print(f"    👥 多专家评审")
                review_result = self.review_system.conduct_multi_expert_review(
                    current_paper, self.config.target_venue
                )
                review_history.append(review_result)
                
                print(f"    📊 评审评分: {review_result.overall_score:.2f}/10")
                print(f"    🎯 建议: {review_result.final_recommendation}")
                
                # 检查是否达到质量阈值
                if review_result.overall_score >= self.config.quality_threshold:
                    print(f"    ✅ 达到质量阈值 ({self.config.quality_threshold})")
                    break
                
                # 执行自动修订
                if self.config.enable_auto_revision and review_result.revision_plans:
                    print(f"    🔧 自动修订")
                    revision_session = self.revision_engine.execute_paper_revision(
                        current_paper, review_result
                    )
                    revision_history.append(revision_session)
                    
                    if revision_session.improvement_metrics.get('overall_improvement', 0) > 0.1:
                        current_paper = revision_session.final_paper
                        print(f"    ✅ 修订完成，质量提升: {revision_session.improvement_metrics['overall_improvement']:.2f}")
                    else:
                        print(f"    ⚠️ 修订效果有限，保持原版本")
                        break
                else:
                    print(f"    ⏭️ 跳过修订")
                    break
            else:
                print(f"    ⏭️ 跳过多专家评审")
                break
            
            iteration += 1
        
        # 计算最终质量指标
        final_quality_metrics = self._calculate_final_quality_metrics(review_history, revision_history)
        
        return current_paper, final_quality_metrics
    
    def _calculate_final_quality_metrics(self, review_history: List[PaperReviewResult],
                                       revision_history: List[PaperRevisionSession]) -> PaperQualityMetrics:
        """计算最终质量指标"""
        
        if not review_history:
            # 没有评审历史，返回默认指标
            return PaperQualityMetrics(
                overall_score=6.0,
                novelty_score=6.0,
                technical_quality_score=6.0,
                clarity_score=6.0,
                significance_score=6.0,
                reproducibility_score=6.0,
                expert_consensus=0.7,
                improvement_history=[6.0]
            )
        
        # 使用最新的评审结果
        latest_review = review_history[-1]
        
        # 计算各维度平均分
        all_scores = {'novelty': [], 'technical_quality': [], 'clarity': [], 
                      'significance': [], 'reproducibility': []}
        
        for review in latest_review.expert_reviews:
            for criterion, score in review.criterion_scores.items():
                if criterion in all_scores:
                    all_scores[criterion].append(score)
        
        # 计算平均分
        avg_scores = {}
        for criterion, scores in all_scores.items():
            avg_scores[criterion] = sum(scores) / len(scores) if scores else 6.0
        
        # 计算改进历史
        improvement_history = [review.overall_score for review in review_history]
        
        return PaperQualityMetrics(
            overall_score=latest_review.overall_score,
            novelty_score=avg_scores.get('novelty', 6.0),
            technical_quality_score=avg_scores.get('technical_quality', 6.0),
            clarity_score=avg_scores.get('clarity', 6.0),
            significance_score=avg_scores.get('significance', 6.0),
            reproducibility_score=avg_scores.get('reproducibility', 6.0),
            expert_consensus=latest_review.consensus_level,
            improvement_history=improvement_history
        )
    
    def _generate_latex_output(self, paper_content: Dict[str, Any]) -> Optional[str]:
        """生成LaTeX输出"""
        try:
            # 类型健壮性处理，确保paper_content为dict
            if not isinstance(paper_content, dict):
                # 如果是字符串，尝试解析为JSON，否则包裹为dict
                try:
                    paper_content = json.loads(paper_content)
                except Exception:
                    paper_content = {"content": str(paper_content)}
            # 首先处理内容中可能存在的转义字符问题
            safe_paper_content = self._sanitize_latex_content(paper_content)
            
            if hasattr(self, 'latex_generator'):
                # 确保使用正确的方法名
                if hasattr(self.latex_generator, 'generate_paper_latex'):
                    return self.latex_generator.generate_paper_latex(safe_paper_content)
                else:
                    # 导入项目根目录下的LaTeXGenerator
                    from latex_generator import LaTeXGenerator
                    latex_gen = LaTeXGenerator()
                    return latex_gen.generate_paper_latex(safe_paper_content)
            else:
                return None
        except Exception as e:
            self.logger.warning(f"LaTeX generation failed: {e}")
            return None
            
    def _sanitize_latex_content(self, content: Dict[str, Any]) -> Dict[str, Any]:
        """处理LaTeX内容中的转义字符问题"""
        if not isinstance(content, dict):
            return content
            
        result = {}
        for key, value in content.items():
            if isinstance(value, str):
                # 修复常见的LaTeX转义字符问题
                fixed_value = value.replace('\\u', '\\\\u')  # 修复 \u 转义序列
                fixed_value = fixed_value.replace('\\x', '\\\\x')  # 修复 \x 转义序列
                result[key] = fixed_value
            elif isinstance(value, dict):
                result[key] = self._sanitize_latex_content(value)
            elif isinstance(value, list):
                result[key] = [
                    self._sanitize_latex_content(item) if isinstance(item, dict) 
                    else (item.replace('\\u', '\\\\u').replace('\\x', '\\\\x') if isinstance(item, str) else item)
                    for item in value
                ]
            else:
                result[key] = value
                
        return result
    
    def _create_failure_result(self, error_message: str, warnings: List[str]) -> PaperGenerationResult:
        """创建失败结果"""
        return PaperGenerationResult(
            paper_content={},
            quality_metrics=PaperQualityMetrics(
                overall_score=0.0,
                novelty_score=0.0,
                technical_quality_score=0.0,
                clarity_score=0.0,
                significance_score=0.0,
                reproducibility_score=0.0,
                expert_consensus=0.0,
                improvement_history=[]
            ),
            review_history=[],
            revision_history=[],
            latex_output=None,
            generation_metadata={'error': error_message},
            success=False,
            warnings=warnings
        )

    def _sanitize_for_json(self, obj, depth=0):
        """
        处理不可JSON序列化的对象，增加深度限制防止递归过深
        
        Args:
            obj: 需要处理的对象
            depth: 当前递归深度
            
        Returns:
            可JSON序列化的对象
        """
        # 防止递归过深
        if depth > 20:  # 设置最大递归深度
            return str(obj)
        
        # 处理None
        if obj is None:
            return None
        
        # 处理Mock对象
        if hasattr(obj, '_mock_return_value') or hasattr(obj, '_mock_methods'):
            return str(obj)
        
        # 处理类型对象
        if isinstance(obj, type):
            return str(obj)
        
        # 处理具有__dict__的对象
        if hasattr(obj, '__dict__') and not isinstance(obj, type):
            try:
                return {k: self._sanitize_for_json(v, depth+1) for k, v in obj.__dict__.items() 
                       if not k.startswith('_')}  # 忽略私有属性
            except:
                return str(obj)
        
        # 处理字典
        if isinstance(obj, dict):
            try:
                return {k: self._sanitize_for_json(v, depth+1) for k, v in obj.items()}
            except:
                return str(obj)
        
        # 处理列表和元组
        if isinstance(obj, (list, tuple)):
            try:
                return [self._sanitize_for_json(item, depth+1) for item in obj]
            except:
                return str(obj)
        
        # 尝试基本类型转换
        try:
            json.dumps(obj)
            return obj
        except:
            return str(obj)


def test_enhanced_paper_writer():
    """测试增强论文撰写系统"""
    print("🧪 测试增强论文撰写系统")
    
    # 创建配置
    config = PaperGenerationConfig(
        target_venue="ICML",
        paper_type="research",
        max_review_iterations=2,
        quality_threshold=6.5,
        enable_auto_revision=True,
        enable_multi_expert_review=True,
        latex_output=False  # 简化测试
    )
    
    # 初始化系统
    writer = EnhancedBrainPaperWriter(config=config)
    
    # 生成论文
    research_topic = "Brain-Inspired Adaptive Learning Networks for Visual Recognition"
    result = writer.generate_paper_with_quality_control(research_topic)
    
    # 显示结果
    print(f"\\n📊 生成结果摘要:")
    print(f"成功: {result.success}")
    print(f"最终评分: {result.quality_metrics.overall_score:.2f}/10")
    print(f"评审轮次: {len(result.review_history)}")
    print(f"修订轮次: {len(result.revision_history)}")
    print(f"论文标题: {result.paper_content.get('title', 'N/A')}")
    print(f"部分数量: {len(result.paper_content.get('sections', {}))}")
    
    return result


if __name__ == "__main__":
    test_enhanced_paper_writer()
