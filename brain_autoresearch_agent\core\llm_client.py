"""""
LLM client wrapper for AI Scientist v2 and DeepSeek API
"""

import os
import sys
import json
import re
from typing import Any, List, Dict, Tuple, Optional
import warnings
"""
LLM客户端 - 支持AI Scientist v2和DeepSeek API
"""

import os
import json
import re
from typing import Any, List, Dict, Tuple, Optional
import warnings

# 添加AI Scientist v2的路径到系统路径
AI_SCIENTIST_PATH = os.path.join(os.path.dirname(__file__), '..', '..', 'AI-Scientist-v2')
sys.path.append(AI_SCIENTIST_PATH)

# 导入模型配置管理器
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.model_config import create_model_config

# 尝试导入OpenAI客户端（用于DeepSeek）
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    warnings.warn("OpenAI package not available. Install with: pip install openai")

# 尝试导入AI Scientist v2模块
try:
    from ai_scientist.llm import (
        get_response_from_llm,
        get_batch_responses_from_llm,
        create_client,
        extract_json_between_markers,
        AVAILABLE_LLMS
    )
    from ai_scientist.utils.token_tracker import track_token_usage
    AI_SCIENTIST_AVAILABLE = True
except ImportError as e:
    AI_SCIENTIST_AVAILABLE = False
    warnings.warn(f"Could not import AI Scientist v2 modules: {e}. Using direct API calls.")

class LLMClient:
    """
    LLM client wrapper for AI Scientist v2 and DeepSeek API
    """
    def __init__(self, model: str = None, temperature: float = 0.7, api_key: str = None, config_path: Optional[str] = None, provider: str = None):
        """
        Initialize LLM client.
        Args:
            model: Model name, will use best available model if not provided
            temperature: Temperature parameter
            api_key: API key, will read from env if not provided
            config_path: Optional config file path
            provider: Provider (e.g. "deepseek")
        """
        self.deepseek_mode = "chat"  # Default mode
        self.ai_scientist_mode = False
        self.deepseek_mode = False
        
        # 创建配置对象
        self.config = create_model_config(config_path)
        
        # 如果未指定模型，使用配置管理器选择最佳模型
        if model is None:
            if provider == "deepseek":
                self.model = "deepseek-chat"  # DeepSeek默认模型
            else:
                available_models = self.config.detect_available_models()
                best_model_config = self.config.select_best_model()
                if isinstance(best_model_config, dict) and 'selected_model' in best_model_config:
                    self.model = best_model_config['selected_model']
                else:
                    self.model = "deepseek-chat"  # 默认模型
        else:
            self.model = model
            
        self.temperature = temperature
        self.api_key = api_key or os.getenv("DEEPSEEK_API_KEY")
        
        # 如果明确指定provider为deepseek或模型为deepseek模型，优先使用DeepSeek
        if (provider == "deepseek" or model in ["deepseek-chat", "deepseek-reasoner"]) and self._try_deepseek_setup():
            self.deepseek_mode = True
            print(f"✅ DeepSeek API初始化成功: {self.model}")
            if self.model == "deepseek-reasoner":
                print("🧠 使用DeepSeek推理模式，适合复杂推理和多步分析")
        # 然后尝试AI Scientist v2（其他模型）
        elif self._try_ai_scientist_setup():
            self.ai_scientist_mode = True
            print(f"✅ AI Scientist v2初始化成功: {self.model}")
        else:
            print(f"⚠️ 使用模拟模式: {self.model}")
    
    def _try_deepseek_setup(self) -> bool:
        """尝试设置DeepSeek API"""
        # 检查是否显式设置了模拟模式
        if os.environ.get("MOCK_MODE", "").lower() == "true":
            print("⚠️ 检测到MOCK_MODE=true，强制使用模拟模式")
            return False
            
        if not OPENAI_AVAILABLE or not self.api_key:
            return False
        
        try:
            self.deepseek_client = OpenAI(
                api_key=self.api_key,
                base_url="https://api.deepseek.com"
            )
            # 测试连接
            test_response = self.deepseek_client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10,
                temperature=0.1
            )
            return True
        except Exception as e:
            warnings.warn(f"DeepSeek API setup failed: {e}")
            return False
    
    def _try_ai_scientist_setup(self) -> bool:
        """尝试设置AI Scientist v2"""
        if not AI_SCIENTIST_AVAILABLE:
            return False
        
        # 额外支持的DeepSeek模型列表
        DEEPSEEK_MODELS = ["deepseek-chat", "deepseek-reasoner"]
        
        try:
            # 如果模型是DeepSeek模型，则不检查AVAILABLE_LLMS
            if self.model in DEEPSEEK_MODELS:
                print(f"⚠️ {self.model} 不是AI Scientist支持的模型，但可以直接使用")
                # 在这种情况下返回False，让_try_deepseek_setup处理
                return False
                
            if self.model not in AVAILABLE_LLMS:
                print(f"⚠️ 模型 {self.model} 不在可用列表中")
                print(f"📋 可用模型: {AVAILABLE_LLMS[:3]}...")
                # 直接使用模拟模式而不是警告
                return False
            
            self.client, self.client_model = create_client(self.model)
            print(f"✅ AI Scientist客户端初始化成功")
            return True
        except Exception as e:
            print(f"⚠️ AI Scientist客户端初始化失败: {e}")
            return False
        
    def get_response(
        self, 
        prompt: str, 
        system_message: str = "You are a helpful research assistant.",
        msg_history: Optional[List[Dict[str, Any]]] = None,
        print_debug: bool = False
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """
        获取单个响应
        
        Args:
            prompt: 用户提示
            system_message: 系统消息
            msg_history: 消息历史
            print_debug: 是否打印调试信息
            
        Returns:
            (响应内容, 新的消息历史)
        """
        if msg_history is None:
            msg_history = []
        
        if print_debug:
            print(f"🔍 调用LLM: {self.model}")
            print(f"📝 提示词: {prompt[:100]}...")
        
        # 使用DeepSeek API
        if self.deepseek_mode:
            try:
                return self._get_deepseek_response(prompt, system_message, msg_history, print_debug)
            except Exception as e:
                print(f"❌ DeepSeek API调用失败: {e}")
                return self._mock_response(prompt, msg_history)
        
        # 使用AI Scientist v2
        elif self.ai_scientist_mode:
            try:
                return get_response_from_llm(
                    prompt=prompt,
                    client=self.client,
                    model=self.client_model,
                    system_message=system_message,
                    print_debug=print_debug,
                    msg_history=msg_history,
                    temperature=self.temperature
                )
            except Exception as e:
                print(f"❌ AI Scientist LLM调用失败: {e}")
                return self._mock_response(prompt, msg_history)
        
        # 使用模拟模式
        else:
            return self._mock_response(prompt, msg_history)
    
    def _get_deepseek_response(
        self, 
        prompt: str, 
        system_message: str,
        msg_history: List[Dict[str, Any]],
        print_debug: bool = False
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """使用DeepSeek API获取响应"""
        messages = [{"role": "system", "content": system_message}]
        messages.extend(msg_history)
        messages.append({"role": "user", "content": prompt})
        
        if print_debug:
            print(f"📤 DeepSeek请求: {len(messages)} 条消息")
        
        response = self.deepseek_client.chat.completions.create(
            model=self.model,
            messages=messages,
            temperature=self.temperature,
            max_tokens=4096
        )
        
        content = response.choices[0].message.content
        new_msg_history = msg_history + [
            {"role": "user", "content": prompt},
            {"role": "assistant", "content": content}
        ]
        
        if print_debug:
            print(f"📥 DeepSeek响应: {len(content)} 字符")
        
        return content, new_msg_history
    
    def get_batch_responses(
        self,
        prompt: str,
        system_message: str = "You are a helpful research assistant.",
        n_responses: int = 1,
        msg_history: Optional[List[Dict[str, Any]]] = None,
        print_debug: bool = False
    ) -> Tuple[List[str], List[List[Dict[str, Any]]]]:
        """
        获取批量响应
        
        Args:
            prompt: 用户提示
            system_message: 系统消息
            n_responses: 响应数量
            msg_history: 消息历史
            print_debug: 是否打印调试信息
            
        Returns:
            (响应内容列表, 新的消息历史列表)
        """
        if self.deepseek_mode:
            try:
                return self._get_deepseek_batch_responses(
                    prompt, system_message, n_responses, msg_history, print_debug
                )
            except Exception as e:
                print(f"❌ DeepSeek批量调用失败: {e}")
                return self._mock_batch_response(prompt, msg_history, n_responses)
        
        elif self.ai_scientist_mode:
            try:
                return get_batch_responses_from_llm(
                    prompt=prompt,
                    client=self.client,
                    model=self.client_model,
                    system_message=system_message,
                    print_debug=print_debug,
                    msg_history=msg_history,
                    temperature=self.temperature,
                    n_responses=n_responses
                )
            except Exception as e:
                print(f"❌ 批量LLM调用失败: {e}")
                return self._mock_batch_response(prompt, msg_history, n_responses)
        else:
            return self._mock_batch_response(prompt, msg_history, n_responses)
    
    def _get_deepseek_batch_responses(
        self,
        prompt: str,
        system_message: str,
        n_responses: int,
        msg_history: Optional[List[Dict[str, Any]]],
        print_debug: bool = False
    ) -> Tuple[List[str], List[List[Dict[str, Any]]]]:
        """使用DeepSeek API获取批量响应"""
        responses = []
        histories = []
        
        for i in range(n_responses):
            response, history = self._get_deepseek_response(
                prompt, system_message, msg_history or [], print_debug
            )
            responses.append(response)
            histories.append(history)
        
        return responses, histories
    
    def extract_json(self, llm_output: str) -> Optional[Dict]:
        """
        从LLM输出中提取JSON
        
        Args:
            llm_output: LLM的输出文本
            
        Returns:
            提取的JSON字典，如果没有找到则返回None
        """
        try:
            # 首先尝试AI Scientist的提取方法
            if AI_SCIENTIST_AVAILABLE:
                try:
                    extracted = extract_json_between_markers(llm_output)
                    if extracted:
                        return extracted
                except:
                    pass
            
            # 使用自定义正则表达式提取JSON
            json_patterns = [
                r'```json\s*(\{.*?\})\s*```',
                r'```\s*(\{.*?\})\s*```',
                r'\{.*?\}',
            ]
            
            for pattern in json_patterns:
                matches = re.findall(pattern, llm_output, re.DOTALL)
                for match in matches:
                    try:
                        # 清理和解析JSON
                        json_str = match.strip()
                        parsed = json.loads(json_str)
                        return parsed
                    except json.JSONDecodeError:
                        continue
            
            return None
            
        except Exception as e:
            print(f"⚠️ JSON提取失败: {e}")
            return self._mock_json_extraction(llm_output)
    
    def _mock_response(self, prompt: str, msg_history: Optional[List[Dict[str, Any]]] = None) -> Tuple[str, List[Dict[str, Any]]]:
        """模拟LLM响应（用于测试）"""
        if msg_history is None:
            msg_history = []
        
        # 根据提示词生成更智能的模拟响应
        prompt_lower = prompt.lower()
        
        if "research value" in prompt_lower or "assessment" in prompt_lower:
            mock_response = """
            This research topic demonstrates significant academic value and practical implications for the field of brain-inspired intelligence. From an AI technology perspective, this research direction shows strong innovation potential and forward-thinking approach. The proposed methods could advance our understanding of efficient neural architectures while maintaining biological plausibility. The research addresses current limitations in conventional deep learning approaches and offers promising avenues for energy-efficient computation.
            """
        elif "abstract" in prompt_lower:
            mock_response = """
            Brain-inspired neural networks represent a paradigm shift in artificial intelligence, 
            drawing inspiration from biological neural systems to create more efficient and adaptable 
            computational models. This paper presents a novel approach to developing brain-inspired 
            architectures that significantly improve learning efficiency while maintaining high 
            performance across diverse tasks. Our methodology incorporates key principles from 
            neuroscience, including sparse connectivity, temporal dynamics, and adaptive plasticity. 
            Experimental results demonstrate superior performance compared to traditional deep learning 
            approaches, with 40% improved energy efficiency and 25% faster convergence rates.
            """
        elif "introduction" in prompt_lower:
            mock_response = """
            The quest for artificial intelligence that rivals human cognitive capabilities has led 
            researchers to increasingly look toward the brain for inspiration. Traditional deep 
            learning approaches, while successful, often require massive computational resources 
            and exhibit limited adaptability compared to biological systems. Brain-inspired 
            computing represents a fundamental shift toward more efficient, adaptive, and robust 
            AI systems. This paper explores novel architectures that incorporate key principles 
            from neuroscience to create more efficient learning algorithms.
            """
        elif "methodology" in prompt_lower:
            mock_response = """
            Our brain-inspired approach incorporates three key biological principles: (1) sparse 
            connectivity patterns that reduce computational overhead, (2) temporal dynamics that 
            enable sequential learning, and (3) adaptive plasticity mechanisms that allow for 
            continuous improvement. The architecture employs spiking neural networks with 
            biologically-plausible learning rules, including spike-timing-dependent plasticity 
            (STDP) and homeostatic mechanisms for maintaining network stability.
            """
        elif "experiment" in prompt_lower:
            mock_response = """
            We evaluated our brain-inspired architecture on three benchmark datasets: MNIST, 
            CIFAR-10, and ImageNet. The experimental setup included baseline comparisons with 
            traditional CNNs, RNNs, and state-of-the-art transformer models. Performance metrics 
            included accuracy, energy consumption, training time, and adaptation speed. All 
            experiments were conducted using PyTorch on NVIDIA A100 GPUs with standardized 
            hyperparameter settings.
            """
        elif "results" in prompt_lower:
            mock_response = """
            Our brain-inspired model achieved 94.2% accuracy on MNIST, 89.1% on CIFAR-10, and 
            76.8% on ImageNet, representing competitive performance with significantly reduced 
            computational requirements. Energy consumption was reduced by 40% compared to 
            traditional deep networks, while training time decreased by 25%. The model 
            demonstrated superior adaptation capabilities in transfer learning scenarios.
            """
        elif "conclusion" in prompt_lower:
            mock_response = """
            This work demonstrates the significant potential of brain-inspired architectures for 
            creating more efficient and adaptive AI systems. Our approach successfully combines 
            biological principles with practical engineering considerations, resulting in models 
            that achieve competitive performance with reduced computational overhead. Future work 
            will explore larger-scale applications and investigate additional neurobiological 
            mechanisms for further improvements.
            """
        elif "extract" in prompt_lower or "json" in prompt_lower:
            mock_response = """
            Based on the paper content, here's the extracted information in JSON format:
            
            ```json
            {
                "datasets": ["MNIST", "CIFAR-10", "ImageNet"],
                "network_architectures": ["Spiking Neural Networks", "Brain-inspired CNN"],
                "platforms_tools": ["PyTorch", "NVIDIA A100"],
                "research_methods": ["bio-inspired learning", "STDP"],
                "evaluation_metrics": ["accuracy", "energy efficiency"],
                "brain_inspiration": ["sparse connectivity", "temporal dynamics"],
                "ai_techniques": ["deep learning", "neural plasticity"]
            }
            ```
            """
        elif "discussion" in prompt_lower:
            mock_response = """
            The experimental results demonstrate the effectiveness of our brain-inspired approach in achieving competitive performance while significantly reducing computational requirements. The 40% improvement in energy efficiency can be attributed to the sparse connectivity patterns that mirror biological neural networks. The faster convergence rates observed in our experiments suggest that biologically-plausible learning rules can indeed accelerate the training process compared to traditional backpropagation.

            These findings have important implications for the development of neuromorphic computing systems and edge AI applications where energy efficiency is crucial. However, some limitations should be acknowledged, including the current restriction to relatively simple datasets and the need for further optimization of the plasticity mechanisms. Future work should focus on scaling these approaches to larger, more complex problems while maintaining the biological plausibility of the underlying mechanisms.
            """
        else:
            # 为其他类型的请求提供适当的响应
            if "search" in prompt_lower or "query" in prompt_lower:
                mock_response = """brain-inspired computing, neural plasticity, spiking neural networks, neuromorphic computing, biologically plausible learning"""
            elif "title" in prompt_lower:
                mock_response = """Efficient Brain-Inspired Neural Architectures: A Novel Approach to Energy-Efficient Deep Learning"""
            elif "keywords" in prompt_lower:
                mock_response = """brain-inspired computing, neural plasticity, energy efficiency, spiking neural networks, biologically plausible learning, neuromorphic computing, adaptive algorithms"""
            else:
                mock_response = f"""
                Based on the research inquiry, this analysis provides comprehensive insights into the proposed topic. The brain-inspired approach represents a significant advancement in artificial intelligence, combining biological principles with computational efficiency. The methodology demonstrates strong potential for addressing current limitations in traditional deep learning systems.

                Key technical considerations include the integration of sparse connectivity patterns, temporal dynamics, and adaptive learning mechanisms. These elements work synergistically to create more efficient and robust neural architectures. The research contributes to our understanding of how biological principles can inform computational design, leading to more sustainable and effective AI systems.

                This research direction offers promising opportunities for both theoretical advancement and practical applications in various domains.
                """

        new_msg_history = msg_history + [
            {"role": "user", "content": prompt},
            {"role": "assistant", "content": mock_response}
        ]
        
        return mock_response, new_msg_history
    
    def _mock_batch_response(
        self, 
        prompt: str, 
        msg_history: Optional[List[Dict[str, Any]]] = None, 
        n_responses: int = 1
    ) -> Tuple[List[str], List[List[Dict[str, Any]]]]:
        """模拟批量LLM响应"""
        responses = []
        histories = []
        
        for i in range(n_responses):
            response, history = self._mock_response(f"{prompt} (Response {i+1})", msg_history)
            responses.append(response)
            histories.append(history)
        
        return responses, histories
    
    def _mock_json_extraction(self, llm_output: str) -> Optional[Dict]:
        """模拟JSON提取"""
        # 简单的模拟JSON提取
        if "json" in llm_output.lower():
            return {
                "datasets": ["mock_dataset"],
                "network_architectures": ["mock_architecture"],
                "platforms_tools": ["mock_tool"],
                "research_methods": ["mock_method"],
                "evaluation_metrics": ["mock_metric"],
                "brain_inspiration": ["mock_brain_concept"],
                "ai_techniques": ["mock_ai_technique"]
            }
        return None
    
    def is_available(self) -> bool:
        """检查LLM客户端是否可用"""
        return self.deepseek_mode or self.ai_scientist_mode
    
    def get_available_models(self) -> List[str]:
        """获取可用模型列表"""
        if self.deepseek_mode:
            return ["deepseek-chat", "deepseek-reasoner"]
        elif AI_SCIENTIST_AVAILABLE:
            return AVAILABLE_LLMS
        else:
            return ["deepseek-chat", "gpt-4o-2024-11-20", "claude-3-5-sonnet-20241022"]  # 模拟列表
    
    def __str__(self):
        if self.deepseek_mode:
            mode = "DeepSeek API"
        elif self.ai_scientist_mode:
            mode = "AI Scientist v2"
        else:
            mode = "Mock"
        return f"LLMClient(model={self.model}, temperature={self.temperature}, mode={mode})"
    
    def generate_response(self, prompt: str, system_message: str = "You are a helpful research assistant.") -> str:
        """
        生成响应的简化接口
        
        Args:
            prompt: 用户提示
            system_message: 系统消息
            
        Returns:
            生成的响应文本
        """
        response, _ = self.get_response(prompt, system_message)
        return response

    def get_text_response(self, prompt: str, **kwargs):
        """
        获取文本响应（作为get_response的别名）
        
        Args:
            prompt: 提示词
            **kwargs: 其他参数，可包含system_message, print_debug等，
                     但会忽略不支持的参数如model_type, model_name, temperature等
        
        Returns:
            str: 文本响应
        """
        try:
            # 只提取get_response支持的参数
            supported_params = {
                'system_message': kwargs.get('system_message', "You are a helpful research assistant."),
                'msg_history': kwargs.get('msg_history'),
                'print_debug': kwargs.get('print_debug', False)
            }
            
            # 过滤掉None值
            filtered_params = {k: v for k, v in supported_params.items() if v is not None}
            
            # 调用get_response并只返回文本内容
            response_tuple = self.get_response(prompt, **filtered_params)
            
            # 处理返回值，确保返回字符串
            if isinstance(response_tuple, tuple) and len(response_tuple) > 0:
                return response_tuple[0]  # 只返回文本内容，不返回消息历史
            elif isinstance(response_tuple, str):
                return response_tuple
            else:
                return str(response_tuple)  # 确保返回字符串
        except Exception as e:
            print(f"⚠️ LLMClient.get_text_response错误: {e}")
            return f"获取文本响应失败: {e}"
    
    def generate_chat(self, messages, temperature=None, max_tokens=None, **kwargs):
        """
        生成对话回复 (兼容性方法)
        
        Args:
            messages: 消息列表，格式为[{"role": "system", "content": "..."}, ...]
            temperature: 温度参数
            max_tokens: 最大生成token数
            
        Returns:
            生成的文本响应
        """
        try:
            # 提取系统消息和用户消息
            system_msg = None
            user_msg = None
            
            for msg in messages:
                if msg["role"] == "system":
                    system_msg = msg["content"]
                elif msg["role"] == "user":
                    user_msg = msg["content"]
            
            # 如果找到了系统消息和用户消息，使用get_response
            if user_msg:
                response, _ = self.get_response(
                    prompt=user_msg, 
                    system_message=system_msg or "You are a helpful assistant.",
                    print_debug=False
                )
                return response
            return "未提供用户消息"
        except Exception as e:
            print(f"⚠️ 聊天生成失败: {e}")
            return f"模拟聊天响应: {messages[-1]['content'][:30]}..."
    
    def generate_text(self, prompt, temperature=None, max_tokens=None, **kwargs):
        """
        生成文本 (兼容性方法)
        
        Args:
            prompt: 提示词
            temperature: 温度参数
            max_tokens: 最大生成token数
            
        Returns:
            生成的文本响应
        """
        try:
            response, _ = self.get_response(
                    prompt=prompt,
                system_message="You are a helpful assistant.",
                print_debug=False
                )
            return response
        except Exception as e:
            print(f"⚠️ 文本生成失败: {e}")
            return f"模拟文本响应: {prompt[:30]}..."

    def extract_json_from_text(self, text):
        """
        从文本中提取JSON (兼容性方法)
        
        Args:
            text: 输入文本
            
        Returns:
            提取的JSON对象，如果提取失败则返回None
        """
        try:
            # 使用现有的extract_json方法
            return self.extract_json(text)
        except Exception as e:
            print(f"⚠️ JSON提取失败: {e}")
            # 返回简单的模拟JSON
            return {"error": "JSON提取失败", "mock": True}