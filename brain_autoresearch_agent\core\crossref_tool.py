"""
Crossref API Tool

用于搜索学术文献的引用数据
"""

import requests
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

class CrossrefTool:
    """用于与Crossref API交互的工具类"""
    
    def __init__(self):
        """初始化Crossref工具"""
        self.base_url = "https://api.crossref.org/works"
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def search_papers(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        根据关键词搜索Crossref论文
        
        Args:
            query: 搜索查询
            max_results: 最大返回结果数量
            
        Returns:
            论文信息列表
        """
        params = {
            "query": query,
            "rows": max_results,
            "sort": "relevance",
            "order": "desc"
        }
        
        headers = {
            "User-Agent": "AutoResearchAgent/1.0 (<EMAIL>)"
        }
        
        try:
            response = requests.get(self.base_url, headers=headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            items = data.get('message', {}).get('items', [])
            
            papers = []
            for item in items:
                # 提取基本信息
                title = item.get('title', [''])[0] if isinstance(item.get('title', []), list) else item.get('title', '')
                abstract = item.get('abstract', '')
                
                # 提取DOI和URL
                doi = item.get('DOI', '')
                url = f"https://doi.org/{doi}" if doi else ""
                
                # 提取日期
                published_date = None
                date_parts = item.get('published', {}).get('date-parts', [[]])
                if date_parts and len(date_parts[0]) > 0:
                    try:
                        published_date = datetime(year=date_parts[0][0], 
                                                 month=date_parts[0][1] if len(date_parts[0]) > 1 else 1,
                                                 day=date_parts[0][2] if len(date_parts[0]) > 2 else 1)
                    except (ValueError, TypeError):
                        pass
                
                # 提取年份
                year = date_parts[0][0] if date_parts and len(date_parts[0]) > 0 else None
                
                # 提取作者
                authors = []
                for author in item.get('author', []):
                    given = author.get('given', '')
                    family = author.get('family', '')
                    name = f"{given} {family}".strip() or "Unknown"
                    authors.append({"name": name})
                
                # 提取出版物信息
                venue = item.get('container-title', [''])[0] if isinstance(item.get('container-title', []), list) else ''
                
                # 构建论文信息
                paper = {
                    "title": title,
                    "abstract": abstract,
                    "authors": authors,
                    "year": year,
                    "venue": venue,
                    "url": url,
                    "paperId": doi,
                    "doi": doi,
                    "publicationDate": published_date.isoformat() if published_date else None,
                    "source": "crossref"
                }
                
                papers.append(paper)
            
            return papers
            
        except Exception as e:
            self.logger.error(f"Crossref API request failed: {e}")
            return []
    
    def get_paper_by_doi(self, doi: str) -> Optional[Dict[str, Any]]:
        """
        通过DOI获取论文详情
        
        Args:
            doi: DOI标识符
            
        Returns:
            论文信息字典或None
        """
        url = f"{self.base_url}/{doi}"
        
        headers = {
            "User-Agent": "AutoResearchAgent/1.0 (<EMAIL>)"
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            item = data.get('message', {})
            
            # 同上面方法类似的处理
            title = item.get('title', [''])[0] if isinstance(item.get('title', []), list) else ''
            abstract = item.get('abstract', '')
            
            doi = item.get('DOI', '')
            url = f"https://doi.org/{doi}" if doi else ""
            
            date_parts = item.get('published', {}).get('date-parts', [[]])
            year = date_parts[0][0] if date_parts and len(date_parts[0]) > 0 else None
            
            authors = []
            for author in item.get('author', []):
                given = author.get('given', '')
                family = author.get('family', '')
                name = f"{given} {family}".strip() or "Unknown"
                authors.append({"name": name})
            
            venue = item.get('container-title', [''])[0] if isinstance(item.get('container-title', []), list) else ''
            
            return {
                "title": title,
                "abstract": abstract,
                "authors": authors,
                "year": year,
                "venue": venue,
                "url": url,
                "paperId": doi,
                "doi": doi,
                "source": "crossref"
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get paper by DOI: {e}")
            return None
    
    def mock_search_papers(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        模拟论文搜索（用于测试和开发）
        
        Args:
            query: 搜索查询
            max_results: 最大返回结果数量
            
        Returns:
            模拟的论文信息列表
        """
        mock_papers = []
        for i in range(1, max_results + 1):
            paper = {
                "title": f"Crossref: {query} Research Paper {i}",
                "abstract": f"This is a mock abstract about {query} research, designed to simulate Crossref API responses.",
                "authors": [
                    {"name": f"Crossref Author {i}A"},
                    {"name": f"Crossref Author {i}B"}
                ],
                "year": 2023 - (i % 3),
                "venue": f"Journal of {query.title()} Research",
                "url": f"https://doi.org/10.1234/mock.{i}",
                "paperId": f"10.1234/mock.{i}",
                "doi": f"10.1234/mock.{i}",
                "publicationDate": f"2023-0{i}-01",
                "source": "crossref"
            }
            mock_papers.append(paper)
        
        return mock_papers
