"""
脑启发智能AutoResearch Agent - 所有专家代理综合测试
测试所有五个专家代理的独立功能和协作能力
注意：请在cmd环境下运行 conda activate pytorch 后再执行此脚本
"""

import os
import sys
import time
import json
from typing import Dict, List, Any

# 添加路径以支持模块导入
sys.path.append('.')

def main():
    """运行所有专家代理的综合测试"""
    print("🧠 脑启发智能AutoResearch Agent - 所有专家代理综合测试")
    print("=" * 80)
    print(f"⏰ 测试开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("⚠️  请确保在cmd环境下运行 'conda activate pytorch' 后再执行")
    
    # 设置API密钥
    api_key = "sk-1b1d72e2e10643029de548b655e1f93e"
    os.environ["DEEPSEEK_API_KEY"] = api_key
    
    # 测试结果记录
    test_results = {
        "ai_technology_expert": <PERSON><PERSON><PERSON>,
        "neuroscience_expert": <PERSON><PERSON><PERSON>,
        "data_analysis_expert": <PERSON><PERSON><PERSON>,
        "paper_writing_expert": <PERSON><PERSON><PERSON>,
        "experiment_design_expert": False,
        "multi_expert_collaboration": False,
        "five_expert_consensus": False
    }
    
    print("\n" + "🔬 五专家代理系统全面测试".center(80, "="))
    
    # 1. 测试AI技术专家
    print("\n📋 1. AI技术专家独立功能测试")
    print("-" * 60)
    test_results["ai_technology_expert"] = test_ai_technology_expert(api_key)
    
    # 2. 测试神经科学专家
    print("\n📋 2. 神经科学专家独立功能测试")
    print("-" * 60)
    test_results["neuroscience_expert"] = test_neuroscience_expert(api_key)
    
    # 3. 测试数据分析专家
    print("\n📋 3. 数据分析专家独立功能测试")
    print("-" * 60)
    test_results["data_analysis_expert"] = test_data_analysis_expert(api_key)
    
    # 4. 测试论文写作专家
    print("\n📋 4. 论文写作专家独立功能测试")
    print("-" * 60)
    test_results["paper_writing_expert"] = test_paper_writing_expert(api_key)
    
    # 5. 测试实验设计专家
    print("\n📋 5. 实验设计专家独立功能测试")
    print("-" * 60)
    test_results["experiment_design_expert"] = test_experiment_design_expert(api_key)
    
    # 6. 测试多专家协作
    print("\n📋 6. 多专家协作功能测试")
    print("-" * 60)
    test_results["multi_expert_collaboration"] = test_multi_expert_collaboration(api_key)
    
    # 7. 测试五专家共识
    print("\n📋 7. 五专家共识决策测试")
    print("-" * 60)
    test_results["five_expert_consensus"] = test_five_expert_consensus(api_key)
    
    # 输出测试结果摘要
    print_test_summary(test_results)

def test_ai_technology_expert(api_key: str) -> bool:
    """测试AI技术专家"""
    try:
        from core.llm_client import LLMClient
        from agents.expert_agents.ai_technology_expert import AITechnologyExpert
        
        print("🤖 初始化AI技术专家...")
        llm_client = LLMClient(api_key=api_key, model="deepseek-chat", temperature=0.3)
        ai_expert = AITechnologyExpert(llm_client)
        
        # 测试技术分析
        test_input = {
            "research_topic": "Brain-inspired Attention Mechanisms in Computer Vision",
            "technical_description": "A novel neural network architecture that mimics visual attention pathways in the brain for enhanced object recognition and scene understanding."
        }
        
        print("🔍 执行AI技术分析...")
        response = ai_expert.analyze(test_input)
        
        print(f"✅ AI技术专家分析完成")
        print(f"   置信度: {response.confidence:.2f}")
        print(f"   分析内容: {response.content[:100]}...")
        
        return response.confidence > 0.5
        
    except Exception as e:
        print(f"❌ AI技术专家测试失败: {e}")
        return False

def test_neuroscience_expert(api_key: str) -> bool:
    """测试神经科学专家"""
    try:
        from core.llm_client import LLMClient
        from agents.expert_agents.neuroscience_expert import NeuroscienceExpert
        
        print("🧠 初始化神经科学专家...")
        llm_client = LLMClient(api_key=api_key, model="deepseek-chat", temperature=0.2)
        neuro_expert = NeuroscienceExpert(llm_client)
        
        # 测试脑启发分析
        test_input = {
            "ai_system": "A spiking neural network that implements STDP learning for visual pattern recognition",
            "research_topic": "Biologically Plausible Learning in Artificial Neural Networks"
        }
        
        print("🔬 执行神经科学分析...")
        response = neuro_expert.analyze(test_input)
        
        print(f"✅ 神经科学专家分析完成")
        print(f"   置信度: {response.confidence:.2f}")
        print(f"   分析内容: {response.content[:100]}...")
        
        return response.confidence > 0.5
        
    except Exception as e:
        print(f"❌ 神经科学专家测试失败: {e}")
        return False

def test_data_analysis_expert(api_key: str) -> bool:
    """测试数据分析专家"""
    try:
        from core.llm_client import LLMClient
        from agents.expert_agents.data_analysis_expert import DataAnalysisExpert
        
        print("📊 初始化数据分析专家...")
        llm_client = LLMClient(api_key=api_key, model="deepseek-chat", temperature=0.3)
        data_expert = DataAnalysisExpert(llm_client)
        
        # 测试数据分析
        test_input = {
            "dataset_description": "Neural activity recordings from visual cortex during object recognition tasks",
            "analysis_requirements": "Identify patterns in neural firing rates and their correlation with recognition accuracy"
        }
        
        print("📈 执行数据分析...")
        response = data_expert.analyze(test_input)
        
        print(f"✅ 数据分析专家分析完成")
        print(f"   置信度: {response.confidence:.2f}")
        print(f"   分析内容: {response.content[:100]}...")
        
        return response.confidence > 0.5
        
    except Exception as e:
        print(f"❌ 数据分析专家测试失败: {e}")
        return False

def test_paper_writing_expert(api_key: str) -> bool:
    """测试论文写作专家"""
    try:
        from core.llm_client import LLMClient
        from agents.expert_agents.paper_writing_expert import PaperWritingExpert
        
        print("📝 初始化论文写作专家...")
        llm_client = LLMClient(api_key=api_key, model="deepseek-chat", temperature=0.4)
        writing_expert = PaperWritingExpert(llm_client)
        
        # 测试论文结构化
        test_input = {
            "research_findings": "Novel brain-inspired attention mechanism improves object recognition accuracy by 15%",
            "target_venue": "Nature Neuroscience",
            "writing_task": "abstract_generation"
        }
        
        print("✍️ 执行论文写作分析...")
        response = writing_expert.analyze(test_input)
        
        print(f"✅ 论文写作专家分析完成")
        print(f"   置信度: {response.confidence:.2f}")
        print(f"   分析内容: {response.content[:100]}...")
        
        return response.confidence > 0.5
        
    except Exception as e:
        print(f"❌ 论文写作专家测试失败: {e}")
        return False

def test_experiment_design_expert(api_key: str) -> bool:
    """测试实验设计专家"""
    try:
        from core.llm_client import LLMClient
        from agents.expert_agents.experiment_design_expert import ExperimentDesignExpert
        
        print("🔬 初始化实验设计专家...")
        llm_client = LLMClient(api_key=api_key, model="deepseek-chat", temperature=0.3)
        experiment_expert = ExperimentDesignExpert(llm_client)
        
        # 测试实验设计
        test_input = {
            "research_hypothesis": "Brain-inspired attention mechanisms will outperform traditional CNN attention in object recognition tasks",
            "experimental_context": "Computer vision benchmark evaluation"
        }
        
        print("🧪 执行实验设计分析...")
        response = experiment_expert.analyze(test_input)
        
        print(f"✅ 实验设计专家分析完成")
        print(f"   置信度: {response.confidence:.2f}")
        print(f"   分析内容: {response.content[:100]}...")
        
        return response.confidence > 0.5
        
    except Exception as e:
        print(f"❌ 实验设计专家测试失败: {e}")
        return False

def test_multi_expert_collaboration(api_key: str) -> bool:
    """测试多专家协作"""
    try:
        from core.llm_client import LLMClient
        from agents.expert_agents.ai_technology_expert import AITechnologyExpert
        from agents.expert_agents.neuroscience_expert import NeuroscienceExpert
        
        print("🤝 初始化多专家协作...")
        llm_client = LLMClient(api_key=api_key, model="deepseek-chat", temperature=0.3)
        
        ai_expert = AITechnologyExpert(llm_client)
        neuro_expert = NeuroscienceExpert(llm_client)
        
        # AI专家先分析
        ai_input = {
            "research_topic": "Neuromorphic Computing for Edge AI",
            "technical_description": "Low-power neural processing units inspired by brain architecture"
        }
        
        print("🤖 AI专家分析...")
        ai_response = ai_expert.analyze(ai_input)
        
        # 神经科学专家协作
        collaboration_context = {
            "collaboration_topic": "Neuromorphic Computing for Edge AI",
            "ai_expert_insights": ai_response.content
        }
        
        print("🧠 神经科学专家协作分析...")
        neuro_response = neuro_expert.collaborate(ai_response, collaboration_context)
        
        print(f"✅ 多专家协作完成")
        print(f"   AI专家置信度: {ai_response.confidence:.2f}")
        print(f"   神经科学专家协作置信度: {neuro_response.confidence:.2f}")
        
        return (ai_response.confidence > 0.5 and neuro_response.confidence > 0.5)
        
    except Exception as e:
        print(f"❌ 多专家协作测试失败: {e}")
        return False

def test_five_expert_consensus(api_key: str) -> bool:
    """测试五专家共识决策"""
    try:
        from core.llm_client import LLMClient
        from agents.expert_agents.ai_technology_expert import AITechnologyExpert
        from agents.expert_agents.neuroscience_expert import NeuroscienceExpert
        from agents.expert_agents.data_analysis_expert import DataAnalysisExpert
        from agents.expert_agents.paper_writing_expert import PaperWritingExpert
        from agents.expert_agents.experiment_design_expert import ExperimentDesignExpert
        
        print("🏛️ 初始化五专家共识系统...")
        llm_client = LLMClient(api_key=api_key, model="deepseek-chat", temperature=0.3)
        
        experts = {
            "AI技术专家": AITechnologyExpert(llm_client),
            "神经科学专家": NeuroscienceExpert(llm_client),
            "数据分析专家": DataAnalysisExpert(llm_client),
            "论文写作专家": PaperWritingExpert(llm_client),
            "实验设计专家": ExperimentDesignExpert(llm_client)
        }
        
        # 共同研究议题
        consensus_topic = {
            "research_question": "How can brain-inspired deep learning architectures improve few-shot learning performance?",
            "context": "Developing more efficient learning algorithms that require fewer training examples"
        }
        
        expert_responses = {}
        total_confidence = 0
        
        print("🔄 执行五专家独立分析...")
        for expert_name, expert in experts.items():
            print(f"   {expert_name}分析中...")
            response = expert.analyze(consensus_topic)
            expert_responses[expert_name] = response
            total_confidence += response.confidence
            
        average_confidence = total_confidence / len(experts)
        
        print(f"✅ 五专家共识分析完成")
        print(f"   平均置信度: {average_confidence:.2f}")
        print(f"   参与专家数: {len(expert_responses)}")
        
        # 打印每个专家的核心观点
        for expert_name, response in expert_responses.items():
            print(f"   {expert_name}: {response.content[:80]}...")
        
        return average_confidence > 0.6 and len(expert_responses) == 5
        
    except Exception as e:
        print(f"❌ 五专家共识测试失败: {e}")
        return False

def print_test_summary(test_results: Dict[str, bool]):
    """打印测试结果摘要"""
    print("\n" + "📊 测试结果摘要".center(80, "="))
    
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"\n总体测试结果: {passed_tests}/{total_tests} 通过 ({success_rate:.1f}%)")
    print("-" * 80)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        test_name_cn = {
            "ai_technology_expert": "AI技术专家",
            "neuroscience_expert": "神经科学专家", 
            "data_analysis_expert": "数据分析专家",
            "paper_writing_expert": "论文写作专家",
            "experiment_design_expert": "实验设计专家",
            "multi_expert_collaboration": "多专家协作",
            "five_expert_consensus": "五专家共识"
        }.get(test_name, test_name)
        
        print(f"{test_name_cn:<20} {status}")
    
    print("-" * 80)
    
    if success_rate >= 80:
        print("🎉 所有专家代理系统运行良好!")
    elif success_rate >= 60:
        print("⚠️ 大部分功能正常，部分模块需要优化")
    else:
        print("🔧 系统需要进一步调试和完善")
    
    print(f"\n⏰ 测试完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

if __name__ == "__main__":
    main()
