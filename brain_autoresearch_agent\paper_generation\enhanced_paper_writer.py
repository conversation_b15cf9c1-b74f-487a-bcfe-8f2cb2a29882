"""
Enhanced Paper Writer with AI Scientist v2 Features
Integrates visual review, LaTeX generation, and quality optimization
"""

import os
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

from core.hybrid_model_client import get_hybrid_client, ModelType


@dataclass
class PaperSection:
    """论文章节数据结构"""
    title: str
    content: str
    subsections: List['PaperSection'] = None
    figures: List[str] = None
    citations: List[str] = None
    quality_score: float = 0.0
    
    def __post_init__(self):
        if self.subsections is None:
            self.subsections = []
        if self.figures is None:
            self.figures = []
        if self.citations is None:
            self.citations = []


@dataclass
class PaperMetadata:
    """论文元数据"""
    title: str
    authors: List[str]
    abstract: str
    keywords: List[str]
    research_area: str
    methodology: str
    contribution_type: str
    novelty_score: float = 0.0
    technical_quality: float = 0.0
    clarity_score: float = 0.0
    significance_score: float = 0.0
    overall_quality: float = 0.0


class EnhancedPaperWriter:
    """增强论文写作器"""
    
    def __init__(self):
        """初始化增强论文写作器"""
        self.hybrid_client = get_hybrid_client()
        self.output_dir = "output"
        self.templates_dir = "paper_generation/latex_templates"
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        print("✅ Enhanced Paper Writer initialized")
        print(f"📁 Output directory: {self.output_dir}")
    
    def generate_enhanced_paper(self, topic: str, research_focus: str, 
                              methodology: str = None, **kwargs) -> Dict[str, Any]:
        """生成增强版论文"""
        print(f"🚀 开始生成增强版论文...")
        print(f"📝 主题: {topic}")
        print(f"🔬 研究重点: {research_focus}")
        
        start_time = time.time()
        
        try:
            # 第1步: 生成论文元数据
            metadata = self._generate_paper_metadata(topic, research_focus, methodology)
            print(f"✅ 步骤1: 论文元数据生成完成")
            
            # 第2步: 生成论文结构
            paper_structure = self._generate_paper_structure(metadata)
            print(f"✅ 步骤2: 论文结构生成完成")
            
            # 第3步: 生成各个章节
            sections = self._generate_all_sections(metadata, paper_structure)
            print(f"✅ 步骤3: 章节内容生成完成")
            
            # 第4步: 质量评估和优化
            optimized_sections = self._optimize_sections_quality(sections, metadata)
            print(f"✅ 步骤4: 质量优化完成")
            
            # 第5步: 生成引用和参考文献
            references = self._generate_references(optimized_sections, metadata)
            print(f"✅ 步骤5: 参考文献生成完成")
            
            # 第6步: 生成最终论文
            final_paper = self._compile_final_paper(metadata, optimized_sections, references)
            print(f"✅ 步骤6: 最终论文编译完成")
            
            # 第7步: 格式化输出 (JSON, Markdown, LaTeX)
            outputs = self._generate_all_formats(final_paper, metadata)
            print(f"✅ 步骤7: 多格式输出完成")
            
            # 第8步: 最终质量评估
            final_quality = self._evaluate_final_quality(final_paper, metadata)
            print(f"✅ 步骤8: 最终质量评估完成")
            
            generation_time = time.time() - start_time
            
            # 编译结果
            result = {
                "metadata": asdict(metadata),
                "sections": [asdict(section) for section in optimized_sections],
                "references": references,
                "outputs": outputs,
                "quality_metrics": {
                    "novelty_score": metadata.novelty_score,
                    "technical_quality": metadata.technical_quality,
                    "clarity_score": metadata.clarity_score,
                    "significance_score": metadata.significance_score,
                    "overall_quality": final_quality,
                    "generation_time": generation_time
                },
                "generation_info": {
                    "timestamp": datetime.now().isoformat(),
                    "model_info": str(self.hybrid_client),
                    "processing_time": f"{generation_time:.2f}s"
                }
            }
            
            print(f"\\n🎉 论文生成完成!")
            print(f"⏱️ 生成时间: {generation_time:.2f}秒")
            print(f"📊 总体质量分数: {final_quality:.1f}/10")
            
            return result
            
        except Exception as e:
            print(f"❌ 论文生成失败: {e}")
            import traceback
            traceback.print_exc()
            raise e
    
    def _generate_paper_metadata(self, topic: str, research_focus: str, 
                                methodology: str = None) -> PaperMetadata:
        """生成论文元数据"""
        prompt = f"""
        Generate comprehensive metadata for an academic paper with the following specifications:
        
        Topic: {topic}
        Research Focus: {research_focus}
        Methodology: {methodology or 'To be determined'}
        
        Please provide:
        1. A compelling paper title (academic style)
        2. Abstract (150-200 words)
        3. 5-7 relevant keywords
        4. Research area classification
        5. Methodology approach
        6. Type of contribution (theoretical, empirical, methodological, etc.)
        
        Format as structured text with clear sections.
        Ensure the abstract is scientifically rigorous and clearly states the problem, approach, and expected contributions.
        """
        
        system_message = """You are an expert academic writer and researcher specializing in AI and machine learning. 
        Generate high-quality academic metadata that would be suitable for top-tier conferences and journals.
        Focus on clarity, novelty, and technical rigor."""
        
        response = self.hybrid_client.generate_text(
            prompt=prompt,
            task_type=ModelType.ACADEMIC_WRITING,
            system_message=system_message,
            temperature=0.7
        )
        
        # 解析响应生成元数据
        metadata = self._parse_metadata_response(response, topic, research_focus)
        
        return metadata
    
    def _parse_metadata_response(self, response: str, topic: str, research_focus: str) -> PaperMetadata:
        """解析元数据响应"""
        lines = response.split('\\n')
        
        # 提取标题
        title = topic  # 默认值
        for line in lines:
            if 'title' in line.lower() and ':' in line:
                title = line.split(':', 1)[1].strip()
                break
        
        # 提取摘要
        abstract = ""
        in_abstract = False
        for line in lines:
            if 'abstract' in line.lower():
                in_abstract = True
                continue
            elif in_abstract and line.strip():
                if any(keyword in line.lower() for keyword in ['keyword', 'research area', 'methodology']):
                    break
                abstract += line.strip() + " "
        
        # 提取关键词
        keywords = []
        for line in lines:
            if 'keyword' in line.lower() and ':' in line:
                kw_text = line.split(':', 1)[1].strip()
                keywords = [kw.strip() for kw in kw_text.split(',')]
                break
        
        if not keywords:
            keywords = [topic.lower(), research_focus.lower(), "deep learning", "neural networks", "AI"]
        
        # 使用推理模型评估初始质量分数
        quality_scores = self._evaluate_metadata_quality(title, abstract, keywords)
        
        return PaperMetadata(
            title=title,
            authors=["AI Research Assistant"],  # 可配置
            abstract=abstract.strip(),
            keywords=keywords[:7],  # 限制到7个关键词
            research_area="Artificial Intelligence",
            methodology="Deep Learning",
            contribution_type="Methodological",
            novelty_score=quality_scores.get('novelty', 6.0),
            technical_quality=quality_scores.get('technical', 6.0),
            clarity_score=quality_scores.get('clarity', 6.0),
            significance_score=quality_scores.get('significance', 6.0)
        )
    
    def _evaluate_metadata_quality(self, title: str, abstract: str, keywords: List[str]) -> Dict[str, float]:
        """评估元数据质量"""
        evaluation_prompt = f"""
        Evaluate the quality of this academic paper metadata on a scale of 1-10:
        
        Title: {title}
        Abstract: {abstract}
        Keywords: {', '.join(keywords)}
        
        Rate each aspect:
        1. Novelty (1-10): How novel and innovative is the research?
        2. Technical Quality (1-10): How technically sound is the approach?
        3. Clarity (1-10): How clear and well-written is the abstract?
        4. Significance (1-10): How significant is the potential impact?
        
        Provide scores as: novelty=X, technical=Y, clarity=Z, significance=W
        """
        
        response = self.hybrid_client.generate_text(
            prompt=evaluation_prompt,
            task_type=ModelType.REASONING,
            system_message="You are an expert paper reviewer for top AI conferences. Be critical but fair.",
            temperature=0.3
        )
        
        # 解析评分
        scores = {'novelty': 6.0, 'technical': 6.0, 'clarity': 6.0, 'significance': 6.0}
        
        import re
        for key in scores.keys():
            pattern = f"{key}=([0-9.]+)"
            match = re.search(pattern, response.lower())
            if match:
                try:
                    scores[key] = float(match.group(1))
                except:
                    pass
        
        return scores
    
    def _generate_paper_structure(self, metadata: PaperMetadata) -> List[str]:
        """生成论文结构"""
        structure_prompt = f"""
        Generate a detailed structure for an academic paper titled "{metadata.title}" 
        in the {metadata.research_area} field.
        
        Research focus: {metadata.contribution_type}
        Abstract: {metadata.abstract[:200]}...
        
        Provide a comprehensive structure with:
        1. Main sections (Introduction, Related Work, Methodology, etc.)
        2. Key subsections for each main section
        3. Logical flow and organization
        
        Format as a numbered list with main sections and bullet points for subsections.
        Ensure the structure supports the research contribution type: {metadata.contribution_type}
        """
        
        response = self.hybrid_client.generate_text(
            prompt=structure_prompt,
            task_type=ModelType.ACADEMIC_WRITING,
            system_message="You are an expert at structuring academic papers for maximum impact and clarity.",
            temperature=0.6
        )
        
        # 解析结构
        structure = self._parse_structure_response(response)
        
        return structure
    
    def _parse_structure_response(self, response: str) -> List[str]:
        """解析结构响应"""
        # 标准学术论文结构
        default_structure = [
            "Abstract",
            "Introduction", 
            "Related Work",
            "Methodology",
            "Experimental Setup",
            "Results and Analysis",
            "Discussion",
            "Conclusion",
            "References"
        ]
        
        # 尝试从响应中提取结构
        lines = response.split('\\n')
        extracted_sections = []
        
        for line in lines:
            line = line.strip()
            if line and (line[0].isdigit() or line.startswith('-') or line.startswith('•')):
                # 清理格式
                clean_line = re.sub(r'^[0-9\.\-\•\s]+', '', line).strip()
                if clean_line and len(clean_line) > 3:
                    extracted_sections.append(clean_line)
        
        # 如果提取失败，使用默认结构
        if len(extracted_sections) < 5:
            return default_structure
        
        return extracted_sections[:9]  # 限制到9个主要部分
    
    def _generate_all_sections(self, metadata: PaperMetadata, 
                             structure: List[str]) -> List[PaperSection]:
        """生成所有章节内容"""
        sections = []
        
        for i, section_title in enumerate(structure):
            if section_title.lower() in ['abstract', 'references']:
                continue  # 这些单独处理
            
            print(f"  📝 生成章节: {section_title}")
            
            section_content = self._generate_section_content(
                section_title, metadata, structure, i
            )
            
            section = PaperSection(
                title=section_title,
                content=section_content
            )
            
            sections.append(section)
        
        return sections
    
    def _generate_section_content(self, section_title: str, metadata: PaperMetadata,
                                structure: List[str], section_index: int) -> str:
        """生成特定章节内容"""
        
        # 为不同章节制定特定的提示
        section_prompts = {
            "introduction": f"""
            Write a comprehensive Introduction section for the paper "{metadata.title}".
            
            The introduction should:
            1. Establish the research context and motivation
            2. Clearly state the problem being addressed
            3. Review relevant background briefly
            4. Highlight the research gap or limitation
            5. Present the paper's main contributions
            6. Outline the paper structure
            
            Research focus: {metadata.research_area}
            Methodology: {metadata.methodology}
            Keywords: {', '.join(metadata.keywords)}
            
            Write 800-1200 words with proper academic tone and structure.
            """,
            
            "related work": f"""
            Write a comprehensive Related Work section for "{metadata.title}".
            
            Cover these areas:
            1. Foundational work in {metadata.research_area}
            2. Recent advances in {metadata.methodology}
            3. Relevant methodologies and approaches
            4. Limitations of existing work
            5. How this work differs/advances the field
            
            Keywords for context: {', '.join(metadata.keywords)}
            
            Write 600-800 words with proper citations and critical analysis.
            """,
            
            "methodology": f"""
            Write a detailed Methodology section for "{metadata.title}".
            
            Include:
            1. Overall approach and framework
            2. Key algorithms and techniques
            3. Implementation details
            4. Theoretical justification
            5. Computational considerations
            6. Evaluation methodology
            
            Research focus: {metadata.contribution_type}
            Technical approach: {metadata.methodology}
            
            Write 800-1000 words with technical depth and clarity.
            """,
            
            "experimental setup": f"""
            Write an Experimental Setup section for "{metadata.title}".
            
            Cover:
            1. Datasets and data preparation
            2. Baseline methods and comparisons
            3. Implementation details and hyperparameters
            4. Evaluation metrics and protocols
            5. Hardware and software environment
            6. Experimental design and controls
            
            Write 400-600 words with sufficient detail for reproducibility.
            """,
            
            "results and analysis": f"""
            Write a Results and Analysis section for "{metadata.title}".
            
            Include:
            1. Main experimental results
            2. Comparative analysis with baselines
            3. Ablation studies and analysis
            4. Performance metrics and statistical significance
            5. Qualitative analysis and insights
            6. Discussion of limitations
            
            Write 800-1000 words with clear presentation of findings.
            """,
            
            "discussion": f"""
            Write a Discussion section for "{metadata.title}".
            
            Address:
            1. Interpretation of results
            2. Implications for the field
            3. Comparison with previous work
            4. Strengths and limitations
            5. Future research directions
            6. Broader impact considerations
            
            Write 400-600 words with thoughtful analysis.
            """,
            
            "conclusion": f"""
            Write a Conclusion section for "{metadata.title}".
            
            Summarize:
            1. Main contributions and achievements
            2. Key findings and insights
            3. Practical implications
            4. Future work and research directions
            5. Final thoughts on significance
            
            Write 300-400 words with clear summary and future outlook.
            """
        }
        
        # 获取章节特定提示
        section_key = section_title.lower()
        prompt = section_prompts.get(section_key, f"""
        Write a comprehensive {section_title} section for the academic paper "{metadata.title}".
        
        Context:
        - Research area: {metadata.research_area}
        - Methodology: {metadata.methodology}
        - Contribution type: {metadata.contribution_type}
        - Keywords: {', '.join(metadata.keywords)}
        
        Write 400-800 words with appropriate academic tone and structure.
        Ensure the content is relevant, well-structured, and contributes to the overall paper narrative.
        """)
        
        system_message = f"""You are an expert academic writer specializing in {metadata.research_area}. 
        Write high-quality content suitable for publication in top-tier venues. 
        Ensure technical accuracy, clarity, and proper academic style.
        Focus on {metadata.contribution_type} contributions."""
        
        content = self.hybrid_client.generate_text(
            prompt=prompt,
            task_type=ModelType.ACADEMIC_WRITING,
            system_message=system_message,
            temperature=0.7,
            max_tokens=2048
        )
        
        return content
    
    def _optimize_sections_quality(self, sections: List[PaperSection], 
                                 metadata: PaperMetadata) -> List[PaperSection]:
        """优化章节质量"""
        optimized_sections = []
        
        for section in sections:
            print(f"  🔧 优化章节: {section.title}")
            
            # 评估当前质量
            quality_score = self._evaluate_section_quality(section, metadata)
            section.quality_score = quality_score
            
            # 如果质量分数低于阈值，进行优化
            if quality_score < 7.0:
                optimized_content = self._optimize_section_content(section, metadata)
                section.content = optimized_content
                section.quality_score = self._evaluate_section_quality(section, metadata)
            
            optimized_sections.append(section)
        
        return optimized_sections
    
    def _evaluate_section_quality(self, section: PaperSection, metadata: PaperMetadata) -> float:
        """评估章节质量"""
        evaluation_prompt = f"""
        Evaluate the quality of this {section.title} section from an academic paper on a scale of 1-10:
        
        Paper title: {metadata.title}
        Section: {section.title}
        Content: {section.content[:1000]}...
        
        Rate based on:
        1. Technical accuracy and depth
        2. Clarity and organization
        3. Relevance to the research topic
        4. Academic writing quality
        5. Completeness and coverage
        
        Provide a single numeric score (1-10) with brief justification.
        """
        
        response = self.hybrid_client.generate_text(
            prompt=evaluation_prompt,
            task_type=ModelType.REASONING,
            system_message="You are an expert reviewer for top AI conferences. Evaluate objectively.",
            temperature=0.3
        )
        
        # 提取数字分数
        import re
        score_match = re.search(r'([0-9\.]+)/10|score.*?([0-9\.]+)', response.lower())
        if score_match:
            try:
                score = float(score_match.group(1) or score_match.group(2))
                return min(10.0, max(1.0, score))
            except:
                pass
        
        return 6.0  # 默认分数
    
    def _optimize_section_content(self, section: PaperSection, metadata: PaperMetadata) -> str:
        """优化章节内容"""
        optimization_prompt = f"""
        Improve and optimize this {section.title} section from an academic paper:
        
        Paper context:
        - Title: {metadata.title}
        - Research area: {metadata.research_area}
        - Methodology: {metadata.methodology}
        
        Current content:
        {section.content}
        
        Please improve:
        1. Technical accuracy and depth
        2. Clarity and structure
        3. Academic writing style
        4. Logical flow and organization
        5. Completeness of coverage
        
        Provide the improved version maintaining the same length but higher quality.
        Ensure pure English output with no mixed languages.
        """
        
        optimized_content = self.hybrid_client.generate_text(
            prompt=optimization_prompt,
            task_type=ModelType.ACADEMIC_WRITING,
            system_message="You are an expert academic editor specializing in improving research papers for top-tier publication.",
            temperature=0.6,
            max_tokens=2048
        )
        
        return optimized_content
    
    def _generate_references(self, sections: List[PaperSection], 
                           metadata: PaperMetadata) -> List[Dict[str, str]]:
        """生成参考文献"""
        references_prompt = f"""
        Generate a comprehensive reference list for an academic paper titled "{metadata.title}" 
        in the field of {metadata.research_area}.
        
        Research focus: {metadata.methodology}
        Keywords: {', '.join(metadata.keywords)}
        
        Generate 15-25 high-quality references including:
        1. Foundational papers in the field
        2. Recent advances (2020-2024)
        3. Key methodological papers
        4. Relevant survey/review papers
        5. Important conference/journal papers
        
        Format each reference as:
        [ID] Author(s). "Title." Conference/Journal, Year.
        
        Focus on top-tier venues: NeurIPS, ICML, ICLR, Nature, Science, etc.
        Ensure references are realistic and relevant to the research area.
        """
        
        response = self.hybrid_client.generate_text(
            prompt=references_prompt,
            task_type=ModelType.ACADEMIC_WRITING,
            system_message="You are an expert academic librarian with deep knowledge of AI/ML literature.",
            temperature=0.7
        )
        
        # 解析参考文献
        references = self._parse_references(response)
        
        return references
    
    def _parse_references(self, response: str) -> List[Dict[str, str]]:
        """解析参考文献响应"""
        references = []
        lines = response.split('\\n')
        
        for line in lines:
            line = line.strip()
            if line and (line.startswith('[') or line[0].isdigit()):
                # 简单解析
                ref_dict = {
                    "id": len(references) + 1,
                    "citation": line,
                    "type": "article"
                }
                references.append(ref_dict)
        
        # 确保至少有一些基础参考文献
        if len(references) < 5:
            default_refs = [
                {"id": 1, "citation": "LeCun, Y., Bengio, Y., & Hinton, G. (2015). Deep learning. Nature, 521(7553), 436-444.", "type": "article"},
                {"id": 2, "citation": "Goodfellow, I., Bengio, Y., & Courville, A. (2016). Deep Learning. MIT Press.", "type": "book"},
                {"id": 3, "citation": "Krizhevsky, A., Sutskever, I., & Hinton, G. E. (2012). ImageNet classification with deep convolutional neural networks. NIPS.", "type": "conference"},
                {"id": 4, "citation": "Vaswani, A., et al. (2017). Attention is all you need. NIPS.", "type": "conference"},
                {"id": 5, "citation": "Brown, T., et al. (2020). Language models are few-shot learners. NeurIPS.", "type": "conference"}
            ]
            references.extend(default_refs)
        
        return references[:25]  # 限制参考文献数量
    
    def _compile_final_paper(self, metadata: PaperMetadata, sections: List[PaperSection],
                           references: List[Dict[str, str]]) -> Dict[str, Any]:
        """编译最终论文"""
        final_paper = {
            "title": metadata.title,
            "authors": metadata.authors,
            "abstract": metadata.abstract,
            "keywords": metadata.keywords,
            "sections": {},
            "references": references,
            "metadata": asdict(metadata)
        }
        
        # 添加所有章节
        for section in sections:
            final_paper["sections"][section.title.lower().replace(" ", "_")] = {
                "title": section.title,
                "content": section.content,
                "subsections": [asdict(subsec) for subsec in section.subsections],
                "quality_score": section.quality_score
            }
        
        return final_paper
    
    def _generate_all_formats(self, final_paper: Dict[str, Any], 
                            metadata: PaperMetadata) -> Dict[str, str]:
        """生成所有格式输出"""
        outputs = {}
        
        # JSON格式
        json_output = self._generate_json_format(final_paper)
        outputs["json"] = self._save_output(json_output, "json", metadata.title)
        
        # Markdown格式
        markdown_output = self._generate_markdown_format(final_paper)
        outputs["markdown"] = self._save_output(markdown_output, "md", metadata.title)
        
        # LaTeX格式
        try:
            latex_output = self._generate_latex_format(final_paper)
            outputs["latex"] = self._save_output(latex_output, "tex", metadata.title)
        except Exception as e:
            print(f"⚠️ LaTeX生成失败: {e}")
            outputs["latex"] = None
        
        return outputs
    
    def _generate_json_format(self, final_paper: Dict[str, Any]) -> str:
        """生成JSON格式"""
        return json.dumps(final_paper, indent=2, ensure_ascii=False)
    
    def _generate_markdown_format(self, final_paper: Dict[str, Any]) -> str:
        """生成Markdown格式"""
        markdown = f"""# {final_paper['title']}

**Authors: <AUTHORS>

## Abstract

{final_paper['abstract']}

**Keywords:** {', '.join(final_paper['keywords'])}

---

"""
        
        # 添加各个章节
        for section_key, section_data in final_paper['sections'].items():
            markdown += f"## {section_data['title']}\\n\\n"
            markdown += f"{section_data['content']}\\n\\n"
            
            # 添加子章节
            for subsection in section_data.get('subsections', []):
                markdown += f"### {subsection['title']}\\n\\n"
                markdown += f"{subsection['content']}\\n\\n"
        
        # 添加参考文献
        markdown += "## References\\n\\n"
        for ref in final_paper['references']:
            markdown += f"{ref['id']}. {ref['citation']}\\n\\n"
        
        return markdown
    
    def _generate_latex_format(self, final_paper: Dict[str, Any]) -> str:
        """生成LaTeX格式"""
        latex_template = f"""\\documentclass[conference]{{IEEEtran}}
\\usepackage[utf8]{{inputenc}}
\\usepackage{{cite}}
\\usepackage{{amsmath,amssymb,amsfonts}}
\\usepackage{{algorithmic}}
\\usepackage{{graphicx}}
\\usepackage{{textcomp}}
\\usepackage{{xcolor}}

\\title{{{final_paper['title']}}}

\\author{{
\\IEEEauthorblockN{{{', '.join(final_paper['authors'])}}}
\\IEEEauthorblockA{{AI Research Laboratory}}
}}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
{final_paper['abstract']}
\\end{{abstract}}

\\begin{{IEEEkeywords}}
{', '.join(final_paper['keywords'])}
\\end{{IEEEkeywords}}

"""
        
        # 添加各个章节
        for section_key, section_data in final_paper['sections'].items():
            section_title = section_data['title']
            section_content = section_data['content']
            
            # 清理LaTeX特殊字符
            clean_content = self._clean_latex_content(section_content)
            
            latex_template += f"\\section{{{section_title}}}\\n"
            latex_template += f"{clean_content}\\n\\n"
        
        # 添加参考文献
        latex_template += "\\begin{thebibliography}{99}\\n"
        for ref in final_paper['references']:
            ref_text = self._clean_latex_content(ref['citation'])
            latex_template += f"\\bibitem{{ref{ref['id']}}} {ref_text}\\n"
        latex_template += "\\end{thebibliography}\\n"
        
        latex_template += "\\end{document}"
        
        return latex_template
    
    def _clean_latex_content(self, content: str) -> str:
        """清理LaTeX内容"""
        # 替换特殊字符
        replacements = {
            '&': '\\&',
            '%': '\\%',
            '$': '\\$',
            '#': '\\#',
            '_': '\\_',
            '{': '\\{',
            '}': '\\}',
            '^': '\\textasciicircum{}',
            '~': '\\textasciitilde{}',
            '\\': '\\textbackslash{}'
        }
        
        for old, new in replacements.items():
            content = content.replace(old, new)
        
        return content
    
    def _save_output(self, content: str, format_type: str, title: str) -> str:
        """保存输出文件"""
        # 生成安全的文件名
        safe_title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_title = safe_title.replace(' ', '_')[:50]  # 限制长度
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{safe_title}_{timestamp}.{format_type}"
        filepath = os.path.join(self.output_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ {format_type.upper()}文件保存: {filepath}")
            return filepath
        except Exception as e:
            print(f"❌ 保存{format_type.upper()}文件失败: {e}")
            return None
    
    def _evaluate_final_quality(self, final_paper: Dict[str, Any], 
                              metadata: PaperMetadata) -> float:
        """评估最终质量"""
        evaluation_prompt = f"""
        Evaluate the overall quality of this complete academic paper on a scale of 1-10:
        
        Title: {final_paper['title']}
        Abstract: {final_paper['abstract'][:300]}...
        
        Number of sections: {len(final_paper['sections'])}
        Number of references: {len(final_paper['references'])}
        
        Consider:
        1. Overall coherence and structure
        2. Technical depth and accuracy
        3. Clarity of presentation
        4. Novelty and significance
        5. Completeness and coverage
        6. Academic writing quality
        
        Provide a single numeric score (1-10) representing the overall paper quality.
        """
        
        response = self.hybrid_client.generate_text(
            prompt=evaluation_prompt,
            task_type=ModelType.REASONING,
            system_message="You are a senior reviewer for top AI conferences. Evaluate the complete paper objectively.",
            temperature=0.3
        )
        
        # 提取最终分数
        import re
        score_match = re.search(r'([0-9\.]+)/10|score.*?([0-9\.]+)', response.lower())
        if score_match:
            try:
                score = float(score_match.group(1) or score_match.group(2))
                return min(10.0, max(1.0, score))
            except:
                pass
        
        # 基于组件质量计算
        section_scores = [section_data.get('quality_score', 6.0) 
                         for section_data in final_paper['sections'].values()]
        avg_section_quality = sum(section_scores) / len(section_scores) if section_scores else 6.0
        
        # 综合评分
        final_score = (
            metadata.novelty_score * 0.25 +
            metadata.technical_quality * 0.25 +
            metadata.clarity_score * 0.25 +
            avg_section_quality * 0.25
        )
        
        return round(final_score, 1)


if __name__ == "__main__":
    # 测试增强论文写作器
    print("🧪 测试增强论文写作器...")
    
    writer = EnhancedPaperWriter()
    
    # 生成测试论文
    test_result = writer.generate_enhanced_paper(
        topic="Neural Plasticity-Inspired Deep Learning",
        research_focus="Adaptive learning mechanisms in neural networks",
        methodology="Biologically-inspired plasticity algorithms"
    )
    
    print(f"\\n📊 测试结果:")
    print(f"  📝 标题: {test_result['metadata']['title']}")
    print(f"  📄 章节数: {len(test_result['sections'])}")
    print(f"  📚 参考文献: {len(test_result['references'])}")
    print(f"  🎯 质量分数: {test_result['quality_metrics']['overall_quality']}/10")
    print(f"  ⏱️ 生成时间: {test_result['quality_metrics']['generation_time']:.2f}秒")
    
    print(f"\\n🎉 增强论文写作器测试完成!")
