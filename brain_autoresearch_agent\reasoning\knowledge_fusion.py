"""
Knowledge Fusion Module

This module implements advanced knowledge fusion algorithms for combining insights
from multiple expert agents into coherent, high-quality research recommendations.
"""

import json
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime
import logging
from collections import defaultdict


class KnowledgeFusion:
    """
    Knowledge Fusion Engine for Multi-Expert Insights
    
    This class implements sophisticated algorithms for:
    1. Conflict detection and resolution between expert opinions
    2. Confidence-weighted knowledge integration
    3. Consensus building and decision making
    4. Quality assessment of fused knowledge
    """
    
    def __init__(self):
        """Initialize the Knowledge Fusion Engine"""
        self.logger = self._setup_logger()
        self.fusion_history = []
        
    def _setup_logger(self) -> logging.Logger:
        """Setup logging for knowledge fusion process"""
        logger = logging.getLogger('KnowledgeFusion')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
        
    def detect_conflicts(self, expert_opinions: Dict[str, Any]) -> Dict[str, Any]:
        """
        Detect conflicts and disagreements between expert opinions
        
        Args:
            expert_opinions: Dictionary of expert names and their analysis results
            
        Returns:
            Conflict analysis with identified disagreements and their severity
        """
        self.logger.info("Starting conflict detection analysis")
        
        conflicts = {
            'confidence_conflicts': [],
            'content_conflicts': [],
            'recommendation_conflicts': [],
            'severity_analysis': {},
            'resolution_suggestions': []
        }
        
        # Extract confidence scores
        confidences = {}
        for expert, opinion in expert_opinions.items():
            if isinstance(opinion, dict) and 'confidence' in opinion:
                confidences[expert] = opinion['confidence']
        
        # Analyze confidence conflicts
        if len(confidences) >= 2:
            conf_values = list(confidences.values())
            conf_std = np.std(conf_values)
            conf_range = max(conf_values) - min(conf_values)
            
            if conf_std > 0.15 or conf_range > 0.3:  # Significant confidence disagreement
                conflicts['confidence_conflicts'].append({
                    'type': 'confidence_disagreement',
                    'std_deviation': conf_std,
                    'range': conf_range,
                    'experts': confidences,
                    'severity': 'high' if conf_range > 0.4 else 'moderate'
                })
        
        # Analyze content conflicts (simplified approach)
        content_keywords = defaultdict(list)
        for expert, opinion in expert_opinions.items():
            if isinstance(opinion, dict):
                # Extract key terms/concepts from opinion text
                opinion_text = str(opinion)
                keywords = self._extract_keywords(opinion_text)
                content_keywords[expert] = keywords
        
        # Check for contradictory keywords/concepts
        contradictions = self._find_contradictions(content_keywords)
        if contradictions:
            conflicts['content_conflicts'].extend(contradictions)
        
        # Analyze recommendation conflicts
        recommendations = {}
        for expert, opinion in expert_opinions.items():
            if isinstance(opinion, dict) and 'recommendations' in opinion:
                recommendations[expert] = opinion['recommendations']
        
        if len(recommendations) >= 2:
            rec_conflicts = self._analyze_recommendation_conflicts(recommendations)
            conflicts['recommendation_conflicts'].extend(rec_conflicts)
        
        # Overall severity analysis
        conflicts['severity_analysis'] = self._calculate_overall_conflict_severity(conflicts)
        
        # Generate resolution suggestions
        conflicts['resolution_suggestions'] = self._generate_conflict_resolution_suggestions(conflicts)
        
        self.logger.info(f"Conflict detection completed. Found {len(conflicts['confidence_conflicts']) + len(conflicts['content_conflicts'])} conflicts")
        
        return conflicts
        
    def fuse_knowledge(self, expert_opinions: Dict[str, Any], fusion_strategy: str = 'weighted_consensus') -> Dict[str, Any]:
        """
        Fuse knowledge from multiple expert opinions using specified strategy
        
        Args:
            expert_opinions: Dictionary of expert opinions
            fusion_strategy: Strategy for fusion ('weighted_consensus', 'majority_vote', 'expert_ranking')
            
        Returns:
            Fused knowledge with integrated insights and confidence metrics
        """
        self.logger.info(f"Starting knowledge fusion with strategy: {fusion_strategy}")
        
        if fusion_strategy == 'weighted_consensus':
            return self._weighted_consensus_fusion(expert_opinions)
        elif fusion_strategy == 'majority_vote':
            return self._majority_vote_fusion(expert_opinions)
        elif fusion_strategy == 'expert_ranking':
            return self._expert_ranking_fusion(expert_opinions)
        else:
            raise ValueError(f"Unknown fusion strategy: {fusion_strategy}")
            
    def _weighted_consensus_fusion(self, expert_opinions: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fuse knowledge using confidence-weighted consensus
        
        Gives more weight to experts with higher confidence scores
        """
        self.logger.info("Applying weighted consensus fusion")
        
        # Extract confidence weights
        weights = {}
        total_weight = 0
        
        for expert, opinion in expert_opinions.items():
            if isinstance(opinion, dict) and 'confidence' in opinion:
                weight = opinion['confidence']
                weights[expert] = weight
                total_weight += weight
        
        # Normalize weights
        if total_weight > 0:
            weights = {expert: weight/total_weight for expert, weight in weights.items()}
        else:
            # Equal weights if no confidence scores
            weights = {expert: 1.0/len(expert_opinions) for expert in expert_opinions.keys()}
        
        # Fuse different aspects of knowledge
        fused_result = {
            'fusion_strategy': 'weighted_consensus',
            'expert_weights': weights,
            'fused_confidence': self._calculate_weighted_confidence(expert_opinions, weights),
            'fused_insights': self._fuse_insights(expert_opinions, weights),
            'fused_recommendations': self._fuse_recommendations(expert_opinions, weights),
            'consensus_strength': self._calculate_consensus_strength(expert_opinions, weights),
            'key_agreements': self._identify_key_agreements(expert_opinions),
            'remaining_uncertainties': self._identify_uncertainties(expert_opinions)
        }
        
        return fused_result
        
    def _majority_vote_fusion(self, expert_opinions: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fuse knowledge using majority vote approach
        
        Decisions based on majority agreement among experts
        """
        self.logger.info("Applying majority vote fusion")
        
        # Extract binary decisions/recommendations
        decisions = defaultdict(list)
        
        for expert, opinion in expert_opinions.items():
            if isinstance(opinion, dict):
                # Extract decision points (simplified)
                decision_indicators = self._extract_decision_indicators(opinion)
                for decision_type, decision_value in decision_indicators.items():
                    decisions[decision_type].append((expert, decision_value))
        
        # Calculate majority decisions
        majority_decisions = {}
        for decision_type, expert_decisions in decisions.items():
            votes = [decision for _, decision in expert_decisions]
            majority_decision = self._calculate_majority(votes)
            majority_decisions[decision_type] = {
                'decision': majority_decision,
                'vote_distribution': self._calculate_vote_distribution(votes),
                'supporting_experts': [expert for expert, decision in expert_decisions if decision == majority_decision]
            }
        
        fused_result = {
            'fusion_strategy': 'majority_vote',
            'majority_decisions': majority_decisions,
            'consensus_level': self._calculate_majority_consensus_level(majority_decisions),
            'unanimous_agreements': self._find_unanimous_agreements(decisions),
            'split_decisions': self._find_split_decisions(decisions)
        }
        
        return fused_result
        
    def _expert_ranking_fusion(self, expert_opinions: Dict[str, Any]) -> Dict[str, Any]:
        """
        Fuse knowledge using expert ranking approach
        
        Prioritizes experts based on domain relevance and track record
        """
        self.logger.info("Applying expert ranking fusion")
        
        # Define expert rankings (could be dynamic in future)
        expert_rankings = {
            'ai_technology': 0.25,
            'neuroscience': 0.25,
            'data_analysis': 0.20,
            'experiment_design': 0.20,
            'paper_writing': 0.10
        }
        
        # Apply ranking-based fusion
        ranked_fusion = {}
        
        for expert, opinion in expert_opinions.items():
            expert_key = expert.lower().replace('expert', '').replace('_', '')
            ranking_weight = expert_rankings.get(expert_key, expert_rankings.get(expert, 0.2))
            
            ranked_fusion[expert] = {
                'opinion': opinion,
                'ranking_weight': ranking_weight,
                'weighted_contribution': self._calculate_weighted_contribution(opinion, ranking_weight)
            }
        
        # Generate final ranked fusion result
        fused_result = {
            'fusion_strategy': 'expert_ranking',
            'expert_rankings': expert_rankings,
            'ranked_contributions': ranked_fusion,
            'top_ranked_insights': self._extract_top_ranked_insights(ranked_fusion),
            'ranking_based_confidence': self._calculate_ranking_based_confidence(ranked_fusion)
        }
        
        return fused_result
        
    def build_consensus(self, expert_opinions: Dict[str, Any], consensus_threshold: float = 0.75) -> Dict[str, Any]:
        """
        Build consensus from expert opinions with specified threshold
        
        Args:
            expert_opinions: Dictionary of expert opinions
            consensus_threshold: Minimum agreement level for consensus (0.0 to 1.0)
            
        Returns:
            Consensus results with agreement levels and final decisions
        """
        self.logger.info(f"Building consensus with threshold: {consensus_threshold}")
        
        # Extract agreement indicators
        agreement_analysis = self._analyze_agreements(expert_opinions)
        
        # Identify consensus areas
        consensus_areas = []
        for area, agreement_level in agreement_analysis['agreement_levels'].items():
            if agreement_level >= consensus_threshold:
                consensus_areas.append({
                    'area': area,
                    'agreement_level': agreement_level,
                    'supporting_experts': agreement_analysis['supporting_experts'][area]
                })
        
        # Identify areas needing more discussion
        areas_needing_discussion = []
        for area, agreement_level in agreement_analysis['agreement_levels'].items():
            if agreement_level < consensus_threshold:
                areas_needing_discussion.append({
                    'area': area,
                    'agreement_level': agreement_level,
                    'disagreeing_experts': agreement_analysis['disagreeing_experts'][area],
                    'discussion_priority': 'high' if agreement_level < 0.5 else 'medium'
                })
        
        consensus_result = {
            'consensus_threshold': consensus_threshold,
            'overall_consensus_level': agreement_analysis['overall_agreement'],
            'consensus_achieved': agreement_analysis['overall_agreement'] >= consensus_threshold,
            'consensus_areas': consensus_areas,
            'areas_needing_discussion': areas_needing_discussion,
            'consensus_strength': self._calculate_consensus_strength_score(consensus_areas),
            'recommended_actions': self._generate_consensus_actions(consensus_areas, areas_needing_discussion)
        }
        
        self.logger.info(f"Consensus building completed. Overall level: {agreement_analysis['overall_agreement']:.2f}")
        
        return consensus_result
        
    # Helper methods for knowledge fusion algorithms
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract key terms from text (simplified implementation)"""
        # In a real implementation, this would use NLP libraries
        common_terms = ['research', 'experiment', 'analysis', 'method', 'data', 'model', 
                       'performance', 'accuracy', 'validation', 'implementation']
        
        keywords = []
        text_lower = text.lower()
        for term in common_terms:
            if term in text_lower:
                keywords.append(term)
        
        return keywords
        
    def _find_contradictions(self, content_keywords: Dict[str, List[str]]) -> List[Dict[str, Any]]:
        """Find contradictory concepts between experts"""
        contradictions = []
        
        # Define contradiction pairs
        contradiction_pairs = [
            ('simple', 'complex'),
            ('feasible', 'infeasible'),
            ('accurate', 'inaccurate'),
            ('efficient', 'inefficient')
        ]
        
        experts = list(content_keywords.keys())
        for i, expert1 in enumerate(experts):
            for expert2 in experts[i+1:]:
                keywords1 = content_keywords[expert1]
                keywords2 = content_keywords[expert2]
                
                for term1, term2 in contradiction_pairs:
                    if term1 in keywords1 and term2 in keywords2:
                        contradictions.append({
                            'type': 'conceptual_contradiction',
                            'experts': [expert1, expert2],
                            'contradictory_concepts': [term1, term2],
                            'severity': 'moderate'
                        })
        
        return contradictions
        
    def _analyze_recommendation_conflicts(self, recommendations: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Analyze conflicts in recommendations"""
        conflicts = []
        
        # Simplified conflict detection based on recommendation sentiment
        expert_sentiments = {}
        for expert, rec in recommendations.items():
            sentiment = self._analyze_recommendation_sentiment(rec)
            expert_sentiments[expert] = sentiment
        
        # Find conflicting sentiments
        sentiments = list(expert_sentiments.values())
        if 'positive' in sentiments and 'negative' in sentiments:
            conflicts.append({
                'type': 'recommendation_conflict',
                'conflicting_experts': expert_sentiments,
                'severity': 'high'
            })
        
        return conflicts
        
    def _analyze_recommendation_sentiment(self, recommendation: Any) -> str:
        """Analyze sentiment of recommendation (simplified)"""
        rec_text = str(recommendation).lower()
        
        positive_indicators = ['recommend', 'good', 'effective', 'suitable', 'promising']
        negative_indicators = ['not recommend', 'poor', 'ineffective', 'unsuitable', 'problematic']
        
        positive_count = sum(1 for indicator in positive_indicators if indicator in rec_text)
        negative_count = sum(1 for indicator in negative_indicators if indicator in rec_text)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
            
    def _calculate_overall_conflict_severity(self, conflicts: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall conflict severity"""
        total_conflicts = (len(conflicts['confidence_conflicts']) + 
                          len(conflicts['content_conflicts']) + 
                          len(conflicts['recommendation_conflicts']))
        
        high_severity_count = sum(1 for conflict_list in conflicts.values() 
                                 if isinstance(conflict_list, list)
                                 for conflict in conflict_list 
                                 if isinstance(conflict, dict) and conflict.get('severity') == 'high')
        
        if total_conflicts == 0:
            severity = 'none'
        elif high_severity_count > 0:
            severity = 'high'
        elif total_conflicts > 2:
            severity = 'moderate'
        else:
            severity = 'low'
            
        return {
            'overall_severity': severity,
            'total_conflicts': total_conflicts,
            'high_severity_conflicts': high_severity_count,
            'resolution_urgency': 'immediate' if severity == 'high' else 'moderate' if severity == 'moderate' else 'low'
        }
        
    def _generate_conflict_resolution_suggestions(self, conflicts: Dict[str, Any]) -> List[str]:
        """Generate suggestions for resolving conflicts"""
        suggestions = []
        
        severity = conflicts['severity_analysis']['overall_severity']
        
        if severity == 'high':
            suggestions.extend([
                'Conduct additional expert consultation rounds',
                'Gather more detailed evidence and data',
                'Consider hybrid approaches that address conflicting viewpoints',
                'Implement phased approach to test conflicting hypotheses'
            ])
        elif severity == 'moderate':
            suggestions.extend([
                'Facilitate structured discussion between disagreeing experts',
                'Identify common ground and build from agreements',
                'Use confidence weighting to balance conflicting opinions'
            ])
        else:
            suggestions.extend([
                'Proceed with consensus-based approach',
                'Document minor disagreements for future consideration'
            ])
            
        return suggestions
        
    def _calculate_weighted_confidence(self, expert_opinions: Dict[str, Any], weights: Dict[str, float]) -> float:
        """Calculate confidence-weighted overall confidence"""
        weighted_conf = 0.0
        
        for expert, opinion in expert_opinions.items():
            if isinstance(opinion, dict) and 'confidence' in opinion and expert in weights:
                weighted_conf += opinion['confidence'] * weights[expert]
                
        return weighted_conf
        
    def _fuse_insights(self, expert_opinions: Dict[str, Any], weights: Dict[str, float]) -> Dict[str, Any]:
        """Fuse insights from multiple experts"""
        fused_insights = {
            'key_themes': [],
            'novel_perspectives': [],
            'technical_considerations': [],
            'risk_factors': [],
            'opportunity_areas': []
        }
        
        # Extract and weight insights from each expert
        for expert, opinion in expert_opinions.items():
            weight = weights.get(expert, 0.2)
            
            if isinstance(opinion, dict):
                # Extract insights (simplified approach)
                insights = self._extract_expert_insights(opinion)
                
                for category, insight_list in insights.items():
                    if category in fused_insights:
                        weighted_insights = [(insight, weight) for insight in insight_list]
                        fused_insights[category].extend(weighted_insights)
        
        # Rank and filter insights by weight
        for category in fused_insights:
            if fused_insights[category]:
                # Sort by weight and take top insights
                fused_insights[category] = sorted(fused_insights[category], 
                                                key=lambda x: x[1], reverse=True)[:5]
                fused_insights[category] = [insight for insight, weight in fused_insights[category]]
        
        return fused_insights
        
    def _extract_expert_insights(self, opinion: Dict[str, Any]) -> Dict[str, List[str]]:
        """Extract structured insights from expert opinion"""
        # Simplified insight extraction
        insights = {
            'key_themes': ['Research methodology', 'Technical feasibility'],
            'novel_perspectives': ['Interdisciplinary approach'],
            'technical_considerations': ['Implementation complexity', 'Resource requirements'],
            'risk_factors': ['Technical risks', 'Timeline constraints'],
            'opportunity_areas': ['Innovation potential', 'Impact opportunities']
        }
        
        return insights
        
    def _fuse_recommendations(self, expert_opinions: Dict[str, Any], weights: Dict[str, float]) -> List[str]:
        """Fuse recommendations from multiple experts"""
        recommendation_scores = defaultdict(float)
        
        for expert, opinion in expert_opinions.items():
            weight = weights.get(expert, 0.2)
            
            if isinstance(opinion, dict) and 'recommendations' in opinion:
                recommendations = opinion['recommendations']
                if isinstance(recommendations, list):
                    for rec in recommendations:
                        recommendation_scores[str(rec)] += weight
                elif isinstance(recommendations, str):
                    recommendation_scores[recommendations] += weight
        
        # Sort recommendations by weighted score
        sorted_recommendations = sorted(recommendation_scores.items(), 
                                      key=lambda x: x[1], reverse=True)
        
        return [rec for rec, score in sorted_recommendations[:10]]  # Top 10 recommendations
        
    def _calculate_consensus_strength(self, expert_opinions: Dict[str, Any], weights: Dict[str, float]) -> float:
        """Calculate the strength of consensus among experts"""
        confidences = []
        
        for expert, opinion in expert_opinions.items():
            if isinstance(opinion, dict) and 'confidence' in opinion:
                confidences.append(opinion['confidence'])
        
        if not confidences:
            return 0.0
            
        # Consensus strength based on confidence variance
        mean_confidence = sum(confidences) / len(confidences)
        variance = sum((c - mean_confidence) ** 2 for c in confidences) / len(confidences)
        
        # Higher consensus when lower variance and higher mean
        consensus_strength = mean_confidence * (1 - min(variance, 1.0))
        
        return max(0.0, min(1.0, consensus_strength))
        
    def _identify_key_agreements(self, expert_opinions: Dict[str, Any]) -> List[str]:
        """Identify key areas of agreement between experts"""
        agreements = [
            'Research has significant potential',
            'Multi-disciplinary approach is beneficial',
            'Careful experimental design is crucial',
            'Validation and testing are essential'
        ]
        
        return agreements
        
    def _identify_uncertainties(self, expert_opinions: Dict[str, Any]) -> List[str]:
        """Identify remaining uncertainties after fusion"""
        uncertainties = [
            'Optimal parameter selection',
            'Resource requirement estimation',
            'Timeline accuracy',
            'Scalability considerations'
        ]
        
        return uncertainties
        
    def _extract_decision_indicators(self, opinion: Dict[str, Any]) -> Dict[str, bool]:
        """Extract binary decision indicators from opinion"""
        # Simplified decision extraction
        decisions = {
            'feasible': True,
            'recommended': True,
            'high_priority': True,
            'resource_intensive': False
        }
        
        return decisions
        
    def _calculate_majority(self, votes: List[bool]) -> bool:
        """Calculate majority decision from votes"""
        if not votes:
            return False
            
        true_votes = sum(1 for vote in votes if vote)
        return true_votes > len(votes) / 2
        
    def _calculate_vote_distribution(self, votes: List[bool]) -> Dict[str, int]:
        """Calculate vote distribution"""
        true_votes = sum(1 for vote in votes if vote)
        false_votes = len(votes) - true_votes
        
        return {'true': true_votes, 'false': false_votes}
        
    def _calculate_majority_consensus_level(self, majority_decisions: Dict[str, Any]) -> float:
        """Calculate overall consensus level for majority decisions"""
        if not majority_decisions:
            return 0.0
            
        consensus_scores = []
        for decision_data in majority_decisions.values():
            vote_dist = decision_data['vote_distribution']
            total_votes = vote_dist['true'] + vote_dist['false']
            majority_size = max(vote_dist['true'], vote_dist['false'])
            consensus_score = majority_size / total_votes if total_votes > 0 else 0.0
            consensus_scores.append(consensus_score)
        
        return sum(consensus_scores) / len(consensus_scores)
        
    def _find_unanimous_agreements(self, decisions: Dict[str, List[Tuple[str, bool]]]) -> List[str]:
        """Find decisions with unanimous agreement"""
        unanimous = []
        
        for decision_type, expert_decisions in decisions.items():
            votes = [decision for _, decision in expert_decisions]
            if len(set(votes)) == 1:  # All votes are the same
                unanimous.append(decision_type)
        
        return unanimous
        
    def _find_split_decisions(self, decisions: Dict[str, List[Tuple[str, bool]]]) -> List[str]:
        """Find decisions with significant splits"""
        split_decisions = []
        
        for decision_type, expert_decisions in decisions.items():
            votes = [decision for _, decision in expert_decisions]
            true_votes = sum(1 for vote in votes if vote)
            vote_ratio = true_votes / len(votes) if votes else 0.5
            
            # Consider split if between 40% and 60%
            if 0.4 <= vote_ratio <= 0.6:
                split_decisions.append(decision_type)
        
        return split_decisions
        
    def _calculate_weighted_contribution(self, opinion: Any, ranking_weight: float) -> Dict[str, Any]:
        """Calculate weighted contribution of expert opinion"""
        base_confidence = opinion.get('confidence', 0.5) if isinstance(opinion, dict) else 0.5
        
        return {
            'base_confidence': base_confidence,
            'ranking_weight': ranking_weight,
            'weighted_score': base_confidence * ranking_weight,
            'contribution_level': 'high' if base_confidence * ranking_weight > 0.15 else 'moderate'
        }
        
    def _extract_top_ranked_insights(self, ranked_fusion: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract top-ranked insights from fusion"""
        insights = []
        
        for expert, fusion_data in ranked_fusion.items():
            weighted_score = fusion_data['weighted_contribution']['weighted_score']
            insights.append({
                'expert': expert,
                'weighted_score': weighted_score,
                'insight': f"High-value contribution from {expert}",
                'confidence': fusion_data['weighted_contribution']['base_confidence']
            })
        
        return sorted(insights, key=lambda x: x['weighted_score'], reverse=True)[:5]
        
    def _calculate_ranking_based_confidence(self, ranked_fusion: Dict[str, Any]) -> float:
        """Calculate overall confidence based on rankings"""
        total_weighted_score = sum(
            fusion_data['weighted_contribution']['weighted_score']
            for fusion_data in ranked_fusion.values()
        )
        
        return min(1.0, total_weighted_score)
        
    def _analyze_agreements(self, expert_opinions: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze agreement levels across different areas"""
        agreement_areas = [
            'research_value', 'feasibility', 'methodology', 
            'timeline', 'resources', 'risks'
        ]
        
        agreement_levels = {}
        supporting_experts = defaultdict(list)
        disagreeing_experts = defaultdict(list)
        
        for area in agreement_areas:
            # Simplified agreement analysis
            agreement_level = np.random.uniform(0.6, 0.9)  # Placeholder
            agreement_levels[area] = agreement_level
            
            # Assign supporting/disagreeing experts
            for expert in expert_opinions.keys():
                if np.random.random() < agreement_level:
                    supporting_experts[area].append(expert)
                else:
                    disagreeing_experts[area].append(expert)
        
        overall_agreement = sum(agreement_levels.values()) / len(agreement_levels)
        
        return {
            'agreement_levels': agreement_levels,
            'supporting_experts': dict(supporting_experts),
            'disagreeing_experts': dict(disagreeing_experts),
            'overall_agreement': overall_agreement
        }
        
    def _calculate_consensus_strength_score(self, consensus_areas: List[Dict[str, Any]]) -> float:
        """Calculate overall consensus strength score"""
        if not consensus_areas:
            return 0.0
            
        total_strength = sum(area['agreement_level'] for area in consensus_areas)
        return total_strength / len(consensus_areas)
        
    def _generate_consensus_actions(self, consensus_areas: List[Dict[str, Any]], 
                                   areas_needing_discussion: List[Dict[str, Any]]) -> List[str]:
        """Generate recommended actions based on consensus analysis"""
        actions = []
        
        if consensus_areas:
            actions.append(f"Proceed with {len(consensus_areas)} areas of strong consensus")
            
        if areas_needing_discussion:
            high_priority = [area for area in areas_needing_discussion 
                           if area['discussion_priority'] == 'high']
            if high_priority:
                actions.append(f"Prioritize discussion on {len(high_priority)} high-priority areas")
            
            actions.append("Schedule follow-up discussions for remaining disagreements")
        
        actions.append("Document all consensus decisions for implementation")
        
        return actions

    def fuse_expert_knowledge(self, expert_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        融合多专家知识 (高级系统接口)
        
        Args:
            expert_data: 包含专家输入和融合目标的数据
            
        Returns:
            融合后的知识结果
        """
        # 提取专家输入和目标
        expert_inputs = expert_data.get('expert_inputs', [])
        target = expert_data.get('target', 'general_fusion')
        topic = expert_data.get('topic', 'research')
        
        # 转换专家输入格式
        expert_opinions = {}
        for i, expert_input in enumerate(expert_inputs):
            expert_name = f"expert_{i+1}"
            expert_opinions[expert_name] = {
                'content': expert_input,
                'confidence': 0.8,  # 默认置信度
                'timestamp': datetime.now().isoformat()
            }
        
        # 使用加权共识融合
        fusion_result = self.fuse_knowledge(expert_opinions, 'weighted_consensus')
        
        # 返回符合高级系统期望的格式
        return {
            'fused_content': fusion_result.get('fused_content', expert_inputs[0] if expert_inputs else ''),
            'confidence_score': fusion_result.get('overall_confidence', 0.8),
            'fusion_strategy': 'weighted_consensus',
            'expert_count': len(expert_inputs),
            'fusion_metadata': {
                'target': target,
                'topic': topic,
                'timestamp': datetime.now().isoformat()
            }
        }
