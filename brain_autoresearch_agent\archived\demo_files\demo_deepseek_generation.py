"""
DeepSeek API 完整论文生成演示
使用真实的DeepSeek API进行完整的论文生成工作流测试
"""

import os
import sys
import asyncio
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from paper_generation.unified_paper_workflow import (
    UnifiedPaperGenerationWorkflow, 
    UnifiedWorkflowConfig
)
from paper_generation.enhanced_brain_paper_writer import PaperGenerationConfig


def setup_deepseek_environment():
    """设置DeepSeek环境"""
    print("🔧 配置DeepSeek环境...")
    
    # 设置API密钥
    api_key = "sk-1b1d72e2e10643029de548b655e1f93e"
    os.environ["DEEPSEEK_API_KEY"] = api_key
    os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
    
    # 禁用模拟模式，强制使用真实API
    os.environ["MOCK_MODE"] = "false"
    os.environ["ENABLE_MOCK_DATA"] = "false"
    
    print(f"✅ DeepSeek环境配置完成")
    print(f"🔑 API密钥已设置")
    print(f"🌐 API基础URL: https://api.deepseek.com")
    print(f"🚫 模拟模式已禁用")
    
    return api_key


async def demo_deepseek_paper_generation():
    """DeepSeek完整论文生成演示"""
    print("=" * 80)
    print("🧠 DeepSeek API 完整论文生成演示")
    print("=" * 80)
    
    # 1. 设置环境
    setup_deepseek_environment()
    
    # 2. 创建研究主题和需求
    research_topic = "基于脑神经可塑性的自适应深度学习算法研究"
    research_requirements = {
        "target_conference": "ICML 2024",
        "paper_length": "8页",
        "focus_areas": [
            "神经可塑性机制建模",
            "自适应学习算法设计", 
            "生物启发的网络架构",
            "能效优化方法",
            "实验验证和分析"
        ],
        "innovation_requirements": [
            "提出新的脑启发可塑性学习机制",
            "设计高效的自适应网络架构",
            "实现能效优化的学习算法",
            "提供完整的理论分析和实验验证"
        ],
        "technical_requirements": [
            "支持在线学习和适应",
            "保持生物学合理性",
            "实现计算效率提升",
            "具备良好的可扩展性"
        ]
    }
    
    print(f"📋 研究主题: {research_topic}")
    print(f"🎯 目标会议: {research_requirements['target_conference']}")
    print(f"📄 论文长度: {research_requirements['paper_length']}")
    print(f"🔬 重点领域数量: {len(research_requirements['focus_areas'])}")
    print(f"💡 创新要求数量: {len(research_requirements['innovation_requirements'])}")
    
    # 3. 创建DeepSeek配置
    print(f"\\n⚙️ 创建DeepSeek论文生成配置...")
    
    paper_config = PaperGenerationConfig(
        target_venue="ICML",
        paper_type="research",
        max_review_iterations=2,  # 减少轮次以节省API调用
        quality_threshold=7.0,   # 合理的质量要求
        enable_auto_revision=True,
        enable_multi_expert_review=True,
        latex_output=True
    )
    
    config = UnifiedWorkflowConfig(
        use_advanced_writer=True,
        paper_generation_config=paper_config,
        output_formats=["markdown", "latex", "json"],
        output_directory=f"output/deepseek_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        enable_workflow_extraction=True,
        enable_integration_analysis=True
    )
    
    print(f"✅ 配置创建完成")
    print(f"📊 质量阈值: {paper_config.quality_threshold}")
    print(f"🔄 最大评审轮次: {paper_config.max_review_iterations}")
    print(f"📁 输出目录: {config.output_directory}")
    
    # 4. 创建LLM客户端
    print(f"\\n🤖 创建DeepSeek LLM客户端...")
    
    llm_client = LLMClient(
        provider="deepseek",
        model="deepseek-chat",
        temperature=0.7,
        api_key=os.environ["DEEPSEEK_API_KEY"]
    )
    
    # 验证客户端状态
    if llm_client.deepseek_mode:
        print(f"✅ DeepSeek客户端创建成功")
        print(f"🧠 模型: {llm_client.model}")
        print(f"🌡️ 温度: {llm_client.temperature}")
    else:
        print(f"❌ DeepSeek客户端创建失败，将使用模拟模式")
        response = input("是否继续? [y/N]: ")
        if response.lower() not in ['y', 'yes']:
            return
    
    # 5. 测试API连接
    print(f"\\n🧪 测试DeepSeek API连接...")
    try:
        test_response = llm_client.generate_response(
            "请简述脑启发人工智能的核心概念。限制在100字以内。"
        )
        if test_response and len(test_response.strip()) > 20:
            print(f"✅ API连接测试成功")
            print(f"📝 测试响应: {test_response[:150]}...")
        else:
            print(f"⚠️ API响应异常，可能影响后续生成")
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return
    
    # 6. 创建并执行工作流
    print(f"\\n🚀 创建统一论文生成工作流...")
    
    workflow = UnifiedPaperGenerationWorkflow(llm_client, config)
    
    print(f"🔄 开始论文生成流程...")
    print(f"⏱️ 预计需要 5-10 分钟（取决于API响应速度）")
    
    start_time = datetime.now()
    
    try:
        # 执行论文生成
        result = await workflow.generate_complete_paper(
            research_topic=research_topic,
            research_requirements=research_requirements,
            reference_papers=[]
        )
        
        generation_time = (datetime.now() - start_time).total_seconds()
        
        # 7. 显示结果
        print(f"\\n" + "=" * 60)
        print(f"📊 论文生成结果")
        print(f"=" * 60)
        
        if result.success:
            print(f"✅ 论文生成成功!")
            print(f"📁 输出目录: {config.output_directory}")
            print(f"📄 生成格式: {', '.join(config.output_formats)}")
            print(f"⏱️ 总生成时间: {generation_time:.2f}秒")
            print(f"📊 论文质量评分: {result.paper_quality_score:.2f}/10")
            
            # 显示输出文件
            print(f"\\n📂 生成的文件:")
            total_size = 0
            for format_name, file_path in result.output_files.items():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    total_size += file_size
                    print(f"  📄 {format_name}: {os.path.basename(file_path)} ({file_size:.1f} KB)")
                else:
                    print(f"  ❌ {format_name}: 文件未生成")
            
            print(f"\\n💾 总文件大小: {total_size:.1f} KB")
            
            # 显示论文核心信息
            if hasattr(result, 'paper_content') and result.paper_content:
                paper_content = result.paper_content
                print(f"\\n📋 论文核心信息:")
                print(f"  📝 标题长度: {len(paper_content.get('title', ''))}")
                print(f"  📄 摘要长度: {len(paper_content.get('abstract', ''))}")
                print(f"  📑 章节数量: {len([k for k in paper_content.keys() if k not in ['title', 'abstract', 'keywords']])}")
                
                # 显示摘要
                if 'abstract' in paper_content and paper_content['abstract']:
                    print(f"\\n📄 论文摘要:")
                    print("-" * 40)
                    abstract = paper_content['abstract']
                    print(abstract[:400] + "..." if len(abstract) > 400 else abstract)
                    print("-" * 40)
            
            # 显示评估信息
            if hasattr(result, 'evaluation_results') and result.evaluation_results:
                eval_results = result.evaluation_results
                print(f"\\n📊 质量评估结果:")
                for metric, score in eval_results.items():
                    if isinstance(score, (int, float)):
                        print(f"  {metric}: {score:.2f}/10")
                    else:
                        print(f"  {metric}: {score}")
            
            print(f"\\n🎉 DeepSeek API论文生成演示完成！")
            print(f"💡 您可以在 {config.output_directory} 查看生成的论文文件")
            
        else:
            print(f"❌ 论文生成失败")
            error_msg = getattr(result, 'error_message', '未知错误')
            print(f"🔍 错误信息: {error_msg}")
            print(f"⏱️ 执行时间: {generation_time:.2f}秒")
            
    except Exception as e:
        print(f"\\n💥 生成过程中出现异常:")
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        
        generation_time = (datetime.now() - start_time).total_seconds()
        print(f"⏱️ 执行时间: {generation_time:.2f}秒")


def main():
    """主函数"""
    print("🚀 启动DeepSeek API完整论文生成演示...")
    
    # 检查必要的依赖
    try:
        import openai
        print("✅ OpenAI库可用")
    except ImportError:
        print("❌ 缺少OpenAI库，请运行: pip install openai")
        return
    
    # 运行异步演示
    asyncio.run(demo_deepseek_paper_generation())


if __name__ == "__main__":
    main()
