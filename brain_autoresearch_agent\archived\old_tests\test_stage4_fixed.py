"""
Fixed Stage 4 Complete Test: Paper Writing with DeepSeek API
Fixed version with correct parameter handling and result access
"""

import os
import sys
import json
import asyncio
from datetime import datetime
from pathlib import Path

# Add project path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from paper_generation.unified_paper_workflow import (
    UnifiedPaperGenerationWorkflow, 
    UnifiedWorkflowConfig
)
from paper_generation.enhanced_brain_paper_writer import PaperGenerationConfig
from stage3_simulation import create_stage3_simulation, save_stage3_simulation


def setup_deepseek_for_stage4():
    """Setup DeepSeek environment specifically for Stage 4 testing"""
    print("🔧 Setting up DeepSeek for Stage 4 testing...")
    
    # Configure API
    api_key = "sk-1b1d72e2e10643029de548b655e1f93e"
    os.environ["DEEPSEEK_API_KEY"] = api_key
    os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
    os.environ["MOCK_MODE"] = "false"
    os.environ["ENABLE_MOCK_DATA"] = "false"
    
    print(f"✅ DeepSeek configured for Stage 4")
    print(f"🎯 Focus: Paper writing, review, and revision")
    
    return api_key


async def test_stage4_fixed_workflow():
    """Test Stage 4 workflow with proper parameter handling"""
    print("=" * 80)
    print("📝 Stage 4 Fixed Test: Paper Writing with DeepSeek")
    print("=" * 80)
    
    # 1. Setup environment
    setup_deepseek_for_stage4()
    
    # 2. Load Stage 3 simulation data
    print(f"\\n📋 Loading Stage 3 simulation data...")
    stage3_data = create_stage3_simulation()
    stage3_file = save_stage3_simulation()
    
    print(f"✅ Stage 3 data loaded")
    print(f"🎯 Research Topic: {stage3_data['research_topic']}")
    print(f"📊 Research Value Score: {stage3_data['research_value_assessment']['overall_score']}/10")
    
    # 3. Create Stage 4 configuration
    print(f"\\n⚙️ Creating Stage 4 paper generation configuration...")
    
    paper_config = PaperGenerationConfig(
        target_venue="ICML",
        paper_type="research",
        max_review_iterations=2,  # Reduced for testing
        quality_threshold=7.5,   # High quality requirement
        enable_auto_revision=True,
        enable_multi_expert_review=True,
        latex_output=True,
        language="english"
    )
    
    config = UnifiedWorkflowConfig(
        use_advanced_writer=True,
        paper_generation_config=paper_config,
        output_formats=["markdown", "latex", "json"],
        output_directory=f"output/stage4_fixed_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        enable_workflow_extraction=True,
        enable_integration_analysis=True
    )
    
    print(f"✅ Stage 4 configuration created")
    print(f"📊 Quality threshold: {paper_config.quality_threshold}")
    print(f"🔄 Max review iterations: {paper_config.max_review_iterations}")
    print(f"📁 Output directory: {config.output_directory}")
    
    # 4. Create DeepSeek LLM client
    print(f"\\n🧠 Creating DeepSeek LLM client...")
    
    llm_client = LLMClient(
        provider="deepseek",
        model="deepseek-chat",
        temperature=0.7,
        api_key=os.environ["DEEPSEEK_API_KEY"]
    )
    
    if llm_client.deepseek_mode:
        print(f"✅ DeepSeek client created successfully")
        print(f"✏️ Writing model: {llm_client.model}")
    else:
        print(f"❌ DeepSeek client creation failed")
        return False
    
    # 5. Prepare enhanced research requirements
    print(f"\\n📋 Preparing enhanced research requirements...")
    
    enhanced_requirements = {
        "target_conference": "ICML 2024", 
        "paper_length": "8 pages",
        "focus_areas": stage3_data['experimental_design']['experimental_validation']['datasets'],
        "innovation_requirements": stage3_data['innovation_highlights']['technical_contributions'],
        "experimental_framework": stage3_data['experimental_design']['experimental_framework'],
        "literature_context": stage3_data['literature_integration'],
        "visualization_plan": stage3_data['visualization_plan'],
        "research_value_assessment": stage3_data['research_value_assessment']
    }
    
    print(f"✅ Enhanced requirements prepared")
    print(f"📊 Focus areas: {len(enhanced_requirements['focus_areas'])}")
    print(f"💡 Innovation requirements: {len(enhanced_requirements['innovation_requirements'])}")
    
    # 6. Execute workflow
    print(f"\\n🚀 Executing complete Stage 4 workflow...")
    
    workflow = UnifiedPaperGenerationWorkflow(llm_client, config)
    
    print(f"🔄 Starting comprehensive paper generation...")
    print(f"⏱️ Estimated time: 8-12 minutes")
    
    start_time = datetime.now()
    
    try:
        # Execute paper generation with correct parameters
        result = await workflow.generate_complete_paper(
            research_topic=stage3_data['research_topic'],
            research_requirements=enhanced_requirements,
            reference_papers=stage3_data['literature_integration']['key_references']
        )
        
        generation_time = (datetime.now() - start_time).total_seconds()
        
        # 7. Analyze and display results
        print(f"\\n" + "=" * 60)
        print(f"📊 Stage 4 Fixed Test Results")
        print(f"=" * 60)
        
        if result.success:
            print(f"✅ Complete paper generation successful!")
            print(f"📁 Output directory: {config.output_directory}")
            print(f"📄 Generated formats: {', '.join(config.output_formats)}")
            print(f"⏱️ Total generation time: {generation_time:.2f} seconds ({generation_time/60:.1f} minutes)")
            
            # Access paper quality through correct path
            try:
                quality_score = result.paper_generation.quality_metrics.overall_score
                print(f"📊 Final paper quality score: {quality_score:.2f}/10")
            except AttributeError:
                print(f"📊 Quality score: Unable to access (check result structure)")
            
            # Analyze file outputs
            print(f"\\n📂 Generated files analysis:")
            total_size = 0
            file_count = 0
            for format_name, file_path in result.output_files.items():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    total_size += file_size
                    file_count += 1
                    print(f"  📄 {format_name}: {os.path.basename(file_path)} ({file_size:.1f} KB)")
                    
                    # Display content preview for markdown
                    if format_name == "markdown" and file_size > 5:
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                print(f"    📝 Content length: {len(content)} characters")
                                
                                # Check for key sections
                                sections = ['abstract', 'introduction', 'methodology', 'results', 'conclusion']
                                found_sections = [s for s in sections if s.lower() in content.lower()]
                                print(f"    📋 Sections found: {len(found_sections)}/{len(sections)}")
                        except Exception as e:
                            print(f"    ⚠️ Could not read content: {e}")
                else:
                    print(f"  ❌ {format_name}: File not generated")
            
            print(f"\\n💾 Summary: {file_count} files, {total_size:.1f} KB total")
            
            # Display paper structure analysis
            try:
                paper_content = result.paper_generation.paper_content
                if paper_content:
                    print(f"\\n📋 Paper structure analysis:")
                    
                    # Count sections
                    if isinstance(paper_content, dict):
                        sections = [k for k in paper_content.keys() if k not in ['title', 'abstract', 'keywords']]
                        print(f"  📖 Total sections: {len(sections)}")
                        
                        # Check abstract
                        abstract = paper_content.get('abstract', '')
                        if abstract:
                            print(f"  📝 Abstract length: {len(abstract)} characters")
                            
                            # Check for Stage 3 integration
                            integration_keywords = ['plasticity', 'adaptive', 'continual learning', 'energy efficient']
                            found_keywords = [kw for kw in integration_keywords if kw.lower() in abstract.lower()]
                            print(f"  🎯 Stage 3 integration: {len(found_keywords)}/{len(integration_keywords)} keywords")
                            
                            if len(found_keywords) >= 3:
                                print(f"  ✅ Strong Stage 3 integration detected")
                            else:
                                print(f"  ⚠️ Limited Stage 3 integration")
                        else:
                            print(f"  ⚠️ No abstract found")
                    else:
                        print(f"  ⚠️ Paper content format unexpected: {type(paper_content)}")
                else:
                    print(f"  ❌ No paper content found")
            except AttributeError as e:
                print(f"  ⚠️ Could not access paper content: {e}")
            
            # Display workflow extraction results
            try:
                workflow_extraction = result.workflow_extraction
                if workflow_extraction and workflow_extraction.success:
                    print(f"\\n🔍 Workflow extraction analysis:")
                    print(f"  📊 Extracted workflows: {len(workflow_extraction.extracted_workflows)}")
                    print(f"  🔬 Research gaps: {len(workflow_extraction.research_gaps)}")
                    print(f"  💡 Innovation opportunities: {len(workflow_extraction.innovation_opportunities)}")
                    print(f"  🔧 Technical requirements: {len(workflow_extraction.technical_requirements)}")
                else:
                    print(f"\\n⚠️ Workflow extraction not successful")
            except AttributeError:
                print(f"\\n⚠️ Could not access workflow extraction results")
            
            # Display integration insights
            try:
                integration_insights = result.integration_insights
                if integration_insights:
                    print(f"\\n🔗 Integration insights:")
                    for key, value in integration_insights.items():
                        if isinstance(value, (int, float)):
                            print(f"  {key}: {value:.3f}")
                        elif isinstance(value, list) and len(value) <= 3:
                            print(f"  {key}: {', '.join(map(str, value))}")
            except (AttributeError, TypeError):
                print(f"\\n⚠️ Could not display integration insights")
            
            print(f"\\n🎉 Stage 4 fixed test SUCCESSFUL!")
            print(f"✅ Paper generation workflow operational")
            print(f"✅ Stage 3 integration functional")
            print(f"✅ Multi-format output generated")
            print(f"✅ DeepSeek API working correctly")
            
            return True
            
        else:
            print(f"❌ Stage 4 test failed")
            error_msg = getattr(result, 'generation_summary', 'Unknown error')
            print(f"🔍 Error details: {error_msg}")
            print(f"⏱️ Execution time: {generation_time:.2f} seconds")
            return False
            
    except Exception as e:
        print(f"\\n💥 Stage 4 test exception:")
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
        generation_time = (datetime.now() - start_time).total_seconds()
        print(f"⏱️ Execution time: {generation_time:.2f} seconds")
        return False


async def main():
    """Main test execution"""
    print("🚀 Starting Stage 4 Fixed Complete Test...")
    
    # Verify dependencies
    try:
        import openai
        print("✅ OpenAI library available")
    except ImportError:
        print("❌ Missing OpenAI library, please run: pip install openai")
        return False
    
    # Run Stage 4 fixed test
    success = await test_stage4_fixed_workflow()
    
    if success:
        print(f"\\n🎊 Stage 4 fixed test PASSED!")
        print(f"💡 System is ready for:")
        print(f"  1. Full pipeline integration (Stages 1-4)")
        print(f"  2. Production deployment testing")
        print(f"  3. Large-scale research topic validation")
        print(f"  4. Performance optimization")
    else:
        print(f"\\n❌ Stage 4 fixed test FAILED")
        print(f"🔧 Additional debugging may be needed")
    
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
