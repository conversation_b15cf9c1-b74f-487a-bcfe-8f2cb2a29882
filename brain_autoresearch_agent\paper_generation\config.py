"""
Paper Generation Configuration

This configuration file defines parameters and settings for
automated academic paper generation in brain-inspired intelligence research.
"""

# Paper Generation Settings
PAPER_GENERATION_CONFIG = {
    
    # LLM Settings
    "llm": {
        "model": "deepseek-chat",
        "temperature": 0.7,
        "max_tokens": 4000,
        "timeout": 60
    },
    
    # Literature Review Settings
    "literature_review": {
        "max_papers_per_query": 15,
        "include_recent_only": True,
        "recent_years_threshold": 5,
        "enable_caching": True,
        "rate_limit_delay": 0.5,
        
        # Search strategy weights
        "search_strategies": {
            "foundational": 0.20,      # 20% foundational papers
            "recent_advances": 0.25,   # 25% recent advances
            "methodological": 0.20,    # 20% methodological papers
            "applications": 0.15,      # 15% application papers
            "neuroscience_basis": 0.10, # 10% neuroscience papers
            "comparative": 0.10        # 10% comparative studies
        }
    },
    
    # Paper Structure Settings
    "paper_structure": {
        "target_venues": ["ICML", "NeurIPS", "ICLR", "AAAI", "IJCAI"],
        "default_venue": "ICML",
        "paper_types": ["research", "survey", "position"],
        "default_type": "research",
        
        # Section requirements
        "required_sections": [
            "abstract", "introduction", "related_work", 
            "methodology", "experiments", "results", 
            "discussion", "conclusion"
        ],
        
        # Word count targets
        "word_counts": {
            "abstract": {"min": 200, "max": 300},
            "introduction": {"min": 800, "max": 1500},
            "related_work": {"min": 1000, "max": 2000},
            "methodology": {"min": 1200, "max": 2500},
            "experiments": {"min": 800, "max": 1500},
            "results": {"min": 800, "max": 1500},
            "discussion": {"min": 600, "max": 1200},
            "conclusion": {"min": 300, "max": 600}
        }
    },
    
    # Multi-Expert Reasoning Settings
    "expert_reasoning": {
        "enabled_experts": [
            "ai_technology", 
            "neuroscience", 
            "data_analysis", 
            "paper_writing", 
            "experiment_design"
        ],
        
        "reasoning_phases": [
            "value_assessment",
            "experiment_design", 
            "implementation_strategy",
            "visualization_planning"
        ],
        
        "collaboration_rounds": 2,
        "consensus_threshold": 0.7,
        "expert_weights": {
            "ai_technology": 0.25,
            "neuroscience": 0.20,
            "data_analysis": 0.20,
            "paper_writing": 0.20,
            "experiment_design": 0.15
        }
    },
    
    # Content Generation Settings
    "content_generation": {
        "enable_multi_expert_review": True,
        "refinement_iterations": 2,
        "quality_threshold": 7.5,  # Out of 10
        
        # Generation strategies
        "abstract_strategy": "comprehensive_summary",
        "introduction_strategy": "problem_motivation",
        "methodology_strategy": "detailed_technical",
        "results_strategy": "evidence_based",
        "discussion_strategy": "analytical_synthesis"
    },
    
    # LaTeX Generation Settings
    "latex_generation": {
        "include_figures": True,
        "include_tables": True,
        "include_algorithms": True,
        "bibliography_style": "natbib",
        
        # Template preferences by venue
        "venue_templates": {
            "ICML": "icml2025",
            "NeurIPS": "neurips_2024", 
            "ICLR": "iclr2025_conference",
            "AAAI": "aaai25",
            "IJCAI": "ijcai25"
        },
        
        # Figure and table placeholders
        "default_figures": [
            "architecture_diagram",
            "performance_comparison", 
            "ablation_study",
            "visualization_results"
        ],
        
        "default_tables": [
            "performance_comparison",
            "ablation_study",
            "computational_complexity",
            "dataset_statistics"
        ]
    },
    
    # Citation Management Settings
    "citation_management": {
        "citation_style": "author_year",
        "max_citations_per_paper": 80,
        "min_citations_per_paper": 30,
        
        # Citation distribution guidelines
        "citation_distribution": {
            "foundational": {"min": 8, "max": 15},
            "recent": {"min": 10, "max": 20},
            "methodological": {"min": 8, "max": 15},
            "comparative": {"min": 5, "max": 12},
            "survey": {"min": 3, "max": 6}
        },
        
        "auto_generate_bibtex": True,
        "validate_citations": True
    },
    
    # Quality Assurance Settings
    "quality_assurance": {
        "enable_automated_review": True,
        "review_criteria": [
            "technical_accuracy",
            "writing_quality", 
            "novelty_assessment",
            "experimental_rigor",
            "reproducibility"
        ],
        
        "minimum_quality_scores": {
            "technical_accuracy": 7.0,
            "writing_quality": 7.0,
            "novelty_assessment": 6.5,
            "experimental_rigor": 7.0,
            "reproducibility": 6.5
        },
        
        "enable_plagiarism_check": True,
        "enable_grammar_check": True
    },
    
    # Output Settings
    "output": {
        "base_directory": "output/papers",
        "create_timestamp_dirs": True,
        "save_intermediate_results": True,
        
        # Output formats
        "formats": {
            "json": True,          # Complete paper data
            "latex": True,         # LaTeX source
            "pdf": False,          # PDF compilation (requires LaTeX)
            "markdown": True,      # Markdown version
            "docx": False          # Word document (optional)
        },
        
        # File naming
        "filename_template": "{topic}_{venue}_{timestamp}",
        "sanitize_filenames": True
    },
    
    # Research Topic Templates
    "research_topics": {
        "brain_inspired_computing": {
            "keywords": ["neuromorphic", "spiking neural networks", "brain-inspired"],
            "typical_venues": ["Nature Machine Intelligence", "Neural Networks", "IEEE TNNLS"],
            "core_concepts": ["plasticity", "adaptation", "efficiency", "learning"]
        },
        
        "neural_architecture_search": {
            "keywords": ["AutoML", "architecture search", "neural architecture"],
            "typical_venues": ["ICML", "NeurIPS", "ICLR"],
            "core_concepts": ["optimization", "search space", "performance"]
        },
        
        "neuromorphic_engineering": {
            "keywords": ["neuromorphic", "hardware", "memristor", "spike"],
            "typical_venues": ["IEEE JETCAS", "Frontiers in Neuroscience"],
            "core_concepts": ["energy efficiency", "real-time", "parallelism"]
        }
    },
    
    # Venue-Specific Guidelines
    "venue_guidelines": {
        "ICML": {
            "focus": "Machine learning methodology and theory",
            "page_limit": 8,
            "review_criteria": ["novelty", "technical_quality", "clarity", "significance"],
            "preferred_sections": ["abstract", "introduction", "method", "experiments", "conclusion"]
        },
        
        "NeurIPS": {
            "focus": "Neural information processing systems",
            "page_limit": 9,
            "review_criteria": ["technical_quality", "novelty", "potential_impact", "clarity"],
            "preferred_sections": ["abstract", "introduction", "background", "method", "experiments", "discussion"]
        },
        
        "ICLR": {
            "focus": "Learning representations",
            "page_limit": 8,
            "review_criteria": ["technical_quality", "clarity", "originality", "significance"],
            "preferred_sections": ["abstract", "introduction", "related_work", "method", "experiments", "conclusion"]
        }
    },
    
    # Debugging and Logging
    "debug": {
        "enable_verbose_logging": True,
        "log_level": "INFO",
        "save_debug_info": True,
        "track_generation_time": True,
        "enable_step_by_step_output": True
    }
}


# Domain-Specific Configurations
BRAIN_RESEARCH_DOMAINS = {
    "cognitive_neuroscience": {
        "keywords": ["cognition", "behavior", "neural mechanisms"],
        "methods": ["fMRI", "EEG", "behavioral experiments"],
        "venues": ["Nature Neuroscience", "Current Biology", "Neuron"]
    },
    
    "computational_neuroscience": {
        "keywords": ["neural modeling", "simulation", "dynamics"],
        "methods": ["mathematical modeling", "simulation", "analysis"],
        "venues": ["Journal of Computational Neuroscience", "Neural Computation"]
    },
    
    "machine_learning": {
        "keywords": ["learning algorithms", "optimization", "generalization"],
        "methods": ["experimental validation", "theoretical analysis"],
        "venues": ["ICML", "NeurIPS", "JMLR"]
    },
    
    "robotics": {
        "keywords": ["embodied intelligence", "sensorimotor", "adaptation"],
        "methods": ["robotic experiments", "simulation", "hardware"],
        "venues": ["RSS", "ICRA", "Autonomous Robots"]
    }
}


# Expert System Configurations
EXPERT_SYSTEM_CONFIG = {
    "ai_technology": {
        "specialization": "AI algorithms, deep learning, optimization",
        "focus_areas": ["neural networks", "learning algorithms", "AI systems"],
        "review_weight": 0.25
    },
    
    "neuroscience": {
        "specialization": "Brain structure, neural mechanisms, biological plausibility", 
        "focus_areas": ["neural circuits", "plasticity", "brain function"],
        "review_weight": 0.20
    },
    
    "data_analysis": {
        "specialization": "Experimental design, statistical analysis, evaluation",
        "focus_areas": ["experiments", "metrics", "validation"],
        "review_weight": 0.20
    },
    
    "paper_writing": {
        "specialization": "Academic writing, structure, clarity",
        "focus_areas": ["writing quality", "organization", "presentation"],
        "review_weight": 0.20
    },
    
    "experiment_design": {
        "specialization": "Research methodology, experimental protocols",
        "focus_areas": ["methodology", "validation", "reproducibility"],
        "review_weight": 0.15
    }
}


# Default Paper Templates
DEFAULT_PAPER_TEMPLATES = {
    "research_paper": {
        "structure": [
            "abstract", "introduction", "related_work", "methodology",
            "experiments", "results", "discussion", "conclusion", "references"
        ],
        "emphasis": "novel methodology and experimental validation"
    },
    
    "survey_paper": {
        "structure": [
            "abstract", "introduction", "background", "taxonomy", 
            "detailed_review", "analysis", "future_directions", "conclusion", "references"
        ],
        "emphasis": "comprehensive coverage and critical analysis"
    },
    
    "position_paper": {
        "structure": [
            "abstract", "introduction", "position_statement", "supporting_arguments",
            "counterarguments", "implications", "conclusion", "references"
        ],
        "emphasis": "clear position and compelling arguments"
    }
}


def get_config():
    """Return the complete configuration"""
    return PAPER_GENERATION_CONFIG


def get_domain_config(domain: str):
    """Get domain-specific configuration"""
    return BRAIN_RESEARCH_DOMAINS.get(domain, {})


def get_expert_config(expert_type: str):
    """Get expert-specific configuration"""
    return EXPERT_SYSTEM_CONFIG.get(expert_type, {})


def get_template_config(template_type: str):
    """Get paper template configuration"""
    return DEFAULT_PAPER_TEMPLATES.get(template_type, {})


if __name__ == "__main__":
    import json
    
    print("📋 Paper Generation Configuration")
    print("=" * 50)
    
    config = get_config()
    print(f"Available venues: {config['paper_structure']['target_venues']}")
    print(f"Enabled experts: {config['expert_reasoning']['enabled_experts']}")
    print(f"Output formats: {list(config['output']['formats'].keys())}")
    
    # Save configuration to file
    with open("paper_generation_config.json", "w") as f:
        json.dump({
            "main_config": PAPER_GENERATION_CONFIG,
            "domains": BRAIN_RESEARCH_DOMAINS,
            "experts": EXPERT_SYSTEM_CONFIG,
            "templates": DEFAULT_PAPER_TEMPLATES
        }, f, indent=2)
    
    print("✅ Configuration saved to paper_generation_config.json")
