"""
脑启发智能AutoResearch Agent - 基础代理类
提供所有专家代理的统一接口和基础功能
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
import json
import time
from dataclasses import dataclass

from core.unified_api_client import UnifiedAPIClient


@dataclass
class AgentResponse:
    """代理响应数据结构"""
    agent_type: str
    content: str
    confidence: float  # 置信度 0-1
    reasoning: str     # 推理过程
    metadata: Dict[str, Any]
    timestamp: str

    def get(self, key, default=None):
        """
        获取元数据中的值，提供类似字典的访问方式
        
        Args:
            key: 键名
            default: 默认值
            
        Returns:
            对应的值或默认值
        """
        if key == 'content':
            return self.content
        elif key == 'confidence':
            return self.confidence
        elif key == 'reasoning':
            return self.reasoning
        elif key == 'agent_type':
            return self.agent_type
        elif key in self.metadata:
            return self.metadata[key]
        return default


@dataclass
class AgentTask:
    """代理任务数据结构"""
    task_id: str
    task_type: str
    input_data: Dict[str, Any]
    requirements: List[str] = None  # 默认为None
    priority: int = 1  # 1-5, 5最高
    
    def __post_init__(self):
        if self.requirements is None:
            self.requirements = []


class BaseAgent(ABC):
    """基础代理类 - 所有专家代理的父类"""
    
    def __init__(self, 
                 agent_type: str,
                 unified_client: UnifiedAPIClient,
                 specialization: str,
                 temperature: float = 0.3):
        """
        初始化基础代理
        
        Args:
            agent_type: 代理类型名称
            unified_client: 统一API客户端实例
            specialization: 专业领域描述
            temperature: LLM生成温度
        """
        self.agent_type = agent_type
        self.unified_client = unified_client
        self.specialization = specialization
        self.temperature = temperature
        
        # 代理状态
        self.is_active = True
        self.task_count = 0
        self.success_count = 0
        
        # 专家知识库 (子类可扩展)
        self.knowledge_base = {}
        
        # 初始化专家系统提示词
        self.system_prompt = self._build_system_prompt()
    
    @abstractmethod
    def _build_system_prompt(self) -> str:
        """构建专家系统提示词 - 子类必须实现"""
        pass
    
    @abstractmethod
    def analyze(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析输入数据 - 子类必须实现主要分析逻辑"""
        pass
    
    def process_task(self, task: AgentTask) -> AgentResponse:
        """
        处理代理任务的统一入口
        """
        try:
            print(f"🤖 {self.agent_type}正在处理任务: {task.task_id}")
            self.task_count += 1
            if not self._validate_input(task.input_data):
                return self._create_error_response(
                    "输入数据验证失败", 
                    task.task_id
                )
            response = self.analyze(task.input_data)
            # === 新增：强制校验content为JSON结构 ===
            import json
            content = response.content
            if isinstance(content, str):
                try:
                    parsed = json.loads(content)
                    response.content = parsed
                except Exception as e:
                    # 兜底为结构化错误内容
                    response.content = {
                        "error": "Agent output is not valid JSON",
                        "raw": content,
                        "exception": str(e)
                    }
                    response.confidence = 0.0
                    response.reasoning += f" | JSON解析失败: {e}"
            # === END ===
            if response.confidence > 0.7:
                self.success_count += 1
            response.metadata.update({
                "task_id": task.task_id,
                "task_type": task.task_type,
                "processing_time": time.time()
            })
            return response
        except Exception as e:
            print(f"❌ {self.agent_type}任务处理失败: {e}")
            return self._create_error_response(str(e), task.task_id)
    
    def _validate_input(self, input_data: Dict[str, Any]) -> bool:
        """验证输入数据格式 - 子类可重写"""
        try:
            # 检查基本类型
            if not isinstance(input_data, dict):
                return False
            
            # 兼容Mock对象的长度检查
            try:
                return len(input_data) > 0
            except (TypeError, AttributeError):
                # 处理Mock对象或其他无法len()的情况
                return hasattr(input_data, '__iter__') or bool(input_data)
        except Exception:
            return False
    
    def _create_error_response(self, error_msg: str, task_id: str = "") -> AgentResponse:
        """创建错误响应"""
        return AgentResponse(
            agent_type=self.agent_type,
            content=f"处理失败: {error_msg}",
            confidence=0.0,
            reasoning=f"错误原因: {error_msg}",
            metadata={"error": True, "task_id": task_id},
            timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
        )
    
    def get_llm_response(self, 
                        prompt: str, 
                        system_message: str = None,
                        extract_json: bool = False) -> Tuple[str, Optional[Dict]]:
        """
        获取LLM响应的统一方法
        
        Args:
            prompt: 用户提示词
            system_message: 系统消息（可选，默认使用专家系统提示词）
            extract_json: 是否提取JSON
            
        Returns:
            Tuple[响应文本, JSON数据(如果requested)]
        """
        try:
            system_msg = system_message or self.system_prompt
            
            # 调用UnifiedAPIClient的get_text_response方法，不传递任何可能造成问题的参数
            response = self.unified_client.get_text_response(
                prompt=prompt,
                system_message=system_msg
            )
            
            # 如果返回的是APIResponse对象，获取其content
            if hasattr(response, 'content'):
                response = response.content
                
            json_data = None
            if extract_json:
                if hasattr(self.unified_client, 'extract_json_from_text'):
                    json_data = self.unified_client.extract_json_from_text(response)
                else:
                    json_data = self.unified_client.extract_json(response)
            
            return response, json_data
            
        except Exception as e:
            print(f"❌ {self.agent_type} LLM响应失败: {e}")
            return f"LLM响应错误: {e}", None
    
    def get_status(self) -> Dict[str, Any]:
        """获取代理状态信息"""
        success_rate = (self.success_count / self.task_count * 100) if self.task_count > 0 else 0
        
        return {
            "agent_type": self.agent_type,
            "specialization": self.specialization,
            "is_active": self.is_active,
            "task_count": self.task_count,
            "success_count": self.success_count,
            "success_rate": f"{success_rate:.1f}%",
            "llm_available": True  # unified_client总是可用的
        }
    
    def update_knowledge(self, key: str, value: Any):
        """更新专家知识库"""
        self.knowledge_base[key] = value
        print(f"📚 {self.agent_type}知识库已更新: {key}")
    
    def get_knowledge(self, key: str, default: Any = None) -> Any:
        """获取专家知识"""
        return self.knowledge_base.get(key, default)
    
    def collaborate_with(self, other_agent: 'BaseAgent', 
                        shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        与其他专家代理协作
        
        Args:
            other_agent: 协作的专家代理
            shared_data: 共享数据
            
        Returns:
            协作结果
        """
        try:
            print(f"🤝 {self.agent_type} 正在与 {other_agent.agent_type} 协作")
            
            # 基础协作逻辑 - 子类可重写扩展
            collaboration_prompt = f"""
            As a {self.specialization} expert, I need to collaborate with a {other_agent.specialization} expert to analyze the following data:
            
            {json.dumps(shared_data, ensure_ascii=False, indent=2)}
            
            Please provide analysis from my professional perspective and consider synergistic effects with the {other_agent.specialization} field.
            
            Please return collaborative analysis results in JSON format:
            {{
                "my_perspective": "my professional viewpoint",
                "collaboration_insights": "collaborative insights",
                "synergy_opportunities": ["synergy1", "synergy2"],
                "confidence": 0.8
            }}
            """
            
            print(f"   📤 {self.agent_type} 发送协作请求...")
            response, json_data = self.get_llm_response(
                prompt=collaboration_prompt,
                extract_json=True
            )
            
            if json_data:
                print(f"   ✅ 协作响应成功")
                my_perspective = json_data.get('my_perspective', 'N/A')
                collab_insights = json_data.get('collaboration_insights', 'N/A')
                synergy_ops = json_data.get('synergy_opportunities', [])
                
                if len(my_perspective) > 100:
                    my_perspective = my_perspective[:100] + "..."
                if len(collab_insights) > 100:
                    collab_insights = collab_insights[:100] + "..."
                    
                print(f"   💭 专业观点: {my_perspective}")
                print(f"   💡 协作洞察: {collab_insights}")
                print(f"   🎯 协同机会: {len(synergy_ops)}")
                
                return {
                    "collaborator": self.agent_type,
                    "partner": other_agent.agent_type,
                    "result": json_data,
                    "timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
                }
            else:
                print(f"   ❌ 协作响应解析失败")
                return {
                    "collaborator": self.agent_type,
                    "partner": other_agent.agent_type,
                    "result": {"error": "协作分析失败"},
                    "timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
                }
                
        except Exception as e:
            print(f"❌ 协作失败: {e}")
            return {
                "collaborator": self.agent_type,
                "partner": other_agent.agent_type,
                "result": {"error": str(e)},
                "timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def __str__(self):
        """字符串表示"""
        return f"{self.agent_type}({self.specialization})"
    
    def __repr__(self):
        """详细字符串表示"""
        return f"BaseAgent(type='{self.agent_type}', spec='{self.specialization}', tasks={self.task_count})"


class AgentCapabilities:
    """代理能力定义类"""
    
    # 通用能力
    ANALYSIS = "analysis"           # 数据分析
    REASONING = "reasoning"         # 逻辑推理  
    SYNTHESIS = "synthesis"         # 信息综合
    EVALUATION = "evaluation"       # 结果评估
    
    # 专业能力
    NEUROSCIENCE = "neuroscience"       # 神经科学
    AI_TECHNOLOGY = "ai_technology"     # AI技术
    DATA_SCIENCE = "data_science"       # 数据科学
    DATA_PROCESSING = "data_processing" # 数据处理
    WRITING = "writing"                 # 学术写作
    PAPER_WRITING = "paper_writing"     # 论文写作
    EXPERIMENT = "experiment"           # 实验分析
    EXPERIMENT_DESIGN = "experiment_design"  # 实验设计
    
    @classmethod
    def get_all_capabilities(cls) -> List[str]:
        """获取所有可用能力"""
        return [
            cls.ANALYSIS, cls.REASONING, cls.SYNTHESIS, cls.EVALUATION,
            cls.NEUROSCIENCE, cls.AI_TECHNOLOGY, cls.DATA_SCIENCE, 
            cls.PAPER_WRITING, cls.EXPERIMENT
        ]


# 专家代理注册表
EXPERT_REGISTRY = {}

def register_expert(agent_class):
    """专家代理注册装饰器"""
    def decorator(cls):
        EXPERT_REGISTRY[agent_class] = cls
        return cls
    return decorator

def get_registered_experts() -> Dict[str, type]:
    """获取所有注册的专家代理"""
    return EXPERT_REGISTRY.copy()
