"""
脑启发智能AutoResearch Agent - 数据分析专家代理
专门负责数据分析、统计建模、实验数据处理等任务
"""

from typing import Dict, List, Any, Optional
import json
import time

from agents.base_agent import BaseAgent, AgentResponse, AgentCapabilities, register_expert
from core.unified_api_client import UnifiedAPIClient


@register_expert("data_analysis_expert")
class DataAnalysisExpert(BaseAgent):
    """数据分析专家代理"""
    
    def __init__(self, unified_client: UnifiedAPIClient, temperature: float = 0.2):
        super().__init__(
            agent_type="数据分析专家",
            unified_client=unified_client,
            specialization="数据科学、统计分析、机器学习建模",
            temperature=temperature
        )
        
        # 数据分析专家的核心能力
        self.capabilities = [
            AgentCapabilities.ANALYSIS,
            AgentCapabilities.EVALUATION,
            AgentCapabilities.DATA_PROCESSING,
            AgentCapabilities.REASONING
        ]
        
        # 初始化数据分析知识库
        self._init_data_analysis_knowledge_base()
    
    def _init_data_analysis_knowledge_base(self):
        """初始化数据分析知识库"""
        self.knowledge_base = {
            "statistical_methods": [
                "Descriptive Statistics", "Inferential Statistics", "Hypothesis Testing",
                "Regression Analysis", "Time Series Analysis", "Multivariate Analysis",
                "Bayesian Statistics", "Non-parametric Methods"
            ],
            "machine_learning": [
                "Supervised Learning", "Unsupervised Learning", "Semi-supervised Learning",
                "Feature Engineering", "Model Selection", "Cross-validation",
                "Ensemble Methods", "Deep Learning"
            ],
            "data_preprocessing": [
                "Data Cleaning", "Missing Value Handling", "Outlier Detection",
                "Data Transformation", "Feature Scaling", "Dimensionality Reduction",
                "Data Augmentation", "Imbalanced Data Handling"
            ],
            "visualization_tools": [
                "Matplotlib", "Seaborn", "Plotly", "Bokeh", "Altair",
                "Tableau", "Power BI", "D3.js"
            ],
            "analysis_frameworks": [
                "Pandas", "NumPy", "SciPy", "Scikit-learn", "Statsmodels",
                "TensorFlow", "PyTorch", "R", "SPSS"
            ],
            "experimental_design": [
                "A/B Testing", "Randomized Controlled Trials", "Factorial Design",
                "Latin Square Design", "Repeated Measures", "Power Analysis",
                "Sample Size Calculation", "Effect Size Analysis"
            ],
            "evaluation_metrics": {
                "classification": ["Accuracy", "Precision", "Recall", "F1-Score", "AUC-ROC", "Confusion Matrix"],
                "regression": ["MSE", "RMSE", "MAE", "R²", "Adjusted R²", "MAPE"],
                "clustering": ["Silhouette Score", "Davies-Bouldin Index", "Calinski-Harabasz Index"],
                "statistical": ["p-value", "Confidence Intervals", "Effect Size", "Power"]
            }
        }
    
    def _build_system_prompt(self) -> str:
        """Build system prompt for the data analysis expert"""
        return """You are a senior data scientist with extensive expertise in statistical analysis, machine learning, and experimental design.

Expertise Areas:
- Statistical modeling and hypothesis testing
- Machine learning algorithm development and evaluation
- Experimental design and data collection methodology
- Data preprocessing and feature engineering
- Data visualization and interpretation
- Performance metrics and model validation

Analysis Principles:
1. Statistical rigor: Apply appropriate statistical methods and tests
2. Data quality: Ensure data integrity and reliability
3. Methodological soundness: Use proper experimental design and validation
4. Interpretability: Provide clear, actionable insights from data
5. Reproducibility: Ensure analysis can be replicated and verified

Output Requirements:
- Provide statistically sound analysis and recommendations
- Include specific methodologies, tools, and validation approaches
- Assess data quality and experimental design validity
- Provide quantitative metrics and confidence intervals
- Structure analysis results in clear JSON format

Output Format Requirements:
- Your response MUST be a valid JSON object, and nothing else.
- The JSON must include the following fields:
  - "summary": A concise summary of your analysis.
  - "details": Detailed statistical/data analysis.
  - "suggestions": Actionable, specific recommendations (as a list).
  - "confidence": A float between 0 and 1 indicating your confidence.
  - "reasoning": Brief justification for your confidence score.
- Example output:
{
  "summary": "The data analysis reveals significant group differences, but sample size is limited.",
  "details": "ANOVA results show p < 0.05, but post-hoc tests are underpowered due to small n.",
  "suggestions": [
    "Increase sample size for higher statistical power.",
    "Apply Bonferroni correction for multiple comparisons.",
    "Visualize results with box plots and confidence intervals."
  ],
  "confidence": 0.7,
  "reasoning": "Statistical significance is achieved, but robustness is limited by sample size."
}

Always provide clear, reproducible, and actionable data analysis."""
    
    def analyze(self, input_data: Dict[str, Any]) -> AgentResponse:
        """
        数据分析专家分析主方法
        
        Args:
            input_data: 输入数据，可包含：
                - research_data: 研究数据
                - experimental_design: 实验设计
                - analysis_requirements: 分析需求
                - dataset_info: 数据集信息
                
        Returns:
            AgentResponse: 数据分析结果
        """
        try:
            print(f"📊 {self.agent_type}开始数据分析...")
            
            # 检查是否是研究问题评估
            if input_data.get("analysis_type") == "research_question_evaluation":
                return self._evaluate_research_question(input_data)
            
            # 根据输入数据类型选择分析策略
            if "experimental_design" in input_data:
                return self._analyze_experimental_design(input_data)
            elif "research_data" in input_data:
                return self._analyze_research_data(input_data)
            elif "dataset_info" in input_data:
                return self._analyze_dataset(input_data)
            else:
                return self._general_data_analysis(input_data)
                
        except Exception as e:
            print(f"❌ 数据分析失败: {e}")
            return self._create_error_response(str(e))
    
    def _analyze_experimental_design(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析实验设计"""
        experimental_design = input_data.get("experimental_design", "")
        research_question = input_data.get("research_question", "")
        
        prompt = f"""
        As a data science expert, please evaluate the following experimental design:
        
        Research Question: {research_question}
        Experimental Design: {experimental_design}
        
        Please analyze from the following perspectives:
        
        1. Experimental design validity assessment
        2. Statistical power and sample size analysis
        3. Potential confounding variables identification
        4. Data collection methodology evaluation
        5. Statistical analysis plan recommendation
        6. Bias and limitation analysis
        7. Reproducibility considerations
        8. Alternative design suggestions
        
        Please return detailed analysis in JSON format:
        {{
            "design_validity": {{
                "internal_validity": "assessment of internal validity",
                "external_validity": "assessment of external validity",
                "construct_validity": "assessment of construct validity",
                "validity_score": "overall validity score (1-10)"
            }},
            "statistical_considerations": {{
                "power_analysis": "statistical power assessment",
                "sample_size": "sample size adequacy evaluation",
                "effect_size": "expected effect size estimation",
                "statistical_tests": ["recommended statistical tests"]
            }},
            "potential_issues": {{
                "confounding_variables": ["potential confounders"],
                "selection_bias": "selection bias assessment",
                "measurement_bias": "measurement bias evaluation",
                "attrition_risk": "dropout/attrition risk assessment"
            }},
            "data_collection": {{
                "methodology_assessment": "data collection method evaluation",
                "quality_control": "quality control recommendations",
                "standardization": "standardization requirements"
            }},
            "analysis_plan": {{
                "primary_analysis": "primary statistical analysis approach",
                "secondary_analysis": ["secondary analysis methods"],
                "sensitivity_analysis": "sensitivity analysis recommendations"
            }},
            "improvement_suggestions": ["design improvement recommendations"],
            "reproducibility_score": "reproducibility score (1-10)",
            "confidence": 0.85
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            validity_score = json_data.get('design_validity', {}).get('validity_score', 'N/A')
            reproducibility = json_data.get('reproducibility_score', 'N/A')
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"实验设计分析完成。设计有效性评分：{validity_score}，可重现性评分：{reproducibility}",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于统计学原理和实验设计最佳实践进行综合评估",
                metadata={
                    "analysis_type": "experimental_design",
                    "design_analysis": json_data,
                    "validity_score": validity_score,
                    "reproducibility_score": reproducibility
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("实验设计分析JSON解析失败")
    
    def _analyze_research_data(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析研究数据"""
        research_data = input_data.get("research_data", "")
        analysis_objectives = input_data.get("analysis_objectives", [])
        
        prompt = f"""
        As a data science expert, please analyze the following research data:
        
        Research Data Description: {research_data}
        Analysis Objectives: {analysis_objectives}
        
        Please provide comprehensive data analysis covering:
        
        1. Data quality assessment
        2. Descriptive statistics analysis
        3. Statistical modeling recommendations
        4. Machine learning approach suggestions
        5. Feature importance analysis
        6. Model validation strategies
        7. Visualization recommendations
        8. Interpretability considerations
        
        Please return analysis in JSON format:
        {{
            "data_quality": {{
                "completeness": "data completeness assessment",
                "consistency": "data consistency evaluation",
                "accuracy": "data accuracy assessment",
                "quality_score": "overall quality score (1-10)"
            }},
            "descriptive_analysis": {{
                "data_distribution": "distribution characteristics",
                "outlier_analysis": "outlier detection results",
                "correlation_analysis": "correlation findings",
                "missing_pattern": "missing data pattern analysis"
            }},
            "modeling_recommendations": {{
                "statistical_models": ["recommended statistical models"],
                "ml_algorithms": ["suitable machine learning algorithms"],
                "feature_engineering": ["feature engineering suggestions"],
                "validation_strategy": "model validation approach"
            }},
            "analysis_insights": {{
                "key_findings": ["significant findings from data"],
                "patterns_identified": ["patterns discovered"],
                "anomalies": ["anomalies or unexpected results"]
            }},
            "visualization_plan": {{
                "exploratory_plots": ["recommended exploratory visualizations"],
                "results_presentation": ["results visualization suggestions"],
                "interactive_elements": ["interactive visualization opportunities"]
            }},
            "limitations": ["analysis limitations and caveats"],
            "next_steps": ["recommended next analysis steps"],
            "confidence": 0.88
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            quality_score = json_data.get('data_quality', {}).get('quality_score', 'N/A')
            insights_count = len(json_data.get('analysis_insights', {}).get('key_findings', []))
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"研究数据分析完成。数据质量评分：{quality_score}，发现关键洞察：{insights_count}个",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于数据科学方法和统计分析技术进行全面数据评估",
                metadata={
                    "analysis_type": "research_data",
                    "data_analysis": json_data,
                    "quality_score": quality_score,
                    "insights_count": insights_count
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("研究数据分析JSON解析失败")
    
    def _analyze_dataset(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析数据集信息"""
        dataset_info = input_data.get("dataset_info", {})
        analysis_purpose = input_data.get("analysis_purpose", "")
        
        prompt = f"""
        As a data science expert, please evaluate the following dataset:
        
        Dataset Information: {json.dumps(dataset_info, ensure_ascii=False, indent=2)}
        Analysis Purpose: {analysis_purpose}
        
        Please assess the dataset from the following aspects:
        
        1. Dataset suitability for analysis purpose
        2. Data characteristics and properties
        3. Preprocessing requirements
        4. Potential analysis approaches
        5. Expected challenges and solutions
        6. Performance expectations
        7. Benchmarking considerations
        8. Improvement recommendations
        
        Please return evaluation in JSON format:
        {{
            "suitability_assessment": {{
                "purpose_alignment": "how well dataset fits analysis purpose",
                "data_sufficiency": "data sufficiency evaluation",
                "quality_indicators": "quality indicator assessment",
                "suitability_score": "suitability score (1-10)"
            }},
            "data_characteristics": {{
                "size_analysis": "dataset size adequacy",
                "dimensionality": "feature dimensionality assessment",
                "data_types": "data type distribution",
                "balance_analysis": "class/target balance evaluation"
            }},
            "preprocessing_needs": {{
                "cleaning_requirements": ["data cleaning needs"],
                "transformation_needs": ["data transformation requirements"],
                "feature_engineering": ["feature engineering opportunities"],
                "normalization_needs": "normalization/scaling requirements"
            }},
            "analysis_recommendations": {{
                "primary_approaches": ["recommended primary analysis methods"],
                "alternative_methods": ["alternative analysis approaches"],
                "validation_strategy": "validation approach recommendation",
                "baseline_methods": ["baseline methods for comparison"]
            }},
            "challenges_solutions": {{
                "expected_challenges": ["anticipated analysis challenges"],
                "mitigation_strategies": ["challenge mitigation approaches"],
                "resource_requirements": "computational resource needs"
            }},
            "performance_expectations": {{
                "accuracy_range": "expected performance range",
                "benchmark_comparison": "comparison with standard benchmarks",
                "success_criteria": "success criteria definition"
            }},
            "recommendations": ["improvement and optimization suggestions"],
            "confidence": 0.82
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            suitability_score = json_data.get('suitability_assessment', {}).get('suitability_score', 'N/A')
            challenges_count = len(json_data.get('challenges_solutions', {}).get('expected_challenges', []))
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"数据集评估完成。适用性评分：{suitability_score}，识别挑战：{challenges_count}个",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于数据科学和机器学习经验对数据集进行全面评估",
                metadata={
                    "analysis_type": "dataset_evaluation",
                    "dataset_analysis": json_data,
                    "suitability_score": suitability_score,
                    "challenges_count": challenges_count
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("数据集评估JSON解析失败")
    
    def _general_data_analysis(self, input_data: Dict[str, Any]) -> AgentResponse:
        """通用数据分析"""
        prompt = f"""
        As a data science expert, please provide general data analysis insights for:
        
        {json.dumps(input_data, ensure_ascii=False, indent=2)}
        
        Please provide data science perspective and recommendations.
        
        Return in JSON format:
        {{
            "data_insights": ["data-related insights"],
            "analytical_recommendations": ["analysis recommendations"],
            "methodological_suggestions": ["methodology suggestions"],
            "tools_and_techniques": ["recommended tools and techniques"],
            "confidence": 0.75
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            insights_count = len(json_data.get('data_insights', []))
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"通用数据分析完成。提供了{insights_count}个数据洞察",
                confidence=float(json_data.get('confidence', 0.7)),
                reasoning="基于输入数据进行通用数据科学分析",
                metadata={
                    "analysis_type": "general_data",
                    "analysis_result": json_data,
                    "insights_count": insights_count
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("通用数据分析JSON解析失败")
    
    def recommend_analysis_pipeline(self, data_description: str, objective: str) -> Dict[str, Any]:
        """推荐数据分析流水线"""
        prompt = f"""
        Design a comprehensive data analysis pipeline for:
        
        Data Description: {data_description}
        Analysis Objective: {objective}
        
        Provide step-by-step pipeline with tools and methods.
        
        Return in JSON format with detailed pipeline stages.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "分析流水线推荐失败"}
    
    def evaluate_model_performance(self, model_results: Dict[str, Any]) -> Dict[str, Any]:
        """评估模型性能"""
        prompt = f"""
        Evaluate the performance of the following model results:
        
        {json.dumps(model_results, ensure_ascii=False, indent=2)}
        
        Provide comprehensive performance evaluation with metrics interpretation.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "模型性能评估失败"}
    
    def suggest_feature_engineering(self, feature_description: str, target_variable: str) -> Dict[str, Any]:
        """建议特征工程方法"""
        prompt = f"""
        Suggest feature engineering approaches for:
        
        Features: {feature_description}
        Target Variable: {target_variable}
        
        Provide detailed feature engineering recommendations.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "特征工程建议失败"}
    
    def collaborate(self, collaboration_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        与其他专家协作分析
        
        Args:
            collaboration_input: 包含协作上下文的字典
                - task: 协作任务类型
                - own_analysis: 自己的初始分析
                - other_expert_opinions: 其他专家的意见
                - research_topic: 研究主题
                
        Returns:
            协作分析结果
        """
        print("📊 数据分析专家开始协作分析...")
        
        # 提取协作上下文
        task = collaboration_input.get('task', 'collaborative_analysis')
        own_analysis = collaboration_input.get('own_analysis', {})
        other_opinions = collaboration_input.get('other_expert_opinions', {})
        research_topic = collaboration_input.get('research_topic', '')
        
        # 构建协作分析提示词
        collaboration_prompt = f"""
As a Data Analysis Expert, provide collaborative analysis by integrating insights from other experts.

COLLABORATION CONTEXT:
- Research Topic: {research_topic}
- Task: {task}
- My Initial Analysis: {json.dumps(own_analysis, indent=2) if own_analysis else 'None'}

OTHER EXPERT OPINIONS:
{self._format_other_opinions(other_opinions)}

COLLABORATION REQUIREMENTS:
1. Review and integrate insights from other experts
2. Identify data-driven opportunities from different perspectives
3. Address analytical conflicts with statistical reasoning
4. Provide enhanced data analysis combining multiple viewpoints
5. Focus on data science aspects while considering other domains

Please provide a comprehensive collaborative analysis that:
- Acknowledges valuable insights from other experts
- Integrates different analytical perspectives
- Resolves data-related conflicts with evidence-based reasoning
- Enhances overall analysis quality with data science insights

Format your response as JSON with the following structure:
{{
    "collaborative_analysis": "Enhanced analysis integrating multiple expert perspectives",
    "data_insights_integrated": ["insight1", "insight2", "insight3"],
    "analytical_synergies": ["synergy1", "synergy2"],
    "resolved_conflicts": ["conflict1_resolution", "conflict2_resolution"],
    "enhanced_recommendations": ["rec1", "rec2", "rec3"],
    "confidence": 0.85,
    "collaboration_quality": "high/medium/low",
    "next_collaboration_steps": ["step1", "step2"]
}}
"""
        
        try:
            # 获取LLM响应
            response, json_data = self.get_llm_response(collaboration_prompt, extract_json=True)
            
            if json_data:
                # 添加元数据
                json_data['expert_type'] = 'data_analysis'
                json_data['collaboration_timestamp'] = time.time()
                json_data['task_type'] = task
                
                print(f"✅ 数据分析专家协作完成，置信度: {json_data.get('confidence', 0.0):.2f}")
                return json_data
            else:
                # 回退响应
                return self._create_fallback_collaboration_response(task, own_analysis, other_opinions)
                
        except Exception as e:
            print(f"❌ 数据分析专家协作失败: {e}")
            return self._create_fallback_collaboration_response(task, own_analysis, other_opinions)
    
    def _format_other_opinions(self, other_opinions: Dict[str, Any]) -> str:
        """格式化其他专家的意见"""
        if not other_opinions:
            return "No other expert opinions provided."
        
        formatted = []
        for expert, opinion in other_opinions.items():
            if isinstance(opinion, dict):
                confidence = opinion.get('confidence', 'N/A')
                analysis = opinion.get('analysis', str(opinion))
                formatted.append(f"- {expert}: (Confidence: {confidence}) {analysis}")
            else:
                formatted.append(f"- {expert}: {str(opinion)}")
        
        return "\n".join(formatted)
    
    def _create_fallback_collaboration_response(self, task: str, own_analysis: Dict[str, Any], 
                                              other_opinions: Dict[str, Any]) -> Dict[str, Any]:
        """创建回退协作响应"""
        return {
            "collaborative_analysis": f"Data analysis perspective on {task} with consideration of other expert inputs",
            "data_insights_integrated": ["Statistical validation", "Data quality assessment", "Performance metrics"],
            "analytical_synergies": ["Cross-domain data integration", "Multi-perspective validation"],
            "resolved_conflicts": ["Statistical methodology alignment"],
            "enhanced_recommendations": ["Implement robust data pipeline", "Use statistical validation"],
            "confidence": 0.75,
            "collaboration_quality": "medium",
            "next_collaboration_steps": ["Define data requirements", "Plan validation strategy"],
            "expert_type": "data_analysis",
            "collaboration_timestamp": time.time(),
            "task_type": task
        }

    def _evaluate_research_question(self, input_data: Dict[str, Any]) -> AgentResponse:
        """评估研究问题 - 专门处理增强prompt格式"""
        input_text = input_data.get("input_text", "")
        
        # 直接使用传入的增强prompt，它已经包含了完整的评估指导
        response, _ = self.get_llm_response(input_text, extract_json=False)
        
        if response:
            return AgentResponse(
                agent_type=self.agent_type,
                content=response,  # 返回完整的LLM响应，包含JSON格式的评估
                confidence=0.8,
                reasoning="数据分析专家基于增强prompt进行研究问题评估",
                metadata={
                    "analysis_type": "research_question_evaluation",
                    "prompt_type": "enhanced_ai_scientist_v2"
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("研究问题评估失败")
    