@echo off
echo 开始移动文件到归档目录...

REM 移动快速测试文件
echo 移动快速测试文件...
move quick_*.py archived\old_tests\ 2>NUL
move *_diagnostic*.py archived\old_tests\ 2>NUL
move system_diagnosis.py archived\old_tests\ 2>NUL

REM 移动演示文件
echo 移动演示文件...
move demo_*.py archived\demo_files\ 2>NUL
move stage3_simulation.py archived\demo_files\ 2>NUL
move enhanced_reasoning_demo.py archived\demo_files\ 2>NUL

REM 移动重复/修复版本的测试文件
echo 移动重复测试文件...
move test_*_fixed.py archived\old_tests\ 2>NUL
move test_*_backup.py archived\old_tests\ 2>NUL
move test_stage4_complete.py archived\old_tests\ 2>NUL
move test_priority_one_complete_fixed.py archived\old_tests\ 2>NUL

REM 移动临时文档
echo 移动临时文档...
move STAGE1_CORE_ANALYSIS.md archived\temp_docs\ 2>NUL
move STAGE2_TESTS_ANALYSIS.md archived\temp_docs\ 2>NUL
move STAGE3_DOCS_ANALYSIS.md archived\temp_docs\ 2>NUL
move *_COMPARISON_*.md archived\temp_docs\ 2>NUL
move COMPREHENSIVE_TESTING_PLAN.md archived\temp_docs\ 2>NUL
move CODE_ANALYSIS_AND_CLEANUP_PLAN.md archived\temp_docs\ 2>NUL
move CODE_CLEANUP_SUMMARY.md archived\temp_docs\ 2>NUL
move HONEST_PROJECT_ASSESSMENT.md archived\temp_docs\ 2>NUL

REM 移动重复项目状态文档
echo 移动重复状态文档...
move PROJECT_PROGRESS_RECORD.md archived\temp_docs\ 2>NUL
move PROJECT_STATUS_FINAL.md archived\temp_docs\ 2>NUL
move PROGRESS_LOG.md archived\temp_docs\ 2>NUL
move FEATURE_COMPLETION_REPORT.md archived\temp_docs\ 2>NUL
move FEATURE_IMPLEMENTATION_STATUS.md archived\temp_docs\ 2>NUL
move PRIORITY_ONE_COMPLETION_REPORT.md archived\temp_docs\ 2>NUL
move PRIORITY_TWO_COMPLETION_REPORT.py archived\temp_docs\ 2>NUL

REM 移动计划文档
echo 移动计划文档...
move PROJECT_PLAN.md archived\temp_docs\ 2>NUL
move PAPER_WRITING_PHASE_PLAN.md archived\temp_docs\ 2>NUL
move PAPER_WRITING_SYSTEM_IMPLEMENTATION_PLAN.md archived\temp_docs\ 2>NUL
move FUTURE_DEVELOPMENT_PLAN.md archived\temp_docs\ 2>NUL

REM 移动重复测试文件
echo 移动更多测试文件...
move test_basic_api.py archived\old_tests\ 2>NUL
move test_basic_priority_two.py archived\old_tests\ 2>NUL
move test_citation_manager.py archived\old_tests\ 2>NUL
move test_advanced_system_integration.py archived\old_tests\ 2>NUL
move test_new_features.py archived\old_tests\ 2>NUL

echo 文件移动完成！
pause
