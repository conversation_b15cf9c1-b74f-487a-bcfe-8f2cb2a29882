"""
专门测试论文段落生成功能
确认修复后的内容生成正常
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置环境变量确保使用真实API
os.environ["DEEPSEEK_API_KEY"] = "***********************************"
os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
os.environ["MOCK_MODE"] = "false"
os.environ["ENABLE_MOCK_DATA"] = "false"

# 确保按正确顺序导入
from core.llm_client import LLMClient
from core.unified_api_client import get_unified_client
from paper_generation.brain_paper_writer import BrainPaperWriter
from agents.expert_agents.paper_writing_expert import PaperWritingExpert

def test_direct_section_generation():
    """测试论文段落生成"""
    print("\n" + "=" * 80)
    print("🧪 论文段落生成测试（修复后）")
    print("=" * 80)
    
    # 创建输出目录
    output_dir = "output/test_section_generation"
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建统一API客户端
    unified_client = get_unified_client()
    print("✅ 统一API客户端初始化成功")
    
    # 直接创建写作专家代理
    print("🔄 初始化写作专家代理...")
    paper_expert = PaperWritingExpert(unified_client=unified_client)
    print("✅ 写作专家代理初始化成功")
    
    # 创建论文写作器
    print("🔄 初始化论文写作器...")
    
    # 注意：这里我们需要使用LLMClient而不是UnifiedAPIClient作为BrainPaperWriter的客户端
    llm_client = LLMClient(
        model="deepseek-chat", 
        temperature=0.7,
        api_key=os.environ.get("DEEPSEEK_API_KEY"),
        provider="deepseek"
    )
    
    paper_writer = BrainPaperWriter(llm_client=llm_client)
    print("✅ 论文写作器初始化成功")
    
    # 测试数据
    research_topic = "Neural-Inspired Learning Algorithms for Efficient AI"
    context = {
        "research_topic": research_topic,
        "target_venue": "ICML"
    }
    
    # 测试结果
    results = {}
    
    # 1. 测试写作专家的段落生成方法
    print("\n📝 测试专家代理段落生成...")
    sections_to_test = ["abstract", "introduction"]
    
    for section in sections_to_test:
        print(f"  生成 {section} 段落...")
        
        # 创建专门的段落生成输入数据
        section_input = {
            "section_name": section,
            "research_topic": research_topic,
            "target_venue": "ICML",
            "analysis_type": f"{section}_section",
            "input_text": f"Write a detailed {section} for a paper on {research_topic}. Provide only the actual content."
        }
        
        try:
            # 调用专家生成
            start_time = datetime.now()
            response = paper_expert._generate_section_content(section_input)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 记录结果
            content = response.content
            content_length = len(content) if content else 0
            
            print(f"  ✅ 内容长度: {content_length} 字符")
            print(f"  ⏱️ 生成时间: {duration:.2f}秒")
            if content_length > 0:
                print(f"  📄 内容预览: {content[:100]}...\n")
            
            results[f"expert_{section}"] = {
                "length": content_length,
                "duration": duration,
                "content_preview": content[:200] if content else "",
                "success": content_length > 100
            }
        except Exception as e:
            print(f"  ❌ 生成失败: {e}")
            results[f"expert_{section}"] = {
                "length": 0,
                "duration": 0,
                "content_preview": "",
                "success": False,
                "error": str(e)
            }
    
    # 2. 测试论文写作器的段落生成方法
    print("\n📝 测试BrainPaperWriter段落生成...")
    
    for section in sections_to_test:
        print(f"  生成 {section} 段落...")
        
        try:
            # 调用论文写作器生成
            start_time = datetime.now()
            content = paper_writer.generate_single_section(section, context)
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            # 记录结果
            content_length = len(content) if content else 0
            
            print(f"  ✅ 内容长度: {content_length} 字符")
            print(f"  ⏱️ 生成时间: {duration:.2f}秒")
            if content_length > 0:
                print(f"  📄 内容预览: {content[:100]}...\n")
            
            results[f"writer_{section}"] = {
                "length": content_length,
                "duration": duration,
                "content_preview": content[:200] if content else "",
                "success": content_length > 100
            }
        except Exception as e:
            print(f"  ❌ 生成失败: {e}")
            results[f"writer_{section}"] = {
                "length": 0,
                "duration": 0,
                "content_preview": "",
                "success": False,
                "error": str(e)
            }
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = os.path.join(output_dir, f"section_generation_test_{timestamp}.json")
    
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            "timestamp": timestamp,
            "research_topic": research_topic,
            "results": results
        }, f, ensure_ascii=False, indent=2)
    
    # 输出总结
    print("\n" + "=" * 80)
    print("📊 测试结果摘要")
    
    success_count = sum(1 for r in results.values() if r["success"])
    total_count = len(results)
    
    print(f"✅ 成功生成: {success_count}/{total_count}")
    if total_count > 0:
        avg_length = sum(r['length'] for r in results.values())/total_count
        print(f"📏 平均内容长度: {avg_length:.1f} 字符")
    print(f"💾 详细结果已保存至: {results_file}")
    print("=" * 80)
    
    # 返回成功标志
    return success_count == total_count

if __name__ == "__main__":
    success = test_direct_section_generation()
    exit(0 if success else 1) 