"""
API状态快速检查工具
"""

import os
import sys

# 设置API密钥
API_KEY = "sk-1b1d72e2e10643029de548b655e1f93e"
os.environ["DEEPSEEK_API_KEY"] = API_KEY

def quick_api_test():
    """快速API测试"""
    print("🔍 快速API状态检查")
    print(f"🔑 API密钥: {API_KEY[:8]}...")
    
    try:
        # 添加项目路径
        project_root = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, project_root)
        
        from core.llm_client import LLMClient
        
        # 创建客户端
        client = LLMClient(model="deepseek-chat", api_key=API_KEY)
        
        print(f"✅ 客户端创建成功")
        print(f"   DeepSeek模式: {getattr(client, 'deepseek_mode', False)}")
        print(f"   AI Scientist模式: {getattr(client, 'ai_scientist_mode', False)}")
        
        # 简单测试
        response = client.get_response("测试消息：请回复'API工作正常'")
        
        if response:
            response_text = response[0] if isinstance(response, tuple) else response
            print(f"✅ API调用成功")
            print(f"   响应: {response_text[:50]}...")
            return True
        else:
            print(f"❌ API调用失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    quick_api_test()
