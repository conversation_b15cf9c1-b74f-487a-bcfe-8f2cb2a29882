"""
脑启发智能AutoResearch Agent - 实验设计专家代理
专门负责实验设计、验证方案、测试策略等任务
"""

from typing import Dict, List, Any, Optional
import json
import time

from agents.base_agent import BaseAgent, AgentResponse, AgentCapabilities, register_expert
from core.unified_api_client import UnifiedAPIClient


@register_expert("experiment_design_expert")
class ExperimentDesignExpert(BaseAgent):
    """实验设计专家代理"""
    
    def __init__(self, unified_client: UnifiedAPIClient, temperature: float = 0.2):
        super().__init__(
            agent_type="实验设计专家",
            unified_client=unified_client,
            specialization="实验设计、验证方案、测试策略",
            temperature=temperature
        )
        
        # 实验设计专家的核心能力
        self.capabilities = [
            AgentCapabilities.EXPERIMENT_DESIGN,
            AgentCapabilities.ANALYSIS,
            AgentCapabilities.EVALUATION,
            AgentCapabilities.REASONING
        ]
        
        # 初始化实验设计知识库
        self._init_experiment_knowledge_base()
    
    def _init_experiment_knowledge_base(self):
        """初始化实验设计知识库"""
        self.knowledge_base = {
            "experimental_designs": {
                "controlled_experiments": ["Randomized Controlled Trial", "Quasi-experimental", "Natural Experiment"],
                "factorial_designs": ["Full Factorial", "Fractional Factorial", "Latin Square"],
                "comparative_studies": ["A/B Testing", "Multi-arm Bandit", "Cross-over Design"],
                "observational_studies": ["Cohort Study", "Case-control Study", "Cross-sectional Study"]
            },
            "validation_strategies": {
                "internal_validity": ["Random Assignment", "Control Groups", "Blinding", "Confounding Control"],
                "external_validity": ["Sampling Strategy", "Generalizability", "Replication", "Meta-analysis"],
                "construct_validity": ["Measurement Validity", "Operational Definitions", "Content Validity"],
                "statistical_validity": ["Power Analysis", "Effect Size", "Significance Testing", "Multiple Comparisons"]
            },
            "experimental_controls": [
                "Positive Control", "Negative Control", "Placebo Control", "Historical Control",
                "Concurrent Control", "Active Control", "Vehicle Control"
            ],
            "bias_mitigation": [
                "Selection Bias", "Confirmation Bias", "Observer Bias", "Reporting Bias",
                "Attrition Bias", "Performance Bias", "Detection Bias"
            ],
            "statistical_methods": {
                "hypothesis_testing": ["t-test", "ANOVA", "Chi-square", "Mann-Whitney U"],
                "regression_analysis": ["Linear Regression", "Logistic Regression", "Multiple Regression"],
                "non_parametric": ["Wilcoxon", "Kruskal-Wallis", "Spearman Correlation"],
                "multivariate": ["MANOVA", "PCA", "Factor Analysis", "Cluster Analysis"]
            },
            "measurement_tools": {
                "quantitative": ["Surveys", "Tests", "Behavioral Measures", "Physiological Measures"],
                "qualitative": ["Interviews", "Observations", "Focus Groups", "Case Studies"],
                "mixed_methods": ["Sequential Explanatory", "Concurrent Triangulation", "Transformative"]
            },
            "ethical_considerations": [
                "Informed Consent", "Risk-Benefit Analysis", "Privacy Protection",
                "Vulnerable Populations", "Data Security", "Research Ethics"
            ]
        }
    
    def _build_system_prompt(self) -> str:
        """Build system prompt for the experimental design expert"""
        return """You are a distinguished experimental design expert with extensive experience in research methodology across multiple disciplines.

Expertise Areas:
- Experimental design and methodology
- Statistical analysis and hypothesis testing
- Research validation and verification
- Bias identification and mitigation
- Measurement and instrumentation
- Ethical considerations in research

Design Principles:
1. Scientific rigor: Ensure methodological soundness and validity
2. Bias minimization: Identify and control for potential biases
3. Statistical power: Design experiments with adequate statistical power
4. Practical feasibility: Balance ideal design with resource constraints
5. Ethical compliance: Adhere to research ethics and participant safety
6. Reproducibility: Design experiments that can be replicated

Validation Framework:
- Internal validity: Causal relationship establishment
- External validity: Generalizability assessment
- Construct validity: Measurement accuracy evaluation
- Statistical validity: Power and effect size analysis

Output Requirements:
- Provide rigorous experimental design analysis and recommendations
- Include specific experimental methods, controls, and validation strategies
- Assess feasibility, risks, and ethical considerations
- Provide quantitative metrics and expected outcomes
- Structure analysis results in clear JSON format

Output Format Requirements:
- Your response MUST be a valid JSON object, and nothing else.
- The JSON must include the following fields:
  - "summary": A concise summary of your analysis.
  - "details": Detailed experimental design analysis.
  - "suggestions": Actionable, specific recommendations (as a list).
  - "confidence": A float between 0 and 1 indicating your confidence.
  - "reasoning": Brief justification for your confidence score.
- Example output:
{
  "summary": "The proposed experiment is well-designed but lacks a proper control group.",
  "details": "Randomized controlled trial is appropriate, but blinding procedures are not specified.",
  "suggestions": [
    "Add a placebo control group.",
    "Specify blinding procedures.",
    "Increase sample size for higher power."
  ],
  "confidence": 0.8,
  "reasoning": "Design is robust, but missing controls reduce internal validity."
}

Always ensure scientific rigor and practical feasibility in your recommendations."""
    
    def analyze(self, input_data: Dict[str, Any]) -> AgentResponse:
        """
        实验设计专家分析主方法
        
        Args:
            input_data: 输入数据，可包含：
                - research_hypothesis: 研究假设
                - experimental_design: 实验设计
                - validation_requirements: 验证需求
                - study_constraints: 研究约束
                
        Returns:
            AgentResponse: 实验设计分析结果
        """
        try:
            print(f"🔬 {self.agent_type}开始实验设计分析...")
            
            # 检查是否是研究问题评估
            if input_data.get("analysis_type") == "research_question_evaluation":
                return self._evaluate_research_question(input_data)
            
            # 根据输入数据类型选择分析策略
            if "research_hypothesis" in input_data:
                return self._design_experiment(input_data)
            elif "experimental_design" in input_data:
                return self._evaluate_experimental_design(input_data)
            elif "validation_requirements" in input_data:
                return self._design_validation_strategy(input_data)
            else:
                return self._general_experiment_analysis(input_data)
                
        except Exception as e:
            print(f"❌ 实验设计分析失败: {e}")
            return self._create_error_response(str(e))
    
    def _design_experiment(self, input_data: Dict[str, Any]) -> AgentResponse:
        """设计实验方案"""
        research_hypothesis = input_data.get("research_hypothesis", "")
        research_objectives = input_data.get("research_objectives", [])
        constraints = input_data.get("constraints", {})
        
        prompt = f"""
        As an experimental design expert, please design a comprehensive experimental approach for:
        
        Research Hypothesis: {research_hypothesis}
        Research Objectives: {research_objectives}
        Constraints: {json.dumps(constraints, ensure_ascii=False, indent=2)}
        
        Please design a complete experimental framework covering:
        
        1. Experimental design selection and rationale
        2. Sample size and power analysis
        3. Variable identification and operationalization
        4. Control strategy and bias mitigation
        5. Data collection methodology
        6. Statistical analysis plan
        7. Validation and verification approach
        8. Ethical considerations and risk assessment
        
        Please return comprehensive design in JSON format:
        {{
            "experimental_design": {{
                "design_type": "selected experimental design type",
                "design_rationale": "rationale for design selection",
                "study_structure": "overall study structure description",
                "timeline": "estimated study timeline"
            }},
            "sample_design": {{
                "target_population": "target population definition",
                "sampling_strategy": "sampling method and rationale",
                "sample_size": "recommended sample size",
                "power_analysis": "statistical power analysis results",
                "inclusion_criteria": ["participant inclusion criteria"],
                "exclusion_criteria": ["participant exclusion criteria"]
            }},
            "variables": {{
                "independent_variables": ["independent variables and levels"],
                "dependent_variables": ["dependent variables and measures"],
                "control_variables": ["variables to be controlled"],
                "confounding_variables": ["potential confounding variables"]
            }},
            "methodology": {{
                "data_collection": "data collection procedures",
                "measurement_instruments": ["measurement tools and instruments"],
                "experimental_procedures": "step-by-step experimental procedures",
                "quality_control": "quality control measures"
            }},
            "controls_and_validation": {{
                "control_groups": ["control group specifications"],
                "randomization": "randomization strategy",
                "blinding": "blinding procedures if applicable",
                "bias_mitigation": ["bias mitigation strategies"]
            }},
            "statistical_analysis": {{
                "primary_analysis": "primary statistical analysis approach",
                "secondary_analysis": ["secondary analysis methods"],
                "hypothesis_testing": "hypothesis testing strategy",
                "significance_criteria": "significance levels and criteria"
            }},
            "validation_strategy": {{
                "internal_validity": "internal validity assurance measures",
                "external_validity": "external validity considerations",
                "construct_validity": "construct validity verification",
                "reliability_measures": "reliability assessment methods"
            }},
            "ethical_considerations": {{
                "ethics_approval": "ethics approval requirements",
                "informed_consent": "informed consent procedures",
                "risk_assessment": "risk assessment and mitigation",
                "data_protection": "data protection and privacy measures"
            }},
            "feasibility_assessment": {{
                "resource_requirements": "required resources and budget",
                "timeline_feasibility": "timeline feasibility assessment",
                "potential_challenges": ["anticipated challenges"],
                "mitigation_strategies": ["challenge mitigation approaches"]
            }},
            "success_criteria": ["experimental success criteria"],
            "limitations": ["study limitations and constraints"],
            "recommendations": ["implementation recommendations"],
            "confidence": 0.88
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            design_type = json_data.get('experimental_design', {}).get('design_type', 'N/A')
            sample_size = json_data.get('sample_design', {}).get('sample_size', 'N/A')
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"实验设计方案完成。设计类型：{design_type}，推荐样本量：{sample_size}",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于研究假设和约束条件设计系统性实验方案",
                metadata={
                    "analysis_type": "experiment_design",
                    "design_plan": json_data,
                    "design_type": design_type,
                    "sample_size": sample_size
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("实验设计方案JSON解析失败")
    
    def _evaluate_experimental_design(self, input_data: Dict[str, Any]) -> AgentResponse:
        """评估实验设计"""
        experimental_design = input_data.get("experimental_design", "")
        research_context = input_data.get("research_context", "")
        
        prompt = f"""
        As an experimental design expert, please evaluate the following experimental design:
        
        Research Context: {research_context}
        Experimental Design: {experimental_design}
        
        Please provide comprehensive evaluation covering:
        
        1. Design validity and appropriateness
        2. Methodological strengths and weaknesses
        3. Bias and confounding assessment
        4. Statistical power and sample size evaluation
        5. Control strategy effectiveness
        6. Measurement validity assessment
        7. Ethical and practical considerations
        8. Improvement recommendations
        
        Please return evaluation in JSON format:
        {{
            "design_evaluation": {{
                "appropriateness": "design appropriateness for research question",
                "methodological_soundness": "methodological soundness assessment",
                "validity_score": "overall validity score (1-10)",
                "design_strengths": ["key design strengths"],
                "design_weaknesses": ["identified design weaknesses"]
            }},
            "validity_assessment": {{
                "internal_validity": "internal validity evaluation",
                "external_validity": "external validity assessment",
                "construct_validity": "construct validity evaluation",
                "statistical_validity": "statistical validity assessment"
            }},
            "bias_analysis": {{
                "selection_bias": "selection bias assessment",
                "measurement_bias": "measurement bias evaluation",
                "confounding_control": "confounding variable control adequacy",
                "bias_mitigation": "bias mitigation effectiveness"
            }},
            "statistical_considerations": {{
                "power_adequacy": "statistical power adequacy",
                "sample_size_justification": "sample size justification assessment",
                "analysis_plan": "statistical analysis plan evaluation",
                "multiple_comparisons": "multiple comparisons handling"
            }},
            "control_strategy": {{
                "control_adequacy": "control group adequacy",
                "randomization_quality": "randomization strategy quality",
                "blinding_effectiveness": "blinding effectiveness assessment",
                "placebo_controls": "placebo control appropriateness"
            }},
            "measurement_evaluation": {{
                "instrument_validity": "measurement instrument validity",
                "reliability_assessment": "measurement reliability evaluation",
                "outcome_measures": "outcome measure appropriateness",
                "data_quality": "data quality assurance measures"
            }},
            "practical_considerations": {{
                "feasibility": "practical feasibility assessment",
                "resource_efficiency": "resource efficiency evaluation",
                "timeline_realism": "timeline realism assessment",
                "implementation_challenges": ["implementation challenges"]
            }},
            "ethical_assessment": {{
                "ethics_compliance": "ethics compliance evaluation",
                "participant_safety": "participant safety considerations",
                "informed_consent": "informed consent adequacy",
                "risk_benefit": "risk-benefit ratio assessment"
            }},
            "improvement_recommendations": {{
                "design_modifications": ["design modification suggestions"],
                "methodological_improvements": ["methodological improvements"],
                "control_enhancements": ["control strategy enhancements"],
                "analysis_improvements": ["analysis plan improvements"]
            }},
            "overall_assessment": "overall design quality assessment",
            "recommendation": "recommendation for proceeding with design",
            "confidence": 0.86
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            validity_score = json_data.get('design_evaluation', {}).get('validity_score', 'N/A')
            recommendation = json_data.get('recommendation', 'N/A')
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"实验设计评估完成。有效性评分：{validity_score}，建议：{recommendation}",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于实验设计原理和最佳实践进行全面评估",
                metadata={
                    "analysis_type": "design_evaluation",
                    "evaluation_result": json_data,
                    "validity_score": validity_score,
                    "recommendation": recommendation
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("实验设计评估JSON解析失败")
    
    def _design_validation_strategy(self, input_data: Dict[str, Any]) -> AgentResponse:
        """设计验证策略"""
        validation_requirements = input_data.get("validation_requirements", "")
        system_description = input_data.get("system_description", "")
        
        prompt = f"""
        As an experimental design expert, please design a comprehensive validation strategy for:
        
        System Description: {system_description}
        Validation Requirements: {validation_requirements}
        
        Please design validation approach covering:
        
        1. Validation framework and methodology
        2. Performance metrics and benchmarks
        3. Test case design and coverage
        4. Baseline comparison strategies
        5. Cross-validation approaches
        6. Robustness and generalization testing
        7. Ablation study design
        8. Statistical significance testing
        
        Please return validation strategy in JSON format:
        {{
            "validation_framework": {{
                "validation_approach": "overall validation methodology",
                "validation_stages": ["validation stages and sequence"],
                "success_criteria": ["validation success criteria"],
                "failure_conditions": ["conditions indicating validation failure"]
            }},
            "performance_metrics": {{
                "primary_metrics": ["primary performance metrics"],
                "secondary_metrics": ["secondary performance metrics"],
                "evaluation_criteria": "evaluation criteria and thresholds",
                "metric_justification": "rationale for metric selection"
            }},
            "test_design": {{
                "test_scenarios": ["comprehensive test scenarios"],
                "test_data": "test data requirements and specifications",
                "edge_cases": ["edge cases and boundary conditions"],
                "stress_testing": "stress testing procedures"
            }},
            "baseline_comparison": {{
                "baseline_methods": ["baseline methods for comparison"],
                "comparison_criteria": "comparison criteria and fairness",
                "benchmark_datasets": ["appropriate benchmark datasets"],
                "competitive_analysis": "competitive analysis approach"
            }},
            "cross_validation": {{
                "cv_strategy": "cross-validation strategy",
                "fold_design": "fold design and stratification",
                "validation_sets": "validation set composition",
                "holdout_strategy": "holdout testing strategy"
            }},
            "robustness_testing": {{
                "noise_robustness": "noise robustness testing",
                "distribution_shift": "distribution shift testing",
                "adversarial_testing": "adversarial robustness evaluation",
                "generalization_tests": ["generalization testing approaches"]
            }},
            "ablation_studies": {{
                "component_ablation": "component ablation study design",
                "feature_ablation": "feature ablation testing",
                "hyperparameter_sensitivity": "hyperparameter sensitivity analysis",
                "architectural_variants": "architectural variant testing"
            }},
            "statistical_testing": {{
                "significance_tests": ["appropriate significance tests"],
                "multiple_comparisons": "multiple comparisons correction",
                "effect_size": "effect size calculation and interpretation",
                "confidence_intervals": "confidence interval reporting"
            }},
            "validation_timeline": "validation timeline and milestones",
            "resource_requirements": "resource requirements for validation",
            "risk_mitigation": ["validation risk mitigation strategies"],
            "quality_assurance": "validation quality assurance measures",
            "confidence": 0.87
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            validation_approach = json_data.get('validation_framework', {}).get('validation_approach', 'N/A')
            metrics_count = len(json_data.get('performance_metrics', {}).get('primary_metrics', []))
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"验证策略设计完成。验证方法：{validation_approach}，核心指标：{metrics_count}个",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于验证需求设计系统性验证方案",
                metadata={
                    "analysis_type": "validation_strategy",
                    "validation_plan": json_data,
                    "validation_approach": validation_approach,
                    "metrics_count": metrics_count
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("验证策略设计JSON解析失败")
    
    def _general_experiment_analysis(self, input_data: Dict[str, Any]) -> AgentResponse:
        """通用实验分析"""
        prompt = f"""
        As an experimental design expert, please provide experimental design insights for:
        
        {json.dumps(input_data, ensure_ascii=False, indent=2)}
        
        Please provide general experimental design recommendations and considerations.
        
        Return in JSON format:
        {{
            "experimental_insights": ["experiment-related insights"],
            "design_recommendations": ["design recommendations"],
            "methodological_considerations": ["methodological considerations"],
            "validation_suggestions": ["validation suggestions"],
            "confidence": 0.75
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            insights_count = len(json_data.get('experimental_insights', []))
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"通用实验分析完成。提供了{insights_count}个实验洞察",
                confidence=float(json_data.get('confidence', 0.7)),
                reasoning="基于输入数据进行通用实验设计分析",
                metadata={
                    "analysis_type": "general_experiment",
                    "analysis_result": json_data,
                    "insights_count": insights_count
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("通用实验分析JSON解析失败")
    
    def design_ablation_study(self, system_components: List[str], research_question: str) -> Dict[str, Any]:
        """设计消融研究"""
        prompt = f"""
        Design a comprehensive ablation study for:
        
        System Components: {system_components}
        Research Question: {research_question}
        
        Provide detailed ablation study design with systematic component removal strategy.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "消融研究设计失败"}
    
    def optimize_experimental_power(self, effect_size: float, alpha: float, desired_power: float) -> Dict[str, Any]:
        """优化实验统计功效"""
        prompt = f"""
        Optimize experimental design for statistical power:
        
        Expected Effect Size: {effect_size}
        Alpha Level: {alpha}
        Desired Power: {desired_power}
        
        Provide sample size calculations and power optimization recommendations.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "统计功效优化失败"}
    
    def assess_experimental_ethics(self, study_description: str, participant_info: str) -> Dict[str, Any]:
        """评估实验伦理"""
        prompt = f"""
        Assess ethical considerations for the following study:
        
        Study Description: {study_description}
        Participant Information: {participant_info}
        
        Provide comprehensive ethical assessment and recommendations.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "实验伦理评估失败"}
    
    def collaborate(self, collaboration_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        与其他专家协作分析
        
        Args:
            collaboration_input: 包含协作上下文的字典
                - task: 协作任务类型
                - own_analysis: 自己的初始分析
                - other_expert_opinions: 其他专家的意见
                - research_topic: 研究主题
                
        Returns:
            协作分析结果
        """
        print("🔬 实验设计专家开始协作分析...")
        
        # 提取协作上下文
        task = collaboration_input.get('task', 'collaborative_analysis')
        own_analysis = collaboration_input.get('own_analysis', {})
        other_opinions = collaboration_input.get('other_expert_opinions', {})
        research_topic = collaboration_input.get('research_topic', '')
        
        # 构建协作分析提示词
        collaboration_prompt = f"""
As an Experiment Design Expert, provide collaborative analysis by integrating insights from other experts.

COLLABORATION CONTEXT:
- Research Topic: {research_topic}
- Task: {task}
- My Initial Analysis: {json.dumps(own_analysis, indent=2) if own_analysis else 'None'}

OTHER EXPERT OPINIONS:
{self._format_other_opinions(other_opinions)}

COLLABORATION REQUIREMENTS:
1. Review and integrate insights from other experts
2. Identify experimental opportunities from different perspectives
3. Address conflicts with experimental design principles
4. Provide enhanced analysis combining multiple viewpoints
5. Focus on experimental design aspects while considering other domains

Please provide a comprehensive collaborative analysis that:
- Acknowledges valuable insights from other experts
- Integrates different perspectives with experimental design knowledge
- Resolves conflicts with evidence-based experimental reasoning
- Enhances overall analysis quality with experimental design insights

Format your response as JSON with the following structure:
{{
    "collaborative_analysis": "Enhanced analysis integrating multiple expert perspectives",
    "experimental_insights_integrated": ["insight1", "insight2", "insight3"],
    "methodological_synergies": ["synergy1", "synergy2"],
    "resolved_conflicts": ["conflict1_resolution", "conflict2_resolution"],
    "enhanced_recommendations": ["rec1", "rec2", "rec3"],
    "confidence": 0.85,
    "collaboration_quality": "high/medium/low",
    "next_collaboration_steps": ["step1", "step2"]
}}
"""
        
        try:
            # 获取LLM响应
            response, json_data = self.get_llm_response(collaboration_prompt, extract_json=True)
            
            if json_data:
                # 添加元数据
                json_data['expert_type'] = 'experiment_design'
                json_data['collaboration_timestamp'] = time.time()
                json_data['task_type'] = task
                
                print(f"✅ 实验设计专家协作完成，置信度: {json_data.get('confidence', 0.0):.2f}")
                return json_data
            else:
                # 回退响应
                return self._create_fallback_collaboration_response(task, own_analysis, other_opinions)
                
        except Exception as e:
            print(f"❌ 实验设计专家协作失败: {e}")
            return self._create_fallback_collaboration_response(task, own_analysis, other_opinions)
    
    def _format_other_opinions(self, other_opinions: Dict[str, Any]) -> str:
        """格式化其他专家的意见"""
        if not other_opinions:
            return "No other expert opinions provided."
        
        formatted = []
        for expert, opinion in other_opinions.items():
            if isinstance(opinion, dict):
                confidence = opinion.get('confidence', 'N/A')
                analysis = opinion.get('analysis', str(opinion))
                formatted.append(f"- {expert}: (Confidence: {confidence}) {analysis}")
            else:
                formatted.append(f"- {expert}: {str(opinion)}")
        
        return "\n".join(formatted)
    
    def _create_fallback_collaboration_response(self, task: str, own_analysis: Dict[str, Any], 
                                              other_opinions: Dict[str, Any]) -> Dict[str, Any]:
        """创建回退协作响应"""
        return {
            "collaborative_analysis": f"Experimental design perspective on {task} with consideration of other expert inputs",
            "experimental_insights_integrated": ["Experimental rigor", "Methodological validity", "Statistical power"],
            "methodological_synergies": ["Cross-domain experimental integration", "Multi-perspective validation"],
            "resolved_conflicts": ["Experimental methodology alignment"],
            "enhanced_recommendations": ["Implement rigorous experimental design", "Use validated methodologies"],
            "confidence": 0.75,
            "collaboration_quality": "medium",
            "next_collaboration_steps": ["Define experimental parameters", "Plan validation strategy"],
            "expert_type": "experiment_design",
            "collaboration_timestamp": time.time(),
            "task_type": task
        }
    def _evaluate_research_question(self, input_data: Dict[str, Any]) -> AgentResponse:
        """评估研究问题 - 专门处理增强prompt格式"""
        input_text = input_data.get("input_text", "")
        
        # 直接使用传入的增强prompt，它已经包含了完整的评估指导
        response, _ = self.get_llm_response(input_text, extract_json=False)
        
        if response:
            return AgentResponse(
                agent_type=self.agent_type,
                content=response,  # 返回完整的LLM响应，包含JSON格式的评估
                confidence=0.8,
                reasoning="实验设计专家基于增强prompt进行研究问题评估",
                metadata={
                    "analysis_type": "research_question_evaluation",
                    "prompt_type": "enhanced_ai_scientist_v2"
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("研究问题评估失败")
    