"""
脑启发智能领域预建论文索引
"""

from typing import List, Dict

BRAIN_INSPIRED_PAPERS_INDEX = {
    "attention_mechanisms": [
        {
            "title": "Attention Is All You Need",
            "authors": ["<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>", "<PERSON><PERSON>, <PERSON>."],
            "year": 2017,
            "venue": "NIPS",
            "abstract": "The dominant sequence transduction models are based on complex recurrent or convolutional neural networks...",
            "keywords": ["attention", "transformer", "neural networks"],
            "citation_count": 50000
        }
    ],
    "spiking_neural_networks": [
        {
            "title": "Surrogate Gradient Learning in Spiking Neural Networks",
            "authors": ["<PERSON><PERSON>, F.", "Ganguli, S."],
            "year": 2018,
            "venue": "IEEE Signal Processing Magazine",
            "abstract": "Spiking neural networks (SNNs) are considered the third generation of neural networks...",
            "keywords": ["spiking", "neuromorphic", "gradient learning"],
            "citation_count": 400
        }
    ],
    "neuromorphic_computing": [
        {
            "title": "Loihi: A Neuromorphic Manycore Processor with On-Chip Learning",
            "authors": ["<PERSON>, <PERSON>", "Srinivasa, N."],
            "year": 2018,
            "venue": "IEEE Micro",
            "abstract": "This paper presents Loihi, Intel's 5th generation neuromorphic research processor...",
            "keywords": ["neuromorphic", "Loihi", "on-chip learning"],
            "citation_count": 800
        }
    ]
}

def search_prebuilt_index(query: str, max_results: int = 10) -> List[Dict]:
    """在预建索引中搜索"""
    results = []
    query_lower = query.lower()
    
    for category, papers in BRAIN_INSPIRED_PAPERS_INDEX.items():
        for paper in papers:
            # 简单的关键词匹配
            if any(keyword in query_lower for keyword in paper["keywords"]):
                results.append(paper)
            elif any(word in paper["title"].lower() for word in query_lower.split()):
                results.append(paper)
    
    # 按引用数排序
    results.sort(key=lambda x: x["citation_count"], reverse=True)
    
    return results[:max_results]
