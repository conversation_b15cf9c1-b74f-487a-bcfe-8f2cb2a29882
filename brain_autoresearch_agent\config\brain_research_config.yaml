# 脑启发智能AutoResearch Agent配置文件

# 项目基本信息
project:
  name: "brain_autoresearch_agent"
  version: "0.1.0"
  domain: "brain_inspired_intelligence"

# LLM模型配置
llm:
  # 默认模型配置
  default_model: "deepseek-chat"  # 更新为DeepSeek作为默认模型
  default_temperature: 0.7
  max_tokens: 4096
  
  # DeepSeek模型配置
  deepseek:
    base_url: "https://api.deepseek.com"
    models:
      chat: "deepseek-chat"      # DeepSeek-V3-0324
      reasoner: "deepseek-reasoner"  # DeepSeek-R1-0528
  
  # 不同任务的模型配置（优化为英文prompt）
  tasks:
    paper_extraction:
      model: "deepseek-chat"
      temperature: 0.1  # Very low temperature for accurate extraction
      max_tokens: 4096
      language: "english"  # 优化DeepSeek性能
    
    expert_reasoning:
      model: "deepseek-reasoner"  # Use reasoning model for expert analysis
      temperature: 0.7
      max_tokens: 4096
      language: "english"  # 优化DeepSeek性能
    
    paper_writing:
      model: "deepseek-chat"
      temperature: 0.8  # Higher temperature for creative writing
      max_tokens: 4096
      language: "english"  # 优化DeepSeek性能
    
    review_evaluation:
      model: "deepseek-chat"
      temperature: 0.5
      max_tokens: 4096
      language: "english"  # 优化DeepSeek性能

# 专家代理配置
agents:
  ai_expert:
    name: "AI_Expert"
    expertise: ["machine_learning", "deep_learning", "neural_networks", "computer_vision", "nlp"]
    model: "deepseek-chat"
    temperature: 0.7
    
  neuro_expert:
    name: "Neuro_Expert"
    expertise: ["neuroscience", "brain_mechanisms", "biological_intelligence", "cognitive_science"]
    model: "deepseek-reasoner"  # 使用推理模型进行神经科学推理
    temperature: 0.7

# 推理流程配置
reasoning:
  max_discussion_rounds: 5
  consensus_threshold: 0.8
  debate_topics:
    - "research_value"
    - "experimental_feasibility"
    - "novelty_assessment"
    - "impact_evaluation"

# 论文工作流提取配置
paper_extraction:
  categories:
    - "datasets"
    - "network_architectures"
    - "platforms_tools"
    - "research_methods"
    - "evaluation_metrics"
    - "brain_inspiration"
    - "ai_techniques"
  
  max_paper_length: 10000  # 最大处理的论文字符数
  batch_size: 5  # 批量处理的论文数量

# 论文生成配置
paper_generation:
  template: "brain_intelligence_template"
  sections:
    - "abstract"
    - "introduction"
    - "related_work"
    - "methodology"
    - "experiments"
    - "results"
    - "discussion"
    - "conclusion"
  
  citation_style: "ACL"
  max_references: 50

# API配置
apis:
  semantic_scholar:
    base_url: "https://api.semanticscholar.org/graph/v1"
    rate_limit: 100  # 每分钟请求限制
    
  openai:
    rate_limit: 60  # 每分钟请求限制
    
  anthropic:
    rate_limit: 50  # 每分钟请求限制

# 测试配置
testing:
  sample_papers:
    - title: "Attention Is All You Need"
      content: "Sample abstract about attention mechanisms..."
    
    - title: "Deep Residual Learning for Image Recognition"
      content: "Sample abstract about ResNet..."

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/brain_autoresearch.log"

# 输出配置
output:
  results_dir: "results"
  papers_dir: "results/papers"
  workflows_dir: "results/workflows"
  visualizations_dir: "results/visualizations"
