"""
Stage 4 Complete Test: Paper Writing with DeepSeek API
Tests the complete paper generation workflow using simulated Stage 3 output
"""

import os
import sys
import json
import asyncio
from datetime import datetime
from pathlib import Path

# Add project path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LL<PERSON>lient
from paper_generation.unified_paper_workflow import (
    UnifiedPaperGenerationWorkflow, 
    UnifiedWorkflowConfig
)
from paper_generation.enhanced_brain_paper_writer import PaperGenerationConfig
from stage3_simulation import create_stage3_simulation, save_stage3_simulation


def setup_deepseek_for_stage4():
    """Setup DeepSeek environment specifically for Stage 4 testing"""
    print("🔧 Setting up DeepSeek for Stage 4 testing...")
    
    # Configure API
    api_key = "sk-1b1d72e2e10643029de548b655e1f93e"
    os.environ["DEEPSEEK_API_KEY"] = api_key
    os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
    os.environ["MOCK_MODE"] = "false"
    os.environ["ENABLE_MOCK_DATA"] = "false"
    
    print(f"✅ DeepSeek configured for Stage 4")
    print(f"🎯 Focus: Paper writing, review, and revision")
    
    return api_key


async def test_stage4_complete_workflow():
    """Test complete Stage 4 workflow: paper writing + review + revision"""
    print("=" * 80)
    print("📝 Stage 4 Complete Test: Paper Writing with DeepSeek")
    print("=" * 80)
    
    # 1. Setup environment
    setup_deepseek_for_stage4()
    
    # 2. Load Stage 3 simulation data
    print(f"\\n📋 Loading Stage 3 simulation data...")
    stage3_data = create_stage3_simulation()
    stage3_file = save_stage3_simulation()
    
    print(f"✅ Stage 3 data loaded")
    print(f"🎯 Research Topic: {stage3_data['research_topic']}")
    print(f"📊 Research Value Score: {stage3_data['research_value_assessment']['overall_score']}/10")
    print(f"🧪 Experimental Framework: {len(stage3_data['experimental_design']['experimental_framework'])} phases")
    
    # 3. Create Stage 4 specific configuration
    print(f"\\n⚙️ Creating Stage 4 paper generation configuration...")
    
    paper_config = PaperGenerationConfig(
        target_venue="ICML",
        paper_type="research",
        max_review_iterations=3,  # Full review cycles for quality
        quality_threshold=8.0,   # High quality requirement
        enable_auto_revision=True,
        enable_multi_expert_review=True,
        latex_output=True,
        language="english",
        # Stage 4 specific settings
        enable_literature_integration=True,
        enable_experimental_validation=True,
        enable_visualization_generation=True
    )
    
    config = UnifiedWorkflowConfig(
        use_advanced_writer=True,
        paper_generation_config=paper_config,
        output_formats=["markdown", "latex", "json"],
        output_directory=f"output/stage4_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        enable_workflow_extraction=True,
        enable_integration_analysis=True,
        # Stage 4 specific workflow settings
        use_stage3_input=True,
        stage3_data_path=stage3_file
    )
    
    print(f"✅ Stage 4 configuration created")
    print(f"📊 Quality threshold: {paper_config.quality_threshold}")
    print(f"🔄 Max review iterations: {paper_config.max_review_iterations}")
    print(f"📁 Output directory: {config.output_directory}")
    
    # 4. Create DeepSeek LLM client with reasoning capability
    print(f"\\n🧠 Creating DeepSeek LLM client...")
    
    llm_client = LLMClient(
        provider="deepseek",
        model="deepseek-chat",  # Primary model for writing
        temperature=0.7,
        api_key=os.environ["DEEPSEEK_API_KEY"]
    )
    
    # Create reasoning client for complex analysis
    reasoning_client = LLMClient(
        provider="deepseek", 
        model="deepseek-reasoner",  # For complex reasoning tasks
        temperature=0.6,
        api_key=os.environ["DEEPSEEK_API_KEY"]
    )
    
    if llm_client.deepseek_mode and reasoning_client.deepseek_mode:
        print(f"✅ DeepSeek clients created successfully")
        print(f"✏️ Writing model: {llm_client.model}")
        print(f"🧠 Reasoning model: {reasoning_client.model}")
    else:
        print(f"❌ DeepSeek client creation failed")
        return False
    
    # 5. Test Stage 4 components individually
    print(f"\\n🧪 Testing Stage 4 components...")
    
    # Test literature integration
    print(f"📚 Testing literature integration...")
    try:
        lit_prompt = f"""
        Based on the research topic '{stage3_data['research_topic']}', provide a comprehensive literature review that:
        1. Summarizes key related work in neural plasticity and continual learning
        2. Identifies research gaps that this work addresses
        3. Positions the research within the current state of the art
        4. Suggests 5-7 key references for citation
        
        Focus on recent work (2020-2024) and maintain academic rigor.
        """
        lit_response = reasoning_client.generate_response(lit_prompt)
        print(f"✅ Literature integration: OK ({len(lit_response)} chars)")
    except Exception as e:
        print(f"❌ Literature integration failed: {e}")
        return False
    
    # Test experimental validation writing
    print(f"🔬 Testing experimental validation writing...")
    try:
        exp_prompt = f"""
        Write a detailed experimental methodology section for the research on '{stage3_data['research_topic']}'. Include:
        1. Experimental setup and datasets
        2. Baseline methods for comparison
        3. Evaluation metrics and statistical analysis
        4. Expected results and validation criteria
        
        Base the methodology on the experimental design: {json.dumps(stage3_data['experimental_design']['experimental_framework'], indent=2)}
        """
        exp_response = llm_client.generate_response(exp_prompt)
        print(f"✅ Experimental validation: OK ({len(exp_response)} chars)")
    except Exception as e:
        print(f"❌ Experimental validation failed: {e}")
        return False
    
    # Test multi-expert review simulation
    print(f"👥 Testing multi-expert review...")
    try:
        review_prompt = f"""
        As an expert reviewer for ICML, provide a detailed review of this research proposal on '{stage3_data['research_topic']}'. 
        Evaluate:
        1. Technical novelty and contribution (1-5 scale)
        2. Experimental design and validation (1-5 scale)  
        3. Biological plausibility and relevance (1-5 scale)
        4. Potential impact and significance (1-5 scale)
        
        Provide specific feedback and suggestions for improvement.
        Research value score: {stage3_data['research_value_assessment']['overall_score']}/10
        """
        review_response = reasoning_client.generate_response(review_prompt)
        print(f"✅ Multi-expert review: OK ({len(review_response)} chars)")
    except Exception as e:
        print(f"❌ Multi-expert review failed: {e}")
        return False
    
    # 6. Execute complete workflow
    print(f"\\n🚀 Executing complete Stage 4 workflow...")
    
    # Prepare enhanced research requirements with Stage 3 data
    enhanced_requirements = {
        **stage3_data['experimental_design'],
        "literature_context": stage3_data['literature_integration'],
        "visualization_plan": stage3_data['visualization_plan'],
        "implementation_methodology": stage3_data['implementation_methodology'],
        "innovation_highlights": stage3_data['innovation_highlights'],
        "research_value_assessment": stage3_data['research_value_assessment']
    }
    
    workflow = UnifiedPaperGenerationWorkflow(llm_client, config)
    
    print(f"🔄 Starting comprehensive paper generation...")
    print(f"⏱️ Estimated time: 10-15 minutes (including review cycles)")
    
    start_time = datetime.now()
    
    try:
        # Execute complete paper generation with Stage 3 integration
        result = await workflow.generate_complete_paper(
            research_topic=stage3_data['research_topic'],
            research_requirements=enhanced_requirements,
            reference_papers=stage3_data['literature_integration']['key_references']
        )
        
        generation_time = (datetime.now() - start_time).total_seconds()
        
        # 7. Analyze and display results
        print(f"\\n" + "=" * 60)
        print(f"📊 Stage 4 Complete Test Results")
        print(f"=" * 60)
        
        if result.success:
            print(f"✅ Complete paper generation successful!")
            print(f"📁 Output directory: {config.output_directory}")
            print(f"📄 Generated formats: {', '.join(config.output_formats)}")
            print(f"⏱️ Total generation time: {generation_time:.2f} seconds ({generation_time/60:.1f} minutes)")
            print(f"📊 Final paper quality score: {result.paper_generation.quality_metrics.overall_score:.2f}/10")
            
            # Analyze file outputs
            print(f"\\n📂 Generated files analysis:")
            total_size = 0
            file_count = 0
            for format_name, file_path in result.output_files.items():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    total_size += file_size
                    file_count += 1
                    print(f"  📄 {format_name}: {os.path.basename(file_path)} ({file_size:.1f} KB)")
                    
                    # Display file preview for key formats
                    if format_name == "markdown" and file_size > 5:  # Significant content
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                                print(f"    📝 Content preview: {len(content)} characters")
                        except:
                            pass
                else:
                    print(f"  ❌ {format_name}: File not generated")
            
            print(f"\\n💾 Summary: {file_count} files, {total_size:.1f} KB total")
            
            # Display paper structure analysis
            if hasattr(result.paper_generation, 'paper_content') and result.paper_generation.paper_content:
                paper_content = result.paper_generation.paper_content
                print(f"\\n📋 Paper structure analysis:")
                sections = [k for k in paper_content.keys() if k not in ['title', 'abstract', 'keywords']]
                print(f"  📖 Total sections: {len(sections)}")
                print(f"  📝 Abstract length: {len(paper_content.get('abstract', ''))} characters")
                
                # Check for Stage 3 integration
                abstract = paper_content.get('abstract', '').lower()
                integration_keywords = ['plasticity', 'adaptive', 'continual learning', 'energy efficient']
                found_keywords = [kw for kw in integration_keywords if kw in abstract]
                print(f"  🎯 Stage 3 integration indicators: {len(found_keywords)}/{len(integration_keywords)} keywords found")
                
                if len(found_keywords) >= 3:
                    print(f"  ✅ Strong Stage 3 integration detected")
                else:
                    print(f"  ⚠️ Limited Stage 3 integration")
            
            # Display review and revision analysis
            if hasattr(result.paper_generation, 'review_history') and result.paper_generation.review_history:
                print(f"\\n👥 Review and revision analysis:")
                print(f"  🔄 Review cycles completed: {len(result.paper_generation.review_history)}")
                for i, review in enumerate(result.paper_generation.review_history):
                    if isinstance(review, dict) and 'score' in review:
                        print(f"  📊 Review {i+1} score: {review['score']:.2f}/10")
            
            # Display quality improvement trajectory
            if hasattr(result.paper_generation, 'quality_trajectory') and result.paper_generation.quality_trajectory:
                print(f"\\n📈 Quality improvement trajectory:")
                for i, score in enumerate(result.paper_generation.quality_trajectory):
                    print(f"  Version {i+1}: {score:.2f}/10")
                
                if len(result.paper_generation.quality_trajectory) > 1:
                    improvement = result.paper_generation.quality_trajectory[-1] - result.paper_generation.quality_trajectory[0]
                    print(f"  📊 Total improvement: +{improvement:.2f} points")
            
            print(f"\\n🎉 Stage 4 complete test successful!")
            print(f"✅ All paper writing components functional")
            print(f"✅ Multi-expert review system operational")
            print(f"✅ Automatic revision system working")
            print(f"✅ Stage 3 integration successful")
            
            return True
            
        else:
            print(f"❌ Stage 4 test failed")
            error_msg = getattr(result, 'generation_summary', 'Unknown error')
            print(f"🔍 Error details: {error_msg}")
            print(f"⏱️ Execution time: {generation_time:.2f} seconds")
            return False
            
    except Exception as e:
        print(f"\\n💥 Stage 4 test exception:")
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
        generation_time = (datetime.now() - start_time).total_seconds()
        print(f"⏱️ Execution time: {generation_time:.2f} seconds")
        return False


async def main():
    """Main test execution"""
    print("🚀 Starting Stage 4 Complete Test...")
    
    # Verify dependencies
    try:
        import openai
        print("✅ OpenAI library available")
    except ImportError:
        print("❌ Missing OpenAI library, please run: pip install openai")
        return False
    
    # Run Stage 4 complete test
    success = await test_stage4_complete_workflow()
    
    if success:
        print(f"\\n🎊 Stage 4 complete test PASSED!")
        print(f"💡 Next steps:")
        print(f"  1. Review generated paper quality and structure")
        print(f"  2. Validate Stage 3 integration effectiveness") 
        print(f"  3. Prepare for full pipeline integration (Stages 1-4)")
        print(f"  4. Consider scaling to larger research topics")
    else:
        print(f"\\n❌ Stage 4 complete test FAILED")
        print(f"🔧 Debugging needed before pipeline integration")
    
    return success


if __name__ == "__main__":
    result = asyncio.run(main())
