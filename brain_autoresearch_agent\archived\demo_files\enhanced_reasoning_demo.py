"""
增强版推理工作流 - 支持真实API和完整专家团队
"""

import os
import sys
from typing import Dict, List, Any, Optional, Callable

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from reasoning.reasoning_workflow import ExperimentReasoningWorkflow
from agents.agent_manager import Agent<PERSON>anager
from core.llm_client import LLMClient


class EnhancedReasoningWorkflow(ExperimentReasoningWorkflow):
    """增强版推理工作流"""
    
    def __init__(self, api_key: str = None, use_real_api: bool = True):
        """
        初始化增强版工作流
        
        Args:
            api_key: API密钥
            use_real_api: 是否使用真实API
        """
        print("🚀 初始化增强版推理工作流...")
        
        # 配置API
        if api_key:
            os.environ['DEEPSEEK_API_KEY'] = api_key
        
        # 创建LLM客户端
        if use_real_api and os.getenv('DEEPSEEK_API_KEY'):
            self.llm_client = LLMClient(model="deepseek-chat")
            print("✅ 使用DeepSeek真实API")
        else:
            self.llm_client = LLMClient()
            print("⚠️ 使用模拟模式（建议配置API密钥获得更好效果）")
        
        # 初始化增强的专家团队
        self._initialize_enhanced_experts()
        
        # 初始化推理组件
        from reasoning.research_question_evaluator import ResearchQuestionEvaluator
        from reasoning.hypothesis_experiment_designer import HypothesisExperimentDesigner  
        from reasoning.implementation_planner import ImplementationPlanner
        from reasoning.visualization_advisor import VisualizationAdvisor
        
        self.question_evaluator = ResearchQuestionEvaluator(self.llm_client)
        self.experiment_designer = HypothesisExperimentDesigner(self.llm_client)
        self.implementation_planner = ImplementationPlanner(self.llm_client)
        self.visualization_advisor = VisualizationAdvisor(self.llm_client)
        
        # 替换专家团队
        self.question_evaluator.agent_manager = self.enhanced_agent_manager
        self.experiment_designer.agent_manager = self.enhanced_agent_manager
        self.implementation_planner.agent_manager = self.enhanced_agent_manager
        self.visualization_advisor.agent_manager = self.enhanced_agent_manager
        
        # 继承其他配置
        super().__init__(self.llm_client)
    
    def _initialize_enhanced_experts(self):
        """初始化完整的专家团队"""
        
        print("👥 初始化专家团队...")
        
        self.enhanced_agent_manager = AgentManager(self.llm_client)
        self.enhanced_agent_manager.agents.clear()  # 清空默认注册
        
        # 专家配置
        expert_configs = [
            {
                "id": "ai_technology",
                "class": "AITechnologyExpert", 
                "module": "agents.expert_agents.ai_technology_expert",
                "description": "AI技术专家"
            },
            {
                "id": "neuroscience", 
                "class": "NeuroscienceExpert",
                "module": "agents.expert_agents.neuroscience_expert",
                "description": "神经科学专家"
            },
            {
                "id": "data_analysis",
                "class": "DataAnalysisExpert", 
                "module": "agents.expert_agents.data_analysis_expert",
                "description": "数据分析专家"
            },
            {
                "id": "experiment_design",
                "class": "ExperimentDesignExpert",
                "module": "agents.expert_agents.experiment_design_expert", 
                "description": "实验设计专家"
            },
            {
                "id": "paper_writing",
                "class": "PaperWritingExpert",
                "module": "agents.expert_agents.paper_writing_expert",
                "description": "论文写作专家"  
            }
        ]
        
        successfully_registered = []
        failed_registrations = []
        
        for config in expert_configs:
            try:
                # 动态导入专家类
                import importlib
                module = importlib.import_module(config["module"])
                expert_class = getattr(module, config["class"])
                
                # 创建专家实例
                expert = expert_class(self.llm_client)
                
                # 注册专家
                self.enhanced_agent_manager.register_agent(config["id"], expert)
                successfully_registered.append(config["description"])
                
            except Exception as e:
                print(f"❌ {config['description']} 注册失败: {e}")
                failed_registrations.append(config["description"])
        
        print(f"✅ 成功注册 {len(successfully_registered)} 个专家")
        if failed_registrations:
            print(f"⚠️ 注册失败 {len(failed_registrations)} 个专家")
    
    def run_enhanced_reasoning_flow(self,
                                  research_question: str,
                                  hypothesis: List[str],
                                  background: Dict[str, Any] = None,
                                  workflow_context: Dict[str, Any] = None,
                                  target_venue: str = "NeurIPS",
                                  enable_deep_analysis: bool = True,
                                  progress_callback: Optional[Callable] = None):
        """
        运行增强版推理流程
        
        Args:
            research_question: 研究问题
            hypothesis: 假设列表
            background: 研究背景
            workflow_context: 工作流上下文
            target_venue: 目标期刊
            enable_deep_analysis: 是否启用深度分析
            progress_callback: 进度回调
        """
        
        print(f"\n🎯 增强版推理流程启动")
        print(f"🔬 研究问题: {research_question}")
        print(f"👥 专家团队: {len(self.enhanced_agent_manager.agents)} 个专家")
        print(f"🎯 目标期刊: {target_venue}")
        print("=" * 80)
        
        # 如果启用深度分析，增加分析轮次
        if enable_deep_analysis:
            original_rounds = self.workflow_config.get("evaluation_rounds", 3)
            self.workflow_config["evaluation_rounds"] = 5
            print(f"🔍 启用深度分析模式（{self.workflow_config['evaluation_rounds']}轮评估）")
        
        try:
            # 调用原始推理流程
            session = self.run_complete_reasoning_flow(
                research_question=research_question,
                hypothesis=hypothesis,
                background=background or {},
                workflow_context=workflow_context or {},
                target_venue=target_venue,
                progress_callback=progress_callback
            )
            
            # 增强分析
            if enable_deep_analysis:
                session = self._perform_deep_analysis(session)
            
            return session
            
        except Exception as e:
            print(f"❌ 增强推理流程失败: {e}")
            raise
        
        finally:
            # 恢复原始配置
            if enable_deep_analysis:
                self.workflow_config["evaluation_rounds"] = original_rounds
    
    def _perform_deep_analysis(self, session):
        """执行深度分析"""
        
        print(f"\n🔬 执行深度分析...")
        
        # 交叉验证实验设计
        if session.experiment_plan:
            print("  🔍 交叉验证实验设计...")
            session.experiment_plan.validation_notes = self._cross_validate_experiment(session.experiment_plan)
        
        # 可行性深度评估
        if session.implementation_plan:
            print("  ⚙️ 深度评估实现可行性...")
            session.implementation_plan.feasibility_analysis = self._deep_feasibility_analysis(session.implementation_plan)
        
        # 风险评估
        print("  ⚠️ 执行风险评估...")
        session.risk_assessment = self._assess_research_risks(session)
        
        return session
    
    def _cross_validate_experiment(self, experiment_plan):
        """交叉验证实验设计"""
        return {
            "statistical_power": "样本量需要进行功效分析",
            "confounding_factors": "需要控制数据集规模和网络容量的混淆",
            "reproducibility": "建议使用固定随机种子和详细的超参数记录"
        }
    
    def _deep_feasibility_analysis(self, implementation_plan):
        """深度可行性分析"""
        return {
            "computational_requirements": "需要GPU集群，估计训练时间2-4周",
            "technical_challenges": "生物可塑性机制的数学建模是主要挑战",
            "resource_availability": "需要神经科学和机器学习的跨领域合作"
        }
    
    def _assess_research_risks(self, session):
        """评估研究风险"""
        return {
            "high_risk": ["生物机制建模的准确性", "大规模实验的计算成本"],
            "medium_risk": ["基线方法的选择", "评估指标的全面性"],
            "low_risk": ["数据集的可获得性", "论文写作和发表"],
            "mitigation_strategies": [
                "与神经科学实验室建立合作",
                "分阶段验证生物机制",
                "准备备选的简化实验方案"
            ]
        }


def demonstrate_enhanced_workflow():
    """演示增强版工作流"""
    
    print("🎯 增强版推理工作流演示")
    print("=" * 80)
    
    # 检查API配置
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if api_key:
        print(f"✅ 检测到API密钥: {api_key[:8]}...")
        use_real_api = True
    else:
        print("⚠️ 未检测到API密钥，使用模拟模式")
        print("💡 提示：设置 DEEPSEEK_API_KEY 环境变量以启用真实API")
        use_real_api = False
    
    # 创建增强工作流
    enhanced_workflow = EnhancedReasoningWorkflow(use_real_api=use_real_api)
    
    # 真实的研究场景
    research_question = "基于海马体CA3-CA1回路的记忆巩固机制，如何设计解决灾难性遗忘的持续学习算法？"
    
    hypothesis = [
        "海马体的双重编码机制（快速CA3模式分离和慢速CA1模式完成）可以实现新旧知识的平衡编码",
        "基于睡眠期间的内存重放机制可以通过经验回放增强关键知识的保持",
        "齿状回的神经发生过程可以通过网络结构的动态调整适应新的学习任务"
    ]
    
    background = {
        "domain": "continual learning with neurobiological inspiration",
        "biological_basis": {
            "hippocampus": "海马体在记忆编码、巩固和检索中的核心作用",
            "ca3_ca1_circuit": "CA3区域的模式分离和CA1区域的模式完成机制",
            "memory_replay": "睡眠期间海马体-新皮质的内存重放机制",
            "neurogenesis": "齿状回颗粒细胞的持续神经发生"
        },
        "technical_motivation": "现有深度学习模型在持续学习场景下的灾难性遗忘问题",
        "related_work": [
            "Elastic Weight Consolidation (EWC)",
            "Progressive Neural Networks", 
            "Memory Replay Methods",
            "Neural Architecture Search for Continual Learning"
        ],
        "research_gap": "缺乏生物学启发的系统性持续学习架构设计"
    }
    
    workflow_context = {
        "computational_resources": "配备4张V100 GPU的服务器集群",
        "collaboration": "与神经科学实验室的合作项目",
        "timeline": "18个月的博士研究项目",
        "evaluation_datasets": ["Split CIFAR-100", "Permuted MNIST", "CelebA Attribute Transfer"]
    }
    
    def detailed_progress_callback(stage: str, description: str, progress: float):
        """详细进度追踪"""
        print(f"📊 [{progress*100:5.1f}%] {stage}: {description}")
    
    try:
        # 运行增强推理流程
        session = enhanced_workflow.run_enhanced_reasoning_flow(
            research_question=research_question,
            hypothesis=hypothesis,
            background=background,
            workflow_context=workflow_context,
            target_venue="NeurIPS",
            enable_deep_analysis=True,
            progress_callback=detailed_progress_callback
        )
        
        print(f"\n🎉 增强推理流程完成!")
        print(f"📋 会话ID: {session.session_id}")
        
        # 显示增强结果
        if hasattr(session, 'risk_assessment'):
            print(f"\n🛡️ 风险评估:")
            for risk_level, risks in session.risk_assessment.items():
                if risk_level != 'mitigation_strategies':
                    print(f"  {risk_level}: {len(risks) if isinstance(risks, list) else '1'} 项")
        
        # 生成增强交付物
        deliverables = enhanced_workflow.generate_final_deliverables(session)
        
        return session, deliverables
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None


if __name__ == "__main__":
    # 运行增强演示
    session, deliverables = demonstrate_enhanced_workflow()
    
    if session:
        print(f"\n📊 增强结果分析:")
        print(f"  🎯 研究价值评分: {session.research_problem.value_score:.2f}/10")
        print(f"  👥 专家意见数量: {len(session.research_problem.expert_opinions)}")
        print(f"  📁 交付物数量: {len(deliverables)}")
        
        # API使用状态
        if os.getenv('DEEPSEEK_API_KEY'):
            print(f"  🌐 API状态: 真实API调用")
        else:
            print(f"  🤖 API状态: 模拟模式")
            print(f"  💡 建议配置API密钥以获得真实专家见解")
    
    print(f"\n✨ 增强演示完成!")
