"""
假设到实验设计器 - 增强版
根据hypothesis生成实验方案，集成多专家协作和统一API客户端
"""

import os
import sys
import json
import time
import uuid
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.unified_api_client import UnifiedAPIClient
from reasoning.data_models import (
    ResearchProblem, ExperimentPlan, ExperimentVariable
)
from agents.agent_manager import AgentManager


class EnhancedHypothesisExperimentDesigner:
    """增强假设到实验设计器"""
    
    def __init__(self, unified_client: Optional[UnifiedAPIClient] = None):
        """
        初始化增强设计器
        
        Args:
            unified_client: 统一API客户端实例
        """
        # 初始化统一API客户端
        if unified_client is None:
            unified_client = UnifiedAPIClient()
        
        self.unified_client = unified_client
        self.agent_manager = AgentManager(unified_client)
        
        # 实验设计原则
        self.design_principles = {
            "validity": "确保实验能够有效验证假设",
            "reliability": "保证实验结果的可重现性",
            "controllability": "控制无关变量的影响",
            "measurability": "选择可量化的评估指标",
            "feasibility": "考虑实际实施的可行性"
        }
        
        # 脑启发智能领域的常用评估指标
        self.brain_inspired_metrics = {
            "performance": ["accuracy", "f1_score", "precision", "recall", "auc"],
            "efficiency": ["training_time", "inference_time", "memory_usage", "energy_consumption"],
            "adaptability": ["learning_speed", "transfer_learning", "catastrophic_forgetting"],
            "robustness": ["noise_tolerance", "adversarial_robustness", "generalization"],
            "bio_plausibility": ["spike_patterns", "synaptic_plasticity", "neural_dynamics"],
            "scalability": ["parameter_efficiency", "computational_complexity", "convergence_rate"]
        }
    
    def design_experiment(self, research_problem: ResearchProblem) -> ExperimentPlan:
        """
        根据研究问题和假设设计实验方案
        
        Args:
            research_problem: 已评估的研究问题
            
        Returns:
            详细的实验计划
        """
        print(f"\n🔬 开始设计实验方案")
        print(f"📋 研究问题: {research_problem.question}")
        print(f"🧪 假设数量: {len(research_problem.hypothesis)}")
        
        # 第一步：分析假设并选择实验类型
        experiment_type = self._determine_experiment_type(research_problem)
        print(f"📊 实验类型: {experiment_type}")
        
        # 第二步：设计实验方案
        experiment_design = self._design_experiment_methodology(research_problem, experiment_type)
        
        # 第三步：定义实验变量
        variables = self._define_experiment_variables(research_problem, experiment_design)
        
        # 第四步：选择评估指标
        metrics = self._select_evaluation_metrics(research_problem, experiment_design)
        
        # 第五步：论证实验合理性
        rationale = self._generate_experiment_rationale(research_problem, experiment_design)
        
        # 第六步：分析逻辑关系
        logical_connection = self._analyze_logical_connection(research_problem, experiment_design)
        
        # 创建实验计划
        experiment_plan = ExperimentPlan(
            research_question=research_problem.question,
            hypothesis=research_problem.hypothesis,
            experiment_type=experiment_type,
            design=experiment_design,
            methodology={},
            variables=variables,
            metrics=metrics,
            rationale=rationale,
            logical_connection=logical_connection
        )
        
        # 第七步：完善实验方法论
        experiment_plan.methodology = self._develop_methodology(experiment_plan)
        
        # 第八步：验证和优化
        experiment_plan = self._validate_and_optimize(experiment_plan)
        
        print(f"\n✅ 实验方案设计完成!")
        print(f"📊 实验类型: {experiment_plan.experiment_type}")
        print(f"🔬 变量数量: {len(experiment_plan.variables)}")
        print(f"📈 评估指标: {len(experiment_plan.metrics)}")
        
        return experiment_plan
    
    def _determine_experiment_type(self, research_problem: ResearchProblem) -> str:
        """确定最适合的实验类型"""
        
        analysis_prompt = f"""
        基于以下研究问题和假设，确定最适合的实验类型：
        
        研究问题: {research_problem.question}
        假设: {research_problem.hypothesis}
        背景: {json.dumps(research_problem.background, ensure_ascii=False)}
        
        可选的实验类型:
        {json.dumps(EXPERIMENT_TEMPLATES, ensure_ascii=False, indent=2)}
        
        请分析：
        1. 每种实验类型的适用性
        2. 推荐的实验类型及理由
        3. 可能需要组合多种实验类型的情况
        
        请以JSON格式输出：
        {{
            "recommended_type": "<推荐的主要实验类型>",
            "reasoning": "<选择理由>",
            "alternative_types": ["<备选类型1>", "<备选类型2>"],
            "combination_needed": <是否需要组合多种类型>,
            "experiment_sequence": ["<实验步骤1>", "<实验步骤2>"]
        }}
        """
        
        try:
            # 优先使用实验设计专家
            experiment_expert = self.agent_manager.get_agent('experiment_design')
            if experiment_expert:
                response = experiment_expert.analyze({
                    "input_text": analysis_prompt,
                    "analysis_type": "experiment_type_selection"
                })
                
                if response and response.get('analysis'):
                    analysis_text = response['analysis']
                    json_start = analysis_text.find('{')
                    json_end = analysis_text.rfind('}') + 1
                    if json_start != -1 and json_end > json_start:
                        json_text = analysis_text[json_start:json_end]
                        result = json.loads(json_text)
                        return result.get("recommended_type", "controlled_experiment")
            
            # 备用LLM方案
            response = self.llm_client.get_response(analysis_prompt)
            if response:
                response_text = response[0] if isinstance(response, tuple) else response
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    result = json.loads(json_text)
                    return result.get("recommended_type", "controlled_experiment")
                    
        except Exception as e:
            print(f"  ⚠️ 实验类型分析失败: {e}")
        
        # 默认返回对照实验
        return "controlled_experiment"
    
    def _design_experiment_methodology(self, research_problem: ResearchProblem, 
                                     experiment_type: str) -> Dict[str, Any]:
        """设计具体的实验方法论"""
        
        template = EXPERIMENT_TEMPLATES.get(experiment_type, EXPERIMENT_TEMPLATES["controlled_experiment"])
        
        design_prompt = f"""
        为脑启发智能研究设计具体的实验方法论：
        
        研究问题: {research_problem.question}
        假设: {research_problem.hypothesis}
        实验类型: {experiment_type} - {template['description']}
        
        需要设计的内容：
        1. 实验设置和配置
        2. 数据集选择和预处理
        3. 基线方法选择
        4. 实验组和对照组设计
        5. 参数设置和调优策略
        6. 重复实验和统计显著性
        
        特别关注脑启发智能的特点：
        - 生物可解释性
        - 学习效率
        - 适应性和可塑性
        - 能耗效率
        
        请以JSON格式输出详细设计：
        {{
            "experimental_setup": {{
                "environment": "<实验环境配置>",
                "hardware_requirements": "<硬件需求>",
                "software_dependencies": ["<依赖1>", "<依赖2>"]
            }},
            "dataset_design": {{
                "primary_datasets": ["<主要数据集1>", "<主要数据集2>"],
                "preprocessing": ["<预处理步骤1>", "<预处理步骤2>"],
                "data_split": "<数据划分策略>",
                "augmentation": ["<数据增强方法>"]
            }},
            "baseline_methods": {{
                "traditional_ml": ["<传统方法1>", "<传统方法2>"],
                "deep_learning": ["<深度学习方法1>", "<深度学习方法2>"],
                "brain_inspired": ["<脑启发方法1>", "<脑启发方法2>"]
            }},
            "experimental_groups": {{
                "control_group": "<对照组设计>",
                "experimental_groups": ["<实验组1>", "<实验组2>"],
                "ablation_components": ["<消融组件1>", "<消融组件2>"]
            }},
            "parameter_strategy": {{
                "hyperparameter_ranges": {{}},
                "optimization_method": "<参数优化方法>",
                "validation_strategy": "<验证策略>"
            }},
            "statistical_design": {{
                "sample_size": "<样本量设计>",
                "replication": "<重复次数>",
                "significance_testing": "<显著性检验方法>",
                "effect_size": "<效应量计算>"
            }}
        }}
        """
        
        try:
            response = self.llm_client.get_response(design_prompt)
            if response:
                response_text = response[0] if isinstance(response, tuple) else response
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    return json.loads(json_text)
        except Exception as e:
            print(f"  ⚠️ 实验方法论设计失败: {e}")
        
        # 返回默认设计
        return {
            "experimental_setup": {
                "environment": "Python 3.8+, PyTorch/TensorFlow",
                "hardware_requirements": "GPU推荐，CPU可选",
                "software_dependencies": ["numpy", "matplotlib", "sklearn"]
            },
            "dataset_design": {
                "primary_datasets": ["MNIST", "CIFAR-10"],
                "preprocessing": ["normalization", "data_augmentation"],
                "data_split": "70/15/15 train/val/test",
                "augmentation": ["rotation", "translation"]
            }
        }
    
    def _define_experiment_variables(self, research_problem: ResearchProblem, 
                                   experiment_design: Dict[str, Any]) -> List[ExperimentVariable]:
        """定义实验变量"""
        
        variables_prompt = f"""
        为以下实验定义详细的实验变量：
        
        研究问题: {research_problem.question}
        假设: {research_problem.hypothesis}
        实验设计: {json.dumps(experiment_design, ensure_ascii=False)}
        
        请识别和定义以下类型的变量：
        1. 自变量 (Independent Variables): 研究者主动操作的变量
        2. 因变量 (Dependent Variables): 观察和测量的结果变量
        3. 控制变量 (Control Variables): 需要保持恒定的变量
        4. 混淆变量 (Confounding Variables): 可能影响结果的额外变量
        
        请以JSON格式输出：
        {{
            "variables": [
                {{
                    "name": "<变量名>",
                    "type": "<independent/dependent/control/confounding>",
                    "description": "<变量描述>",
                    "values": ["<可能取值1>", "<可能取值2>"],
                    "measurement_method": "<测量方法>"
                }}
            ]
        }}
        """
        
        try:
            response = self.llm_client.get_response(variables_prompt)
            if response:
                response_text = response[0] if isinstance(response, tuple) else response
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    result = json.loads(json_text)
                    
                    variables = []
                    for var_data in result.get("variables", []):
                        variable = ExperimentVariable(
                            name=var_data.get("name", ""),
                            type=var_data.get("type", ""),
                            description=var_data.get("description", ""),
                            values=var_data.get("values", []),
                            measurement_method=var_data.get("measurement_method", "")
                        )
                        variables.append(variable)
                    return variables
        except Exception as e:
            print(f"  ⚠️ 变量定义失败: {e}")
        
        # 返回默认变量
        return [
            ExperimentVariable(
                name="模型架构",
                type="independent",
                description="不同的神经网络架构",
                values=["传统CNN", "脑启发架构"],
                measurement_method="架构参数对比"
            ),
            ExperimentVariable(
                name="准确率",
                type="dependent", 
                description="模型在测试集上的分类准确率",
                values=["0.0-1.0"],
                measurement_method="测试集评估"
            )
        ]
    
    def _select_evaluation_metrics(self, research_problem: ResearchProblem, 
                                 experiment_design: Dict[str, Any]) -> List[str]:
        """选择合适的评估指标"""
        
        # 基于问题类型和假设选择指标
        selected_metrics = []
        
        question_lower = research_problem.question.lower()
        hypothesis_text = " ".join(research_problem.hypothesis).lower()
        
        # 根据关键词推荐指标
        if any(word in question_lower for word in ["准确", "性能", "效果"]):
            selected_metrics.extend(self.brain_inspired_metrics["performance"])
        
        if any(word in question_lower for word in ["效率", "速度", "时间"]):
            selected_metrics.extend(self.brain_inspired_metrics["efficiency"])
            
        if any(word in question_lower for word in ["适应", "学习", "可塑"]):
            selected_metrics.extend(self.brain_inspired_metrics["adaptability"])
            
        if any(word in question_lower for word in ["鲁棒", "稳定", "泛化"]):
            selected_metrics.extend(self.brain_inspired_metrics["robustness"])
            
        if any(word in question_lower for word in ["生物", "神经", "脑"]):
            selected_metrics.extend(self.brain_inspired_metrics["bio_plausibility"])
            
        if any(word in question_lower for word in ["扩展", "规模", "复杂"]):
            selected_metrics.extend(self.brain_inspired_metrics["scalability"])
        
        # 去重并限制数量
        selected_metrics = list(set(selected_metrics))[:8]
        
        # 如果没有选中任何指标，使用默认指标
        if not selected_metrics:
            selected_metrics = ["accuracy", "training_time", "inference_time", "memory_usage"]
        
        return selected_metrics
    
    def _generate_experiment_rationale(self, research_problem: ResearchProblem, 
                                     experiment_design: Dict[str, Any]) -> str:
        """生成实验合理性论证"""
        
        rationale_prompt = f"""
        为以下实验设计生成详细的合理性论证：
        
        研究问题: {research_problem.question}
        假设: {research_problem.hypothesis}
        实验设计: {json.dumps(experiment_design, ensure_ascii=False)}
        
        请从以下角度论证实验的合理性：
        1. 实验设计的科学性和严谨性
        2. 变量控制的充分性
        3. 评估指标的适当性
        4. 方法选择的合理性
        5. 结果解释的有效性
        
        论证应该逻辑清晰、有说服力，并体现对脑启发智能领域的深入理解。
        """
        
        try:
            response = self.llm_client.get_response(rationale_prompt)
            if response:
                return response[0] if isinstance(response, tuple) else response
        except Exception as e:
            print(f"  ⚠️ 合理性论证生成失败: {e}")
        
        return f"该实验设计针对研究问题'{research_problem.question}'进行了系统性的方案设计，通过科学的变量控制和合适的评估指标，能够有效验证提出的假设。"
    
    def _analyze_logical_connection(self, research_problem: ResearchProblem, 
                                  experiment_design: Dict[str, Any]) -> str:
        """分析实验与研究问题的逻辑关系"""
        
        connection_prompt = f"""
        分析实验设计与研究问题之间的逻辑关系：
        
        研究问题: {research_problem.question}
        假设: {research_problem.hypothesis}
        实验设计概要: {json.dumps(experiment_design, ensure_ascii=False)}
        
        请详细分析：
        1. 实验如何直接回答研究问题
        2. 实验结果如何支持或反驳假设
        3. 实验设计的逻辑链条
        4. 可能的替代解释和控制方法
        5. 结果解释的局限性
        
        分析应该体现严谨的科学逻辑和批判性思维。
        """
        
        try:
            response = self.llm_client.get_response(connection_prompt)
            if response:
                return response[0] if isinstance(response, tuple) else response
        except Exception as e:
            print(f"  ⚠️ 逻辑关系分析失败: {e}")
        
        return f"实验通过系统性的对比分析，能够直接验证假设的正确性，并为研究问题提供实证支持。"
    
    def _develop_methodology(self, experiment_plan: ExperimentPlan) -> Dict[str, Any]:
        """完善实验方法论"""
        
        methodology = {
            "data_collection": {
                "protocols": "标准化数据收集协议",
                "quality_control": "数据质量检查和清洗",
                "documentation": "详细记录实验过程"
            },
            "analysis_methods": {
                "statistical_tests": ["t-test", "ANOVA", "effect_size"],
                "visualization": ["learning_curves", "performance_comparison", "error_analysis"],
                "interpretation": "结果解释框架"
            },
            "validation": {
                "cross_validation": "k-fold交叉验证",
                "independent_test": "独立测试集验证",
                "robustness_check": "鲁棒性检验"
            },
            "reproducibility": {
                "random_seeds": "固定随机种子",
                "environment": "实验环境记录",
                "code_availability": "代码开源计划"
            }
        }
        
        return methodology
    
    def _validate_and_optimize(self, experiment_plan: ExperimentPlan) -> ExperimentPlan:
        """验证和优化实验方案"""
        
        # 验证实验完整性
        if not experiment_plan.variables:
            print("  ⚠️ 警告：缺少实验变量定义")
        
        if not experiment_plan.metrics:
            print("  ⚠️ 警告：缺少评估指标")
        
        if not experiment_plan.rationale:
            print("  ⚠️ 警告：缺少合理性论证")
        
        # 估算资源需求
        experiment_plan.resources_needed = [
            "计算资源：GPU训练环境",
            "数据资源：标准数据集",
            "时间资源：预计2-4周", 
            "人力资源：1-2名研究人员"
        ]
        
        # 设置实验时长
        experiment_plan.duration = "2-4周"
        
        # 估算样本量
        experiment_plan.sample_size = 1000
        
        return experiment_plan
    
    def generate_experiment_report(self, experiment_plan: ExperimentPlan) -> str:
        """生成实验设计报告"""
        
        report = f"""
# 实验设计报告

## 研究问题
{experiment_plan.research_question}

## 研究假设
{chr(10).join(f"H{i+1}: {h}" for i, h in enumerate(experiment_plan.hypothesis))}

## 实验类型
**{experiment_plan.experiment_type}**

## 实验设计

### 实验变量
{chr(10).join(f"- **{var.name}** ({var.type}): {var.description}" for var in experiment_plan.variables)}

### 评估指标
{chr(10).join(f"- {metric}" for metric in experiment_plan.metrics)}

### 实验方法论
{json.dumps(experiment_plan.methodology, ensure_ascii=False, indent=2)}

## 合理性论证
{experiment_plan.rationale}

## 逻辑关系分析
{experiment_plan.logical_connection}

## 资源需求
- **预计时长**: {experiment_plan.duration}
- **样本量**: {experiment_plan.sample_size}
- **资源需求**: {chr(10).join(f"  - {r}" for r in experiment_plan.resources_needed)}
        """
        
        return report.strip()


# 测试函数
def test_hypothesis_experiment_designer():
    """测试假设到实验设计器"""
    
    print("🧪 测试假设到实验设计器")
    
    # 创建测试研究问题
    test_problem = ResearchProblem(
        question="如何设计一种基于脑神经可塑性的自适应神经网络架构？",
        hypothesis=[
            "脑神经可塑性机制可以指导神经网络结构的动态调整",
            "自适应架构能够提高学习效率和泛化能力",
            "生物启发的连接调整规则优于传统的梯度优化"
        ],
        background={
            "domain": "brain-inspired intelligence",
            "related_work": "神经可塑性、自适应网络、生物启发计算",
            "motivation": "现有神经网络结构固定，缺乏生物系统的自适应能力"
        },
        value_score=8.5
    )
    
    # 创建设计器并设计实验
    designer = HypothesisExperimentDesigner()
    experiment_plan = designer.design_experiment(test_problem)
    
    # 生成报告
    report = designer.generate_experiment_report(experiment_plan)
    print(f"\n📋 实验设计报告:")
    print(report)
    
    return experiment_plan


if __name__ == "__main__":
    test_hypothesis_experiment_designer()
