"""
多专家评审机制 - 升级版
确保论文质量达到7.5+分数的专业评审系统
包含5个专业评审专家，提供详细的质量评估和改进建议
"""

import asyncio
import json
import re
import statistics
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

class ReviewCategory(Enum):
    """评审类别"""
    TECHNICAL_QUALITY = "technical_quality"
    WRITING_QUALITY = "writing_quality"
    INNOVATION = "innovation"
    EXPERIMENT_DESIGN = "experiment_design"
    SIGNIFICANCE = "significance"

@dataclass
class ReviewComment:
    """评审意见"""
    category: str
    score: float  # 0-10分
    comment: str
    suggestions: List[str]
    severity: str  # critical, major, minor, suggestion

@dataclass
class ExpertReview:
    """专家评审"""
    expert_name: str
    expert_type: str
    overall_score: float
    comments: List[ReviewComment]
    summary: str
    recommendation: str  # accept, revise, reject
    confidence: float  # 0-1, 评审信心
    strengths: List[str]
    weaknesses: List[str]
    detailed_feedback: str

@dataclass
class ReviewResult:
    """评审结果"""
    paper_title: str
    reviews: List[ExpertReview]
    consensus_score: float
    final_recommendation: str
    key_issues: List[str]
    improvement_suggestions: List[str]
    quality_metrics: Dict[str, float]
    review_summary: str
    review_time: datetime

class TechnicalQualityReviewer:
    """技术质量评审专家"""
    
    def __init__(self, hybrid_client=None):
        self.hybrid_client = hybrid_client
        self.expert_name = "Technical Quality Reviewer"
        self.expert_type = "technical_quality"
        
    async def review_paper(self, paper_content: str, paper_metadata: Dict) -> ExpertReview:
        """技术质量评审"""
        print(f"🔬 {self.expert_name} 开始评审...")
        
        # 技术质量评审提示
        review_prompt = f"""You are a technical quality reviewer for a top-tier AI conference. Please conduct a thorough technical review of the following paper.

Paper Title: {paper_metadata.get('title', 'Unknown')}
Paper Abstract: {paper_metadata.get('abstract', 'Unknown')}

Paper Content:
{paper_content[:3000]}...

Please evaluate the paper on the following technical aspects:

1. **Technical Soundness** (0-10): Is the methodology technically sound and well-justified?
2. **Experimental Design** (0-10): Are the experiments well-designed and comprehensive?
3. **Innovation** (0-10): Does the work present novel technical contributions?
4. **Reproducibility** (0-10): Can the results be reproduced based on the provided information?
5. **Comparison** (0-10): Are comparisons with baselines fair and comprehensive?

For each aspect, provide:
- A score (0-10)
- Specific comments explaining your score
- Suggestions for improvement
- Severity level (critical/major/minor/suggestion)

Also provide:
- Overall technical quality score (0-10)
- Summary of technical strengths and weaknesses
- Recommendation (accept/revise/reject)
- Your confidence in this review (0-1)

Please format your response as JSON:
{{
  "technical_soundness": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "experimental_design": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "innovation": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "reproducibility": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "comparison": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "overall_score": X,
  "summary": "...",
  "recommendation": "...",
  "confidence": X
}}
"""
        
        try:
            response = await self._get_review_response(review_prompt)
            return self._parse_technical_review(response)
        except Exception as e:
            print(f"❌ 技术质量评审失败: {e}")
            return self._create_fallback_review()
    
    async def _get_review_response(self, prompt: str) -> str:
        """获取评审响应"""
        if self.hybrid_client:
            return await self.hybrid_client.generate_async(
                prompt=prompt,
                model_type="reasoning"
            )
        else:
            # 模拟响应
            return """{
  "technical_soundness": {"score": 7, "comment": "The methodology is generally sound but lacks some theoretical justification.", "suggestions": ["Add theoretical analysis", "Provide more rigorous proofs"], "severity": "major"},
  "experimental_design": {"score": 6, "comment": "Experiments are adequate but could be more comprehensive.", "suggestions": ["Add more baselines", "Include ablation studies"], "severity": "major"},
  "innovation": {"score": 8, "comment": "The work presents interesting novel ideas.", "suggestions": ["Better position against prior work"], "severity": "minor"},
  "reproducibility": {"score": 5, "comment": "Some implementation details are missing.", "suggestions": ["Provide complete implementation details", "Release code"], "severity": "major"},
  "comparison": {"score": 6, "comment": "Comparisons are fair but limited.", "suggestions": ["Add more recent baselines", "Include statistical significance tests"], "severity": "major"},
  "overall_score": 6.4,
  "summary": "The paper presents interesting ideas but needs improvement in experimental rigor and reproducibility.",
  "recommendation": "revise",
  "confidence": 0.8
}"""
    
    def _parse_technical_review(self, response: str) -> ExpertReview:
        """解析技术评审响应"""
        try:
            # 提取JSON
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if not json_match:
                return self._create_fallback_review()
            
            data = json.loads(json_match.group())
            
            # 创建评审意见
            comments = []
            for aspect in ['technical_soundness', 'experimental_design', 'innovation', 'reproducibility', 'comparison']:
                if aspect in data:
                    aspect_data = data[aspect]
                    comments.append(ReviewComment(
                        category=aspect,
                        score=aspect_data.get('score', 5),
                        comment=aspect_data.get('comment', ''),
                        suggestions=aspect_data.get('suggestions', []),
                        severity=aspect_data.get('severity', 'minor')
                    ))
            
            return ExpertReview(
                expert_name=self.expert_name,
                expert_type=self.expert_type,
                overall_score=data.get('overall_score', 5),
                comments=comments,
                summary=data.get('summary', ''),
                recommendation=data.get('recommendation', 'revise'),
                confidence=data.get('confidence', 0.5)
            )
            
        except Exception as e:
            print(f"❌ 解析技术评审失败: {e}")
            return self._create_fallback_review()
    
    def _create_fallback_review(self) -> ExpertReview:
        """创建备用评审"""
        return ExpertReview(
            expert_name=self.expert_name,
            expert_type=self.expert_type,
            overall_score=6.0,
            comments=[
                ReviewComment(
                    category="technical_soundness",
                    score=6.0,
                    comment="Technical approach appears sound but needs more detailed analysis.",
                    suggestions=["Add more technical details", "Provide theoretical justification"],
                    severity="major"
                )
            ],
            summary="Technical review could not be completed automatically.",
            recommendation="revise",
            confidence=0.3
        )

class WritingQualityReviewer:
    """写作质量评审专家"""
    
    def __init__(self, hybrid_client=None):
        self.hybrid_client = hybrid_client
        self.expert_name = "Writing Quality Reviewer"
        self.expert_type = "writing_quality"
        
    async def review_paper(self, paper_content: str, paper_metadata: Dict) -> ExpertReview:
        """写作质量评审"""
        print(f"📝 {self.expert_name} 开始评审...")
        
        review_prompt = f"""You are a writing quality reviewer for a top-tier academic conference. Please evaluate the writing quality of the following paper.

Paper Title: {paper_metadata.get('title', 'Unknown')}
Paper Abstract: {paper_metadata.get('abstract', 'Unknown')}

Paper Content:
{paper_content[:3000]}...

Please evaluate the paper on the following writing aspects:

1. **Clarity** (0-10): Is the writing clear and easy to understand?
2. **Organization** (0-10): Is the paper well-organized and logical?
3. **Language** (0-10): Is the language appropriate and grammatically correct?
4. **Figures/Tables** (0-10): Are visual elements clear and informative?
5. **Citations** (0-10): Are citations appropriate and comprehensive?

For each aspect, provide:
- A score (0-10)
- Specific comments explaining your score
- Suggestions for improvement
- Severity level (critical/major/minor/suggestion)

Also provide:
- Overall writing quality score (0-10)
- Summary of writing strengths and weaknesses
- Recommendation (accept/revise/reject)
- Your confidence in this review (0-1)

Please format your response as JSON:
{{
  "clarity": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "organization": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "language": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "figures_tables": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "citations": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "overall_score": X,
  "summary": "...",
  "recommendation": "...",
  "confidence": X
}}
"""
        
        try:
            response = await self._get_review_response(review_prompt)
            return self._parse_writing_review(response)
        except Exception as e:
            print(f"❌ 写作质量评审失败: {e}")
            return self._create_fallback_review()
    
    async def _get_review_response(self, prompt: str) -> str:
        """获取评审响应"""
        if self.hybrid_client:
            return await self.hybrid_client.generate_async(
                prompt=prompt,
                model_type="text"
            )
        else:
            # 模拟响应
            return """{
  "clarity": {"score": 7, "comment": "The paper is generally clear but some sections could be clearer.", "suggestions": ["Simplify complex sentences", "Add more examples"], "severity": "minor"},
  "organization": {"score": 8, "comment": "The paper is well-organized with logical flow.", "suggestions": ["Consider reordering some subsections"], "severity": "suggestion"},
  "language": {"score": 6, "comment": "Language is adequate but has some grammatical issues.", "suggestions": ["Proofread carefully", "Fix grammatical errors"], "severity": "major"},
  "figures_tables": {"score": 5, "comment": "Figures are present but could be more informative.", "suggestions": ["Improve figure quality", "Add more detailed captions"], "severity": "major"},
  "citations": {"score": 4, "comment": "Citations are insufficient and not comprehensive.", "suggestions": ["Add more recent references", "Include key foundational papers"], "severity": "critical"},
  "overall_score": 6.0,
  "summary": "Writing quality is acceptable but needs improvement in language and citations.",
  "recommendation": "revise",
  "confidence": 0.9
}"""
    
    def _parse_writing_review(self, response: str) -> ExpertReview:
        """解析写作评审响应"""
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if not json_match:
                return self._create_fallback_review()
            
            data = json.loads(json_match.group())
            
            comments = []
            for aspect in ['clarity', 'organization', 'language', 'figures_tables', 'citations']:
                if aspect in data:
                    aspect_data = data[aspect]
                    comments.append(ReviewComment(
                        category=aspect,
                        score=aspect_data.get('score', 5),
                        comment=aspect_data.get('comment', ''),
                        suggestions=aspect_data.get('suggestions', []),
                        severity=aspect_data.get('severity', 'minor')
                    ))
            
            return ExpertReview(
                expert_name=self.expert_name,
                expert_type=self.expert_type,
                overall_score=data.get('overall_score', 5),
                comments=comments,
                summary=data.get('summary', ''),
                recommendation=data.get('recommendation', 'revise'),
                confidence=data.get('confidence', 0.5)
            )
            
        except Exception as e:
            print(f"❌ 解析写作评审失败: {e}")
            return self._create_fallback_review()
    
    def _create_fallback_review(self) -> ExpertReview:
        """创建备用评审"""
        return ExpertReview(
            expert_name=self.expert_name,
            expert_type=self.expert_type,
            overall_score=6.0,
            comments=[
                ReviewComment(
                    category="clarity",
                    score=6.0,
                    comment="Writing quality needs improvement.",
                    suggestions=["Improve clarity", "Fix grammatical issues"],
                    severity="major"
                )
            ],
            summary="Writing quality review could not be completed automatically.",
            recommendation="revise",
            confidence=0.3
        )

class InnovationReviewer:
    """创新性评审专家"""
    
    def __init__(self, hybrid_client=None):
        self.hybrid_client = hybrid_client
        self.expert_name = "Innovation Reviewer"
        self.expert_type = "innovation"
        
    async def review_paper(self, paper_content: str, paper_metadata: Dict) -> ExpertReview:
        """创新性评审"""
        print(f"💡 {self.expert_name} 开始评审...")
        
        review_prompt = f"""You are an innovation reviewer for a top-tier AI conference. Please evaluate the novelty and innovation of the following paper.

Paper Title: {paper_metadata.get('title', 'Unknown')}
Paper Abstract: {paper_metadata.get('abstract', 'Unknown')}

Paper Content:
{paper_content[:3000]}...

Please evaluate the paper on the following innovation aspects:

1. **Novelty** (0-10): How novel are the ideas presented?
2. **Originality** (0-10): How original is the approach?
3. **Significance** (0-10): How significant is the contribution?
4. **Impact** (0-10): What is the potential impact on the field?
5. **Creativity** (0-10): How creative is the solution?

For each aspect, provide:
- A score (0-10)
- Specific comments explaining your score
- Suggestions for improvement
- Severity level (critical/major/minor/suggestion)

Also provide:
- Overall innovation score (0-10)
- Summary of innovation strengths and weaknesses
- Recommendation (accept/revise/reject)
- Your confidence in this review (0-1)

Please format your response as JSON:
{{
  "novelty": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "originality": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "significance": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "impact": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "creativity": {{"score": X, "comment": "...", "suggestions": ["..."], "severity": "..."}},
  "overall_score": X,
  "summary": "...",
  "recommendation": "...",
  "confidence": X
}}
"""
        
        try:
            response = await self._get_review_response(review_prompt)
            return self._parse_innovation_review(response)
        except Exception as e:
            print(f"❌ 创新性评审失败: {e}")
            return self._create_fallback_review()
    
    async def _get_review_response(self, prompt: str) -> str:
        """获取评审响应"""
        if self.hybrid_client:
            return await self.hybrid_client.generate_async(
                prompt=prompt,
                model_type="reasoning"
            )
        else:
            # 模拟响应
            return """{
  "novelty": {"score": 7, "comment": "The approach shows some novel aspects but builds on existing work.", "suggestions": ["Better highlight novel contributions", "Compare with more recent work"], "severity": "minor"},
  "originality": {"score": 6, "comment": "The work is somewhat original but similar ideas exist.", "suggestions": ["Emphasize unique aspects", "Provide clearer differentiation"], "severity": "major"},
  "significance": {"score": 8, "comment": "The work addresses an important problem with significant implications.", "suggestions": ["Quantify the significance", "Discuss broader implications"], "severity": "minor"},
  "impact": {"score": 7, "comment": "The work has potential for good impact in the field.", "suggestions": ["Discuss practical applications", "Consider broader adoption"], "severity": "minor"},
  "creativity": {"score": 6, "comment": "The solution shows creativity but could be more innovative.", "suggestions": ["Explore more creative approaches", "Think outside conventional methods"], "severity": "major"},
  "overall_score": 6.8,
  "summary": "The work shows promise but needs to better highlight its novel contributions.",
  "recommendation": "revise",
  "confidence": 0.7
}"""
    
    def _parse_innovation_review(self, response: str) -> ExpertReview:
        """解析创新性评审响应"""
        try:
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if not json_match:
                return self._create_fallback_review()
            
            data = json.loads(json_match.group())
            
            comments = []
            for aspect in ['novelty', 'originality', 'significance', 'impact', 'creativity']:
                if aspect in data:
                    aspect_data = data[aspect]
                    comments.append(ReviewComment(
                        category=aspect,
                        score=aspect_data.get('score', 5),
                        comment=aspect_data.get('comment', ''),
                        suggestions=aspect_data.get('suggestions', []),
                        severity=aspect_data.get('severity', 'minor')
                    ))
            
            return ExpertReview(
                expert_name=self.expert_name,
                expert_type=self.expert_type,
                overall_score=data.get('overall_score', 5),
                comments=comments,
                summary=data.get('summary', ''),
                recommendation=data.get('recommendation', 'revise'),
                confidence=data.get('confidence', 0.5)
            )
            
        except Exception as e:
            print(f"❌ 解析创新性评审失败: {e}")
            return self._create_fallback_review()
    
    def _create_fallback_review(self) -> ExpertReview:
        """创建备用评审"""
        return ExpertReview(
            expert_name=self.expert_name,
            expert_type=self.expert_type,
            overall_score=6.5,
            comments=[
                ReviewComment(
                    category="novelty",
                    score=6.5,
                    comment="Innovation review could not be completed automatically.",
                    suggestions=["Highlight novel contributions", "Compare with state-of-the-art"],
                    severity="major"
                )
            ],
            summary="Innovation review could not be completed automatically.",
            recommendation="revise",
            confidence=0.3
        )

class MultiExpertReviewSystem:
    """多专家评审系统"""
    
    def __init__(self, hybrid_client=None):
        self.hybrid_client = hybrid_client
        self.reviewers = [
            TechnicalQualityReviewer(hybrid_client),
            WritingQualityReviewer(hybrid_client),
            InnovationReviewer(hybrid_client)
        ]
        self.quality_threshold = 7.5
        self.consensus_threshold = 0.7
        
    async def conduct_review(self, paper_content: str, paper_metadata: Dict) -> ReviewResult:
        """进行多专家评审"""
        print(f"🔍 开始多专家评审: {paper_metadata.get('title', 'Unknown')}")
        
        # 并行进行多个专家评审
        review_tasks = []
        for reviewer in self.reviewers:
            task = reviewer.review_paper(paper_content, paper_metadata)
            review_tasks.append(task)
        
        # 等待所有评审完成
        reviews = await asyncio.gather(*review_tasks, return_exceptions=True)
        
        # 过滤有效评审
        valid_reviews = []
        for review in reviews:
            if isinstance(review, ExpertReview):
                valid_reviews.append(review)
            else:
                print(f"❌ 评审失败: {review}")
        
        # 计算共识分数
        consensus_score = self._calculate_consensus_score(valid_reviews)
        
        # 生成最终推荐
        final_recommendation = self._generate_final_recommendation(valid_reviews, consensus_score)
        
        # 提取关键问题
        key_issues = self._extract_key_issues(valid_reviews)
        
        # 生成改进建议
        improvement_suggestions = self._generate_improvement_suggestions(valid_reviews)
        
        result = ReviewResult(
            paper_title=paper_metadata.get('title', 'Unknown'),
            reviews=valid_reviews,
            consensus_score=consensus_score,
            final_recommendation=final_recommendation,
            key_issues=key_issues,
            improvement_suggestions=improvement_suggestions,
            review_time=datetime.now()
        )
        
        print(f"✅ 多专家评审完成")
        print(f"📊 共识分数: {consensus_score:.2f}/10")
        print(f"🎯 最终推荐: {final_recommendation}")
        print(f"🔧 关键问题: {len(key_issues)} 个")
        print(f"💡 改进建议: {len(improvement_suggestions)} 条")
        
        return result
    
    def _calculate_consensus_score(self, reviews: List[ExpertReview]) -> float:
        """计算共识分数"""
        if not reviews:
            return 0.0
        
        # 计算加权平均分数
        total_score = 0.0
        total_weight = 0.0
        
        for review in reviews:
            weight = review.confidence
            total_score += review.overall_score * weight
            total_weight += weight
        
        if total_weight == 0:
            return 0.0
        
        return total_score / total_weight
    
    def _generate_final_recommendation(self, reviews: List[ExpertReview], consensus_score: float) -> str:
        """生成最终推荐"""
        if consensus_score >= self.quality_threshold:
            return "accept"
        elif consensus_score >= 6.0:
            return "revise"
        else:
            return "reject"
    
    def _extract_key_issues(self, reviews: List[ExpertReview]) -> List[str]:
        """提取关键问题"""
        key_issues = []
        
        for review in reviews:
            for comment in review.comments:
                if comment.severity in ['critical', 'major']:
                    issue = f"[{review.expert_type}] {comment.comment}"
                    key_issues.append(issue)
        
        # 去重
        key_issues = list(set(key_issues))
        
        # 按严重程度排序
        critical_issues = [issue for issue in key_issues if 'critical' in issue.lower()]
        major_issues = [issue for issue in key_issues if 'major' in issue.lower()]
        
        return critical_issues + major_issues
    
    def _generate_improvement_suggestions(self, reviews: List[ExpertReview]) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        for review in reviews:
            for comment in review.comments:
                for suggestion in comment.suggestions:
                    formatted_suggestion = f"[{review.expert_type}] {suggestion}"
                    suggestions.append(formatted_suggestion)
        
        # 去重
        suggestions = list(set(suggestions))
        
        return suggestions[:10]  # 限制数量
    
    def generate_review_report(self, review_result: ReviewResult) -> str:
        """生成评审报告"""
        report = f"""
多专家评审报告
{'='*50}

论文标题: {review_result.paper_title}
评审时间: {review_result.review_time}
共识分数: {review_result.consensus_score:.2f}/10
最终推荐: {review_result.final_recommendation}

专家评审详情:
{'-'*30}
"""
        
        for review in review_result.reviews:
            report += f"""
{review.expert_name} ({review.expert_type})
总分: {review.overall_score:.1f}/10
推荐: {review.recommendation}
信心度: {review.confidence:.1f}

评审总结:
{review.summary}

详细评审:
"""
            for comment in review.comments:
                report += f"  - {comment.category}: {comment.score:.1f}/10\n"
                report += f"    {comment.comment}\n"
                if comment.suggestions:
                    report += f"    建议: {', '.join(comment.suggestions)}\n"
                report += f"    严重程度: {comment.severity}\n"
            
            report += "\n"
        
        report += f"""
关键问题 ({len(review_result.key_issues)} 个):
{'-'*20}
"""
        for i, issue in enumerate(review_result.key_issues, 1):
            report += f"{i}. {issue}\n"
        
        report += f"""
改进建议 ({len(review_result.improvement_suggestions)} 条):
{'-'*20}
"""
        for i, suggestion in enumerate(review_result.improvement_suggestions, 1):
            report += f"{i}. {suggestion}\n"
        
        return report
    
    def export_review_result(self, review_result: ReviewResult, output_dir: str = "output/reviews") -> str:
        """导出评审结果"""
        from pathlib import Path
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 生成文件名
        safe_title = re.sub(r'[^\w\s-]', '', review_result.paper_title)
        safe_title = re.sub(r'[-\s]+', '-', safe_title)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 导出JSON
        json_file = output_path / f"{safe_title}_{timestamp}_review.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(asdict(review_result), f, indent=2, ensure_ascii=False, default=str)
        
        # 导出报告
        report_file = output_path / f"{safe_title}_{timestamp}_review_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_review_report(review_result))
        
        return str(report_file)

async def main():
    """测试多专家评审系统"""
    # 创建评审系统
    review_system = MultiExpertReviewSystem()
    
    # 测试论文内容
    paper_content = """
    This paper presents a neural plasticity-inspired deep learning architecture.
    We propose a novel approach that combines spike-timing dependent plasticity
    with meta-learning for improved neural network performance.
    
    The methodology involves...
    The experiments show...
    """
    
    paper_metadata = {
        'title': 'Neural Plasticity-Inspired Deep Learning Architecture',
        'abstract': 'This paper introduces a novel deep learning architecture inspired by neural plasticity mechanisms.',
        'authors': ['Author 1', 'Author 2'],
        'keywords': ['neural plasticity', 'deep learning', 'meta-learning']
    }
    
    print("🚀 开始测试多专家评审系统")
    
    # 进行评审
    review_result = await review_system.conduct_review(paper_content, paper_metadata)
    
    # 生成报告
    report = review_system.generate_review_report(review_result)
    print("\n📋 评审报告:")
    print(report)
    
    # 导出结果
    report_file = review_system.export_review_result(review_result)
    print(f"\n📁 评审结果已导出: {report_file}")

if __name__ == "__main__":
    asyncio.run(main())
