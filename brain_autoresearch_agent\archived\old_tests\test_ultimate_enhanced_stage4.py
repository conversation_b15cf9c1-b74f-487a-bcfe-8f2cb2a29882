"""
Ultimate Enhanced Stage 4 Test with AI Scientist v2 Integration
Tests the complete enhanced system with all advanced features
"""

import os
import sys
import time
import asyncio
import json
from datetime import datetime
from typing import Dict, List, Any

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from paper_generation.ai_scientist_v2_integrated_writer import AIScientistIntegratedWriter
from paper_generation.enhanced_paper_writer import EnhancedPaperWriter
from core.hybrid_model_client import get_hybrid_client
from core.visual_review_system import VisualReviewSystem


class UltimateTestSuite:
    """终极测试套件"""
    
    def __init__(self):
        """初始化测试套件"""
        self.results = []
        self.start_time = time.time()
        
        print("🚀 Ultimate Enhanced Testing Suite v2.0")
        print("=" * 80)
        print("🎯 目标: 验证AI Scientist v2级别的完整论文生成系统")
        print("🔬 测试范围: 混合模型 + 视觉优化 + LaTeX验证 + 质量控制")
        print("=" * 80)
    
    async def run_complete_test_suite(self):
        """运行完整测试套件"""
        try:
            # 测试1: 基础系统验证
            await self._test_basic_system_verification()
            
            # 测试2: AI Scientist v2集成版本
            await self._test_ai_scientist_v2_integration()
            
            # 测试3: 增强版本对比
            await self._test_enhanced_version_comparison()
            
            # 测试4: 性能和质量基准测试
            await self._test_performance_benchmarks()
            
            # 生成最终报告
            await self._generate_final_report()
            
        except Exception as e:
            print(f"💥 测试套件执行失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def _test_basic_system_verification(self):
        """测试1: 基础系统验证"""
        print(f"\\n🧪 测试1: 基础系统验证")
        print("-" * 60)
        
        test_start = time.time()
        
        try:
            # 验证混合客户端
            print("  🔧 验证混合模型客户端...")
            hybrid_client = get_hybrid_client()
            model_status = hybrid_client.get_model_status()
            
            active_models = sum(1 for status in model_status.values() if status)
            print(f"  ✅ 活跃模型: {active_models}/4")
            
            # 验证视觉系统
            print("  👁️ 验证视觉评估系统...")
            visual_system = VisualReviewSystem()
            
            test_paper = {
                "title": "Test Paper for System Verification",
                "abstract": "This is a test abstract for system verification purposes.",
                "sections": {"intro": {"title": "Introduction", "content": "Test content"}},
                "references": [{"id": 1, "citation": "Test citation"}]
            }
            
            visual_score = visual_system.get_visual_quality_score(test_paper)
            print(f"  ✅ 视觉评估: {visual_score:.1f}/10")
            
            # 验证增强写作器
            print("  📝 验证增强论文写作器...")
            writer = EnhancedPaperWriter()
            print(f"  ✅ 增强写作器初始化成功")
            
            test_time = time.time() - test_start
            
            result = {
                "test_name": "Basic System Verification",
                "success": True,
                "active_models": active_models,
                "visual_score": visual_score,
                "test_time": test_time,
                "timestamp": datetime.now().isoformat()
            }
            
            self.results.append(result)
            print(f"  ✅ 基础系统验证完成 ({test_time:.2f}秒)")
            
        except Exception as e:
            print(f"  ❌ 基础系统验证失败: {e}")
            self.results.append({
                "test_name": "Basic System Verification",
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    async def _test_ai_scientist_v2_integration(self):
        """测试2: AI Scientist v2集成版本"""
        print(f"\\n🚀 测试2: AI Scientist v2集成版本")
        print("-" * 60)
        
        test_start = time.time()
        
        try:
            # 初始化AI Scientist v2集成写作器
            print("  🔬 初始化AI Scientist v2集成写作器...")
            ai_writer = AIScientistIntegratedWriter({
                "max_paper_length": 8,
                "num_cite_rounds": 5,  # 减少轮数以加快测试
                "quality_threshold": 7.0,
                "enable_latex_validation": False,  # 暂时禁用编译测试
                "enable_visual_review": True
            })
            
            print("  ✅ AI Scientist v2写作器初始化成功")
            
            # 生成测试论文
            print("  📝 生成AI Scientist v2风格论文...")
            research_topic = "Neuroplasticity-Inspired Meta-Learning for Few-Shot Adaptation"
            
            # 模拟实验数据
            experimental_data = {
                "baseline": {
                    "accuracy": 0.85,
                    "f1_score": 0.82,
                    "training_time": "2.5 hours"
                },
                "proposed": {
                    "accuracy": 0.92,
                    "f1_score": 0.90,
                    "training_time": "1.8 hours"
                },
                "ablation": {
                    "meta_component": 0.88,
                    "plasticity_module": 0.89,
                    "full_model": 0.92
                },
                "statistics": {
                    "p_value": 0.001,
                    "confidence_interval": [0.89, 0.95]
                }
            }
            
            result = await ai_writer.generate_ai_scientist_paper(
                research_topic=research_topic,
                experimental_data=experimental_data,
                research_context={
                    "domain": "meta-learning",
                    "application": "few-shot learning"
                }
            )
            
            test_time = time.time() - test_start
            
            # 分析结果
            paper_result = {
                "test_name": "AI Scientist v2 Integration",
                "success": True,
                "paper_title": result.paper_content.get("title", ""),
                "section_count": len(result.paper_content.get("sections", {})),
                "citation_count": len(result.citations),
                "overall_quality": result.quality_metrics.get("overall_quality", 0),
                "experimental_quality": result.quality_metrics.get("experimental_quality", 0),
                "citation_quality": result.quality_metrics.get("citation_quality", 0),
                "latex_generated": bool(result.latex_output),
                "pdf_generated": bool(result.pdf_path),
                "test_time": test_time,
                "timestamp": datetime.now().isoformat()
            }
            
            self.results.append(paper_result)
            
            print(f"\\n  📊 AI Scientist v2测试结果:")
            print(f"    📝 论文标题: {paper_result['paper_title']}")
            print(f"    📄 章节数: {paper_result['section_count']}")
            print(f"    📚 引用数: {paper_result['citation_count']}")
            print(f"    🎯 总体质量: {paper_result['overall_quality']:.1f}/10")
            print(f"    🧪 实验质量: {paper_result['experimental_quality']:.1f}/10")
            print(f"    📖 引用质量: {paper_result['citation_quality']:.1f}/10")
            print(f"    📄 LaTeX生成: {'成功' if paper_result['latex_generated'] else '失败'}")
            print(f"    ⏱️ 生成时间: {test_time:.2f}秒")
            print(f"  ✅ AI Scientist v2集成测试完成")
            
        except Exception as e:
            print(f"  ❌ AI Scientist v2集成测试失败: {e}")
            import traceback
            traceback.print_exc()
            
            self.results.append({
                "test_name": "AI Scientist v2 Integration",
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    async def _test_enhanced_version_comparison(self):
        """测试3: 增强版本对比"""
        print(f"\\n📊 测试3: 增强版本对比")
        print("-" * 60)
        
        test_start = time.time()
        
        try:
            print("  🔄 运行增强版本对比测试...")
            
            # 使用标准增强写作器
            print("    📝 测试标准增强写作器...")
            enhanced_writer = EnhancedPaperWriter()
            
            enhanced_result = enhanced_writer.generate_enhanced_paper(
                topic="Neuroplasticity-Inspired Meta-Learning for Few-Shot Adaptation",
                research_focus="Biologically-inspired adaptive learning mechanisms",
                methodology="Meta-learning with synaptic plasticity simulation"
            )
            
            test_time = time.time() - test_start
            
            comparison_result = {
                "test_name": "Enhanced Version Comparison", 
                "success": True,
                "enhanced_quality": enhanced_result['quality_metrics']['overall_quality'],
                "enhanced_sections": len(enhanced_result['sections']),
                "enhanced_references": len(enhanced_result['references']),
                "enhanced_time": enhanced_result['quality_metrics']['generation_time'],
                "comparison_time": test_time,
                "timestamp": datetime.now().isoformat()
            }
            
            self.results.append(comparison_result)
            
            print(f"\\n  📈 增强版本对比结果:")
            print(f"    🎯 增强版质量: {comparison_result['enhanced_quality']:.1f}/10")
            print(f"    📄 增强版章节: {comparison_result['enhanced_sections']}")
            print(f"    📚 增强版引用: {comparison_result['enhanced_references']}")
            print(f"    ⏱️ 增强版时间: {comparison_result['enhanced_time']:.2f}秒")
            print(f"  ✅ 增强版本对比完成")
            
        except Exception as e:
            print(f"  ❌ 增强版本对比失败: {e}")
            self.results.append({
                "test_name": "Enhanced Version Comparison",
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    async def _test_performance_benchmarks(self):
        """测试4: 性能和质量基准测试"""
        print(f"\\n⚡ 测试4: 性能和质量基准测试")
        print("-" * 60)
        
        test_start = time.time()
        
        benchmark_topics = [
            "Quantum-Inspired Neural Architecture Search",
            "Federated Learning with Privacy-Preserving Mechanisms", 
            "Multimodal Transformer for Cross-Domain Understanding"
        ]
        
        benchmark_results = []
        
        for i, topic in enumerate(benchmark_topics, 1):
            print(f"  🧪 基准测试 {i}/3: {topic[:40]}...")
            
            try:
                topic_start = time.time()
                
                # 使用增强写作器进行快速测试
                writer = EnhancedPaperWriter()
                
                result = writer.generate_enhanced_paper(
                    topic=topic,
                    research_focus="Advanced AI methodology",
                    methodology="Deep learning with novel architectures"
                )
                
                topic_time = time.time() - topic_start
                
                benchmark_results.append({
                    "topic": topic,
                    "quality_score": result['quality_metrics']['overall_quality'],
                    "generation_time": topic_time,
                    "sections": len(result['sections']),
                    "references": len(result['references']),
                    "success": True
                })
                
                print(f"    ✅ 基准{i}: {result['quality_metrics']['overall_quality']:.1f}/10 ({topic_time:.1f}s)")
                
            except Exception as e:
                print(f"    ❌ 基准{i}: 失败 - {e}")
                benchmark_results.append({
                    "topic": topic,
                    "success": False,
                    "error": str(e)
                })
        
        test_time = time.time() - test_start
        
        # 计算基准统计
        successful_benchmarks = [b for b in benchmark_results if b.get('success', False)]
        
        if successful_benchmarks:
            avg_quality = sum(b['quality_score'] for b in successful_benchmarks) / len(successful_benchmarks)
            avg_time = sum(b['generation_time'] for b in successful_benchmarks) / len(successful_benchmarks)
            
            benchmark_summary = {
                "test_name": "Performance Benchmarks",
                "success": True,
                "total_benchmarks": len(benchmark_topics),
                "successful_benchmarks": len(successful_benchmarks),
                "average_quality": avg_quality,
                "average_generation_time": avg_time,
                "total_test_time": test_time,
                "benchmark_details": benchmark_results,
                "timestamp": datetime.now().isoformat()
            }
            
            print(f"\\n  📈 基准测试总结:")
            print(f"    ✅ 成功率: {len(successful_benchmarks)}/{len(benchmark_topics)}")
            print(f"    🎯 平均质量: {avg_quality:.1f}/10")
            print(f"    ⏱️ 平均时间: {avg_time:.2f}秒")
            print(f"    📊 总测试时间: {test_time:.2f}秒")
        else:
            benchmark_summary = {
                "test_name": "Performance Benchmarks",
                "success": False,
                "error": "No successful benchmarks",
                "timestamp": datetime.now().isoformat()
            }
            print(f"  ❌ 所有基准测试失败")
        
        self.results.append(benchmark_summary)
        print(f"  ✅ 性能基准测试完成")
    
    async def _generate_final_report(self):
        """生成最终测试报告"""
        print(f"\\n📋 生成最终测试报告")
        print("=" * 80)
        
        total_time = time.time() - self.start_time
        successful_tests = [r for r in self.results if r.get('success', False)]
        
        # 创建综合报告
        final_report = {
            "test_suite_info": {
                "name": "Ultimate Enhanced Stage 4 Test Suite",
                "version": "2.0",
                "timestamp": datetime.now().isoformat(),
                "total_duration": total_time,
                "total_tests": len(self.results),
                "successful_tests": len(successful_tests),
                "success_rate": len(successful_tests) / len(self.results) * 100 if self.results else 0
            },
            "system_capabilities": {
                "hybrid_models": True,
                "visual_optimization": True,
                "ai_scientist_v2_integration": True,
                "latex_generation": True,
                "quality_control": True
            },
            "test_results": self.results,
            "performance_summary": self._calculate_performance_summary(),
            "quality_analysis": self._calculate_quality_analysis(),
            "recommendations": self._generate_recommendations()
        }
        
        # 保存报告
        report_filename = f"ultimate_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path = os.path.join("output", report_filename)
        
        os.makedirs("output", exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        
        # 显示最终总结
        print(f"\\n🎉 测试套件执行完成!")
        print(f"⏱️ 总执行时间: {total_time:.2f}秒")
        print(f"✅ 成功率: {len(successful_tests)}/{len(self.results)} ({len(successful_tests)/len(self.results)*100:.1f}%)")
        
        # 显示性能指标
        performance = self._calculate_performance_summary()
        if performance:
            print(f"\\n📊 性能指标:")
            print(f"  🎯 平均质量分数: {performance.get('average_quality', 0):.1f}/10")
            print(f"  ⏱️ 平均生成时间: {performance.get('average_generation_time', 0):.2f}秒")
            print(f"  📄 平均章节数: {performance.get('average_sections', 0):.1f}")
            print(f"  📚 平均引用数: {performance.get('average_references', 0):.1f}")
        
        # 显示系统状态
        print(f"\\n🔧 系统状态:")
        print(f"  🧠 混合模型: {'运行正常' if self._check_hybrid_models() else '需要检查'}")
        print(f"  👁️ 视觉系统: {'运行正常' if self._check_visual_system() else '需要检查'}")
        print(f"  📝 论文生成: {'运行正常' if self._check_paper_generation() else '需要检查'}")
        
        # 显示建议
        recommendations = self._generate_recommendations()
        if recommendations:
            print(f"\\n💡 改进建议:")
            for rec in recommendations[:3]:
                print(f"  • {rec}")
        
        print(f"\\n📁 详细报告已保存: {report_path}")
        print("=" * 80)
        
        return final_report
    
    def _calculate_performance_summary(self) -> Dict[str, float]:
        """计算性能总结"""
        quality_scores = []
        generation_times = []
        section_counts = []
        reference_counts = []
        
        for result in self.results:
            if result.get('success') and 'overall_quality' in result:
                quality_scores.append(result['overall_quality'])
            if result.get('success') and 'test_time' in result:
                generation_times.append(result['test_time'])
            if result.get('success') and 'section_count' in result:
                section_counts.append(result['section_count'])
            if result.get('success') and 'citation_count' in result:
                reference_counts.append(result['citation_count'])
        
        if not quality_scores:
            return {}
        
        return {
            "average_quality": sum(quality_scores) / len(quality_scores),
            "max_quality": max(quality_scores),
            "min_quality": min(quality_scores),
            "average_generation_time": sum(generation_times) / len(generation_times) if generation_times else 0,
            "average_sections": sum(section_counts) / len(section_counts) if section_counts else 0,
            "average_references": sum(reference_counts) / len(reference_counts) if reference_counts else 0
        }
    
    def _calculate_quality_analysis(self) -> Dict[str, Any]:
        """计算质量分析"""
        successful_results = [r for r in self.results if r.get('success', False)]
        
        if not successful_results:
            return {"status": "No successful tests for analysis"}
        
        quality_distribution = {
            "excellent": 0,  # 8-10
            "good": 0,       # 6-8
            "fair": 0,       # 4-6
            "poor": 0        # 0-4
        }
        
        for result in successful_results:
            quality = result.get('overall_quality', 0)
            if quality >= 8:
                quality_distribution["excellent"] += 1
            elif quality >= 6:
                quality_distribution["good"] += 1
            elif quality >= 4:
                quality_distribution["fair"] += 1
            else:
                quality_distribution["poor"] += 1
        
        return {
            "total_evaluated": len(successful_results),
            "quality_distribution": quality_distribution,
            "quality_percentage": {
                k: v / len(successful_results) * 100 
                for k, v in quality_distribution.items()
            }
        }
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 分析结果生成建议
        successful_tests = [r for r in self.results if r.get('success', False)]
        
        if len(successful_tests) < len(self.results):
            recommendations.append("提高系统稳定性，减少测试失败率")
        
        # 质量分析
        quality_scores = [r.get('overall_quality', 0) for r in successful_tests if 'overall_quality' in r]
        if quality_scores:
            avg_quality = sum(quality_scores) / len(quality_scores)
            if avg_quality < 7.5:
                recommendations.append("优化论文生成质量，目标达到7.5+分数")
            if max(quality_scores) - min(quality_scores) > 2:
                recommendations.append("提高质量一致性，减少结果波动")
        
        # 性能分析
        generation_times = [r.get('test_time', 0) for r in successful_tests if 'test_time' in r]
        if generation_times:
            avg_time = sum(generation_times) / len(generation_times)
            if avg_time > 300:  # 5分钟
                recommendations.append("优化生成速度，减少处理时间")
        
        # AI Scientist v2特性检查
        ai_scientist_results = [r for r in self.results if r.get('test_name') == 'AI Scientist v2 Integration']
        if ai_scientist_results and not ai_scientist_results[0].get('success'):
            recommendations.append("修复AI Scientist v2集成功能")
        
        if not recommendations:
            recommendations.append("系统运行良好，继续维护和监控")
        
        return recommendations
    
    def _check_hybrid_models(self) -> bool:
        """检查混合模型状态"""
        try:
            hybrid_client = get_hybrid_client()
            model_status = hybrid_client.get_model_status()
            return sum(1 for status in model_status.values() if status) >= 3
        except:
            return False
    
    def _check_visual_system(self) -> bool:
        """检查视觉系统状态"""
        try:
            visual_system = VisualReviewSystem()
            test_data = {"title": "Test", "sections": {}, "references": []}
            visual_system.get_visual_quality_score(test_data)
            return True
        except:
            return False
    
    def _check_paper_generation(self) -> bool:
        """检查论文生成状态"""
        try:
            writer = EnhancedPaperWriter()
            return True
        except:
            return False


async def main():
    """主函数"""
    test_suite = UltimateTestSuite()
    await test_suite.run_complete_test_suite()


if __name__ == "__main__":
    print("🚀 启动终极增强测试套件...")
    asyncio.run(main())
