"""
Paper Workflow Extractor

从学术论文中提取结构化的工作流信息，包括数据集、网络结构、
平台工具、研究方法等，为后续的实验设计和论文生成提供基础。
"""

import os
import sys
import json
import time
import re
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime
import logging

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from agents.agent_manager import AgentManager
from paper_generation.workflow_extraction.workflow_data_models import (
    PaperWorkflow, WorkflowExtractionResult, DatasetInfo, NetworkArchitecture,
    ImplementationDetails, ResearchMethod, ExperimentSetup, VisualizationSpec,
    DatasetType, NetworkArchitectureType, ImplementationFramework, 
    ResearchMethodType, WORKFLOW_TEMPLATES
)


class PaperWorkflowExtractor:
    """论文工作流提取器"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化工作流提取器
        
        Args:
            llm_client: LLM客户端实例
        """
        if llm_client is None:
            self.llm_client = LLMClient()
        else:
            self.llm_client = llm_client
            
        self.agent_manager = AgentManager(self.llm_client)
        self.logger = self._setup_logger()
        
        # 提取模式和关键词
        self.dataset_patterns = {
            'common_datasets': [
                r'CIFAR-?10', r'CIFAR-?100', r'ImageNet', r'MNIST', r'Fashion-MNIST',
                r'COCO', r'Pascal VOC', r'MS COCO', r'OpenImages', r'Places365',
                r'WikiText', r'GLUE', r'SQuAD', r'BERT', r'GPT', r'RoBERTa',
                r'Kinetics', r'UCF-101', r'ActivityNet', r'Something-Something',
                r'N-MNIST', r'DVS-CIFAR10', r'SHD', r'TIMIT'
            ],
            'dataset_indicators': [
                r'dataset', r'data set', r'benchmark', r'corpus', r'collection'
            ]
        }
        
        self.architecture_patterns = {
            'network_types': [
                r'CNN', r'ConvNet', r'Convolutional Neural Network',
                r'RNN', r'LSTM', r'GRU', r'Transformer', r'BERT', r'GPT',
                r'ResNet', r'DenseNet', r'VGG', r'AlexNet', r'U-Net',
                r'GAN', r'VAE', r'SNN', r'Spiking Neural Network',
                r'attention', r'self-attention', r'multi-head attention'
            ],
            'brain_inspired': [
                r'brain-inspired', r'neuromorphic', r'bio-inspired',
                r'neural plasticity', r'synaptic plasticity', r'STDP',
                r'spike', r'spiking', r'dendrite', r'axon', r'neuron'
            ]
        }
        
        self.framework_patterns = {
            'deep_learning': [
                r'PyTorch', r'TensorFlow', r'Keras', r'JAX', r'Flax',
                r'Caffe', r'MXNet', r'PaddlePaddle', r'Theano'
            ],
            'neuromorphic': [
                r'Brian2', r'NEST', r'NEURON', r'SpykeTorch', r'snnTorch',
                r'Loihi', r'TrueNorth', r'SpiNNaker'
            ],
            'scientific': [
                r'NumPy', r'SciPy', r'scikit-learn', r'Pandas', r'Matplotlib',
                r'Seaborn', r'Plotly', r'Jupyter'
            ]
        }
        
        self.method_patterns = {
            'learning_types': [
                r'supervised learning', r'unsupervised learning', r'reinforcement learning',
                r'transfer learning', r'meta learning', r'few-shot learning',
                r'self-supervised', r'weakly supervised', r'semi-supervised'
            ],
            'training_methods': [
                r'backpropagation', r'gradient descent', r'Adam', r'SGD',
                r'STDP', r'Hebbian learning', r'evolutionary algorithm'
            ]
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('PaperWorkflowExtractor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def extract_workflow_from_paper(self, paper_content: Dict[str, Any], 
                                  paper_metadata: Optional[Dict[str, Any]] = None) -> WorkflowExtractionResult:
        """
        从论文内容中提取工作流信息
        
        Args:
            paper_content: 论文内容字典 (包含title, abstract, full_text等)
            paper_metadata: 论文元数据 (包含authors, venue, year等)
            
        Returns:
            WorkflowExtractionResult: 提取结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"开始提取论文工作流: {paper_content.get('title', 'Unknown')}")
            
            # 创建基础PaperWorkflow对象
            workflow = PaperWorkflow(
                paper_id=paper_metadata.get('paperId', f"paper_{int(time.time())}") if paper_metadata else f"paper_{int(time.time())}",
                title=paper_content.get('title', ''),
                authors=paper_metadata.get('authors', []) if paper_metadata else [],
                venue=paper_metadata.get('venue', '') if paper_metadata else '',
                year=paper_metadata.get('year') if paper_metadata else None
            )
            
            # 合并论文文本用于分析
            full_text = self._combine_paper_text(paper_content)
            
            # 多阶段提取
            print("📋 第1阶段: 基础信息提取...")
            basic_info = self._extract_basic_information(full_text)
            
            print("📊 第2阶段: 数据集信息提取...")
            datasets = self._extract_datasets(full_text)
            workflow.datasets = datasets
            
            print("🏗️ 第3阶段: 网络架构提取...")
            architectures = self._extract_network_architectures(full_text)
            workflow.network_architectures = architectures
            
            print("🔧 第4阶段: 实现细节提取...")
            implementations = self._extract_implementation_details(full_text)
            workflow.implementation_details = implementations
            
            print("🔬 第5阶段: 研究方法提取...")
            methods = self._extract_research_methods(full_text)
            workflow.research_methods = methods
            
            print("⚗️ 第6阶段: 实验设置提取...")
            experiment_setup = self._extract_experiment_setup(full_text)
            workflow.experiment_setup = experiment_setup
            
            print("📈 第7阶段: 可视化规格提取...")
            visualizations = self._extract_visualizations(full_text)
            workflow.visualizations = visualizations
            
            print("🧠 第8阶段: 脑启发元素识别...")
            brain_elements = self._identify_brain_inspired_elements(full_text)
            workflow.brain_inspired_elements = brain_elements
            
            print("🎯 第9阶段: 关键贡献提取...")
            contributions = self._extract_key_contributions(full_text)
            workflow.contributions = contributions
            
            # 计算提取置信度
            confidence = self._calculate_extraction_confidence(workflow, full_text)
            workflow.extraction_confidence = confidence
            
            extraction_time = time.time() - start_time
            
            self.logger.info(f"工作流提取完成，置信度: {confidence:.2f}, 耗时: {extraction_time:.2f}s")
            
            return WorkflowExtractionResult(
                success=True,
                workflow=workflow,
                confidence_score=confidence,
                extraction_time=extraction_time,
                metadata={
                    'extraction_method': 'multi_stage_llm_analysis',
                    'paper_length': len(full_text),
                    'stages_completed': 9
                }
            )
            
        except Exception as e:
            extraction_time = time.time() - start_time
            self.logger.error(f"工作流提取失败: {e}")
            
            return WorkflowExtractionResult(
                success=False,
                confidence_score=0.0,
                extraction_time=extraction_time,
                errors=[str(e)]
            )
    
    def _combine_paper_text(self, paper_content: Dict[str, Any]) -> str:
        """合并论文文本"""
        text_parts = []
        
        if paper_content.get('title'):
            text_parts.append(f"Title: {paper_content['title']}")
        
        if paper_content.get('abstract'):
            text_parts.append(f"Abstract: {paper_content['abstract']}")
        
        if paper_content.get('full_text'):
            text_parts.append(paper_content['full_text'])
        elif paper_content.get('content'):
            text_parts.append(paper_content['content'])
        
        # 如果有分节内容，也要包含
        for section in ['introduction', 'methodology', 'experiments', 'results', 'conclusion']:
            if paper_content.get(section):
                text_parts.append(f"{section.title()}: {paper_content[section]}")
        
        return "\\n\\n".join(text_parts)
    
    def _extract_basic_information(self, text: str) -> Dict[str, Any]:
        """提取基础信息"""
        prompt = f"""
        Analyze the following research paper and extract basic information:
        
        Paper Text: {text[:3000]}...
        
        Please extract and return in JSON format:
        {{
            "research_area": "main research area (e.g., computer vision, NLP, neuromorphic computing)",
            "problem_type": "type of problem being solved",
            "approach": "main approach or methodology",
            "key_innovations": ["innovation 1", "innovation 2", ...]
        }}
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            
            # 解析JSON
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                return json.loads(json_text)
        
        except Exception as e:
            self.logger.warning(f"基础信息提取失败: {e}")
        
        return {}
    
    def _extract_datasets(self, text: str) -> List[DatasetInfo]:
        """提取数据集信息"""
        datasets = []
        
        # 使用正则表达式和LLM结合的方法
        regex_datasets = self._extract_datasets_regex(text)
        llm_datasets = self._extract_datasets_llm(text)
        
        # 合并和去重
        all_dataset_names = set()
        
        for dataset in regex_datasets + llm_datasets:
            if dataset.name not in all_dataset_names:
                datasets.append(dataset)
                all_dataset_names.add(dataset.name)
        
        return datasets
    
    def _extract_datasets_regex(self, text: str) -> List[DatasetInfo]:
        """使用正则表达式提取数据集"""
        datasets = []
        
        for pattern in self.dataset_patterns['common_datasets']:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                dataset_name = match.group()
                # 确定数据集类型
                dataset_type = self._infer_dataset_type(dataset_name)
                
                datasets.append(DatasetInfo(
                    name=dataset_name,
                    type=dataset_type,
                    description=f"Detected from text: {dataset_name}"
                ))
        
        return datasets
    
    def _extract_datasets_llm(self, text: str) -> List[DatasetInfo]:
        """使用LLM提取数据集信息"""
        prompt = f"""
        Extract dataset information from the following research paper text:
        
        Text: {text[:4000]}...
        
        Please identify all datasets mentioned and return in JSON format:
        {{
            "datasets": [
                {{
                    "name": "dataset name",
                    "type": "Image|Text|Audio|Video|Tabular|Graph|Time Series|Multimodal|Synthetic",
                    "size": "dataset size if mentioned",
                    "description": "brief description",
                    "preprocessing_steps": ["step1", "step2", ...],
                    "split_strategy": "train/val/test split if mentioned"
                }},
                ...
            ]
        }}
        
        Focus on identifying:
        1. Standard benchmark datasets
        2. Custom datasets created by authors
        3. Dataset preprocessing and augmentation techniques
        4. Data split strategies
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                data = json.loads(json_text)
                
                datasets = []
                for dataset_data in data.get('datasets', []):
                    try:
                        dataset_type = DatasetType(dataset_data.get('type', 'Tabular'))
                    except ValueError:
                        dataset_type = DatasetType.TABULAR
                    
                    dataset = DatasetInfo(
                        name=dataset_data.get('name', ''),
                        type=dataset_type,
                        size=dataset_data.get('size'),
                        description=dataset_data.get('description'),
                        preprocessing_steps=dataset_data.get('preprocessing_steps', []),
                        split_strategy=dataset_data.get('split_strategy')
                    )
                    datasets.append(dataset)
                
                return datasets
        
        except Exception as e:
            self.logger.warning(f"LLM数据集提取失败: {e}")
        
        return []
    
    def _infer_dataset_type(self, dataset_name: str) -> DatasetType:
        """推断数据集类型"""
        dataset_name_lower = dataset_name.lower()
        
        # 图像数据集
        image_indicators = ['cifar', 'imagenet', 'mnist', 'coco', 'pascal', 'places']
        if any(indicator in dataset_name_lower for indicator in image_indicators):
            return DatasetType.IMAGE
        
        # 文本数据集
        text_indicators = ['glue', 'squad', 'wikitext', 'bert', 'gpt']
        if any(indicator in dataset_name_lower for indicator in text_indicators):
            return DatasetType.TEXT
        
        # 视频数据集
        video_indicators = ['kinetics', 'ucf', 'activity', 'something']
        if any(indicator in dataset_name_lower for indicator in video_indicators):
            return DatasetType.VIDEO
        
        # 神经形态数据集
        neuromorphic_indicators = ['n-mnist', 'dvs', 'shd', 'timit']
        if any(indicator in dataset_name_lower for indicator in neuromorphic_indicators):
            return DatasetType.TIME_SERIES
        
        return DatasetType.TABULAR
    
    def _extract_network_architectures(self, text: str) -> List[NetworkArchitecture]:
        """提取网络架构信息"""
        architectures = []
        
        prompt = f"""
        Extract neural network architecture information from the following research paper:
        
        Text: {text[:4000]}...
        
        Please identify all neural network architectures and return in JSON format:
        {{
            "architectures": [
                {{
                    "name": "architecture name",
                    "type": "CNN|RNN|LSTM|GRU|Transformer|Attention|ResNet|DenseNet|U-Net|GAN|VAE|SNN|Brain-Inspired|Hybrid|Custom",
                    "description": "brief description of the architecture",
                    "layers": ["layer1", "layer2", ...],
                    "parameters": {{"param1": "value1", "param2": "value2"}},
                    "innovations": ["innovation1", "innovation2", ...],
                    "brain_inspiration": "brain mechanism if applicable"
                }},
                ...
            ]
        }}
        
        Focus on:
        1. Main network architectures used
        2. Novel architectural contributions
        3. Brain-inspired components
        4. Layer configurations and innovations
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                data = json.loads(json_text)
                
                for arch_data in data.get('architectures', []):
                    try:
                        arch_type = NetworkArchitectureType(arch_data.get('type', 'Custom'))
                    except ValueError:
                        arch_type = NetworkArchitectureType.CUSTOM
                    
                    architecture = NetworkArchitecture(
                        name=arch_data.get('name', ''),
                        type=arch_type,
                        description=arch_data.get('description'),
                        layers=arch_data.get('layers', []),
                        parameters=arch_data.get('parameters', {}),
                        innovations=arch_data.get('innovations', []),
                        brain_inspiration=arch_data.get('brain_inspiration')
                    )
                    architectures.append(architecture)
        
        except Exception as e:
            self.logger.warning(f"网络架构提取失败: {e}")
        
        return architectures
    
    def _extract_implementation_details(self, text: str) -> List[ImplementationDetails]:
        """提取实现细节"""
        implementations = []
        
        prompt = f"""
        Extract implementation details from the research paper:
        
        Text: {text[:4000]}...
        
        Please identify implementation frameworks and details in JSON format:
        {{
            "implementations": [
                {{
                    "framework": "PyTorch|TensorFlow|JAX|Keras|Sklearn|NumPy|Brian2|NEST|NEURON|Custom",
                    "version": "framework version if mentioned",
                    "hardware_requirements": ["GPU", "CPU", "special hardware"],
                    "software_dependencies": ["library1", "library2", ...],
                    "code_availability": true/false,
                    "code_url": "code repository URL if mentioned",
                    "computational_requirements": {{"training_time": "X hours", "memory": "Y GB"}}
                }},
                ...
            ]
        }}
        
        Look for:
        1. Deep learning frameworks
        2. Neuromorphic computing platforms
        3. Hardware requirements
        4. Code availability and repositories
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                data = json.loads(json_text)
                
                for impl_data in data.get('implementations', []):
                    try:
                        framework = ImplementationFramework(impl_data.get('framework', 'Custom'))
                    except ValueError:
                        framework = ImplementationFramework.CUSTOM
                    
                    implementation = ImplementationDetails(
                        framework=framework,
                        version=impl_data.get('version'),
                        hardware_requirements=impl_data.get('hardware_requirements', []),
                        software_dependencies=impl_data.get('software_dependencies', []),
                        code_availability=impl_data.get('code_availability', False),
                        code_url=impl_data.get('code_url'),
                        computational_requirements=impl_data.get('computational_requirements', {})
                    )
                    implementations.append(implementation)
        
        except Exception as e:
            self.logger.warning(f"实现细节提取失败: {e}")
        
        return implementations
    
    def _extract_research_methods(self, text: str) -> List[ResearchMethod]:
        """提取研究方法"""
        methods = []
        
        prompt = f"""
        Extract research methodologies from the paper:
        
        Text: {text[:4000]}...
        
        Please identify research methods in JSON format:
        {{
            "methods": [
                {{
                    "name": "method name",
                    "type": "Supervised Learning|Unsupervised Learning|Reinforcement Learning|Transfer Learning|Meta Learning|Self-Supervised|Neuromorphic Computing|Comparative Study|Ablation Study|Theoretical Analysis",
                    "description": "detailed description",
                    "procedure": ["step1", "step2", ...],
                    "advantages": ["advantage1", "advantage2", ...],
                    "limitations": ["limitation1", "limitation2", ...],
                    "applicability": ["use case1", "use case2", ...]
                }},
                ...
            ]
        }}
        
        Focus on:
        1. Learning paradigms
        2. Training methodologies
        3. Evaluation approaches
        4. Novel algorithmic contributions
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                data = json.loads(json_text)
                
                for method_data in data.get('methods', []):
                    try:
                        method_type = ResearchMethodType(method_data.get('type', 'Comparative Study'))
                    except ValueError:
                        method_type = ResearchMethodType.COMPARATIVE_STUDY
                    
                    method = ResearchMethod(
                        name=method_data.get('name', ''),
                        type=method_type,
                        description=method_data.get('description'),
                        procedure=method_data.get('procedure', []),
                        advantages=method_data.get('advantages', []),
                        limitations=method_data.get('limitations', []),
                        applicability=method_data.get('applicability', [])
                    )
                    methods.append(method)
        
        except Exception as e:
            self.logger.warning(f"研究方法提取失败: {e}")
        
        return methods
    
    def _extract_experiment_setup(self, text: str) -> Optional[ExperimentSetup]:
        """提取实验设置"""
        prompt = f"""
        Extract experimental setup from the research paper:
        
        Text: {text[:4000]}...
        
        Please identify experimental design in JSON format:
        {{
            "experiment_setup": {{
                "objective": "main experimental objective",
                "hypotheses": ["hypothesis1", "hypothesis2", ...],
                "independent_variables": ["variable1", "variable2", ...],
                "dependent_variables": ["metric1", "metric2", ...],
                "control_variables": ["control1", "control2", ...],
                "evaluation_metrics": ["accuracy", "f1-score", ...],
                "baseline_methods": ["baseline1", "baseline2", ...],
                "experimental_conditions": {{"condition1": "value1", "condition2": "value2"}}
            }}
        }}
        
        Focus on:
        1. Research objectives and hypotheses
        2. Experimental variables and controls
        3. Evaluation metrics and baselines
        4. Experimental conditions and settings
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                data = json.loads(json_text)
                
                setup_data = data.get('experiment_setup', {})
                if setup_data.get('objective'):
                    return ExperimentSetup(
                        objective=setup_data.get('objective', ''),
                        hypotheses=setup_data.get('hypotheses', []),
                        independent_variables=setup_data.get('independent_variables', []),
                        dependent_variables=setup_data.get('dependent_variables', []),
                        control_variables=setup_data.get('control_variables', []),
                        evaluation_metrics=setup_data.get('evaluation_metrics', []),
                        baseline_methods=setup_data.get('baseline_methods', []),
                        experimental_conditions=setup_data.get('experimental_conditions', {})
                    )
        
        except Exception as e:
            self.logger.warning(f"实验设置提取失败: {e}")
        
        return None
    
    def _extract_visualizations(self, text: str) -> List[VisualizationSpec]:
        """提取可视化规格"""
        visualizations = []
        
        # 简化版本，主要识别图表类型
        chart_patterns = {
            'line_plot': r'line plot|line chart|curve|trend',
            'bar_chart': r'bar chart|bar plot|histogram',
            'scatter_plot': r'scatter plot|scatter chart',
            'heatmap': r'heatmap|heat map|correlation matrix',
            'confusion_matrix': r'confusion matrix',
            'roc_curve': r'ROC curve|receiver operating',
            'loss_curve': r'loss curve|training curve|learning curve',
            'architecture_diagram': r'architecture diagram|network structure|model diagram'
        }
        
        for chart_type, pattern in chart_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                visualization = VisualizationSpec(
                    chart_type=chart_type,
                    purpose=f"Visualizing {chart_type.replace('_', ' ')}",
                    data_source="Experimental results",
                    tools_used=["matplotlib", "seaborn"],
                    description=f"Standard {chart_type.replace('_', ' ')} visualization"
                )
                visualizations.append(visualization)
        
        return visualizations
    
    def _identify_brain_inspired_elements(self, text: str) -> List[str]:
        """识别脑启发元素"""
        brain_elements = []
        
        brain_patterns = {
            'plasticity': r'plasticity|STDP|synaptic plasticity|neural plasticity',
            'spiking': r'spike|spiking|action potential|temporal coding',
            'attention': r'attention|selective attention|focus',
            'memory': r'working memory|long-term memory|short-term memory',
            'learning': r'hebbian|competitive learning|unsupervised learning',
            'architecture': r'cortical|hierarchical|feedforward|feedback',
            'dynamics': r'neural dynamics|temporal dynamics|oscillation'
        }
        
        for element, pattern in brain_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                brain_elements.append(element)
        
        return brain_elements
    
    def _extract_key_contributions(self, text: str) -> List[str]:
        """提取关键贡献"""
        contributions = []
        
        prompt = f"""
        Extract the key contributions and novelties from this research paper:
        
        Text: {text[:4000]}...
        
        Please identify the main contributions in JSON format:
        {{
            "contributions": [
                "contribution 1: brief description",
                "contribution 2: brief description",
                ...
            ]
        }}
        
        Focus on:
        1. Novel algorithms or methods
        2. Architectural innovations
        3. Theoretical contributions
        4. Empirical findings
        5. Performance improvements
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                data = json.loads(json_text)
                contributions = data.get('contributions', [])
        
        except Exception as e:
            self.logger.warning(f"关键贡献提取失败: {e}")
        
        return contributions
    
    def _calculate_extraction_confidence(self, workflow: PaperWorkflow, text: str) -> float:
        """计算提取置信度"""
        confidence_factors = []
        
        # 基于提取到的信息数量
        if workflow.datasets:
            confidence_factors.append(0.15)
        if workflow.network_architectures:
            confidence_factors.append(0.20)
        if workflow.implementation_details:
            confidence_factors.append(0.15)
        if workflow.research_methods:
            confidence_factors.append(0.15)
        if workflow.experiment_setup:
            confidence_factors.append(0.15)
        if workflow.contributions:
            confidence_factors.append(0.10)
        
        # 基于文本长度和内容丰富度
        text_length_factor = min(len(text) / 10000, 1.0) * 0.10
        confidence_factors.append(text_length_factor)
        
        return min(sum(confidence_factors), 1.0)
    
    def save_workflow(self, workflow: PaperWorkflow, output_path: str):
        """保存工作流到文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(workflow.to_dict(), f, indent=2, ensure_ascii=False)
            self.logger.info(f"工作流已保存到: {output_path}")
        except Exception as e:
            self.logger.error(f"保存工作流失败: {e}")
    
    def load_workflow(self, file_path: str) -> PaperWorkflow:
        """从文件加载工作流"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return PaperWorkflow.from_dict(data)
        except Exception as e:
            self.logger.error(f"加载工作流失败: {e}")
            raise


def test_workflow_extractor():
    """测试工作流提取器"""
    print("🧪 测试论文工作流提取器")
    
    # 创建测试数据
    test_paper = {
        'title': 'Brain-Inspired Spiking Neural Networks for Image Classification',
        'abstract': '''This paper presents a novel brain-inspired spiking neural network (SNN) 
        architecture for image classification tasks. We propose a new plasticity mechanism 
        based on spike-timing-dependent plasticity (STDP) and demonstrate its effectiveness 
        on CIFAR-10 and MNIST datasets. Our approach achieves competitive accuracy while 
        reducing computational complexity.''',
        'full_text': '''
        Introduction: Deep learning has shown remarkable success... 
        Methodology: We implemented our SNN using PyTorch and Brian2...
        Experiments: We evaluated our method on CIFAR-10, MNIST, and Fashion-MNIST datasets...
        Results: Our approach achieved 92.3% accuracy on CIFAR-10...
        '''
    }
    
    test_metadata = {
        'paperId': 'test_001',
        'authors': ['John Doe', 'Jane Smith'],
        'venue': 'NeurIPS',
        'year': 2024
    }
    
    # 测试提取器
    extractor = PaperWorkflowExtractor()
    result = extractor.extract_workflow_from_paper(test_paper, test_metadata)
    
    if result.success:
        print(f"✅ 提取成功! 置信度: {result.confidence_score:.2f}")
        print(f"📊 数据集数量: {len(result.workflow.datasets)}")
        print(f"🏗️ 架构数量: {len(result.workflow.network_architectures)}")
        print(f"🔧 实现细节数量: {len(result.workflow.implementation_details)}")
        print(f"🧠 脑启发元素: {result.workflow.brain_inspired_elements}")
        
        # 保存结果
        output_path = "test_workflow_extraction.json"
        extractor.save_workflow(result.workflow, output_path)
        print(f"💾 结果已保存到: {output_path}")
        
    else:
        print(f"❌ 提取失败: {result.errors}")
    
    return result


if __name__ == "__main__":
    test_workflow_extractor()
