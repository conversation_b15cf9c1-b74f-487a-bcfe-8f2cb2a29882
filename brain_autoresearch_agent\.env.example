# API配置示例文件
# 复制此文件为 .env 并填入您的真实API密钥

# ======================
# DeepSeek API (主要推理模型)
# ======================
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com

# ======================
# Qwen API (学术写作和视觉)
# ======================
QWEN_API_KEY=your_qwen_api_key_here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1

# ======================
# Claude API (备选)
# ======================
ANTHROPIC_API_KEY=your_claude_api_key_here

# ======================  
# OpenAI API (备选)
# ======================
OPENAI_API_KEY=your_openai_api_key_here

# ======================
# 其他API配置
# ======================
# 如果使用其他兼容OpenAI的服务
# OPENAI_BASE_URL=https://your-api-endpoint.com/v1
# OPENAI_MODEL=your-custom-model-name

# ======================
# 使用建议
# ======================
# 推荐配置：
# 1. 优先使用 DeepSeek-V3：推理能力强，成本低
# 2. 搭配使用 Qwen-Max：学术写作质量高，支持视觉分析
# 3. 备选使用 Claude 3.5 Sonnet：质量最高，但成本较高
# 4. 确保API账户有足够余额

# 模型选择建议：
# - 推理分析：DeepSeek-V3 (deepseek-reasoner)
# - 学术写作：Qwen-Max (qwen-max)
# - 视觉分析：Qwen-VL-Plus (qwen-vl-plus)
# - 备选方案：Claude 3.5 Sonnet

# 注意：配置后需要重启程序才能生效
