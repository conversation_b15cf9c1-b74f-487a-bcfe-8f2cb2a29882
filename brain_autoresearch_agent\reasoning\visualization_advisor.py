"""
可视化建议生成器
根据结果生成展示图方案，给出画图建议、工具使用建议等
"""

import os
import sys
import json
import time
from typing import Dict, List, Any, Optional, Tuple, TYPE_CHECKING

if TYPE_CHECKING:
    from core.llm_client import LLMClient
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from reasoning.data_models import (
    ExperimentPlan, ImplementationPlan, VisualizationPlan, 
    VisualizationChart, VISUALIZATION_TEMPLATES
)
from agents.agent_manager import AgentManager
from core.llm_client import LLMClient


class VisualizationAdvisor:
    """可视化建议生成器"""
    
    def __init__(self, llm_client: Optional['LLMClient'] = None):
        """
        初始化可视化建议器
        
        Args:
            llm_client: LLM客户端实例
        """
        # 初始化LLM客户端
        if llm_client is None:
            from core.llm_client import LLMClient
            from config.model_config import create_model_config
            model_config = create_model_config()
            llm_client = LLMClient()
        
        self.llm_client = llm_client
        self.agent_manager = AgentManager(llm_client)
        
        # 可视化工具对比
        self.visualization_tools = {
            "matplotlib": {
                "strengths": ["基础绘图", "高度自定义", "科学出版", "静态图表"],
                "weaknesses": ["交互性差", "现代感不足", "复杂配置"],
                "best_for": ["论文图表", "静态分析", "科学可视化"],
                "learning_curve": "中等"
            },
            "seaborn": {
                "strengths": ["统计图表", "美观样式", "简单易用", "与pandas集成"],
                "weaknesses": ["灵活性受限", "自定义困难"],
                "best_for": ["统计分析", "数据探索", "相关性分析"],
                "learning_curve": "简单"
            },
            "plotly": {
                "strengths": ["交互式图表", "现代界面", "Web集成", "3D支持"],
                "weaknesses": ["文件较大", "静态导出限制"],
                "best_for": ["交互展示", "Web应用", "动态图表"],
                "learning_curve": "中等"
            },
            "bokeh": {
                "strengths": ["大数据可视化", "实时更新", "Web原生"],
                "weaknesses": ["复杂性高", "学习成本大"],
                "best_for": ["大数据集", "实时监控", "Web仪表板"],
                "learning_curve": "困难"
            },
            "tensorboard": {
                "strengths": ["深度学习专用", "训练监控", "模型可视化"],
                "weaknesses": ["局限于ML", "不适合通用可视化"],
                "best_for": ["训练监控", "模型调试", "实验对比"],
                "learning_curve": "简单"
            },
            "wandb": {
                "strengths": ["实验管理", "协作分享", "自动记录"],
                "weaknesses": ["需要账号", "网络依赖"],
                "best_for": ["实验跟踪", "团队协作", "超参数调优"],
                "learning_curve": "简单"
            }
        }
        
        # 脑启发智能专用图表类型
        self.brain_inspired_charts = {
            "neural_architecture": {
                "description": "神经网络架构图",
                "recommended_tools": ["matplotlib", "plotly", "networkx"],
                "use_cases": ["网络结构展示", "连接模式分析"],
                "code_example": "networkx + matplotlib绘制网络图"
            },
            "spike_raster": {
                "description": "脉冲栅格图",
                "recommended_tools": ["matplotlib", "brian2"],
                "use_cases": ["脉冲神经网络", "神经元活动模式"],
                "code_example": "事件时间序列可视化"
            },
            "plasticity_evolution": {
                "description": "可塑性演化图",
                "recommended_tools": ["matplotlib", "seaborn"],
                "use_cases": ["权重变化", "连接强度演化"],
                "code_example": "时间序列热图"
            },
            "energy_efficiency": {
                "description": "能耗效率对比",
                "recommended_tools": ["matplotlib", "plotly"],
                "use_cases": ["功耗分析", "效率对比"],
                "code_example": "多维度雷达图"
            },
            "learning_curves": {
                "description": "学习曲线",
                "recommended_tools": ["matplotlib", "tensorboard"],
                "use_cases": ["训练过程", "收敛分析"],
                "code_example": "多条件对比线图"
            },
            "confusion_matrix": {
                "description": "混淆矩阵热图",
                "recommended_tools": ["seaborn", "matplotlib"],
                "use_cases": ["分类性能", "错误分析"],
                "code_example": "annotated heatmap"
            }
        }
        
        # 期刊和会议的图表规范
        self.publication_standards = {
            "IEEE": {
                "figure_format": ["EPS", "PDF", "PNG(300DPI+)"],
                "color_requirements": "彩色可选，黑白必须可读",
                "font_size": "最小8pt，推荐10-12pt",
                "line_width": "最小0.5pt",
                "figure_numbering": "Figure 1, Figure 2, ..."
            },
            "Nature": {
                "figure_format": ["PDF", "EPS", "TIFF"],
                "color_requirements": "RGB色彩模式",
                "font_size": "Arial 6-8pt",
                "line_width": "0.25-0.5pt",
                "figure_numbering": "Fig. 1, Fig. 2, ..."
            },
            "ICML": {
                "figure_format": ["PDF", "PNG"],
                "color_requirements": "确保打印清晰",
                "font_size": "与正文一致",
                "line_width": "清晰可见",
                "figure_numbering": "Figure 1, Figure 2, ..."
            },
            "NeurIPS": {
                "figure_format": ["PDF优先", "PNG备选"],
                "color_requirements": "色盲友好",
                "font_size": "可读性优先",
                "line_width": "适中粗细",
                "figure_numbering": "Figure 1, Figure 2, ..."
            }
        }
    
    def design_visualization_plan(self, experiment_plan: ExperimentPlan, 
                                implementation_plan: ImplementationPlan,
                                target_venue: str = "ICML") -> VisualizationPlan:
        """
        设计可视化方案
        
        Args:
            experiment_plan: 实验计划
            implementation_plan: 实现计划
            target_venue: 目标期刊/会议
            
        Returns:
            详细的可视化计划
        """
        print(f"\n🎨 开始设计可视化方案")
        print(f"🎯 目标期刊: {target_venue}")
        print(f"📊 实验类型: {experiment_plan.experiment_type}")
        
        # 第一步：分析可视化需求
        viz_requirements = self._analyze_visualization_requirements(experiment_plan)
        
        # 第二步：推荐可视化工具
        recommended_tools = self._recommend_visualization_tools(experiment_plan, implementation_plan)
        
        # 第三步：设计具体图表方案
        chart_designs = self._design_chart_specifications(experiment_plan, viz_requirements)
        
        # 第四步：生成代码模板
        code_templates = self._generate_visualization_code(chart_designs, recommended_tools)
        
        # 第五步：适配期刊要求
        publication_guidelines = self._adapt_to_publication_standards(target_venue)
        
        # 第六步：制定设计原则
        design_principles = self._establish_design_principles()
        
        # 创建可视化计划
        visualization_plan = VisualizationPlan(
            experiment_plan_id=experiment_plan.research_question,
            charts=chart_designs,
            recommended_tools=recommended_tools,
            tool_comparison=self._compare_tools(recommended_tools),
            design_principles=design_principles,
            color_schemes=self._recommend_color_schemes(),
            layout_suggestions=self._suggest_layout_arrangements(),
            journal_requirements=publication_guidelines,
            figure_standards=publication_guidelines,
            code_templates=code_templates
        )
        
        print(f"\n✅ 可视化方案设计完成!")
        print(f"📈 图表数量: {len(visualization_plan.charts)}")
        print(f"🛠️ 推荐工具: {len(visualization_plan.recommended_tools)}个")
        print(f"📋 代码模板: {len(visualization_plan.code_templates)}个")
        
        return visualization_plan
    
    def _analyze_visualization_requirements(self, experiment_plan: ExperimentPlan) -> Dict[str, Any]:
        """分析可视化需求"""
        
        analysis_prompt = f"""
        分析以下实验的可视化需求：
        
        研究问题: {experiment_plan.research_question}
        实验类型: {experiment_plan.experiment_type}
        评估指标: {experiment_plan.metrics}
        实验变量: {[var.name for var in experiment_plan.variables]}
        
        请分析需要哪些类型的可视化：
        1. 性能对比图表（准确率、效率等）
        2. 训练过程可视化（学习曲线、收敛性）
        3. 模型分析图表（架构、参数分布）
        4. 统计分析图表（显著性、分布）
        5. 脑启发特色图表（脉冲模式、可塑性等）
        
        请以JSON格式输出：
        {{
            "required_visualizations": [
                {{
                    "category": "<可视化类别>",
                    "chart_types": ["<图表类型1>", "<图表类型2>"],
                    "priority": "<high/medium/low>",
                    "data_requirements": ["<数据需求1>", "<数据需求2>"],
                    "purpose": "<展示目的>"
                }}
            ],
            "brain_inspired_specifics": [
                {{
                    "visualization_type": "<脑启发特色可视化>",
                    "description": "<描述>",
                    "importance": "<重要性>"
                }}
            ],
            "publication_focus": {{
                "main_results": ["<主要结果图表>"],
                "supporting_figures": ["<支撑图表>"],
                "supplementary": ["<补充材料图表>"]
            }}
        }}
        """
        
        try:
            response = self.llm_client.get_response(analysis_prompt)
            if response:
                response_text = response[0] if isinstance(response, tuple) else response
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    return json.loads(json_text)
        except Exception as e:
            print(f"  ⚠️ 可视化需求分析失败: {e}")
        
        # 返回默认需求
        return {
            "required_visualizations": [
                {
                    "category": "performance_comparison",
                    "chart_types": ["bar_chart", "line_chart"],
                    "priority": "high",
                    "data_requirements": ["accuracy", "training_time"],
                    "purpose": "展示性能对比"
                }
            ],
            "brain_inspired_specifics": [
                {
                    "visualization_type": "neural_architecture",
                    "description": "神经网络架构图",
                    "importance": "medium"
                }
            ]
        }
    
    def _recommend_visualization_tools(self, experiment_plan: ExperimentPlan, 
                                     implementation_plan: ImplementationPlan) -> List[str]:
        """推荐可视化工具"""
        
        recommended = []
        
        # 基于编程语言推荐
        if implementation_plan.programming_language.lower() == "python":
            recommended.append("matplotlib")  # 基础必备
            
        # 基于框架推荐
        if "pytorch" in implementation_plan.frameworks:
            recommended.append("tensorboard")
        
        # 基于实验类型推荐
        if experiment_plan.experiment_type in ["benchmark_comparison", "controlled_experiment"]:
            recommended.append("seaborn")  # 统计图表
            
        # 基于评估指标推荐
        if any("time" in metric for metric in experiment_plan.metrics):
            recommended.append("plotly")  # 交互式时间序列
            
        # 脑启发特色推荐
        question_lower = experiment_plan.research_question.lower()
        if any(word in question_lower for word in ["脉冲", "神经元", "可塑性"]):
            recommended.extend(["matplotlib", "networkx"])
        
        # 去重并确保有基础工具
        recommended = list(set(recommended))
        if "matplotlib" not in recommended:
            recommended.insert(0, "matplotlib")
            
        return recommended[:4]  # 限制推荐数量
    
    def _design_chart_specifications(self, experiment_plan: ExperimentPlan, 
                                   viz_requirements: Dict[str, Any]) -> List[VisualizationChart]:
        """设计具体图表规格"""
        
        charts = []
        
        # 基于需求设计图表
        for viz_req in viz_requirements.get("required_visualizations", []):
            chart_types = viz_req.get("chart_types", [])
            
            for chart_type in chart_types:
                if chart_type == "bar_chart":
                    chart = VisualizationChart(
                        chart_type="bar",
                        title="性能对比图",
                        description="不同方法的性能指标对比",
                        data_requirements=["method_names", "performance_scores"],
                        recommended_tool="matplotlib",
                        best_practices=[
                            "使用清晰的标签",
                            "添加误差棒",
                            "选择合适的颜色",
                            "保持一致的风格"
                        ]
                    )
                    charts.append(chart)
                
                elif chart_type == "line_chart":
                    chart = VisualizationChart(
                        chart_type="line",
                        title="训练过程曲线",
                        description="模型训练过程中的指标变化",
                        data_requirements=["epochs", "training_loss", "validation_accuracy"],
                        recommended_tool="matplotlib",
                        best_practices=[
                            "区分训练和验证曲线",
                            "使用不同线型和颜色",
                            "添加网格线",
                            "标注关键点"
                        ]
                    )
                    charts.append(chart)
        
        # 添加脑启发特色图表
        brain_specs = viz_requirements.get("brain_inspired_specifics", [])
        for spec in brain_specs:
            viz_type = spec.get("visualization_type", "")
            if viz_type in self.brain_inspired_charts:
                chart_info = self.brain_inspired_charts[viz_type]
                chart = VisualizationChart(
                    chart_type=viz_type,
                    title=chart_info["description"],
                    description=f"展示{chart_info['description']}的详细信息",
                    data_requirements=chart_info["use_cases"],
                    recommended_tool=chart_info["recommended_tools"][0],
                    best_practices=[
                        "确保生物合理性",
                        "突出关键特征",
                        "提供清晰的说明"
                    ]
                )
                charts.append(chart)
        
        # 如果没有图表，添加默认图表
        if not charts:
            charts.append(VisualizationChart(
                chart_type="bar",
                title="基准对比",
                description="不同方法的性能对比",
                data_requirements=["methods", "scores"],
                recommended_tool="matplotlib"
            ))
        
        return charts
    
    def _generate_visualization_code(self, charts: List[VisualizationChart], 
                                   tools: List[str]) -> Dict[str, str]:
        """生成可视化代码模板"""
        
        code_templates = {}
        
        # 基础设置模板
        code_templates["setup"] = """
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

# 颜色配置
colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
"""
        
        # 为每种图表类型生成代码
        for chart in charts:
            if chart.chart_type == "bar":
                code_templates[f"{chart.chart_type}_chart"] = """
def create_performance_comparison(methods, scores, title="性能对比"):
    plt.figure(figsize=(10, 6))
    bars = plt.bar(methods, scores, color=colors[:len(methods)])
    
    plt.title(title, fontsize=14, fontweight='bold')
    plt.xlabel('方法', fontsize=12)
    plt.ylabel('性能分数', fontsize=12)
    
    # 添加数值标签
    for bar, score in zip(bars, scores):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{score:.3f}', ha='center', va='bottom')
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.grid(axis='y', alpha=0.3)
    plt.show()
"""
            
            elif chart.chart_type == "line":
                code_templates[f"{chart.chart_type}_chart"] = """
def create_training_curves(epochs, train_loss, val_loss, train_acc, val_acc):
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # 损失曲线
    ax1.plot(epochs, train_loss, label='训练损失', color=colors[0])
    ax1.plot(epochs, val_loss, label='验证损失', color=colors[1])
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title('训练损失曲线')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 准确率曲线
    ax2.plot(epochs, train_acc, label='训练准确率', color=colors[0])
    ax2.plot(epochs, val_acc, label='验证准确率', color=colors[1])
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Accuracy')
    ax2.set_title('准确率曲线')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
"""
        
        # 混淆矩阵模板
        if "seaborn" in tools:
            code_templates["confusion_matrix"] = """
def create_confusion_matrix(y_true, y_pred, class_names):
    from sklearn.metrics import confusion_matrix
    
    cm = confusion_matrix(y_true, y_pred)
    plt.figure(figsize=(8, 6))
    
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=class_names, yticklabels=class_names)
    
    plt.title('混淆矩阵', fontsize=14, fontweight='bold')
    plt.xlabel('预测标签', fontsize=12)
    plt.ylabel('真实标签', fontsize=12)
    plt.tight_layout()
    plt.show()
"""
        
        # 脑启发特色图表
        code_templates["neural_architecture"] = """
def visualize_neural_architecture(layer_sizes, layer_names):
    import networkx as nx
    
    G = nx.DiGraph()
    pos = {}
    
    # 添加节点和位置
    y_positions = np.linspace(0, 1, len(layer_sizes))
    for i, (size, name) in enumerate(zip(layer_sizes, layer_names)):
        x_positions = np.linspace(0, 1, size)
        for j in range(size):
            node_id = f"{i}_{j}"
            G.add_node(node_id, layer=i, neuron=j)
            pos[node_id] = (i, x_positions[j])
    
    # 添加连接（简化版本）
    for i in range(len(layer_sizes) - 1):
        for j in range(layer_sizes[i]):
            for k in range(layer_sizes[i + 1]):
                G.add_edge(f"{i}_{j}", f"{i + 1}_{k}")
    
    plt.figure(figsize=(12, 8))
    nx.draw(G, pos, node_size=50, node_color='lightblue',
            edge_color='gray', arrows=True, alpha=0.7)
    
    plt.title('神经网络架构图', fontsize=14, fontweight='bold')
    plt.axis('off')
    plt.tight_layout()
    plt.show()
"""
        
        return code_templates
    
    def _adapt_to_publication_standards(self, target_venue: str) -> Dict[str, Any]:
        """适配期刊出版标准"""
        
        venue_upper = target_venue.upper()
        
        if venue_upper in self.publication_standards:
            return self.publication_standards[venue_upper]
        else:
            # 返回通用标准
            return {
                "figure_format": ["PDF", "PNG"],
                "color_requirements": "彩色和黑白都要清晰",
                "font_size": "10-12pt",
                "line_width": "清晰可见",
                "figure_numbering": "Figure 1, Figure 2, ..."
            }
    
    def _establish_design_principles(self) -> List[str]:
        """制定设计原则"""
        
        return [
            "简洁明了：避免不必要的装饰元素",
            "色彩一致：使用统一的颜色方案",
            "标签清晰：所有轴和图例都要有清楚的标签",
            "比例合适：确保图表比例协调",
            "错误信息：适当显示误差棒或置信区间",
            "可读性优先：确保打印后仍然清晰",
            "科学严谨：准确反映数据特征",
            "视觉层次：突出重要信息"
        ]
    
    def _recommend_color_schemes(self) -> List[str]:
        """推荐颜色方案"""
        
        return [
            "科学期刊经典：蓝色系为主 ['#1f77b4', '#ff7f0e', '#2ca02c']",
            "色盲友好方案：['#0173b2', '#de8f05', '#029e73', '#cc78bc']",
            "灰度兼容方案：['#252525', '#636363', '#969696', '#cccccc']",
            "Nature风格：深色为主 ['#1b9e77', '#d95f02', '#7570b3']",
            "现代简约风格：['#3498db', '#e74c3c', '#2ecc71', '#f39c12']"
        ]
    
    def _suggest_layout_arrangements(self) -> Dict[str, Any]:
        """建议布局安排"""
        
        return {
            "single_figure": {
                "size": "(8, 6)",
                "dpi": "300",
                "margins": "tight_layout()",
                "description": "单个图表的标准布局"
            },
            "subplot_2x1": {
                "size": "(12, 5)",
                "arrangement": "plt.subplots(1, 2)",
                "spacing": "plt.subplots_adjust(wspace=0.3)",
                "description": "水平排列的两个子图"
            },
            "subplot_2x2": {
                "size": "(10, 8)",
                "arrangement": "plt.subplots(2, 2)",
                "spacing": "plt.subplots_adjust(hspace=0.3, wspace=0.3)",
                "description": "2x2网格布局"
            },
            "complex_layout": {
                "tool": "matplotlib.gridspec",
                "description": "复杂不规则布局",
                "example": "GridSpec for custom arrangements"
            }
        }
    
    def _compare_tools(self, recommended_tools: List[str]) -> Dict[str, Any]:
        """对比推荐的工具"""
        
        comparison = {}
        for tool in recommended_tools:
            if tool in self.visualization_tools:
                comparison[tool] = self.visualization_tools[tool]
        
        return comparison
    
    def generate_visualization_guide(self, visualization_plan: VisualizationPlan) -> str:
        """生成可视化指南"""
        
        guide = f"""
# 可视化设计指南

## 推荐工具对比

{chr(10).join(f"### {tool}" + chr(10) + f"**优势**: {', '.join(info.get('strengths', []))}" + chr(10) + f"**适用场景**: {', '.join(info.get('best_for', []))}" + chr(10) + f"**学习难度**: {info.get('learning_curve', 'unknown')}" + chr(10) for tool, info in visualization_plan.tool_comparison.items())}

## 图表设计方案

{chr(10).join(f"### {chart.title}" + chr(10) + f"**类型**: {chart.chart_type}" + chr(10) + f"**描述**: {chart.description}" + chr(10) + f"**推荐工具**: {chart.recommended_tool}" + chr(10) + f"**数据需求**: {', '.join(chart.data_requirements)}" + chr(10) + f"**最佳实践**: {chr(10).join(f'- {practice}' for practice in chart.best_practices)}" + chr(10) for chart in visualization_plan.charts)}

## 设计原则

{chr(10).join(f"- {principle}" for principle in visualization_plan.design_principles)}

## 颜色方案建议

{chr(10).join(f"- {scheme}" for scheme in visualization_plan.color_schemes)}

## 布局建议

{json.dumps(visualization_plan.layout_suggestions, ensure_ascii=False, indent=2)}

## 期刊要求

{json.dumps(visualization_plan.journal_requirements, ensure_ascii=False, indent=2)}

## 代码模板

### 基础设置
```python
{visualization_plan.code_templates.get('setup', '# 基础设置代码')}
```

### 性能对比图
```python
{visualization_plan.code_templates.get('bar_chart', '# 柱状图代码')}
```

### 训练曲线
```python
{visualization_plan.code_templates.get('line_chart', '# 线图代码')}
```

### 混淆矩阵
```python
{visualization_plan.code_templates.get('confusion_matrix', '# 混淆矩阵代码')}
```

## 实施建议

1. **工具选择**: 优先使用 {', '.join(visualization_plan.recommended_tools[:3])}
2. **质量检查**: 确保所有图表在打印后仍然清晰
3. **一致性**: 保持整篇论文的视觉风格一致
4. **可读性**: 测试图表在不同设备上的显示效果
5. **备份方案**: 准备黑白版本以防彩色打印问题

## 常见问题解决

1. **字体问题**: 使用系统支持的字体，避免特殊字符
2. **分辨率问题**: 设置至少300 DPI用于出版
3. **颜色问题**: 测试色盲友好性和灰度转换效果
4. **尺寸问题**: 确保图表在论文中的适当大小
        """
        
        return guide.strip()


# 测试函数
def test_visualization_advisor():
    """测试可视化建议生成器"""
    
    print("🧪 测试可视化建议生成器")
    
    # 创建测试数据
    from reasoning.data_models import ExperimentVariable
    
    test_experiment_plan = ExperimentPlan(
        research_question="如何设计一种基于脑神经可塑性的自适应神经网络架构？",
        hypothesis=["自适应架构能够提高学习效率"],
        experiment_type="controlled_experiment",
        design={},
        methodology={},
        variables=[
            ExperimentVariable("模型架构", "independent", "不同架构", ["CNN", "自适应CNN"], "架构对比")
        ],
        metrics=["accuracy", "training_time", "adaptability"]
    )
    
    test_implementation_plan = ImplementationPlan(
        experiment_plan_id="test",
        programming_language="Python",
        frameworks=["pytorch"],
        libraries=["torch", "matplotlib", "seaborn"]
    )
    
    # 创建可视化建议器并设计方案
    advisor = VisualizationAdvisor()
    visualization_plan = advisor.design_visualization_plan(
        test_experiment_plan, test_implementation_plan, "ICML"
    )
    
    # 生成指南
    guide = advisor.generate_visualization_guide(visualization_plan)
    print(f"\n📋 可视化指南:")
    print(guide)
    
    return visualization_plan


if __name__ == "__main__":
    test_visualization_advisor()
