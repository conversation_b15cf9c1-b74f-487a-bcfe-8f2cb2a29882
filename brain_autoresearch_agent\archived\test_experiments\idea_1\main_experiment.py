"""
Default Classification Experiment

实验目标: The proposed method will outperform baseline methods
实验类型: classification
使用框架: pytorch
数据集: iris
评估指标: accuracy, precision, recall, f1

这是一个自动生成的实验代码，基于Brain AutoResearch Agent的实验代码生成器。
"""


import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import numpy as np
from sklearn.datasets import load_iris
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler

class Default_ExperimentDataset(Dataset):
    def __init__(self, X, y, transform=None):
        self.X = torch.FloatTensor(X)
        self.y = torch.LongTensor(y) if spec.experiment_type == 'classification' else torch.FloatTensor(y)
        self.transform = transform
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        x = self.X[idx]
        y = self.y[idx]
        
        if self.transform:
            x = self.transform(x)
        
        return x, y

def load_data():
    """加载和预处理数据"""
    # 加载数据集
    X, y = load_iris()
    
    # 数据预处理
    scaler = StandardScaler()
    X = scaler.fit_transform(X)
    
    # 分割数据集
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )
    
    # 创建数据集和数据加载器
    train_dataset = Default_ExperimentDataset(X_train, y_train)
    val_dataset = Default_ExperimentDataset(X_val, y_val)
    test_dataset = Default_ExperimentDataset(X_test, y_test)
    
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    return train_loader, val_loader, test_loader, X_train.shape[1]



class Default_ExperimentModel(nn.Module):
    def __init__(self, input_dim, hidden_dim=128, num_classes=10):
        super(Default_ExperimentModel, self).__init__()
        
        # 基础网络结构
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # 创新改进模块 - Enhanced Neural Network with Attention
        self.innovation_module = self._build_innovation_module(hidden_dim)
        
        # 分类头
        self.classifier = nn.Linear(hidden_dim, num_classes)
    
    def _build_innovation_module(self, hidden_dim):
        """构建创新模块 - Enhanced Neural Network with Attention"""
        # 这里实现您的创新方法
        return nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.ReLU(),
        )
    
    def forward(self, x):
        features = self.feature_extractor(x)
        enhanced_features = self.innovation_module(features)
        outputs = self.classifier(enhanced_features)
        return outputs

# 基准模型对比
class BaselineModel(nn.Module):
    def __init__(self, input_dim, hidden_dim=128, num_classes=10):
        super(BaselineModel, self).__init__()
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, num_classes)
        )
    
    def forward(self, x):
        return self.network(x)



def train_model(model, train_loader, val_loader, num_epochs=100, device='cuda'):
    """训练模型"""
    criterion = nn.CrossEntropyLoss() if spec.experiment_type == 'classification' else nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10)
    
    model.to(device)
    best_val_loss = float('inf')
    patience_counter = 0
    early_stopping_patience = 20
    
    train_losses = []
    val_losses = []
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(device), target.to(device)
            
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(device), target.to(device)
                output = model(data)
                val_loss += criterion(output, target).item()
        
        train_loss /= len(train_loader)
        val_loss /= len(val_loader)
        
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        
        scheduler.step(val_loss)
        
        # 早停机制
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            # 保存最佳模型
            torch.save(model.state_dict(), f'default_experiment_best_model.pth')
        else:
            patience_counter += 1
        
        if patience_counter >= early_stopping_patience:
            print(f"早停在第 {epoch+1} 轮")
            break
        
        if (epoch + 1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{num_epochs}], Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}')
    
    return train_losses, val_losses



from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns

def evaluate_model(model, test_loader, device='cuda'):
    """评估分类模型"""
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            predictions = torch.argmax(output, dim=1)
            
            all_predictions.extend(predictions.cpu().numpy())
            all_targets.extend(target.cpu().numpy())
    
    # 计算指标
    accuracy = accuracy_score(all_targets, all_predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(all_targets, all_predictions, average='weighted')
    
    print(f"测试结果:")
    print(f"准确率: {accuracy:.4f}")
    print(f"精确率: {precision:.4f}")
    print(f"召回率: {recall:.4f}")
    print(f"F1分数: {f1:.4f}")
    
    # 绘制混淆矩阵
    cm = confusion_matrix(all_targets, all_predictions)
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
    plt.title('混淆矩阵')
    plt.ylabel('实际类别')
    plt.xlabel('预测类别')
    plt.savefig(f'default_experiment_confusion_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1': f1,
        'predictions': all_predictions,
        'targets': all_targets
    }

def compare_with_baselines(results_dict):
    """与基准方法对比"""
    methods = list(results_dict.keys())
    metrics = ['accuracy', 'precision', 'recall', 'f1']
    
    # 创建对比图
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.flatten()
    
    for i, metric in enumerate(metrics):
        values = [results_dict[method][metric] for method in methods]
        axes[i].bar(methods, values)
        axes[i].set_title(f'{metric.title()} 对比')
        axes[i].set_ylabel(metric.title())
        axes[i].tick_params(axis='x', rotation=45)
        
        # 标注数值
        for j, v in enumerate(values):
            axes[i].text(j, v + 0.01, f'{v:.3f}', ha='center')
    
    plt.tight_layout()
    plt.savefig(f'default_experiment_baseline_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()


def main():
    """主实验流程"""
    print("🚀 开始实验: Default Classification Experiment")
    print("=" * 60)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 加载数据
    print("📊 加载数据...")
    train_loader, val_loader, test_loader, input_dim = load_data()
    print(f"输入维度: {input_dim}")
    
    # 初始化模型
    print("🧠 初始化模型...")
    if spec.experiment_type == 'classification':
        num_classes = len(set([y for _, y in train_loader.dataset]))
        model = Default_ExperimentModel(input_dim, num_classes=num_classes)
        baseline_model = BaselineModel(input_dim, num_classes=num_classes)
    else:
        model = Default_ExperimentModel(input_dim)
        baseline_model = BaselineModel(input_dim)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 训练提出的方法
    print("🔥 训练提出的方法...")
    train_losses, val_losses = train_model(model, train_loader, val_loader, device=device)
    
    # 训练基准方法
    print("📈 训练基准方法...")
    baseline_train_losses, baseline_val_losses = train_model(baseline_model, train_loader, val_loader, device=device)
    
    # 加载最佳模型
    model.load_state_dict(torch.load(f'default_experiment_best_model.pth'))
    
    # 评估模型
    print("📊 评估模型...")
    results = evaluate_model(model, test_loader, device=device)
    baseline_results = evaluate_model(baseline_model, test_loader, device=device)
    
    # 对比结果
    print("📊 结果对比:")
    comparison_results = {
        'Enhanced Neural Network with Attention': results,
        'Baseline': baseline_results
    }
    
    compare_with_baselines(comparison_results)
    
    # 保存结果
    results_summary = {
        'experiment_name': spec.name,
        'experiment_title': spec.title,
        'hypothesis': spec.hypothesis,
        'proposed_method_results': results,
        'baseline_results': baseline_results,
        'improvement': {
            metric: results[metric] - baseline_results[metric] 
            for metric in results.keys() if metric in baseline_results
        }
    }
    
    with open(f'default_experiment_results.json', 'w', encoding='utf-8') as f:
        json.dump(results_summary, f, indent=2, ensure_ascii=False)
    
    print("✅ 实验完成！结果已保存。")
    return results_summary

if __name__ == "__main__":
    results = main()
