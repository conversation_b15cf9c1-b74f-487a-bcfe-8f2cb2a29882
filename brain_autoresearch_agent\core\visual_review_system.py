"""
Visual Review System
Integrates Qwen vision model for paper layout and visual quality assessment
"""

import os
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image, ImageDraw, ImageFont
import io
import base64

from core.hybrid_model_client import get_hybrid_client, ModelType


@dataclass
class VisualAnalysis:
    """视觉分析结果"""
    layout_score: float
    readability_score: float
    visual_appeal: float
    structure_clarity: float
    recommendations: List[str]
    issues_found: List[str]
    overall_score: float


class VisualReviewSystem:
    """视觉评估系统"""
    
    def __init__(self):
        """初始化视觉评估系统"""
        self.hybrid_client = get_hybrid_client()
        self.output_dir = "output/visual_analysis"
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        print("✅ Visual Review System initialized")
    
    def create_paper_preview(self, paper_data: Dict[str, Any], 
                           format_type: str = "academic") -> str:
        """创建论文预览图"""
        try:
            # 创建论文布局预览
            fig, ax = plt.subplots(figsize=(8.5, 11))  # A4 proportions
            ax.set_xlim(0, 100)
            ax.set_ylim(0, 130)
            ax.axis('off')
            
            # 背景
            ax.add_patch(patches.Rectangle((5, 5), 90, 120, 
                                         linewidth=1, edgecolor='black', 
                                         facecolor='white'))
            
            # 标题区域
            title = paper_data.get('title', 'Research Paper Title')
            ax.text(50, 115, title, fontsize=14, weight='bold', 
                   ha='center', va='center', wrap=True)
            
            # 作者区域
            authors = ', '.join(paper_data.get('authors', ['Author Name']))
            ax.text(50, 110, authors, fontsize=10, ha='center', va='center')
            
            # 摘要区域
            ax.add_patch(patches.Rectangle((10, 95), 80, 12, 
                                         linewidth=0.5, edgecolor='gray', 
                                         facecolor='lightgray', alpha=0.3))
            ax.text(50, 101, 'Abstract', fontsize=12, weight='bold', 
                   ha='center', va='center')
            
            abstract = paper_data.get('abstract', '')[:200] + "..."
            ax.text(12, 98, abstract, fontsize=8, ha='left', va='top', wrap=True)
            
            # 章节预览
            sections = paper_data.get('sections', {})
            y_pos = 90
            
            for i, (section_key, section_data) in enumerate(sections.items()):
                if i >= 6:  # 限制显示章节数
                    break
                
                section_title = section_data.get('title', section_key.title())
                
                # 章节标题
                ax.text(12, y_pos, f"{i+1}. {section_title}", 
                       fontsize=10, weight='bold', ha='left', va='center')
                
                # 章节内容预览
                content = section_data.get('content', '')[:150] + "..."
                ax.text(15, y_pos-3, content, fontsize=7, ha='left', va='top', 
                       wrap=True)
                
                y_pos -= 12
            
            # 参考文献区域
            ref_count = len(paper_data.get('references', []))
            ax.text(12, y_pos-5, f"References ({ref_count} items)", 
                   fontsize=10, weight='bold', ha='left', va='center')
            
            # 保存预览图
            preview_path = os.path.join(self.output_dir, 'paper_preview.png')
            plt.savefig(preview_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ 论文预览图已生成: {preview_path}")
            return preview_path
            
        except Exception as e:
            print(f"❌ 预览图生成失败: {e}")
            return None
    
    def analyze_paper_layout(self, paper_data: Dict[str, Any], 
                           preview_image_path: str = None) -> VisualAnalysis:
        """分析论文布局"""
        try:
            # 创建预览图（如果没有提供）
            if not preview_image_path:
                preview_image_path = self.create_paper_preview(paper_data)
            
            if not preview_image_path or not os.path.exists(preview_image_path):
                return self._create_fallback_analysis(paper_data)
            
            # 使用视觉模型分析
            analysis_prompt = f"""
            Analyze this academic paper layout and visual presentation. The paper is titled "{paper_data.get('title', 'Research Paper')}" and contains {len(paper_data.get('sections', {}))} main sections.
            
            Please evaluate:
            1. Layout Organization (1-10): How well-structured is the visual layout?
            2. Readability (1-10): How easy is it to read and follow?
            3. Visual Appeal (1-10): How professional and appealing does it look?
            4. Structure Clarity (1-10): How clear is the information hierarchy?
            
            Also provide:
            - Specific recommendations for improvement
            - Any layout issues or problems identified
            - Overall assessment score (1-10)
            
            Format your response as:
            Layout Score: X/10
            Readability Score: X/10
            Visual Appeal: X/10
            Structure Clarity: X/10
            
            Recommendations:
            - [recommendation 1]
            - [recommendation 2]
            
            Issues Found:
            - [issue 1]
            - [issue 2]
            
            Overall Score: X/10
            """
            
            # 使用Qwen视觉模型分析
            analysis_response = self.hybrid_client.analyze_visual_layout(
                image_path=preview_image_path,
                analysis_prompt=analysis_prompt
            )
            
            # 解析分析结果
            visual_analysis = self._parse_visual_analysis(analysis_response, paper_data)
            
            print(f"✅ 视觉分析完成: {visual_analysis.overall_score:.1f}/10")
            return visual_analysis
            
        except Exception as e:
            print(f"❌ 视觉分析失败: {e}")
            return self._create_fallback_analysis(paper_data)
    
    def _parse_visual_analysis(self, response: str, paper_data: Dict[str, Any]) -> VisualAnalysis:
        """解析视觉分析响应"""
        import re
        
        # 提取分数
        layout_score = self._extract_score(response, "layout")
        readability_score = self._extract_score(response, "readability")
        visual_appeal = self._extract_score(response, "visual appeal")
        structure_clarity = self._extract_score(response, "structure clarity")
        overall_score = self._extract_score(response, "overall")
        
        # 提取建议
        recommendations = self._extract_list_items(response, "recommendations")
        issues_found = self._extract_list_items(response, "issues found")
        
        # 计算总分（如果没有提供）
        if overall_score == 0:
            overall_score = (layout_score + readability_score + visual_appeal + structure_clarity) / 4
        
        return VisualAnalysis(
            layout_score=layout_score,
            readability_score=readability_score,
            visual_appeal=visual_appeal,
            structure_clarity=structure_clarity,
            recommendations=recommendations,
            issues_found=issues_found,
            overall_score=overall_score
        )
    
    def _extract_score(self, text: str, score_type: str) -> float:
        """提取分数"""
        import re
        pattern = f"{score_type}.*?([0-9\.]+)"
        match = re.search(pattern, text.lower())
        if match:
            try:
                return float(match.group(1))
            except:
                pass
        return 6.0  # 默认分数
    
    def _extract_list_items(self, text: str, section_name: str) -> List[str]:
        """提取列表项"""
        lines = text.split('\\n')
        items = []
        in_section = False
        
        for line in lines:
            line = line.strip()
            if section_name.lower() in line.lower():
                in_section = True
                continue
            
            if in_section:
                if line.startswith('-') or line.startswith('•'):
                    items.append(line[1:].strip())
                elif line and not line.startswith(' ') and ':' in line:
                    break  # 下一个部分开始
        
        return items
    
    def _create_fallback_analysis(self, paper_data: Dict[str, Any]) -> VisualAnalysis:
        """创建回退分析结果"""
        # 基于内容结构进行简单分析
        sections_count = len(paper_data.get('sections', {}))
        abstract_length = len(paper_data.get('abstract', ''))
        ref_count = len(paper_data.get('references', []))
        
        # 简单评分逻辑
        layout_score = min(8.0, 5.0 + sections_count * 0.3)
        readability_score = 7.0 if abstract_length > 100 else 5.0
        visual_appeal = 6.5  # 中等分数
        structure_clarity = min(8.0, 6.0 + (sections_count / 10))
        
        overall_score = (layout_score + readability_score + visual_appeal + structure_clarity) / 4
        
        recommendations = [
            "Consider adding visual elements like figures and tables",
            "Ensure consistent formatting throughout the document",
            "Optimize section spacing and hierarchy"
        ]
        
        issues_found = [
            "Limited visual analysis available without image processing",
            "Manual layout review recommended"
        ]
        
        return VisualAnalysis(
            layout_score=layout_score,
            readability_score=readability_score,
            visual_appeal=visual_appeal,
            structure_clarity=structure_clarity,
            recommendations=recommendations,
            issues_found=issues_found,
            overall_score=overall_score
        )
    
    def generate_visual_improvement_suggestions(self, analysis: VisualAnalysis,
                                              paper_data: Dict[str, Any]) -> Dict[str, List[str]]:
        """生成视觉改进建议"""
        suggestions_prompt = f"""
        Based on this visual analysis of an academic paper, provide specific improvement suggestions:
        
        Current Scores:
        - Layout: {analysis.layout_score}/10
        - Readability: {analysis.readability_score}/10
        - Visual Appeal: {analysis.visual_appeal}/10
        - Structure Clarity: {analysis.structure_clarity}/10
        - Overall: {analysis.overall_score}/10
        
        Current Issues:
        {chr(10).join(f'- {issue}' for issue in analysis.issues_found)}
        
        Paper has {len(paper_data.get('sections', {}))} sections and {len(paper_data.get('references', []))} references.
        
        Provide specific suggestions in these categories:
        1. Layout Improvements
        2. Typography and Formatting
        3. Content Organization
        4. Visual Elements
        5. Academic Presentation
        
        Each category should have 3-5 actionable suggestions.
        """
        
        try:
            response = self.hybrid_client.generate_text(
                prompt=suggestions_prompt,
                task_type=ModelType.ACADEMIC_WRITING,
                system_message="You are an expert in academic document design and visual presentation.",
                temperature=0.6
            )
            
            # 解析建议
            suggestions = self._parse_improvement_suggestions(response)
            
            return suggestions
            
        except Exception as e:
            print(f"❌ 改进建议生成失败: {e}")
            return self._create_default_suggestions()
    
    def _parse_improvement_suggestions(self, response: str) -> Dict[str, List[str]]:
        """解析改进建议"""
        categories = {
            "Layout Improvements": [],
            "Typography and Formatting": [],
            "Content Organization": [],
            "Visual Elements": [],
            "Academic Presentation": []
        }
        
        lines = response.split('\\n')
        current_category = None
        
        for line in lines:
            line = line.strip()
            
            # 检查是否是分类标题
            for category in categories.keys():
                if category.lower() in line.lower():
                    current_category = category
                    break
            
            # 添加建议项
            if current_category and (line.startswith('-') or line.startswith('•')):
                suggestion = line[1:].strip()
                if suggestion:
                    categories[current_category].append(suggestion)
        
        return categories
    
    def _create_default_suggestions(self) -> Dict[str, List[str]]:
        """创建默认改进建议"""
        return {
            "Layout Improvements": [
                "Use consistent margins and spacing throughout",
                "Implement proper section hierarchy with clear headings",
                "Balance text density across pages"
            ],
            "Typography and Formatting": [
                "Ensure consistent font sizes and styles",
                "Use proper line spacing for readability",
                "Implement clear citation formatting"
            ],
            "Content Organization": [
                "Organize sections in logical flow",
                "Use clear subsection structure",
                "Ensure smooth transitions between sections"
            ],
            "Visual Elements": [
                "Add relevant figures and diagrams",
                "Include tables for data presentation",
                "Use consistent figure and table formatting"
            ],
            "Academic Presentation": [
                "Follow standard academic paper format",
                "Ensure proper citation style",
                "Include complete reference list"
            ]
        }
    
    def create_visual_report(self, analysis: VisualAnalysis, 
                           suggestions: Dict[str, List[str]],
                           paper_data: Dict[str, Any]) -> str:
        """创建视觉评估报告"""
        try:
            # 创建综合报告
            report = {
                "visual_analysis": {
                    "layout_score": analysis.layout_score,
                    "readability_score": analysis.readability_score,
                    "visual_appeal": analysis.visual_appeal,
                    "structure_clarity": analysis.structure_clarity,
                    "overall_score": analysis.overall_score,
                    "recommendations": analysis.recommendations,
                    "issues_found": analysis.issues_found
                },
                "improvement_suggestions": suggestions,
                "paper_info": {
                    "title": paper_data.get('title', ''),
                    "sections_count": len(paper_data.get('sections', {})),
                    "references_count": len(paper_data.get('references', [])),
                    "abstract_length": len(paper_data.get('abstract', ''))
                },
                "analysis_metadata": {
                    "timestamp": __import__('datetime').datetime.now().isoformat(),
                    "analyzer": "Visual Review System v1.0"
                }
            }
            
            # 保存报告
            from datetime import datetime
            report_filename = f"visual_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            report_path = os.path.join(self.output_dir, report_filename)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 视觉评估报告已保存: {report_path}")
            return report_path
            
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")
            return None
    
    def get_visual_quality_score(self, paper_data: Dict[str, Any]) -> float:
        """获取视觉质量分数"""
        try:
            analysis = self.analyze_paper_layout(paper_data)
            return analysis.overall_score
        except Exception as e:
            print(f"❌ 视觉质量评估失败: {e}")
            return 6.0


if __name__ == "__main__":
    # 测试视觉评估系统
    print("🧪 测试视觉评估系统...")
    
    # 创建测试数据
    test_paper = {
        "title": "Neural Plasticity-Inspired Deep Learning Architecture",
        "authors": ["AI Research Team"],
        "abstract": "This paper presents a novel approach to deep learning inspired by neural plasticity mechanisms found in biological neural networks. Our method incorporates adaptive learning rules that mimic synaptic plasticity, leading to improved performance on various machine learning tasks.",
        "sections": {
            "introduction": {
                "title": "Introduction",
                "content": "Neural plasticity has long been recognized as a fundamental mechanism underlying learning and adaptation in biological neural networks..."
            },
            "methodology": {
                "title": "Methodology", 
                "content": "Our approach is based on the implementation of spike-timing dependent plasticity (STDP) rules in artificial neural networks..."
            },
            "experiments": {
                "title": "Experiments",
                "content": "We conducted extensive experiments on multiple datasets to evaluate the performance of our proposed method..."
            }
        },
        "references": [
            {"id": 1, "citation": "LeCun, Y., et al. (2015). Deep learning. Nature."},
            {"id": 2, "citation": "Goodfellow, I., et al. (2016). Deep Learning. MIT Press."}
        ]
    }
    
    # 初始化系统
    visual_system = VisualReviewSystem()
    
    # 测试预览生成
    print("\\n📊 测试预览生成...")
    preview_path = visual_system.create_paper_preview(test_paper)
    
    # 测试视觉分析
    print("\\n🔍 测试视觉分析...")
    analysis = visual_system.analyze_paper_layout(test_paper, preview_path)
    
    print(f"📊 分析结果:")
    print(f"  Layout: {analysis.layout_score:.1f}/10")
    print(f"  Readability: {analysis.readability_score:.1f}/10")
    print(f"  Visual Appeal: {analysis.visual_appeal:.1f}/10")
    print(f"  Structure Clarity: {analysis.structure_clarity:.1f}/10")
    print(f"  Overall: {analysis.overall_score:.1f}/10")
    
    # 测试改进建议
    print("\\n💡 测试改进建议...")
    suggestions = visual_system.generate_visual_improvement_suggestions(analysis, test_paper)
    
    for category, items in suggestions.items():
        print(f"\\n{category}:")
        for item in items[:3]:  # 显示前3个建议
            print(f"  - {item}")
    
    # 测试报告生成
    print("\\n📋 测试报告生成...")
    report_path = visual_system.create_visual_report(analysis, suggestions, test_paper)
    
    print(f"\\n🎉 视觉评估系统测试完成!")
    print(f"📁 输出目录: {visual_system.output_dir}")
    if report_path:
        print(f"📊 报告文件: {report_path}")
