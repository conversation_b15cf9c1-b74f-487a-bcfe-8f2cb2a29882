"""
基于AI Scientist v2架构的工具基类
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List


class BaseTool(ABC):
    """
    工具基类，参考AI Scientist v2的设计
    
    Attributes:
    -----------
    - name (str): 工具名称
    - description (str): 工具描述
    - parameters (list): 工具参数列表
    """

    def __init__(self, name: str, description: str, parameters: List[Dict[str, Any]]):
        self.name = name
        self.description = description
        self.parameters = parameters

    @abstractmethod
    def use_tool(self, **kwargs) -> Any:
        """工具使用的抽象方法，子类必须实现"""
        pass

    def get_info(self) -> Dict[str, Any]:
        """获取工具信息"""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters
        }
