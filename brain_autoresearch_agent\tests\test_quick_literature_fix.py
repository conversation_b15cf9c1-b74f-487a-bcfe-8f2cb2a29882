#!/usr/bin/env python3
"""
快速文献管理器修复验证
专门测试搜索结果显示的修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_literature_display_fix():
    """测试文献搜索结果显示修复"""
    print("🔧 文献管理器显示修复测试")
    print("="*40)
    
    try:
        from core.enhanced_literature_manager import EnhancedLiteratureManager
        from core.unified_api_client import UnifiedAPIClient
        
        # 创建客户端和管理器
        api_client = UnifiedAPIClient()
        manager = EnhancedLiteratureManager(api_client)
        print("✅ 文献管理器初始化成功")
        
        # 执行搜索
        print("\n🔍 执行搜索测试...")
        search_results = manager.search_literature(
            query="neural networks",
            max_papers=3,
            sources=['arxiv']
        )
        print(f"✅ 搜索完成: 找到 {len(search_results.papers)} 篇论文")
        
        # 测试结果显示（这里是之前出错的地方）
        print(f"\n📚 搜索结果样例:")
        for i, paper in enumerate(search_results.papers[:2], 1):
            try:
                print(f"  {i}. {paper.title[:50]}...")
                
                # 这里是修复的关键部分
                if hasattr(paper, 'authors') and paper.authors:
                    if isinstance(paper.authors, list):
                        # 处理字符串列表
                        if paper.authors and isinstance(paper.authors[0], str):
                            authors_str = ', '.join(paper.authors[:2])
                        # 处理字典列表
                        elif paper.authors and isinstance(paper.authors[0], dict):
                            author_names = []
                            for author in paper.authors[:2]:
                                if isinstance(author, dict) and 'name' in author:
                                    author_names.append(author['name'])
                                elif isinstance(author, dict):
                                    # 尝试其他可能的字段
                                    author_names.append(author.get('name', str(author)))
                            authors_str = ', '.join(author_names) if author_names else 'Unknown'
                        else:
                            authors_str = str(paper.authors[0]) if paper.authors else 'Unknown'
                    else:
                        authors_str = str(paper.authors)
                else:
                    authors_str = 'Unknown'
                
                print(f"     作者: {authors_str}")
                print(f"     年份: {getattr(paper, 'year', 'Unknown')}")
                print()
                
            except Exception as e:
                print(f"  ❌ 处理论文 {i} 时出错: {e}")
                # 但是继续处理下一个
                continue
        
        print("✅ 文献显示修复测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    success = test_literature_display_fix()
    
    print("\n" + "="*40)
    print("📊 修复测试结果")
    print("="*40)
    
    if success:
        print("✅ 文献管理器显示修复成功!")
        print("🎯 可以重新运行完整测试了")
        print("\n💡 建议运行:")
        print("python tests/test_stage1_literature_workflow_complete.py")
    else:
        print("❌ 修复测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
