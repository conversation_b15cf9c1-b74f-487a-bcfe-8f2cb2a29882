"""
Enhanced Stage 4 Test with AI Scientist v2 Features
Tests the improved paper generation system with quality optimization
"""

import os
import sys
import time
import json
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from paper_generation.enhanced_paper_writer import EnhancedPaperWriter
from core.hybrid_model_client import get_hybrid_client


def test_enhanced_stage4():
    """测试增强版Stage 4系统"""
    print("🚀 启动增强版Stage 4测试...")
    print("=" * 60)
    
    start_time = time.time()
    
    try:
        # 1. 初始化系统
        print("\\n📋 Step 1: 系统初始化")
        print("-" * 30)
        
        hybrid_client = get_hybrid_client()
        print(f"✅ 混合模型客户端: {hybrid_client}")
        
        writer = EnhancedPaperWriter()
        print(f"✅ 增强论文写作器已初始化")
        
        # 检查模型状态
        model_status = hybrid_client.get_model_status()
        print(f"\\n📊 模型状态检查:")
        for model_id, is_active in model_status.items():
            status_icon = "✅" if is_active else "❌"
            print(f"  {status_icon} {model_id}: {'Active' if is_active else 'Inactive'}")
        
        active_models = sum(1 for status in model_status.values() if status)
        if active_models < 2:
            print(f"⚠️ 警告: 只有 {active_models} 个模型可用，可能影响性能")
        
        # 2. 测试主题和参数
        print("\\n📝 Step 2: 测试配置")
        print("-" * 30)
        
        test_cases = [
            {
                "name": "神经可塑性深度学习",
                "topic": "Neural Plasticity-Inspired Deep Learning Architecture",
                "research_focus": "Biologically-inspired adaptive learning mechanisms for improved neural network performance",
                "methodology": "Spike-timing dependent plasticity algorithms with meta-learning optimization",
                "expected_quality": 7.5
            },
            {
                "name": "多模态AI系统",
                "topic": "Multimodal AI Systems with Cross-Modal Attention",
                "research_focus": "Unified architecture for vision-language understanding and generation",
                "methodology": "Transformer-based cross-modal attention with contrastive learning",
                "expected_quality": 7.0
            }
        ]
        
        results = []
        
        # 3. 执行测试案例
        for i, test_case in enumerate(test_cases, 1):
            print(f"\\n🧪 Step 3.{i}: 测试案例 - {test_case['name']}")
            print("-" * 50)
            
            case_start_time = time.time()
            
            try:
                # 生成论文
                print(f"📝 主题: {test_case['topic']}")
                print(f"🔬 研究重点: {test_case['research_focus']}")
                print(f"⚙️ 方法论: {test_case['methodology']}")
                print(f"🎯 期望质量: {test_case['expected_quality']}/10")
                
                result = writer.generate_enhanced_paper(
                    topic=test_case['topic'],
                    research_focus=test_case['research_focus'],
                    methodology=test_case['methodology']
                )
                
                case_time = time.time() - case_start_time
                
                # 分析结果
                quality_metrics = result['quality_metrics']
                metadata = result['metadata']
                
                print(f"\\n📊 生成结果分析:")
                print(f"  ⏱️ 生成时间: {case_time:.2f}秒")
                print(f"  📄 论文标题: {metadata['title']}")
                print(f"  📝 摘要长度: {len(metadata['abstract'])} 字符")
                print(f"  📚 章节数量: {len(result['sections'])}")
                print(f"  🔗 参考文献: {len(result['references'])}")
                
                print(f"\\n🎯 质量指标:")
                print(f"  🧠 新颖性: {quality_metrics['novelty_score']:.1f}/10")
                print(f"  🔧 技术质量: {quality_metrics['technical_quality']:.1f}/10")
                print(f"  📖 清晰度: {quality_metrics['clarity_score']:.1f}/10")
                print(f"  🌟 重要性: {quality_metrics['significance_score']:.1f}/10")
                print(f"  🏆 总体质量: {quality_metrics['overall_quality']:.1f}/10")
                
                # 质量评估
                is_passing = quality_metrics['overall_quality'] >= test_case['expected_quality']
                quality_status = "✅ PASS" if is_passing else "❌ FAIL"
                print(f"  {quality_status} (阈值: {test_case['expected_quality']}/10)")
                
                # 输出文件检查
                outputs = result['outputs']
                print(f"\\n📁 输出文件:")
                for format_type, filepath in outputs.items():
                    if filepath and os.path.exists(filepath):
                        file_size = os.path.getsize(filepath) / 1024  # KB
                        print(f"  ✅ {format_type.upper()}: {filepath} ({file_size:.1f} KB)")
                    else:
                        print(f"  ❌ {format_type.upper()}: 生成失败")
                
                # 语言检查 (检查中文混入)
                content_sample = metadata['abstract'] + result['sections'][0]['content'][:500]
                chinese_chars = sum(1 for char in content_sample if '\\u4e00' <= char <= '\\u9fff')
                chinese_ratio = chinese_chars / len(content_sample) * 100
                
                if chinese_ratio > 5:
                    print(f"  ⚠️ 语言警告: 检测到 {chinese_ratio:.1f}% 中文字符")
                else:
                    print(f"  ✅ 语言检查: 纯英文输出 ({chinese_ratio:.1f}% 中文)")
                
                # 保存测试结果
                test_result = {
                    "test_case": test_case,
                    "generation_time": case_time,
                    "quality_metrics": quality_metrics,
                    "metadata": metadata,
                    "outputs": outputs,
                    "language_check": {
                        "chinese_ratio": chinese_ratio,
                        "is_pure_english": chinese_ratio <= 5
                    },
                    "pass_status": is_passing,
                    "timestamp": datetime.now().isoformat()
                }
                
                results.append(test_result)
                
                print(f"\\n{'='*20} 案例 {i} 完成 {'='*20}")
                
            except Exception as e:
                print(f"❌ 测试案例 {i} 失败: {e}")
                import traceback
                traceback.print_exc()
                
                # 记录失败结果
                error_result = {
                    "test_case": test_case,
                    "error": str(e),
                    "pass_status": False,
                    "timestamp": datetime.now().isoformat()
                }
                results.append(error_result)
        
        # 4. 综合分析和报告
        print("\\n📊 Step 4: 综合分析报告")
        print("=" * 60)
        
        total_time = time.time() - start_time
        successful_tests = [r for r in results if r.get('pass_status', False)]
        failed_tests = [r for r in results if not r.get('pass_status', False)]
        
        print(f"\\n🎯 测试总结:")
        print(f"  ⏱️ 总测试时间: {total_time:.2f}秒")
        print(f"  ✅ 成功案例: {len(successful_tests)}/{len(results)}")
        print(f"  ❌ 失败案例: {len(failed_tests)}/{len(results)}")
        
        if successful_tests:
            avg_quality = sum(r['quality_metrics']['overall_quality'] for r in successful_tests) / len(successful_tests)
            avg_time = sum(r['generation_time'] for r in successful_tests) / len(successful_tests)
            
            print(f"\\n📈 成功案例统计:")
            print(f"  🎯 平均质量分数: {avg_quality:.1f}/10")
            print(f"  ⏱️ 平均生成时间: {avg_time:.2f}秒")
            
            # 质量分解分析
            quality_breakdown = {
                'novelty': sum(r['quality_metrics']['novelty_score'] for r in successful_tests) / len(successful_tests),
                'technical': sum(r['quality_metrics']['technical_quality'] for r in successful_tests) / len(successful_tests),
                'clarity': sum(r['quality_metrics']['clarity_score'] for r in successful_tests) / len(successful_tests),
                'significance': sum(r['quality_metrics']['significance_score'] for r in successful_tests) / len(successful_tests)
            }
            
            print(f"\\n🔍 质量分解分析:")
            for metric, score in quality_breakdown.items():
                print(f"  📊 {metric.capitalize()}: {score:.1f}/10")
        
        # 语言纯度分析
        pure_english_count = sum(1 for r in successful_tests if r.get('language_check', {}).get('is_pure_english', False))
        
        print(f"\\n🌐 语言纯度分析:")
        print(f"  ✅ 纯英文输出: {pure_english_count}/{len(successful_tests)}")
        
        if pure_english_count < len(successful_tests):
            print(f"  ⚠️ 需要改进语言过滤机制")
        
        # 输出格式完整性
        format_success = {'json': 0, 'markdown': 0, 'latex': 0}
        for result in successful_tests:
            for format_type, filepath in result.get('outputs', {}).items():
                if filepath and os.path.exists(filepath):
                    format_success[format_type] += 1
        
        print(f"\\n📁 输出格式完整性:")
        for format_type, count in format_success.items():
            print(f"  📄 {format_type.upper()}: {count}/{len(successful_tests)} 成功")
        
        # 5. 保存完整测试报告
        print("\\n💾 Step 5: 保存测试报告")
        print("-" * 30)
        
        report = {
            "test_info": {
                "timestamp": datetime.now().isoformat(),
                "total_time": total_time,
                "test_cases": len(test_cases),
                "successful": len(successful_tests),
                "failed": len(failed_tests)
            },
            "model_status": model_status,
            "results": results,
            "summary": {
                "average_quality": avg_quality if successful_tests else 0,
                "average_time": avg_time if successful_tests else 0,
                "quality_breakdown": quality_breakdown if successful_tests else {},
                "pure_english_ratio": pure_english_count / len(successful_tests) if successful_tests else 0,
                "format_success": format_success
            }
        }
        
        # 保存报告
        report_filename = f"enhanced_stage4_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        report_path = os.path.join("output", report_filename)
        
        os.makedirs("output", exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试报告已保存: {report_path}")
        
        # 6. 最终结论
        print("\\n🏁 Step 6: 最终结论")
        print("=" * 60)
        
        if len(successful_tests) >= len(test_cases) * 0.8:  # 80%成功率
            if avg_quality >= 7.5:
                print("🎉 测试结果: EXCELLENT")
                print("✅ 系统达到AI Scientist v2质量标准")
                print("✅ 建议部署到生产环境")
            elif avg_quality >= 7.0:
                print("✅ 测试结果: GOOD")
                print("⚠️ 质量接近目标，需要小幅优化")
                print("📝 建议微调提示和评估机制")
            else:
                print("⚠️ 测试结果: NEEDS IMPROVEMENT")
                print("❌ 质量低于预期，需要大幅改进")
                print("🔧 建议重新调优模型和算法")
        else:
            print("❌ 测试结果: FAILED")
            print("💥 成功率过低，系统不稳定")
            print("🚨 需要彻底检查和修复")
        
        return report
        
    except Exception as e:
        print(f"\\n💥 测试过程出现严重错误: {e}")
        import traceback
        traceback.print_exc()
        return None


def analyze_improvements():
    """分析相对于原版本的改进"""
    print("\\n🔍 改进分析:")
    print("=" * 50)
    
    improvements = [
        "🧠 混合模型架构: DeepSeek + Qwen",
        "👁️ 视觉模型集成: qwen-vl-plus",
        "📊 实时质量评估和优化",
        "🔧 自适应内容改进机制",
        "📄 增强LaTeX生成支持",
        "🌐 纯英文输出保证",
        "📚 智能参考文献生成",
        "⚡ 性能监控和错误处理",
        "🎯 多维度质量评分系统",
        "💾 完整的输出格式支持"
    ]
    
    for improvement in improvements:
        print(f"  {improvement}")
    
    print(f"\\n📈 预期提升:")
    print(f"  🎯 质量分数: 6.0 → 7.5+ /10")
    print(f"  ⏱️ 生成时间: 10.3分钟 → 8-10分钟")
    print(f"  📄 输出格式: JSON+MD → JSON+MD+LaTeX")
    print(f"  🌐 语言纯度: 混合 → 纯英文")
    print(f"  🔧 稳定性: 中等 → 高")


if __name__ == "__main__":
    print("🚀 Enhanced Stage 4 Testing Suite")
    print("🎯 目标: 验证AI Scientist v2级别的论文生成质量")
    print("=" * 80)
    
    # 显示改进分析
    analyze_improvements()
    
    # 执行测试
    print("\\n" + "="*80)
    report = test_enhanced_stage4()
    
    if report:
        print("\\n🎊 测试完成! 检查输出文件和质量报告")
        print(f"📁 输出目录: output/")
        print(f"📊 详细报告: {report.get('report_path', 'N/A')}")
    else:
        print("\\n💥 测试失败，请检查错误信息并重试")
    
    print("\\n" + "="*80)
