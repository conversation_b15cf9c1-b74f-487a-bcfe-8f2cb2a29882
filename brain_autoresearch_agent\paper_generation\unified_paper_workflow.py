"""
Unified Paper Generation Workflow

统一的论文生成工作流，集成现有的工作流提取器和增强的论文撰写系统
"""

import os
import sys
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
import logging

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from core.paper_workflow import PaperWorkflowExtractor
from paper_generation.enhanced_brain_paper_writer import (
    EnhancedBrainPaperWriter, PaperGenerationConfig, PaperGenerationResult
)
from paper_generation.advanced_brain_paper_writer import AdvancedBrainPaperWriter
from paper_generation.review_system.multi_expert_review_system import MultiExpertReviewSystem
from paper_generation.review_system.auto_revision_engine import AutoRevisionEngine


@dataclass
class UnifiedWorkflowConfig:
    """统一工作流配置"""
    # 工作流提取配置
    enable_workflow_extraction: bool = True
    workflow_analysis_depth: str = "comprehensive"  # basic, standard, comprehensive
    
    # Stage 3 集成配置
    use_stage3_input: bool = False
    stage3_data_path: Optional[str] = None
    enable_integration_analysis: bool = True
    
    # 论文生成配置
    paper_generation_config: PaperGenerationConfig = None
    use_advanced_writer: bool = True  # 使用高级论文撰写器（推荐）
    
    # 输出配置
    output_formats: List[str] = None  # ['markdown', 'latex', 'json']
    save_intermediate_results: bool = True
    output_directory: str = "output"


@dataclass
class WorkflowExtractionResult:
    """工作流提取结果"""
    extracted_workflows: List[Dict[str, Any]]
    research_gaps: List[str]
    methodology_insights: List[str]
    innovation_opportunities: List[str]
    technical_requirements: List[str]
    success: bool
    extraction_metadata: Dict[str, Any]


@dataclass
class UnifiedGenerationResult:
    """统一生成结果"""
    workflow_extraction: WorkflowExtractionResult
    paper_generation: PaperGenerationResult
    integration_insights: Dict[str, Any]
    output_files: Dict[str, str]  # format -> file_path
    generation_summary: str
    success: bool
    total_time: float


class UnifiedPaperGenerationWorkflow:
    """统一论文生成工作流"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None,
                 config: Optional[UnifiedWorkflowConfig] = None):
        """
        初始化统一工作流
        
        Args:
            llm_client: LLM客户端实例
            config: 统一工作流配置
        """
        if llm_client is None:
            self.llm_client = LLMClient()
        else:
            self.llm_client = llm_client
        
        # 设置默认配置
        if config is None:
            paper_config = PaperGenerationConfig(
                target_venue="ICML",
                paper_type="research",
                max_review_iterations=3,
                quality_threshold=7.0,
                enable_auto_revision=True,
                enable_multi_expert_review=True,
                latex_output=True
            )
            config = UnifiedWorkflowConfig(
                paper_generation_config=paper_config,
                output_formats=['markdown', 'latex', 'json']
            )
        
        self.config = config
        
        # 初始化组件
        if self.config.enable_workflow_extraction:
            self.workflow_extractor = PaperWorkflowExtractor(self.llm_client)
        
        # 使用配置中的论文生成配置，如果没有则使用默认配置
        if self.config.paper_generation_config is None:
            self.config.paper_generation_config = PaperGenerationConfig()
            
        # 根据配置选择论文撰写器
        if self.config.use_advanced_writer:
            self.paper_writer = AdvancedBrainPaperWriter(
                self.llm_client, 
                self.config.paper_generation_config
            )
            self.logger_info = "使用高级论文撰写器（推荐）"
        else:
            self.paper_writer = EnhancedBrainPaperWriter(
                self.llm_client, 
                self.config.paper_generation_config
            )
            self.logger_info = "使用增强论文撰写器"
        
        self.logger = self._setup_logger()
        
        # 确保输出目录存在
        os.makedirs(self.config.output_directory, exist_ok=True)
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('UnifiedPaperGenerationWorkflow')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def generate_complete_paper(self, research_topic: str,
                                     research_requirements: Optional[Dict[str, Any]] = None,
                                     reference_papers: Optional[List[str]] = None,
                                     experimental_data: Optional[Dict[str, Any]] = None,
                                     quality_requirements: Optional[Dict[str, Any]] = None) -> UnifiedGenerationResult:
        """
        异步生成完整论文 (用于demo支持)
        
        Args:
            research_topic: 研究主题
            research_requirements: 研究需求 (demo使用)
            reference_papers: 参考论文列表
            experimental_data: 实验数据
            quality_requirements: 质量要求
            
        Returns:
            UnifiedGenerationResult: 生成结果
        """
        # 构建研究上下文
        research_context = {}
        
        if experimental_data:
            research_context['experimental_data'] = experimental_data
            
        if research_requirements:
            research_context['requirements'] = research_requirements
            
        # 调用同步版本
        return self.generate_complete_research_paper(
            research_topic=research_topic,
            reference_papers=reference_papers,
            research_context=research_context if research_context else None
        )
    
    def generate_complete_research_paper(self, research_topic: str,
                                       reference_papers: Optional[List[str]] = None,
                                       research_context: Optional[Dict[str, Any]] = None) -> UnifiedGenerationResult:
        """
        生成完整的研究论文
        
        Args:
            research_topic: 研究主题
            reference_papers: 参考论文列表（可选）
            research_context: 研究上下文信息（可选）
            
        Returns:
            UnifiedGenerationResult: 统一生成结果
        """
        print(f"\\n🚀 启动统一论文生成工作流")
        print(f"📋 研究主题: {research_topic}")
        print(f"🎯 目标会议: {self.config.paper_generation_config.target_venue}")
        print(f"📄 论文类型: {self.config.paper_generation_config.paper_type}")
        print(f"📊 输出格式: {', '.join(self.config.output_formats)}")
        
        start_time = datetime.now()
        output_files = {}
        
        try:
            # 第1阶段: 工作流提取和分析
            print(f"\\n📊 第1阶段: 工作流提取和分析")
            workflow_result = self._extract_research_workflows(research_topic, reference_papers)
            
            # 第2阶段: 集成分析和增强研究上下文
            print(f"\\n🔗 第2阶段: 集成分析和增强研究上下文")
            enhanced_context = self._integrate_workflow_insights(workflow_result, research_context)
            
            # 第3阶段: 论文生成
            print(f"\\n📝 第3阶段: 增强论文生成")
            paper_result = self.paper_writer.generate_paper_with_quality_control(
                research_topic, enhanced_context
            )
            
            # 第4阶段: 输出生成
            print(f"\\n💾 第4阶段: 多格式输出生成")
            output_files = self._generate_output_files(paper_result, workflow_result)
            
            # 第5阶段: 生成综合报告
            print(f"\\n📊 第5阶段: 生成综合分析报告")
            integration_insights = self._generate_integration_insights(workflow_result, paper_result)
            generation_summary = self._generate_summary_report(workflow_result, paper_result, integration_insights)
            
            total_time = (datetime.now() - start_time).total_seconds()
            
            print(f"\\n✅ 统一工作流完成")
            print(f"⏱️ 总用时: {total_time:.1f}秒")
            print(f"📊 最终论文评分: {paper_result.quality_metrics.overall_score:.2f}/10")
            print(f"📁 输出文件: {len(output_files)} 个")
            
            return UnifiedGenerationResult(
                workflow_extraction=workflow_result,
                paper_generation=paper_result,
                integration_insights=integration_insights,
                output_files=output_files,
                generation_summary=generation_summary,
                success=True,
                total_time=total_time
            )
            
        except Exception as e:
            self.logger.error(f"Unified workflow failed: {e}")
            total_time = (datetime.now() - start_time).total_seconds()
            
            return UnifiedGenerationResult(
                workflow_extraction=self._create_empty_workflow_result(),
                paper_generation=self._create_empty_paper_result(),
                integration_insights={'error': str(e)},
                output_files=output_files,
                generation_summary=f"Workflow failed: {e}",
                success=False,
                total_time=total_time
            )
    
    def _extract_research_workflows(self, research_topic: str, 
                                   reference_papers: Optional[List[str]] = None) -> WorkflowExtractionResult:
        """提取研究工作流"""
        
        if not self.config.enable_workflow_extraction:
            return self._create_empty_workflow_result()
        
        try:
            print(f"  🔍 使用PaperWorkflowExtractor分析研究主题")
            
            # 使用现有的工作流提取器
            workflow_analysis = self.workflow_extractor.extract_research_workflow(research_topic)
            
            if not workflow_analysis:
                print(f"  ⚠️ 工作流提取失败，使用基本分析")
                return self._create_basic_workflow_result(research_topic)
            
            # 解析工作流分析结果
            extracted_workflows = self._parse_workflow_analysis(workflow_analysis)
            
            # 分析研究空白和机会
            research_gaps, innovation_opportunities = self._analyze_research_opportunities(
                research_topic, workflow_analysis
            )
            
            # 提取方法论洞察
            methodology_insights = self._extract_methodology_insights(workflow_analysis)
            
            # 确定技术要求
            technical_requirements = self._identify_technical_requirements(workflow_analysis)
            
            print(f"  ✅ 工作流提取完成")
            print(f"    📊 提取工作流: {len(extracted_workflows)} 个")
            print(f"    🔍 研究空白: {len(research_gaps)} 个")
            print(f"    💡 创新机会: {len(innovation_opportunities)} 个")
            
            return WorkflowExtractionResult(
                extracted_workflows=extracted_workflows,
                research_gaps=research_gaps,
                methodology_insights=methodology_insights,
                innovation_opportunities=innovation_opportunities,
                technical_requirements=technical_requirements,
                success=True,
                extraction_metadata={
                    'extraction_method': 'PaperWorkflowExtractor',
                    'analysis_depth': self.config.workflow_analysis_depth,
                    'timestamp': datetime.now().isoformat()
                }
            )
            
        except Exception as e:
            self.logger.warning(f"Workflow extraction failed: {e}")
            return self._create_basic_workflow_result(research_topic)
    
    def _parse_workflow_analysis(self, workflow_analysis: Any) -> List[Dict[str, Any]]:
        """解析工作流分析结果"""
        workflows = []
        
        try:
            if isinstance(workflow_analysis, dict):
                # 如果是字典格式，尝试提取工作流信息
                for key, value in workflow_analysis.items():
                    if 'workflow' in key.lower() or 'method' in key.lower():
                        workflows.append({
                            'type': key,
                            'description': str(value)[:500],
                            'source': 'extracted'
                        })
            elif isinstance(workflow_analysis, str):
                # 如果是字符串，创建单个工作流
                workflows.append({
                    'type': 'general_workflow',
                    'description': workflow_analysis[:1000],
                    'source': 'extracted'
                })
            
            # 如果没有提取到工作流，创建默认的
            if not workflows:
                workflows.append({
                    'type': 'default_research_workflow',
                    'description': 'Standard research methodology workflow',
                    'source': 'default'
                })
                
        except Exception as e:
            self.logger.warning(f"Workflow parsing failed: {e}")
            workflows.append({
                'type': 'error_workflow',
                'description': f'Workflow parsing failed: {e}',
                'source': 'error'
            })
        
        return workflows
    
    def _analyze_research_opportunities(self, research_topic: str, 
                                       workflow_analysis: Any) -> Tuple[List[str], List[str]]:
        """分析研究机会和空白"""
        
        analysis_prompt = f"""
        Based on the research topic "{research_topic}" and the following workflow analysis:
        
        {str(workflow_analysis)[:1500]}...
        
        Please identify:
        1. Research gaps that could be addressed
        2. Innovation opportunities for new approaches
        
        Provide your analysis in JSON format:
        {{
            "research_gaps": ["gap 1", "gap 2", "gap 3"],
            "innovation_opportunities": ["opportunity 1", "opportunity 2", "opportunity 3"]
        }}
        """
        
        try:
            response = self.llm_client.get_response(analysis_prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            
            # 尝试解析JSON
            json_start = response_text.find('{')
            json_end = response_text.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_text = response_text[json_start:json_end]
                analysis = json.loads(json_text)
                
                research_gaps = analysis.get('research_gaps', [])
                innovation_opportunities = analysis.get('innovation_opportunities', [])
                
                return research_gaps, innovation_opportunities
            
        except Exception as e:
            self.logger.warning(f"Research opportunity analysis failed: {e}")
        
        # 返回默认的分析
        return (
            [f"Limited understanding of {research_topic}", "Need for better evaluation methods"],
            [f"Novel approaches to {research_topic}", "Integration with other domains"]
        )
    
    def _extract_methodology_insights(self, workflow_analysis: Any) -> List[str]:
        """提取方法论洞察"""
        insights = []
        
        try:
            analysis_text = str(workflow_analysis)
            
            # 简单的关键词提取
            if 'neural' in analysis_text.lower():
                insights.append("Neural network-based approaches are prominent")
            if 'deep learning' in analysis_text.lower():
                insights.append("Deep learning methodologies are widely used")
            if 'attention' in analysis_text.lower():
                insights.append("Attention mechanisms show strong performance")
            if 'transformer' in analysis_text.lower():
                insights.append("Transformer architectures are influential")
            
            # 如果没有提取到洞察，添加通用洞察
            if not insights:
                insights.extend([
                    "Multi-modal approaches show promise",
                    "End-to-end learning is prevalent",
                    "Evaluation standardization is needed"
                ])
                
        except Exception as e:
            insights.append(f"Methodology analysis failed: {e}")
        
        return insights[:5]  # 限制数量
    
    def _identify_technical_requirements(self, workflow_analysis: Any) -> List[str]:
        """识别技术要求"""
        requirements = []
        
        try:
            analysis_text = str(workflow_analysis).lower()
            
            if 'gpu' in analysis_text or 'cuda' in analysis_text:
                requirements.append("GPU computing capabilities required")
            if 'dataset' in analysis_text or 'data' in analysis_text:
                requirements.append("Large-scale dataset access needed")
            if 'framework' in analysis_text or 'pytorch' in analysis_text or 'tensorflow' in analysis_text:
                requirements.append("Deep learning framework expertise required")
            if 'experiment' in analysis_text:
                requirements.append("Comprehensive experimental validation needed")
            
            # 默认技术要求
            if not requirements:
                requirements.extend([
                    "Programming expertise in Python",
                    "Machine learning framework knowledge",
                    "Experimental design capabilities",
                    "Statistical analysis skills"
                ])
                
        except Exception as e:
            requirements.append(f"Technical requirement analysis failed: {e}")
        
        return requirements[:6]  # 限制数量
    
    def _integrate_workflow_insights(self, workflow_result: WorkflowExtractionResult,
                                   research_context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """集成工作流洞察和研究上下文"""
        
        enhanced_context = research_context.copy() if research_context else {}
        
        # 添加工作流洞察
        enhanced_context['workflow_insights'] = {
            'extracted_workflows': workflow_result.extracted_workflows,
            'research_gaps': workflow_result.research_gaps,
            'methodology_insights': workflow_result.methodology_insights,
            'innovation_opportunities': workflow_result.innovation_opportunities,
            'technical_requirements': workflow_result.technical_requirements
        }
        
        # 添加综合分析
        enhanced_context['synthesis'] = self._synthesize_research_context(workflow_result)
        
        return enhanced_context
    
    def _synthesize_research_context(self, workflow_result: WorkflowExtractionResult) -> Dict[str, Any]:
        """综合研究上下文"""
        
        synthesis = {
            'key_research_themes': [],
            'methodological_directions': [],
            'innovation_potential': 0.0,
            'technical_complexity': 'medium'
        }
        
        try:
            # 分析主要研究主题
            all_descriptions = []
            for workflow in workflow_result.extracted_workflows:
                all_descriptions.append(workflow.get('description', ''))
            
            combined_text = ' '.join(all_descriptions).lower()
            
            # 简单的主题提取
            themes = []
            if 'learning' in combined_text:
                themes.append('machine learning')
            if 'neural' in combined_text:
                themes.append('neural networks')
            if 'vision' in combined_text or 'image' in combined_text:
                themes.append('computer vision')
            if 'language' in combined_text or 'nlp' in combined_text:
                themes.append('natural language processing')
            
            synthesis['key_research_themes'] = themes[:5]
            
            # 评估创新潜力
            innovation_score = min(len(workflow_result.innovation_opportunities) * 0.2, 1.0)
            synthesis['innovation_potential'] = innovation_score
            
            # 评估技术复杂度
            if len(workflow_result.technical_requirements) > 4:
                synthesis['technical_complexity'] = 'high'
            elif len(workflow_result.technical_requirements) > 2:
                synthesis['technical_complexity'] = 'medium'
            else:
                synthesis['technical_complexity'] = 'low'
                
        except Exception as e:
            self.logger.warning(f"Research context synthesis failed: {e}")
        
        return synthesis
    
    def _generate_output_files(self, paper_result: PaperGenerationResult,
                              workflow_result: WorkflowExtractionResult) -> Dict[str, str]:
        """生成输出文件"""
        output_files = {}
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            for output_format in self.config.output_formats:
                filename = f"research_paper_{timestamp}.{output_format}"
                filepath = os.path.join(self.config.output_directory, filename)
                
                if output_format == 'json':
                    # JSON格式：完整的结构化数据
                    content = {
                        'paper_content': paper_result.paper_content,
                        'quality_metrics': paper_result.quality_metrics.__dict__,
                        'workflow_extraction': {
                            'workflows': workflow_result.extracted_workflows,
                            'research_gaps': workflow_result.research_gaps,
                            'methodology_insights': workflow_result.methodology_insights,
                            'innovation_opportunities': workflow_result.innovation_opportunities
                        },
                        'generation_metadata': paper_result.generation_metadata
                    }
                    
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(content, f, indent=2, ensure_ascii=False, default=str)
                
                elif output_format == 'markdown':
                    # Markdown格式：可读的文档
                    content = self._generate_markdown_output(paper_result, workflow_result)
                    
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(content)
                
                elif output_format == 'latex' and paper_result.latex_output:
                    # LaTeX格式：学术论文格式
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(paper_result.latex_output)
                
                output_files[output_format] = filepath
                print(f"  📁 生成 {output_format.upper()} 文件: {filepath}")
                
        except Exception as e:
            self.logger.warning(f"Output file generation failed: {e}")
        
        return output_files
    
    def _generate_markdown_output(self, paper_result: PaperGenerationResult,
                                 workflow_result: WorkflowExtractionResult) -> str:
        """生成Markdown格式输出"""
        
        sections = []
        
        # 论文标题
        title = paper_result.paper_content.get('title', 'Research Paper')
        sections.append(f"# {title}\\n")
        
        # 质量指标
        sections.append("## Quality Metrics\\n")
        metrics = paper_result.quality_metrics
        sections.append(f"- **Overall Score**: {metrics.overall_score:.2f}/10")
        sections.append(f"- **Novelty**: {metrics.novelty_score:.2f}/10")
        sections.append(f"- **Technical Quality**: {metrics.technical_quality_score:.2f}/10")
        sections.append(f"- **Clarity**: {metrics.clarity_score:.2f}/10")
        sections.append(f"- **Significance**: {metrics.significance_score:.2f}/10")
        sections.append(f"- **Reproducibility**: {metrics.reproducibility_score:.2f}/10")
        sections.append(f"- **Expert Consensus**: {metrics.expert_consensus:.2f}")
        sections.append("\\n")
        
        # 论文内容
        paper_sections = paper_result.paper_content.get('sections', {})
        section_order = ['abstract', 'introduction', 'related_work', 'methodology', 
                        'experiments', 'results', 'discussion', 'conclusion']
        
        for section_name in section_order:
            if section_name in paper_sections:
                content = paper_sections[section_name]
                if content and content.strip():
                    sections.append(f"## {section_name.replace('_', ' ').title()}\\n")
                    sections.append(f"{content}\\n\\n")
        
        # 工作流洞察
        if workflow_result.success:
            sections.append("## Research Workflow Insights\\n")
            
            if workflow_result.research_gaps:
                sections.append("### Research Gaps\\n")
                for gap in workflow_result.research_gaps:
                    sections.append(f"- {gap}")
                sections.append("\\n")
            
            if workflow_result.innovation_opportunities:
                sections.append("### Innovation Opportunities\\n")
                for opportunity in workflow_result.innovation_opportunities:
                    sections.append(f"- {opportunity}")
                sections.append("\\n")
            
            if workflow_result.methodology_insights:
                sections.append("### Methodology Insights\\n")
                for insight in workflow_result.methodology_insights:
                    sections.append(f"- {insight}")
                sections.append("\\n")
        
        # 参考文献
        if 'references' in paper_sections:
            sections.append("## References\\n")
            sections.append(f"{paper_sections['references']}\\n")
        
        return "\\n".join(sections)
    
    def _generate_integration_insights(self, workflow_result: WorkflowExtractionResult,
                                     paper_result: PaperGenerationResult) -> Dict[str, Any]:
        """生成集成洞察"""
        
        insights = {
            'workflow_paper_alignment': 0.0,
            'innovation_realization': 0.0,
            'quality_improvement_potential': 0.0,
            'technical_feasibility': 'medium',
            'recommendations': []
        }
        
        try:
            # 评估工作流和论文的一致性
            if workflow_result.success and paper_result.success:
                # 简单的一致性评估
                workflow_themes = set()
                for workflow in workflow_result.extracted_workflows:
                    desc = workflow.get('description', '').lower()
                    if 'learning' in desc:
                        workflow_themes.add('learning')
                    if 'neural' in desc:
                        workflow_themes.add('neural')
                    if 'network' in desc:
                        workflow_themes.add('network')
                
                paper_content = str(paper_result.paper_content).lower()
                paper_themes = set()
                if 'learning' in paper_content:
                    paper_themes.add('learning')
                if 'neural' in paper_content:
                    paper_themes.add('neural')
                if 'network' in paper_content:
                    paper_themes.add('network')
                
                # 计算主题重叠度
                if workflow_themes and paper_themes:
                    overlap = len(workflow_themes.intersection(paper_themes))
                    total_themes = len(workflow_themes.union(paper_themes))
                    insights['workflow_paper_alignment'] = overlap / total_themes if total_themes > 0 else 0.0
            
            # 评估创新实现度
            innovation_count = len(workflow_result.innovation_opportunities) if workflow_result.success else 0
            if innovation_count > 0:
                # 假设论文实现了部分创新机会
                insights['innovation_realization'] = min(paper_result.quality_metrics.novelty_score / 10.0, 1.0)
            
            # 评估质量改进潜力
            current_score = paper_result.quality_metrics.overall_score
            potential_score = min(current_score + 1.5, 10.0)  # 假设最多能提升1.5分
            insights['quality_improvement_potential'] = (potential_score - current_score) / 10.0
            
            # 生成建议
            recommendations = []
            
            if insights['workflow_paper_alignment'] < 0.7:
                recommendations.append("Better integrate workflow insights into paper methodology")
            
            if paper_result.quality_metrics.novelty_score < 7.0:
                recommendations.append("Emphasize novel contributions more clearly")
            
            if paper_result.quality_metrics.reproducibility_score < 6.5:
                recommendations.append("Add more implementation details for reproducibility")
            
            if len(paper_result.review_history) > 0:
                latest_review = paper_result.review_history[-1]
                if latest_review.final_recommendation == "Revision Required":
                    recommendations.append("Address expert review feedback for acceptance")
            
            insights['recommendations'] = recommendations
            
        except Exception as e:
            self.logger.warning(f"Integration insights generation failed: {e}")
            insights['error'] = str(e)
        
        return insights
    
    def _generate_summary_report(self, workflow_result: WorkflowExtractionResult,
                               paper_result: PaperGenerationResult,
                               integration_insights: Dict[str, Any]) -> str:
        """生成综合报告"""
        
        report_parts = []
        
        # 报告标题
        report_parts.append("# Research Paper Generation Summary Report\\n")
        report_parts.append(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n")
        
        # 论文基本信息
        report_parts.append("## Paper Overview")
        title = paper_result.paper_content.get('title', 'Unknown Title')
        report_parts.append(f"**Title**: {title}")
        report_parts.append(f"**Target Venue**: {self.config.paper_generation_config.target_venue}")
        report_parts.append(f"**Paper Type**: {self.config.paper_generation_config.paper_type}")
        report_parts.append(f"**Generation Success**: {'✅' if paper_result.success else '❌'}")
        report_parts.append("")
        
        # 质量评估
        report_parts.append("## Quality Assessment")
        metrics = paper_result.quality_metrics
        report_parts.append(f"**Overall Score**: {metrics.overall_score:.2f}/10")
        report_parts.append(f"**Expert Consensus**: {metrics.expert_consensus:.2f}")
        report_parts.append(f"**Review Iterations**: {len(paper_result.review_history)}")
        report_parts.append(f"**Revision Iterations**: {len(paper_result.revision_history)}")
        report_parts.append("")
        
        # 工作流分析
        if workflow_result.success:
            report_parts.append("## Workflow Analysis")
            report_parts.append(f"**Workflows Extracted**: {len(workflow_result.extracted_workflows)}")
            report_parts.append(f"**Research Gaps Identified**: {len(workflow_result.research_gaps)}")
            report_parts.append(f"**Innovation Opportunities**: {len(workflow_result.innovation_opportunities)}")
            report_parts.append("")
        
        # 集成分析
        report_parts.append("## Integration Analysis")
        report_parts.append(f"**Workflow-Paper Alignment**: {integration_insights.get('workflow_paper_alignment', 0):.2f}")
        report_parts.append(f"**Innovation Realization**: {integration_insights.get('innovation_realization', 0):.2f}")
        report_parts.append(f"**Quality Improvement Potential**: {integration_insights.get('quality_improvement_potential', 0):.2f}")
        report_parts.append("")
        
        # 建议
        recommendations = integration_insights.get('recommendations', [])
        if recommendations:
            report_parts.append("## Recommendations")
            for i, rec in enumerate(recommendations, 1):
                report_parts.append(f"{i}. {rec}")
            report_parts.append("")
        
        # 总结
        report_parts.append("## Summary")
        if paper_result.success:
            report_parts.append("✅ Paper generation completed successfully with integrated workflow insights.")
            if metrics.overall_score >= 7.0:
                report_parts.append("🎯 High-quality paper ready for submission.")
            elif metrics.overall_score >= 6.0:
                report_parts.append("📝 Good quality paper, minor revisions may improve acceptance chances.")
            else:
                report_parts.append("🔧 Paper needs significant revision before submission.")
        else:
            report_parts.append("❌ Paper generation failed. Please review configuration and try again.")
        
        return "\\n".join(report_parts)
    
    def _create_empty_workflow_result(self) -> WorkflowExtractionResult:
        """创建空的工作流结果"""
        return WorkflowExtractionResult(
            extracted_workflows=[],
            research_gaps=[],
            methodology_insights=[],
            innovation_opportunities=[],
            technical_requirements=[],
            success=False,
            extraction_metadata={'status': 'disabled or failed'}
        )
    
    def _create_basic_workflow_result(self, research_topic: str) -> WorkflowExtractionResult:
        """创建基本工作流结果"""
        return WorkflowExtractionResult(
            extracted_workflows=[{
                'type': 'basic_research_workflow',
                'description': f'Standard research approach for {research_topic}',
                'source': 'default'
            }],
            research_gaps=[f'Limited understanding of {research_topic} mechanisms'],
            methodology_insights=['Need for rigorous experimental validation'],
            innovation_opportunities=[f'Novel approaches to {research_topic}'],
            technical_requirements=['Programming expertise', 'Experimental design'],
            success=True,
            extraction_metadata={'status': 'basic_analysis'}
        )
    
    def _create_empty_paper_result(self) -> PaperGenerationResult:
        """创建空的论文结果"""
        from paper_generation.enhanced_brain_paper_writer import PaperQualityMetrics
        
        return PaperGenerationResult(
            paper_content={},
            quality_metrics=PaperQualityMetrics(
                overall_score=0.0, novelty_score=0.0, technical_quality_score=0.0,
                clarity_score=0.0, significance_score=0.0, reproducibility_score=0.0,
                expert_consensus=0.0, improvement_history=[]
            ),
            review_history=[],
            revision_history=[],
            latex_output=None,
            generation_metadata={'status': 'failed'},
            success=False,
            warnings=[]
        )


def test_unified_workflow():
    """测试统一工作流"""
    print("🧪 测试统一论文生成工作流")
    
    # 创建配置
    paper_config = PaperGenerationConfig(
        target_venue="ICML",
        paper_type="research",
        max_review_iterations=2,
        quality_threshold=6.5,
        enable_auto_revision=True,
        enable_multi_expert_review=True,
        latex_output=False  # 简化测试
    )
    
    unified_config = UnifiedWorkflowConfig(
        enable_workflow_extraction=True,
        paper_generation_config=paper_config,
        output_formats=['markdown', 'json'],
        output_directory="test_output"
    )
    
    # 初始化工作流
    workflow = UnifiedPaperGenerationWorkflow(config=unified_config)
    
    # 生成论文
    research_topic = "Brain-Inspired Continual Learning for Dynamic Environments"
    result = workflow.generate_complete_research_paper(research_topic)
    
    # 显示结果
    print(f"\\n📊 统一工作流结果:")
    print(f"成功: {result.success}")
    print(f"总用时: {result.total_time:.1f}秒")
    print(f"论文评分: {result.paper_generation.quality_metrics.overall_score:.2f}/10")
    print(f"工作流提取: {'✅' if result.workflow_extraction.success else '❌'}")
    print(f"输出文件: {len(result.output_files)} 个")
    print(f"建议数量: {len(result.integration_insights.get('recommendations', []))}")
    
    return result


if __name__ == "__main__":
    test_unified_workflow()
