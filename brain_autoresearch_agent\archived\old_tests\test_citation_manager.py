#!/usr/bin/env python3
"""
简单的引用管理器测试
"""

try:
    from paper_generation.enhanced_citation_manager import EnhancedCitationManager
    print("✅ 引用管理器导入成功")
    
    # 创建实例
    manager = EnhancedCitationManager(None)
    print("✅ 引用管理器实例创建成功")
    
    # 检查方法
    methods = [attr for attr in dir(manager) if not attr.startswith('_')]
    print(f"📋 可用方法: {len(methods)} 个")
    
    # 检查特定方法
    if hasattr(manager, 'enhance_citations'):
        print("✅ enhance_citations方法存在")
        print(f"   类型: {type(getattr(manager, 'enhance_citations'))}")
    else:
        print("❌ enhance_citations方法不存在")
    
    if hasattr(manager, 'enhance_citations_sync'):
        print("✅ enhance_citations_sync方法存在")
        print(f"   类型: {type(getattr(manager, 'enhance_citations_sync'))}")
    else:
        print("❌ enhance_citations_sync方法不存在")
        
    print("\n📝 所有方法:")
    for method in methods:
        if 'enhance' in method.lower():
            print(f"   🎯 {method}")
    
    # 测试方法调用
    print("\n🧪 测试方法调用:")
    if hasattr(manager, 'enhance_citations'):
        try:
            test_metadata = {'title': 'Test Paper', 'keywords': ['test'], 'abstract': 'Test abstract'}
            result = manager.enhance_citations("Test content", test_metadata, 5)
            print(f"✅ enhance_citations调用成功: {result.get('success', False)}")
        except Exception as e:
            print(f"❌ enhance_citations调用失败: {e}")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
