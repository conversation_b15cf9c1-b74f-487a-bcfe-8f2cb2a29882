"""
阶段2: 增强多专家代理协作推理系统
实现高质量的多轮交互讨论、共识决策和动态调度
"""

import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass, asdict
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from agents.agent_manager import Agent<PERSON><PERSON><PERSON>
from reasoning.data_models import (
    ReasoningSession,
    CollaborationSession,
    DiscussionRound,
    CollaborationResult
)
from agents.base_agent import AgentTask, AgentResponse
from core.unified_api_client import UnifiedAPIClient

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedMultiAgentCollaborator:
    """增强的多专家代理协作系统"""
    
    def __init__(self, unified_client: UnifiedAPIClient):
        """
        初始化协作系统
        
        Args:
            unified_client: 统一API客户端
        """
        self.unified_client = unified_client
        self.agent_manager = AgentManager(unified_client)
        
        # 协作配置
        self.collaboration_config = {
            "max_rounds": 3,
            "min_consensus_score": 0.75,
            "max_participants": 5,
            "discussion_timeout": 300,  # 5分钟
            "quality_threshold": 0.8
        }
        
        # 会话历史
        self.sessions: List[CollaborationSession] = []
        
        # 专家能力映射
        self.expert_capabilities = {
            "ai_technology": ["algorithms", "architectures", "training", "optimization"],
            "neuroscience": ["brain_models", "biological_plausibility", "neural_dynamics"],
            "data_analysis": ["metrics", "statistics", "visualization", "interpretation"],
            "experiment_design": ["methodology", "controls", "variables", "protocols"],
            "paper_writing": ["structure", "clarity", "citations", "argumentation"]
        }
        
        logger.info("✅ 增强多专家代理协作系统初始化完成")
    
    def create_collaboration_session(
        self, 
        research_topic: str, 
        specific_questions: List[str],
        required_experts: Optional[List[str]] = None
    ) -> CollaborationSession:
        """
        创建协作会话
        
        Args:
            research_topic: 研究主题
            specific_questions: 具体需要讨论的问题
            required_experts: 必需的专家类型
            
        Returns:
            CollaborationSession: 创建的协作会话
        """
        session_id = f"collab_{int(time.time())}"
        
        # 自动选择参与专家
        participants = self._select_optimal_experts(
            research_topic, 
            specific_questions, 
            required_experts
        )
        
        session = CollaborationSession(
            session_id=session_id,
            research_topic=research_topic,
            participants=participants,
            rounds=[],
            final_consensus={},
            quality_score=0.0,
            insights=[],
            recommendations=[],
            duration=0.0
        )
        
        logger.info(f"🚀 创建协作会话: {session_id}")
        logger.info(f"   主题: {research_topic}")
        logger.info(f"   参与专家: {participants}")
        
        return session
    
    def _select_optimal_experts(
        self, 
        topic: str, 
        questions: List[str], 
        required: Optional[List[str]] = None
    ) -> List[str]:
        """
        选择最优专家组合
        
        Args:
            topic: 主题
            questions: 问题列表
            required: 必需专家
            
        Returns:
            List[str]: 选择的专家ID列表
        """
        available_experts = list(self.agent_manager.agents.keys())
        selected = set(required) if required else set()
        
        # 基于主题关键词匹配专家能力
        topic_lower = topic.lower()
        question_text = " ".join(questions).lower()
        combined_text = f"{topic_lower} {question_text}"
        
        expert_scores = {}
        for expert_id in available_experts:
            if expert_id in selected:
                continue
                
            capabilities = self.expert_capabilities.get(expert_id, [])
            score = sum(1 for cap in capabilities if cap in combined_text)
            
            # 特殊规则
            if "neural" in combined_text or "brain" in combined_text:
                if expert_id == "neuroscience":
                    score += 3
                if expert_id == "ai_technology":
                    score += 2
                    
            if "experiment" in combined_text or "design" in combined_text:
                if expert_id == "experiment_design":
                    score += 3
                    
            if "data" in combined_text or "analysis" in combined_text:
                if expert_id == "data_analysis":
                    score += 2
                    
            expert_scores[expert_id] = score
        
        # 按得分排序并选择
        sorted_experts = sorted(expert_scores.items(), key=lambda x: x[1], reverse=True)
        
        for expert_id, score in sorted_experts[:self.collaboration_config["max_participants"]]:
            if score > 0:
                selected.add(expert_id)
        
        # 确保至少有AI技术专家参与
        if "ai_technology" not in selected and len(selected) < self.collaboration_config["max_participants"]:
            selected.add("ai_technology")
        
        return list(selected)
    
    def conduct_multi_round_discussion(
        self, 
        session: CollaborationSession,
        discussion_points: List[str]
    ) -> CollaborationSession:
        """
        进行多轮讨论
        
        Args:
            session: 协作会话
            discussion_points: 讨论要点
            
        Returns:
            CollaborationSession: 更新的会话
        """
        start_time = time.time()
        
        logger.info(f"🗣️ 开始多轮讨论 - 会话: {session.session_id}")
        
        for round_num in range(1, self.collaboration_config["max_rounds"] + 1):
            logger.info(f"   📑 第 {round_num} 轮讨论")
            
            # 确定本轮讨论主题
            if round_num == 1:
                current_topic = f"初步分析: {session.research_topic}"
                discussion_prompt = self._build_initial_discussion_prompt(
                    session.research_topic, discussion_points
                )
            else:
                # 基于上一轮结果继续讨论
                previous_round = session.rounds[-1]
                current_topic = f"深入讨论: 解决分歧并达成共识"
                discussion_prompt = self._build_followup_discussion_prompt(
                    session, previous_round, discussion_points
                )
            
            # 执行本轮讨论
            discussion_round = self._execute_discussion_round(
                session.session_id,
                round_num,
                current_topic,
                session.participants,
                discussion_prompt
            )
            
            session.rounds.append(discussion_round)
            
            # 检查是否达成共识
            if discussion_round.consensus_score >= self.collaboration_config["min_consensus_score"]:
                logger.info(f"   ✅ 第 {round_num} 轮达成共识 (得分: {discussion_round.consensus_score:.2f})")
                break
            else:
                logger.info(f"   ⏳ 第 {round_num} 轮未达成共识 (得分: {discussion_round.consensus_score:.2f})")
                
                # 如果是最后一轮，强制结束
                if round_num == self.collaboration_config["max_rounds"]:
                    logger.info("   ⚠️ 已达最大轮次，结束讨论")
        
        # 生成最终共识
        session.final_consensus = self._generate_final_consensus(session)
        session.quality_score = self._evaluate_session_quality(session)
        session.duration = time.time() - start_time
        
        logger.info(f"✅ 讨论完成 - 质量得分: {session.quality_score:.2f}")
        
        # 保存会话
        self.sessions.append(session)
        
        return session
    
    def _build_initial_discussion_prompt(
        self, 
        research_topic: str, 
        discussion_points: List[str]
    ) -> str:
        """构建初始讨论提示"""
        points_text = "\\n".join([f"- {point}" for point in discussion_points])
        
        return f"""
作为该领域的专家，请对以下研究主题进行深入分析：

研究主题: {research_topic}

需要讨论的关键点:
{points_text}

请从您的专业角度提供：
1. 对该研究主题的专业见解
2. 识别的关键技术挑战和机会
3. 推荐的研究方法和策略
4. 可能的风险和局限性
5. 与其他专家的协作建议

请提供结构化、具体、可操作的建议。
"""
    
    def _build_followup_discussion_prompt(
        self,
        session: CollaborationSession,
        previous_round: DiscussionRound,
        discussion_points: List[str]
    ) -> str:
        """构建后续讨论提示"""
        
        # 提取上一轮的关键冲突和见解
        conflicts_text = "\\n".join([f"- {conflict}" for conflict in previous_round.conflicts])
        insights_text = "\\n".join([f"- {insight}" for insight in previous_round.key_insights])
        
        return f"""
基于前一轮讨论的结果，请继续深入分析：

研究主题: {session.research_topic}

上一轮讨论的关键见解:
{insights_text}

需要解决的分歧点:
{conflicts_text}

请着重：
1. 针对分歧点提供更具体的论证
2. 寻找不同观点间的平衡点
3. 提出具体的解决方案
4. 评估各种方案的可行性
5. 形成具体的共识建议

目标是在专业严谨的前提下达成可操作的共识。
"""
    
    def _execute_discussion_round(
        self,
        session_id: str,
        round_number: int,
        topic: str,
        participants: List[str],
        discussion_prompt: str
    ) -> DiscussionRound:
        """
        执行一轮讨论
        
        Args:
            session_id: 会话ID
            round_number: 轮次编号
            topic: 讨论主题
            participants: 参与者列表
            discussion_prompt: 讨论提示
            
        Returns:
            DiscussionRound: 讨论轮次结果
        """
        responses = {}
        
        # 创建讨论任务
        task = AgentTask(
            task_id=f"{session_id}_round_{round_number}",
            task_type="collaborative_discussion",
            input_data={
                "prompt": discussion_prompt,
                "topic": topic,
                "round": round_number
            },
            requirements=[
                "提供专业分析",
                "考虑跨学科协作",
                "提出具体建议"
            ]
        )
        
        # 并行收集各专家意见
        with ThreadPoolExecutor(max_workers=len(participants)) as executor:
            future_to_agent = {
                executor.submit(self._get_agent_response, agent_id, task): agent_id
                for agent_id in participants
            }
            
            for future in as_completed(future_to_agent):
                agent_id = future_to_agent[future]
                try:
                    response = future.result(timeout=60)  # 60秒超时
                    if response:
                        responses[agent_id] = response
                        logger.info(f"   ✅ 收到 {agent_id} 的响应")
                    else:
                        logger.warning(f"   ⚠️ {agent_id} 未响应")
                except Exception as e:
                    logger.error(f"   ❌ {agent_id} 响应错误: {e}")
        
        # 分析讨论结果
        consensus_score = self._calculate_consensus_score(responses)
        key_insights = self._extract_key_insights(responses)
        conflicts = self._identify_conflicts(responses)
        
        discussion_round = DiscussionRound(
            round_number=round_number,
            topic=topic,
            participants=participants,
            responses=responses,
            consensus_score=consensus_score,
            key_insights=key_insights,
            conflicts=conflicts,
            timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
        )
        
        return discussion_round
    
    def _get_agent_response(self, agent_id: str, task: AgentTask) -> Optional[AgentResponse]:
        """获取代理响应"""
        try:
            return self.agent_manager.assign_task_to_agent(task, agent_id)
        except Exception as e:
            logger.error(f"代理 {agent_id} 响应失败: {e}")
            return None
    
    def _calculate_consensus_score(self, responses: Dict[str, AgentResponse]) -> float:
        """
        计算共识分数
        
        Args:
            responses: 各代理的响应
            
        Returns:
            float: 共识分数 (0-1)
        """
        if len(responses) < 2:
            return 0.0
        
        # 基于关键词重叠度计算相似性
        all_contents = [resp.content.lower() for resp in responses.values()]
        
        # 提取关键概念
        key_concepts = set()
        for content in all_contents:
            # 简单关键词提取（实际中可以使用更复杂的NLP方法）
            words = content.split()
            key_concepts.update([word for word in words if len(word) > 4])
        
        # 计算概念重叠度
        concept_scores = []
        for concept in key_concepts:
            appearances = sum(1 for content in all_contents if concept in content)
            if appearances > 1:
                concept_scores.append(appearances / len(all_contents))
        
        if not concept_scores:
            return 0.0
        
        # 加权平均置信度
        confidence_scores = [resp.confidence for resp in responses.values()]
        avg_confidence = sum(confidence_scores) / len(confidence_scores)
        
        # 综合共识分数
        concept_consensus = sum(concept_scores) / len(concept_scores)
        final_score = (concept_consensus * 0.7 + avg_confidence * 0.3)
        
        return min(1.0, final_score)
    
    def _extract_key_insights(self, responses: Dict[str, AgentResponse]) -> List[str]:
        """从响应中提取关键见解"""
        insights = []
        
        # 收集高置信度的关键点
        for agent_id, response in responses.items():
            if response.confidence > 0.7:
                # 简单的见解提取（实际中可以使用更复杂的NLP）
                content_lines = response.content.split('\\n')
                for line in content_lines:
                    if ('建议' in line or '关键' in line or '重要' in line or 
                        'recommend' in line.lower() or 'key' in line.lower()):
                        insights.append(f"{agent_id}: {line.strip()}")
        
        return insights[:10]  # 限制数量
    
    def _identify_conflicts(self, responses: Dict[str, AgentResponse]) -> List[str]:
        """识别冲突观点"""
        conflicts = []
        
        # 简单冲突检测（实际中需要更复杂的语义分析）
        contents = list(responses.values())
        
        for i, resp1 in enumerate(contents):
            for j, resp2 in enumerate(contents[i+1:], i+1):
                # 检测否定词汇对
                negative_words = ['不', '没有', '不能', '不应该', 'not', 'cannot', 'should not']
                content1_words = set(resp1.content.lower().split())
                content2_words = set(resp2.content.lower().split())
                
                # 如果一个包含否定词而另一个不包含，可能存在冲突
                neg1 = any(word in content1_words for word in negative_words)
                neg2 = any(word in content2_words for word in negative_words)
                
                if neg1 != neg2 and len(content1_words & content2_words) > 3:
                    agent1_id = list(responses.keys())[i]
                    agent2_id = list(responses.keys())[j]
                    conflicts.append(f"{agent1_id} vs {agent2_id}: 观点存在分歧")
        
        return conflicts[:5]  # 限制数量
    
    def _generate_final_consensus(self, session: CollaborationSession) -> Dict[str, Any]:
        """生成最终共识"""
        if not session.rounds:
            return {}
        
        # 收集所有轮次的见解
        all_insights = []
        for round_data in session.rounds:
            all_insights.extend(round_data.key_insights)
        
        # 获取最高质量的响应
        best_responses = {}
        for round_data in session.rounds:
            for agent_id, response in round_data.responses.items():
                if agent_id not in best_responses or response.confidence > best_responses[agent_id].confidence:
                    best_responses[agent_id] = response
        
        # 生成综合共识
        consensus_prompt = f"""
基于多专家的深入讨论，请生成关于"{session.research_topic}"的最终共识报告。

讨论轮次: {len(session.rounds)}
参与专家: {', '.join(session.participants)}

关键见解总结:
{chr(10).join(all_insights)}

请提供：
1. 核心研究结论
2. 一致认同的关键点
3. 推荐的下一步行动
4. 需要注意的风险和限制
5. 跨学科协作建议

输出格式为JSON，包含以上5个部分的结构化内容。
"""
        
        # 使用统一API客户端生成共识
        try:
            response = self.unified_client.get_text_response(
                prompt=consensus_prompt,
                model_type="reasoning",
                temperature=0.3
            )
            
            if response.success:
                consensus_json = self.unified_client.extract_json(response.content)
                if consensus_json:
                    return consensus_json
            
        except Exception as e:
            logger.error(f"生成最终共识失败: {e}")
        
        # 备用方案：基于文本生成简单共识
        return {
            "core_conclusions": "多专家达成基本共识",
            "key_agreements": [insight[:100] for insight in all_insights[:3]],
            "next_actions": ["进一步验证关键假设", "设计详细实验方案"],
            "risks_limitations": ["需要更多数据验证", "跨学科协作复杂性"],
            "collaboration_advice": ["保持定期沟通", "建立共享知识库"]
        }
    
    def _evaluate_session_quality(self, session: CollaborationSession) -> float:
        """评估会话质量"""
        if not session.rounds:
            return 0.0
        
        # 评估维度
        factors = []
        
        # 1. 参与度 (参与专家数量)
        participation_score = min(1.0, len(session.participants) / 4)
        factors.append(participation_score * 0.2)
        
        # 2. 共识质量 (最终共识分数)
        if session.rounds:
            final_consensus_score = session.rounds[-1].consensus_score
            factors.append(final_consensus_score * 0.3)
        
        # 3. 见解质量 (见解数量和置信度)
        insight_count = sum(len(round_data.key_insights) for round_data in session.rounds)
        insight_score = min(1.0, insight_count / 10)
        factors.append(insight_score * 0.2)
        
        # 4. 效率 (轮次数量，越少越好)
        efficiency_score = 1.0 - (len(session.rounds) - 1) / self.collaboration_config["max_rounds"]
        factors.append(efficiency_score * 0.15)
        
        # 5. 响应质量 (平均置信度)
        all_confidences = []
        for round_data in session.rounds:
            for response in round_data.responses.values():
                all_confidences.append(response.confidence)
        
        if all_confidences:
            avg_confidence = sum(all_confidences) / len(all_confidences)
            factors.append(avg_confidence * 0.15)
        
        return sum(factors)
    
    def get_collaboration_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取协作摘要"""
        session = next((s for s in self.sessions if s.session_id == session_id), None)
        if not session:
            return None
        
        return {
            "session_id": session.session_id,
            "research_topic": session.research_topic,
            "participants": session.participants,
            "rounds_count": len(session.rounds),
            "final_consensus": session.final_consensus,
            "quality_score": session.quality_score,
            "duration": session.duration,
            "status": "completed" if session.final_consensus else "in_progress"
        }
    
    def save_session(self, session: CollaborationSession, file_path: str) -> bool:
        """保存会话到文件"""
        try:
            session_data = asdict(session)
            # 序列化AgentResponse对象
            for round_data in session_data["rounds"]:
                serialized_responses = {}
                for agent_id, response in round_data["responses"].items():
                    if hasattr(response, '__dict__'):
                        serialized_responses[agent_id] = response.__dict__
                    else:
                        serialized_responses[agent_id] = str(response)
                round_data["responses"] = serialized_responses
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 会话已保存: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存会话失败: {e}")
            return False

# 全局实例（可选）
_global_collaborator = None

def get_multi_agent_collaborator(unified_client: UnifiedAPIClient = None) -> EnhancedMultiAgentCollaborator:
    """获取全局协作系统实例"""
    global _global_collaborator
    if _global_collaborator is None:
        if unified_client is None:
            unified_client = UnifiedAPIClient()
        _global_collaborator = EnhancedMultiAgentCollaborator(unified_client)
    return _global_collaborator

if __name__ == "__main__":
    # 示例用法
    print("🧪 增强多专家协作系统演示")
    print("=" * 50)
    
    # 创建协作系统
    unified_client = UnifiedAPIClient()
    collaborator = EnhancedMultiAgentCollaborator(unified_client)
    
    # 示例协作会话
    research_topic = "脑启发人工神经网络的能耗优化策略"
    discussion_points = [
        "生物神经网络的能耗机制",
        "现有AI模型的能耗问题",
        "脑启发优化算法的可行性",
        "实验验证方法设计"
    ]
    
    # 创建会话
    session = collaborator.create_collaboration_session(
        research_topic=research_topic,
        specific_questions=discussion_points,
        required_experts=["ai_technology", "neuroscience"]
    )
    
    print(f"\\n📊 会话创建成功:")
    print(f"   ID: {session.session_id}")
    print(f"   参与专家: {session.participants}")
    
    # 注意：实际多轮讨论需要真实的API调用
    print("\\n💡 准备进行多轮协作讨论...")
    print("(实际运行需要有效的API连接)")
