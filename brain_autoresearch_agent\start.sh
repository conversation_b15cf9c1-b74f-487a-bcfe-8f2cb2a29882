#!/bin/bash

# Brain-Inspired Intelligence 论文生成系统 - 快速启动脚本

echo "🧠 Brain-Inspired Intelligence 论文生成系统启动中..."
echo "=================================================="

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ 错误: 未找到Python环境"
    echo "请确保已安装Python 3.8+"
    exit 1
fi

# 检查项目目录
if [ ! -f "paper_cli.py" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 创建必要的目录
echo "📁 创建输出目录..."
mkdir -p output
mkdir -p logs
mkdir -p data

# 检查依赖
echo "📦 检查依赖库..."
python -c "import json, datetime, logging, threading" 2>/dev/null || {
    echo "❌ 错误: 缺少必要的Python库"
    echo "请运行: pip install -r requirements.txt"
    exit 1
}

# 运行系统检查
echo "🔍 运行系统检查..."
python tests/test_complete_system.py > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ 系统检查通过"
else
    echo "⚠️ 系统检查有警告，但可以继续运行"
fi

# 启动主程序
echo "🚀 启动论文生成系统..."
echo "=================================================="
python paper_cli.py "$@"
