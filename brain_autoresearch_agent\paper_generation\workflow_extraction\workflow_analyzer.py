"""
Workflow Analyzer

分析提取的论文工作流，提供实验设计建议、技术选择建议等
"""

import os
import sys
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from agents.agent_manager import AgentManager
from paper_generation.workflow_extraction.workflow_data_models import (
    PaperWorkflow, DatasetInfo, NetworkArchitecture, ImplementationDetails,
    ResearchMethod, WORKFLOW_TEMPLATES
)


class WorkflowAnalyzer:
    """工作流分析器"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化工作流分析器
        
        Args:
            llm_client: LLM客户端实例
        """
        if llm_client is None:
            self.llm_client = LLMClient()
        else:
            self.llm_client = llm_client
            
        self.agent_manager = AgentManager(self.llm_client)
        self.logger = self._setup_logger()
        
        # 技术栈兼容性矩阵
        self.framework_compatibility = {
            'PyTorch': {
                'strengths': ['动态图', '研究友好', '调试方便', '社区活跃'],
                'suitable_for': ['研究原型', '复杂架构', '神经形态计算'],
                'compatible_tools': ['tensorboard', 'wandb', 'optuna'],
                'neuromorphic_support': 'snnTorch, SpykeTorch'
            },
            'TensorFlow': {
                'strengths': ['生产就绪', '分布式训练', '移动部署'],
                'suitable_for': ['生产部署', '大规模训练', '边缘计算'],
                'compatible_tools': ['tensorboard', 'keras', 'tf-agents'],
                'neuromorphic_support': 'limited'
            },
            'Brian2': {
                'strengths': ['神经科学建模', '脉冲神经网络', '生物物理模型'],
                'suitable_for': ['脉冲神经网络', '神经动力学', '计算神经科学'],
                'compatible_tools': ['matplotlib', 'numpy', 'scipy'],
                'neuromorphic_support': 'excellent'
            },
            'JAX': {
                'strengths': ['函数式编程', '自动微分', '并行计算'],
                'suitable_for': ['数值计算', '科学计算', '高性能计算'],
                'compatible_tools': ['optax', 'haiku', 'flax'],
                'neuromorphic_support': 'moderate'
            }
        }
        
        # 数据集特征和要求
        self.dataset_requirements = {
            'Image': {
                'preprocessing': ['normalization', 'data_augmentation', 'resize'],
                'architectures': ['CNN', 'ResNet', 'Vision Transformer'],
                'metrics': ['accuracy', 'top-5 accuracy', 'confusion matrix']
            },
            'Text': {
                'preprocessing': ['tokenization', 'embedding', 'padding'],
                'architectures': ['Transformer', 'BERT', 'RNN'],
                'metrics': ['BLEU', 'F1-score', 'perplexity']
            },
            'Time Series': {
                'preprocessing': ['normalization', 'windowing', 'feature extraction'],
                'architectures': ['LSTM', 'GRU', 'SNN'],
                'metrics': ['MSE', 'MAPE', 'temporal accuracy']
            }
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('WorkflowAnalyzer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def analyze_workflow(self, workflow: PaperWorkflow) -> Dict[str, Any]:
        """
        全面分析工作流
        
        Args:
            workflow: 提取的论文工作流
            
        Returns:
            分析结果字典
        """
        print(f"\n🔍 开始分析工作流: {workflow.title}")
        
        analysis = {
            'workflow_summary': self._generate_workflow_summary(workflow),
            'technical_analysis': self._analyze_technical_choices(workflow),
            'experimental_design': self._analyze_experimental_design(workflow),
            'implementation_suggestions': self._generate_implementation_suggestions(workflow),
            'potential_improvements': self._identify_potential_improvements(workflow),
            'replication_guide': self._generate_replication_guide(workflow),
            'related_templates': self._find_related_templates(workflow),
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        print("✅ 工作流分析完成")
        return analysis
    
    def _generate_workflow_summary(self, workflow: PaperWorkflow) -> Dict[str, Any]:
        """生成工作流摘要"""
        summary = {
            'research_area': self._infer_research_area(workflow),
            'complexity_level': self._assess_complexity(workflow),
            'key_technologies': self._extract_key_technologies(workflow),
            'data_requirements': self._analyze_data_requirements(workflow),
            'computational_demands': self._assess_computational_demands(workflow),
            'novelty_indicators': self._identify_novelty_indicators(workflow)
        }
        
        return summary
    
    def _analyze_technical_choices(self, workflow: PaperWorkflow) -> Dict[str, Any]:
        """分析技术选择"""
        analysis = {}
        
        # 分析框架选择
        if workflow.implementation_details:
            frameworks = [impl.framework.value for impl in workflow.implementation_details]
            analysis['framework_analysis'] = self._analyze_framework_choices(frameworks)
        
        # 分析架构选择
        if workflow.network_architectures:
            architectures = [arch.type.value for arch in workflow.network_architectures]
            analysis['architecture_analysis'] = self._analyze_architecture_choices(architectures, workflow)
        
        # 分析数据集选择
        if workflow.datasets:
            analysis['dataset_analysis'] = self._analyze_dataset_choices(workflow.datasets)
        
        return analysis
    
    def _analyze_framework_choices(self, frameworks: List[str]) -> Dict[str, Any]:
        """分析框架选择"""
        analysis = {
            'frameworks_used': frameworks,
            'compatibility_assessment': {},
            'alternative_suggestions': [],
            'pros_and_cons': {}
        }
        
        for framework in frameworks:
            if framework in self.framework_compatibility:
                comp_info = self.framework_compatibility[framework]
                analysis['compatibility_assessment'][framework] = comp_info
                analysis['pros_and_cons'][framework] = {
                    'pros': comp_info['strengths'],
                    'suitable_for': comp_info['suitable_for']
                }
        
        return analysis
    
    def _analyze_architecture_choices(self, architectures: List[str], workflow: PaperWorkflow) -> Dict[str, Any]:
        """分析架构选择"""
        analysis = {
            'architectures_used': architectures,
            'suitability_assessment': {},
            'brain_inspired_elements': workflow.brain_inspired_elements,
            'innovation_level': self._assess_architecture_innovation(workflow.network_architectures)
        }
        
        # 评估架构与数据集的匹配度
        if workflow.datasets:
            analysis['dataset_architecture_match'] = self._assess_dataset_architecture_match(
                workflow.datasets, architectures
            )
        
        return analysis
    
    def _analyze_dataset_choices(self, datasets: List[DatasetInfo]) -> Dict[str, Any]:
        """分析数据集选择"""
        analysis = {
            'datasets_summary': [],
            'preprocessing_requirements': [],
            'evaluation_suggestions': []
        }
        
        for dataset in datasets:
            dataset_summary = {
                'name': dataset.name,
                'type': dataset.type.value,
                'characteristics': dataset.characteristics,
                'preprocessing_needs': self._get_preprocessing_needs(dataset.type.value)
            }
            analysis['datasets_summary'].append(dataset_summary)
            
            if dataset.type.value in self.dataset_requirements:
                requirements = self.dataset_requirements[dataset.type.value]
                analysis['preprocessing_requirements'].extend(requirements['preprocessing'])
                analysis['evaluation_suggestions'].extend(requirements['metrics'])
        
        # 去重
        analysis['preprocessing_requirements'] = list(set(analysis['preprocessing_requirements']))
        analysis['evaluation_suggestions'] = list(set(analysis['evaluation_suggestions']))
        
        return analysis
    
    def _analyze_experimental_design(self, workflow: PaperWorkflow) -> Dict[str, Any]:
        """分析实验设计"""
        analysis = {
            'design_quality': 'Unknown',
            'missing_elements': [],
            'strength_indicators': [],
            'improvement_suggestions': []
        }
        
        if workflow.experiment_setup:
            setup = workflow.experiment_setup
            
            # 评估实验设计质量
            quality_score = 0
            
            if setup.hypotheses:
                quality_score += 2
                analysis['strength_indicators'].append('明确的研究假设')
            else:
                analysis['missing_elements'].append('研究假设')
            
            if setup.evaluation_metrics:
                quality_score += 2
                analysis['strength_indicators'].append('清晰的评估指标')
            else:
                analysis['missing_elements'].append('评估指标')
            
            if setup.baseline_methods:
                quality_score += 2
                analysis['strength_indicators'].append('基准方法对比')
            else:
                analysis['missing_elements'].append('基准方法')
            
            if setup.control_variables:
                quality_score += 1
                analysis['strength_indicators'].append('控制变量')
            
            if setup.independent_variables and setup.dependent_variables:
                quality_score += 1
                analysis['strength_indicators'].append('变量设计')
            
            # 质量评估
            if quality_score >= 6:
                analysis['design_quality'] = 'Excellent'
            elif quality_score >= 4:
                analysis['design_quality'] = 'Good'
            elif quality_score >= 2:
                analysis['design_quality'] = 'Fair'
            else:
                analysis['design_quality'] = 'Poor'
        else:
            analysis['missing_elements'].append('完整的实验设计')
        
        return analysis
    
    def _generate_implementation_suggestions(self, workflow: PaperWorkflow) -> Dict[str, Any]:
        """生成实现建议"""
        suggestions = {
            'recommended_frameworks': [],
            'architecture_improvements': [],
            'preprocessing_pipeline': [],
            'training_strategies': [],
            'evaluation_protocol': []
        }
        
        # 基于脑启发元素推荐框架
        if workflow.brain_inspired_elements:
            if any(element in ['spiking', 'plasticity'] for element in workflow.brain_inspired_elements):
                suggestions['recommended_frameworks'].append({
                    'framework': 'Brian2',
                    'reason': '专为脉冲神经网络和神经可塑性建模设计'
                })
                suggestions['recommended_frameworks'].append({
                    'framework': 'snnTorch',
                    'reason': 'PyTorch生态下的脉冲神经网络库'
                })
        
        # 基于数据集类型推荐架构改进
        if workflow.datasets:
            for dataset in workflow.datasets:
                if dataset.type.value in self.dataset_requirements:
                    requirements = self.dataset_requirements[dataset.type.value]
                    suggestions['preprocessing_pipeline'].extend(requirements['preprocessing'])
                    suggestions['evaluation_protocol'].extend(requirements['metrics'])
        
        # 去重
        suggestions['preprocessing_pipeline'] = list(set(suggestions['preprocessing_pipeline']))
        suggestions['evaluation_protocol'] = list(set(suggestions['evaluation_protocol']))
        
        return suggestions
    
    def _identify_potential_improvements(self, workflow: PaperWorkflow) -> List[Dict[str, Any]]:
        """识别潜在改进点"""
        improvements = []
        
        # 检查是否可以引入更多脑启发机制
        if len(workflow.brain_inspired_elements) < 2:
            improvements.append({
                'category': 'Brain-Inspired Enhancement',
                'suggestion': '考虑引入更多脑启发机制，如注意力机制、神经可塑性或层次化处理',
                'priority': 'Medium',
                'impact': '提升模型的生物可解释性和性能'
            })
        
        # 检查数据集多样性
        if len(workflow.datasets) < 2:
            improvements.append({
                'category': 'Dataset Diversity',
                'suggestion': '在多个数据集上验证方法的泛化性能',
                'priority': 'High',
                'impact': '增强结果的可信度和普遍性'
            })
        
        # 检查架构创新
        if not any('创新' in str(arch.innovations) for arch in workflow.network_architectures):
            improvements.append({
                'category': 'Architectural Innovation',
                'suggestion': '考虑在现有架构基础上引入新的结构创新',
                'priority': 'Medium',
                'impact': '提升方法的新颖性和技术贡献'
            })
        
        return improvements
    
    def _generate_replication_guide(self, workflow: PaperWorkflow) -> Dict[str, Any]:
        """生成复现指南"""
        guide = {
            'environment_setup': self._generate_environment_setup(workflow),
            'data_preparation': self._generate_data_preparation_steps(workflow),
            'implementation_steps': self._generate_implementation_steps(workflow),
            'evaluation_protocol': self._generate_evaluation_protocol(workflow),
            'expected_results': self._extract_expected_results(workflow)
        }
        
        return guide
    
    def _generate_environment_setup(self, workflow: PaperWorkflow) -> List[str]:
        """生成环境设置步骤"""
        steps = []
        
        if workflow.implementation_details:
            for impl in workflow.implementation_details:
                steps.append(f"安装 {impl.framework.value}")
                if impl.version:
                    steps.append(f"确保版本为 {impl.version}")
                
                steps.extend([f"安装依赖: {dep}" for dep in impl.software_dependencies])
                
                if impl.hardware_requirements:
                    steps.append(f"硬件需求: {', '.join(impl.hardware_requirements)}")
        
        return steps
    
    def _generate_data_preparation_steps(self, workflow: PaperWorkflow) -> List[str]:
        """生成数据准备步骤"""
        steps = []
        
        for dataset in workflow.datasets:
            steps.append(f"下载数据集: {dataset.name}")
            if dataset.source_url:
                steps.append(f"数据源: {dataset.source_url}")
            
            steps.extend([f"预处理: {step}" for step in dataset.preprocessing_steps])
            
            if dataset.split_strategy:
                steps.append(f"数据分割: {dataset.split_strategy}")
        
        return steps
    
    def _generate_implementation_steps(self, workflow: PaperWorkflow) -> List[str]:
        """生成实现步骤"""
        steps = []
        
        for arch in workflow.network_architectures:
            steps.append(f"实现网络架构: {arch.name}")
            if arch.layers:
                steps.extend([f"添加层: {layer}" for layer in arch.layers])
            
            if arch.innovations:
                steps.extend([f"实现创新点: {innovation}" for innovation in arch.innovations])
        
        return steps
    
    def _generate_evaluation_protocol(self, workflow: PaperWorkflow) -> List[str]:
        """生成评估协议"""
        protocol = []
        
        if workflow.experiment_setup:
            setup = workflow.experiment_setup
            
            if setup.evaluation_metrics:
                protocol.extend([f"计算指标: {metric}" for metric in setup.evaluation_metrics])
            
            if setup.baseline_methods:
                protocol.extend([f"与基准方法比较: {baseline}" for baseline in setup.baseline_methods])
        
        return protocol
    
    def _extract_expected_results(self, workflow: PaperWorkflow) -> List[str]:
        """提取预期结果"""
        return workflow.key_results
    
    def _find_related_templates(self, workflow: PaperWorkflow) -> List[str]:
        """找到相关的工作流模板"""
        related_templates = []
        
        # 基于脑启发元素匹配模板
        if workflow.brain_inspired_elements:
            if any(element in ['spiking', 'plasticity'] for element in workflow.brain_inspired_elements):
                related_templates.append('spiking_neural_network')
            
            if 'attention' in workflow.brain_inspired_elements:
                related_templates.append('attention_mechanism')
        
        # 基于架构类型匹配
        for arch in workflow.network_architectures:
            if 'CNN' in arch.type.value or 'Convolutional' in arch.type.value:
                related_templates.append('brain_inspired_cnn')
        
        return list(set(related_templates))
    
    # 辅助方法
    def _infer_research_area(self, workflow: PaperWorkflow) -> str:
        """推断研究领域"""
        if workflow.brain_inspired_elements:
            return "Brain-Inspired Intelligence"
        elif any('CNN' in arch.type.value for arch in workflow.network_architectures):
            return "Computer Vision"
        elif any('Transformer' in arch.type.value for arch in workflow.network_architectures):
            return "Natural Language Processing"
        else:
            return "Machine Learning"
    
    def _assess_complexity(self, workflow: PaperWorkflow) -> str:
        """评估复杂度"""
        complexity_score = 0
        
        complexity_score += len(workflow.datasets)
        complexity_score += len(workflow.network_architectures) * 2
        complexity_score += len(workflow.implementation_details)
        complexity_score += len(workflow.research_methods) * 2
        
        if complexity_score >= 10:
            return "High"
        elif complexity_score >= 5:
            return "Medium"
        else:
            return "Low"
    
    def _extract_key_technologies(self, workflow: PaperWorkflow) -> List[str]:
        """提取关键技术"""
        technologies = []
        
        technologies.extend([impl.framework.value for impl in workflow.implementation_details])
        technologies.extend([arch.type.value for arch in workflow.network_architectures])
        technologies.extend(workflow.brain_inspired_elements)
        
        return list(set(technologies))
    
    def _analyze_data_requirements(self, workflow: PaperWorkflow) -> Dict[str, Any]:
        """分析数据需求"""
        requirements = {
            'data_types': [dataset.type.value for dataset in workflow.datasets],
            'preprocessing_complexity': 'Medium',
            'storage_requirements': 'Unknown'
        }
        
        return requirements
    
    def _assess_computational_demands(self, workflow: PaperWorkflow) -> str:
        """评估计算需求"""
        # 基于架构复杂度和数据集大小评估
        demands = "Medium"
        
        if any('SNN' in arch.type.value for arch in workflow.network_architectures):
            demands = "High"  # 脉冲神经网络通常计算密集
        
        return demands
    
    def _identify_novelty_indicators(self, workflow: PaperWorkflow) -> List[str]:
        """识别新颖性指标"""
        indicators = []
        
        for arch in workflow.network_architectures:
            indicators.extend(arch.innovations)
        
        indicators.extend(workflow.contributions)
        
        return list(set(indicators))
    
    def _assess_architecture_innovation(self, architectures: List[NetworkArchitecture]) -> str:
        """评估架构创新程度"""
        innovation_score = 0
        
        for arch in architectures:
            innovation_score += len(arch.innovations)
            if arch.brain_inspiration:
                innovation_score += 2
        
        if innovation_score >= 5:
            return "High"
        elif innovation_score >= 2:
            return "Medium"
        else:
            return "Low"
    
    def _assess_dataset_architecture_match(self, datasets: List[DatasetInfo], architectures: List[str]) -> Dict[str, str]:
        """评估数据集与架构的匹配度"""
        matches = {}
        
        for dataset in datasets:
            dataset_type = dataset.type.value
            suitable_archs = self.dataset_requirements.get(dataset_type, {}).get('architectures', [])
            
            match_quality = "Poor"
            for arch in architectures:
                if any(suitable in arch for suitable in suitable_archs):
                    match_quality = "Good"
                    break
            
            matches[dataset.name] = match_quality
        
        return matches
    
    def _get_preprocessing_needs(self, dataset_type: str) -> List[str]:
        """获取预处理需求"""
        return self.dataset_requirements.get(dataset_type, {}).get('preprocessing', [])


def test_workflow_analyzer():
    """测试工作流分析器"""
    print("🧪 测试工作流分析器")
    
    # 这里需要先有一个工作流对象进行测试
    # 可以使用之前的PaperWorkflowExtractor生成的结果
    
    analyzer = WorkflowAnalyzer()
    print("✅ 工作流分析器初始化成功")
    
    # 可以在这里添加更多测试逻辑
    
    return analyzer


if __name__ == "__main__":
    test_workflow_analyzer()
