"""
Brain AutoResearch Agent - 分模块真实API测试
按照用户需求分别测试各个模块的功能

用户需求映射测试:
1. 论文数据库构建workflow
2. 多领域专家agents  
3. reasoning flow (4个子模块)
4. 论文撰写 (3个子模块)
"""

import sys
import os
import json
import time
from datetime import datetime
from typing import Dict, Any, List

# 设置API环境变量
os.environ["DEEPSEEK_API_KEY"] = "***********************************"
os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
os.environ["DASHSCOPE_API_KEY"] = "sk-f8559ea97bad4d638416d20db63bc643"
os.environ["QWEN_BASE_URL"] = "https://dashscope.aliyuncs.com/compatible-mode/v1"

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class ModularSystemTester:
    """分模块系统测试器"""
    
    def __init__(self):
        """初始化测试器"""
        print("🧪 Brain AutoResearch Agent - 分模块测试系统")
        print("=" * 60)
        self.test_results = {}
        self.sample_research_topic = "Brain-inspired adaptive neural networks with synaptic plasticity"
    
    def test_module_1_paper_database_workflow(self) -> bool:
        """测试模块1: 论文数据库构建workflow"""
        print("\n📚 测试模块1: 论文数据库构建workflow")
        print("-" * 50)
        
        try:
            # 测试文献搜索工具
            from core.semantic_scholar_tool import SemanticScholarTool
            scholar_tool = SemanticScholarTool()
            
            print("🔍 测试Semantic Scholar搜索...")
            papers = scholar_tool.search_papers(
                query=self.sample_research_topic,
                limit=5
            )
            
            if papers and len(papers) > 0:
                print(f"   ✅ 搜索到论文: {len(papers)} 篇")
                
                # 测试workflow提取
                from core.paper_workflow import PaperWorkflowExtractor
                extractor = PaperWorkflowExtractor()
                
                print("🔧 测试workflow提取...")
                
                workflow_count = 0
                for i, paper in enumerate(papers[:3]):
                    if paper.get('abstract'):
                        workflow = extractor.extract_workflow({
                            'title': paper.get('title', ''),
                            'abstract': paper.get('abstract', ''),
                            'year': paper.get('year', 2024)
                        })
                        
                        if workflow:
                            workflow_count += 1
                            print(f"   ✅ 论文 {i+1}: workflow提取成功")
                            
                            # 显示提取的信息
                            if workflow.get('datasets'):
                                print(f"      数据集: {workflow['datasets'][:3]}")
                            if workflow.get('network_structures'):
                                print(f"      网络结构: {workflow['network_structures'][:3]}")
                
                if workflow_count > 0:
                    print(f"   📊 成功提取workflow: {workflow_count}/3")
                    self.test_results['module_1'] = {
                        'success': True,
                        'papers_found': len(papers),
                        'workflows_extracted': workflow_count
                    }
                    return True
                else:
                    print("   ❌ workflow提取失败")
                    self.test_results['module_1'] = {'success': False, 'error': 'workflow提取失败'}
                    return False
            else:
                print("   ❌ 未找到相关论文")
                self.test_results['module_1'] = {'success': False, 'error': '未找到论文'}
                return False
                
        except Exception as e:
            print(f"   ❌ 模块1测试失败: {e}")
            self.test_results['module_1'] = {'success': False, 'error': str(e)}
            return False
    
    def test_module_2_expert_agents(self) -> bool:
        """测试模块2: 多领域专家agents"""
        print("\n👥 测试模块2: 多领域专家agents")
        print("-" * 50)
        
        try:
            from core.llm_client import LLMClient
            from agents.agent_manager import AgentManager
            from agents.expert_agents.ai_technology_expert import AITechnologyExpert
            from agents.expert_agents.neuroscience_expert import NeuroscienceExpert
            from agents.expert_agents.data_analysis_expert import DataAnalysisExpert
            
            # 初始化LLM客户端
            llm_client = LLMClient()
            
            # 初始化专家代理
            agent_manager = AgentManager(llm_client)
            
            # AgentManager在初始化时已自动注册所有专家代理
            registered_agents = list(agent_manager.agents.keys())
            
            print(f"   ✅ 已注册专家: {len(registered_agents)} 位")
            print(f"      专家列表: {registered_agents}")
            
            # 测试协作分析 (真实API调用)
            print("🤔 测试专家协作分析...")
            
            expert_analyses = agent_manager.collaborative_analysis(
                research_topic=self.sample_research_topic
            )
            
            if expert_analyses and len(expert_analyses) > 0:
                print(f"   ✅ 专家分析完成: {len(expert_analyses)} 位专家参与")
                
                total_confidence = 0
                for analysis in expert_analyses:
                    print(f"      - {analysis.agent_name}: 置信度 {analysis.confidence:.2f}")
                    total_confidence += analysis.confidence
                
                avg_confidence = total_confidence / len(expert_analyses)
                
                if avg_confidence >= 6.0:  # 置信度阈值
                    self.test_results['module_2'] = {
                        'success': True,
                        'expert_count': len(expert_analyses),
                        'average_confidence': avg_confidence
                    }
                    print(f"   📊 平均置信度: {avg_confidence:.2f} (≥6.0 通过)")
                    return True
                else:
                    print(f"   ⚠️ 平均置信度过低: {avg_confidence:.2f}")
                    self.test_results['module_2'] = {'success': False, 'error': '置信度过低'}
                    return False
            else:
                print("   ❌ 专家分析失败")
                self.test_results['module_2'] = {'success': False, 'error': '专家分析失败'}
                return False
                
        except Exception as e:
            print(f"   ❌ 模块2测试失败: {e}")
            self.test_results['module_2'] = {'success': False, 'error': str(e)}
            return False
    
    def test_module_3_reasoning_flow(self) -> bool:
        """测试模块3: reasoning flow (4个子模块)"""
        print("\n🧠 测试模块3: Reasoning Flow")
        print("-" * 50)
        
        reasoning_success = 0
        total_components = 4
        
        try:
            # 3a. 测试研究问题价值讨论
            print("🤔 3a. 测试研究问题价值讨论...")
            
            from reasoning.research_question_evaluator import ResearchQuestionEvaluator
            evaluator = ResearchQuestionEvaluator()
            
            evaluation_result = evaluator.evaluate_research_question(
                research_question=self.sample_research_topic,
                expert_insights={},
                literature_context={}
            )
            
            if evaluation_result and evaluation_result.get('overall_score', 0) > 0:
                score = evaluation_result.get('overall_score', 0)
                print(f"   ✅ 研究价值评估: {score:.2f}/10")
                reasoning_success += 1
            else:
                print("   ❌ 研究价值评估失败")
            
            # 3b. 测试实验方案生成
            print("🧪 3b. 测试实验方案生成...")
            
            from reasoning.hypothesis_experiment_designer import HypothesisExperimentDesigner
            designer = HypothesisExperimentDesigner()
            
            experiment_design = designer.design_experiments(
                research_question=self.sample_research_topic,
                hypotheses=["假设1: 脑启发网络更高效", "假设2: 自适应性更好"],
                literature_insights=[]
            )
            
            if experiment_design and experiment_design.get('experiments'):
                experiments = experiment_design.get('experiments', [])
                print(f"   ✅ 实验设计: {len(experiments)} 个实验")
                reasoning_success += 1
            else:
                print("   ❌ 实验设计失败")
            
            # 3c. 测试实现方法讨论
            print("🛠️ 3c. 测试实现方法讨论...")
            
            from reasoning.implementation_planner import ImplementationPlanner
            planner = ImplementationPlanner()
            
            implementation_plan = planner.generate_implementation_plan(
                research_topic=self.sample_research_topic,
                experiment_design=experiment_design,
                workflow_context={}
            )
            
            if implementation_plan and implementation_plan.get('recommended_framework'):
                framework = implementation_plan.get('recommended_framework', 'N/A')
                print(f"   ✅ 实现计划: 推荐框架 {framework}")
                reasoning_success += 1
            else:
                print("   ❌ 实现计划失败")
            
            # 3d. 测试展示图方案
            print("📊 3d. 测试展示图方案...")
            
            from reasoning.visualization_advisor import VisualizationAdvisor
            advisor = VisualizationAdvisor()
            
            visualization_plan = advisor.suggest_visualizations(
                research_topic=self.sample_research_topic,
                experiment_results=experiment_design,
                implementation_details=implementation_plan
            )
            
            if visualization_plan and visualization_plan.get('suggested_plots'):
                plots = visualization_plan.get('suggested_plots', [])
                print(f"   ✅ 可视化方案: {len(plots)} 个图表类型")
                reasoning_success += 1
            else:
                print("   ❌ 可视化方案失败")
            
            # 评估推理流程整体成功率
            success_rate = reasoning_success / total_components
            
            if success_rate >= 0.5:  # 50%以上成功
                self.test_results['module_3'] = {
                    'success': True,
                    'success_rate': success_rate,
                    'components_passed': reasoning_success,
                    'total_components': total_components
                }
                print(f"   📊 推理流程成功率: {success_rate:.1%} ({reasoning_success}/{total_components})")
                return True
            else:
                self.test_results['module_3'] = {'success': False, 'error': f'成功率过低: {success_rate:.1%}'}
                return False
                
        except Exception as e:
            print(f"   ❌ 模块3测试失败: {e}")
            self.test_results['module_3'] = {'success': False, 'error': str(e)}
            return False
    
    def test_module_4_paper_writing(self) -> bool:
        """测试模块4: 论文撰写 (3个子模块)"""
        print("\n📝 测试模块4: 论文撰写")
        print("-" * 50)
        
        writing_success = 0
        total_components = 3
        
        try:
            # 4a. 测试论文框架+文献调研
            print("📋 4a. 测试论文框架+文献调研...")
            
            from paper_generation.enhanced_citation_manager import EnhancedCitationManager
            citation_manager = EnhancedCitationManager()
            
            # 准备论文元数据
            paper_metadata = {
                'title': self.sample_research_topic,
                'keywords': ['brain-inspired', 'neural networks', 'synaptic plasticity'],
                'abstract': 'Research on brain-inspired adaptive neural networks'
            }
            
            citation_results = citation_manager.collect_citations_sync(
                paper_metadata=paper_metadata,
                target_count=10
            )
            
            if citation_results and len(citation_results) > 0:
                citation_count = len(citation_results)
                print(f"   ✅ 文献调研: 收集到 {citation_count} 个引用")
                writing_success += 1
            else:
                print("   ❌ 文献调研失败")
            
            # 4b. 测试自动论文撰写
            print("✍️ 4b. 测试自动论文撰写...")
            
            from paper_generation.brain_paper_writer import BrainPaperWriter
            paper_writer = BrainPaperWriter()
            
            paper_content = paper_writer.generate_paper(
                topic=self.sample_research_topic,
                research_insights={},
                experiment_plan={},
                literature_review=[],
                citations=citation_results.get('citations', []) if citation_results else []
            )
            
            if paper_content and len(paper_content) > 1000:  # 至少1000字符
                print(f"   ✅ 论文生成: {len(paper_content):,} 字符")
                
                # 保存论文用于下一步测试
                test_paper_file = f"test_paper_{int(time.time())}.tex"
                with open(test_paper_file, 'w', encoding='utf-8') as f:
                    f.write(paper_content)
                
                writing_success += 1
            else:
                print("   ❌ 论文生成失败或内容过短")
                paper_content = None
            
            # 4c. 测试多专家review+revision
            print("👥 4c. 测试多专家review+revision...")
            
            if paper_content:
                from paper_generation.multi_expert_review_system import MultiExpertReviewSystem
                review_system = MultiExpertReviewSystem()
                
                review_results = review_system.conduct_review(
                    paper_content={'title': self.sample_research_topic, 'content': paper_content},
                    target_venue="ICML",
                    quality_threshold=6.0
                )
                
                if review_results and review_results.get('consensus_score', 0) > 0:
                    score = review_results.get('consensus_score', 0)
                    print(f"   ✅ 多专家评审: {score:.2f}/10")
                    
                    # 如果评分较低，测试质量优化
                    if score < 7.0:
                        print("🔧 测试质量优化...")
                        
                        from paper_generation.paper_quality_optimizer import PaperQualityOptimizer
                        optimizer = PaperQualityOptimizer()
                        
                        optimization_results = optimizer.optimize_paper(
                            paper_content=paper_content,
                            review_feedback=review_results,
                            target_quality=7.5
                        )
                        
                        if optimization_results and optimization_results.get('success'):
                            print("   ✅ 质量优化完成")
                        else:
                            print("   ⚠️ 质量优化失败")
                    
                    writing_success += 1
                else:
                    print("   ❌ 多专家评审失败")
            else:
                print("   ⏭️ 跳过评审 (无论文内容)")
            
            # 评估论文撰写整体成功率
            success_rate = writing_success / total_components
            
            if success_rate >= 0.6:  # 60%以上成功
                self.test_results['module_4'] = {
                    'success': True,
                    'success_rate': success_rate,
                    'components_passed': writing_success,
                    'total_components': total_components
                }
                print(f"   📊 论文撰写成功率: {success_rate:.1%} ({writing_success}/{total_components})")
                return True
            else:
                self.test_results['module_4'] = {'success': False, 'error': f'成功率过低: {success_rate:.1%}'}
                return False
                
        except Exception as e:
            print(f"   ❌ 模块4测试失败: {e}")
            self.test_results['module_4'] = {'success': False, 'error': str(e)}
            return False
    
    def test_advanced_features(self) -> bool:
        """测试高级功能 (第二优先级)"""
        print("\n🎯 测试高级功能 (第二优先级)")
        print("-" * 50)
        
        advanced_success = 0
        total_features = 3
        
        try:
            # 会议模板适配
            print("🏛️ 测试会议模板适配...")
            
            from paper_generation.conference_template_adapter import ConferenceTemplateAdapter
            adapter = ConferenceTemplateAdapter()
            
            sample_paper = {
                'title': self.sample_research_topic,
                'content': r'\documentclass{article}\begin{document}\title{Sample}\maketitle\section{Introduction}Test content.\end{document}'
            }
            
            formatted_paper = adapter.format_for_conference(sample_paper, "ICML")
            
            if formatted_paper and len(formatted_paper) > len(sample_paper['content']):
                print("   ✅ 会议模板适配成功")
                advanced_success += 1
            else:
                print("   ❌ 会议模板适配失败")
            
            # 实验代码生成
            print("💻 测试实验代码生成...")
            
            from core.llm_client import LLMClient
            from core.experiment_code_generator import ExperimentCodeGenerator
            
            llm_client = LLMClient(model="deepseek-chat")
            code_generator = ExperimentCodeGenerator(llm_client)
            
            experiment_spec = code_generator.generate_experiment_specification(
                self.sample_research_topic, "ICML"
            )
            
            if experiment_spec:
                print(f"   ✅ 实验规格生成: {experiment_spec.name}")
                
                # 生成代码
                code_output_dir = f"test_code_{int(time.time())}"
                files_created = code_generator.generate_complete_experiment(
                    experiment_spec, code_output_dir
                )
                
                if files_created and len(files_created) > 0:
                    total_size = 0
                    for file_path in files_created.values():
                        if os.path.exists(file_path):
                            total_size += os.path.getsize(file_path)
                    
                    print(f"   ✅ 代码生成成功: {total_size:,} 字节")
                    advanced_success += 1
                else:
                    print("   ❌ 代码生成失败")
            else:
                print("   ❌ 实验规格生成失败")
            
            # 视觉布局优化 (简化测试)
            print("👁️ 测试视觉布局优化...")
            
            try:
                from paper_generation.visual_layout_optimizer import PaperLayoutOptimizer
                optimizer = PaperLayoutOptimizer()
                
                # 由于需要PDF编译，这里只测试分析功能
                sample_latex = r"""
                \documentclass{article}
                \begin{document}
                \title{Test Paper}
                \author{Test Author}
                \maketitle
                \section{Introduction}
                This is a test paper for layout analysis.
                \end{document}
                """
                
                analysis_result = optimizer.analyze_paper_layout(sample_latex, "Test Paper")
                
                if analysis_result and analysis_result.overall_score > 0:
                    print(f"   ✅ 布局分析: {analysis_result.overall_score:.1f}/10")
                    advanced_success += 1
                else:
                    print("   ❌ 布局分析失败")
                    
            except Exception as e:
                print(f"   ❌ 视觉优化测试失败: {e}")
            
            # 评估高级功能成功率
            success_rate = advanced_success / total_features
            
            self.test_results['advanced_features'] = {
                'success': advanced_success > 0,
                'success_rate': success_rate,
                'features_passed': advanced_success,
                'total_features': total_features
            }
            
            print(f"   📊 高级功能成功率: {success_rate:.1%} ({advanced_success}/{total_features})")
            
            return advanced_success > 0
            
        except Exception as e:
            print(f"   ❌ 高级功能测试失败: {e}")
            self.test_results['advanced_features'] = {'success': False, 'error': str(e)}
            return False
    
    def run_all_tests(self):
        """运行所有模块测试"""
        print(f"🎯 开始分模块测试 - 研究主题: {self.sample_research_topic}")
        print("=" * 60)
        
        start_time = time.time()
        
        # 执行各模块测试
        tests = [
            ("模块1: 论文数据库构建", self.test_module_1_paper_database_workflow),
            ("模块2: 多领域专家agents", self.test_module_2_expert_agents),
            ("模块3: Reasoning Flow", self.test_module_3_reasoning_flow),
            ("模块4: 论文撰写", self.test_module_4_paper_writing),
            ("高级功能", self.test_advanced_features)
        ]
        
        passed_tests = 0
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed_tests += 1
            except Exception as e:
                print(f"   💥 {test_name} 测试异常: {e}")
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 生成测试报告
        self._generate_modular_test_report(passed_tests, len(tests), duration)
    
    def _generate_modular_test_report(self, passed_tests: int, total_tests: int, duration: float):
        """生成模块测试报告"""
        print("\n" + "=" * 60)
        print("📊 Brain AutoResearch Agent - 分模块测试报告")
        print("=" * 60)
        
        success_rate = (passed_tests / total_tests) * 100
        
        print(f"🎯 测试主题: {self.sample_research_topic}")
        print(f"⏱️ 测试耗时: {duration:.2f} 秒")
        print(f"📈 总体成功率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        print(f"\n📋 各模块测试结果:")
        
        module_names = {
            'module_1': '📚 模块1: 论文数据库构建workflow',
            'module_2': '👥 模块2: 多领域专家agents',
            'module_3': '🧠 模块3: Reasoning Flow',
            'module_4': '📝 模块4: 论文撰写',
            'advanced_features': '🎯 高级功能 (第二优先级)'
        }
        
        for module_key, module_name in module_names.items():
            result = self.test_results.get(module_key, {})
            if result.get('success', False):
                print(f"   ✅ {module_name}: 通过")
                if 'success_rate' in result:
                    print(f"      └─ 成功率: {result['success_rate']:.1%}")
            else:
                error_msg = result.get('error', '未知错误')
                print(f"   ❌ {module_name}: 失败 - {error_msg}")
        
        # 用户需求满足度分析
        print(f"\n💯 用户需求满足度分析:")
        
        requirements_mapping = {
            'module_1': '✅ 1. 论文数据库构建workflow: 提取数据集、网络结构、平台工具、研究方法',
            'module_2': '✅ 2. 多领域专家agents: AI专家、神经科学专家等',
            'module_3': '✅ 3. Reasoning Flow: 问题讨论、实验方案、实现方法、展示图方案',
            'module_4': '✅ 4. 论文撰写: 框架+调研、自动撰写、多专家review+revision'
        }
        
        for module_key, requirement_desc in requirements_mapping.items():
            if self.test_results.get(module_key, {}).get('success', False):
                print(f"   {requirement_desc}")
            else:
                print(f"   ❌ {requirement_desc[3:]}")
        
        # 保存详细报告
        report_file = f"modular_test_report_{int(time.time())}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump({
                'test_topic': self.sample_research_topic,
                'test_duration': duration,
                'success_rate': success_rate,
                'passed_tests': passed_tests,
                'total_tests': total_tests,
                'detailed_results': self.test_results,
                'timestamp': datetime.now().isoformat()
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 详细报告已保存: {report_file}")
        
        # 最终建议
        if success_rate >= 80:
            print(f"\n🎉 模块测试优秀！")
            print("✅ 系统各模块功能正常，可以进行端到端集成")
        elif success_rate >= 60:
            print(f"\n👍 模块测试良好！")
            print("⚠️ 部分模块需要优化，但核心功能可用")
        else:
            print(f"\n❗ 模块测试需要改进")
            print("🔧 建议优先修复失败的模块后再进行集成测试")

def main():
    """主函数"""
    print("选择测试模式:")
    print("1. 运行所有模块测试")
    print("2. 单独测试特定模块")
    print("3. 快速验证测试")
    
    try:
        choice = input("请选择 (1-3): ").strip()
        
        tester = ModularSystemTester()
        
        if choice == "1":
            tester.run_all_tests()
            
        elif choice == "2":
            print("\n可选测试模块:")
            print("1. 论文数据库构建workflow")
            print("2. 多领域专家agents")
            print("3. Reasoning Flow")
            print("4. 论文撰写")
            print("5. 高级功能")
            
            module_choice = input("选择模块 (1-5): ").strip()
            
            if module_choice == "1":
                tester.test_module_1_paper_database_workflow()
            elif module_choice == "2":
                tester.test_module_2_expert_agents()
            elif module_choice == "3":
                tester.test_module_3_reasoning_flow()
            elif module_choice == "4":
                tester.test_module_4_paper_writing()
            elif module_choice == "5":
                tester.test_advanced_features()
            else:
                print("❌ 无效选择")
                
        elif choice == "3":
            print("\n🚀 快速验证测试...")
            # 只测试关键功能
            if tester.test_module_2_expert_agents():
                print("✅ 专家系统可用")
            if tester.test_module_4_paper_writing():
                print("✅ 论文生成可用")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n💥 测试执行失败: {e}")

if __name__ == "__main__":
    main()
