"""
端到端系统集成测试 - Brain AutoResearch Agent
完整的研究到论文生成流程 - 真实API版本

流程：
1. 研究主题输入
2. 文献分析阶段 - Semantic Scholar搜索
3. 专家协作阶段 - 多专家分析研究问题
4. 推理分析阶段 - 实验设计、实现计划、可视化方案
5. 论文生成阶段 - 完整论文写作、引用收集、格式优化、多专家评审
6. 第二优先级集成 - 会议模板适配、实验代码生成
"""

import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入所有需要的系统组件
from core.llm_client import LLMClient
from core.semantic_scholar_tool import SemanticScholarTool
from agents.agent_manager import AgentManager
from reasoning.reasoning_workflow import ReasoningWorkflow
from paper_generation.brain_paper_writer import BrainPaperWriter
from paper_generation.enhanced_citation_manager import EnhancedCitationManager
from paper_generation.latex_format_expert_clean import LaTeXFormatExpert
from paper_generation.multi_expert_review_system import MultiExpertReviewSystem
from paper_generation.conference_template_adapter import ConferenceTemplateAdapter
from core.experiment_code_generator import ExperimentCodeGenerator

class EndToEndResearchSystem:
    """端到端研究系统"""
    
    def __init__(self, model_name: str = "gpt-4o-mini"):
        """初始化系统组件"""
        print("🚀 初始化端到端研究系统...")
        
        # 核心LLM客户端
        self.llm_client = LLMClient(model=model_name)
        print(f"   🧠 LLM客户端: {model_name}")
        
        # 文献分析工具
        self.scholar_tool = SemanticScholarTool()
        print("   📚 文献分析工具: Semantic Scholar")
        
        # 专家代理系统
        self.agent_manager = AgentManager()
        print("   👥 专家代理系统: 5位专家")
        
        # 推理工作流
        self.reasoning_workflow = ReasoningWorkflow()
        print("   🧠 推理工作流: 4个推理组件")
        
        # 论文生成组件
        self.paper_writer = BrainPaperWriter()
        self.citation_manager = EnhancedCitationManager()
        self.latex_expert = LaTeXFormatExpert()
        self.review_system = MultiExpertReviewSystem()
        print("   📝 论文生成系统: 4个核心组件")
        
        # 第二优先级组件
        self.conference_adapter = ConferenceTemplateAdapter()
        self.code_generator = ExperimentCodeGenerator(self.llm_client)
        print("   🎯 第二优先级系统: 会议模板 + 代码生成")
        
        print("✅ 系统初始化完成\n")
    
    def execute_full_pipeline(self, research_topic: str, target_conference: str = "ICML") -> Dict[str, Any]:
        """执行完整的研究流程"""
        print(f"🎯 开始端到端研究流程")
        print(f"📋 研究主题: {research_topic}")
        print(f"🏛️ 目标会议: {target_conference}")
        print("=" * 80)
        
        pipeline_results = {}
        start_time = time.time()
        
        try:
            # 阶段1: 文献分析
            print("\n📚 阶段1: 文献分析")
            print("-" * 40)
            
            literature_results = self._stage_1_literature_analysis(research_topic)
            pipeline_results['stage_1_literature'] = literature_results
            
            if not literature_results['success']:
                print("❌ 文献分析失败，终止流程")
                return pipeline_results
            
            # 阶段2: 专家协作分析
            print("\n👥 阶段2: 专家协作分析")
            print("-" * 40)
            
            expert_results = self._stage_2_expert_analysis(research_topic, literature_results)
            pipeline_results['stage_2_experts'] = expert_results
            
            if not expert_results['success']:
                print("❌ 专家分析失败，终止流程")
                return pipeline_results
            
            # 阶段3: 推理分析
            print("\n🧠 阶段3: 推理分析")
            print("-" * 40)
            
            reasoning_results = self._stage_3_reasoning_analysis(research_topic, expert_results)
            pipeline_results['stage_3_reasoning'] = reasoning_results
            
            if not reasoning_results['success']:
                print("❌ 推理分析失败，终止流程")
                return pipeline_results
            
            # 阶段4: 论文生成
            print("\n📝 阶段4: 论文生成")
            print("-" * 40)
            
            paper_results = self._stage_4_paper_generation(
                research_topic, target_conference, 
                literature_results, expert_results, reasoning_results
            )
            pipeline_results['stage_4_paper'] = paper_results
            
            # 阶段5: 第二优先级集成
            print("\n🎯 阶段5: 第二优先级集成")
            print("-" * 40)
            
            priority_two_results = self._stage_5_priority_two_integration(
                research_topic, target_conference, paper_results
            )
            pipeline_results['stage_5_priority_two'] = priority_two_results
            
        except Exception as e:
            print(f"❌ 流程执行中出现错误: {e}")
            pipeline_results['error'] = str(e)
            import traceback
            traceback.print_exc()
        
        # 流程总结
        end_time = time.time()
        duration = end_time - start_time
        
        pipeline_results['execution_summary'] = {
            'total_duration': duration,
            'start_time': start_time,
            'end_time': end_time,
            'research_topic': research_topic,
            'target_conference': target_conference
        }
        
        self._generate_final_report(pipeline_results)
        
        return pipeline_results
    
    def _stage_1_literature_analysis(self, research_topic: str) -> Dict[str, Any]:
        """阶段1: 文献分析"""
        try:
            print("🔍 搜索相关文献...")
            
            # 搜索文献
            papers = self.scholar_tool.search_papers(
                query=research_topic,
                limit=15
            )
            
            if not papers or len(papers) == 0:
                return {'success': False, 'error': 'No papers found'}
            
            # 分析文献质量和相关性
            print("📊 分析文献质量...")
            analyzed_papers = []
            
            for paper in papers[:10]:  # 分析前10篇
                analyzed_papers.append({
                    'title': paper.get('title', 'N/A'),
                    'year': paper.get('year', 0),
                    'citation_count': paper.get('citationCount', 0),
                    'authors': paper.get('authors', []),
                    'abstract': paper.get('abstract', '')[:200] + "..." if paper.get('abstract') else '',
                    'relevance_score': min(paper.get('citationCount', 0) / 100, 1.0)  # 简单的相关性评分
                })
            
            result = {
                'success': True,
                'papers_found': len(papers),
                'papers_analyzed': len(analyzed_papers),
                'top_papers': analyzed_papers[:5],
                'average_citations': sum(p['citation_count'] for p in analyzed_papers) / len(analyzed_papers),
                'year_range': [min(p['year'] for p in analyzed_papers if p['year'] > 0), 
                              max(p['year'] for p in analyzed_papers if p['year'] > 0)]
            }
            
            print(f"   ✅ 找到文献: {len(papers)} 篇")
            print(f"   📊 分析文献: {len(analyzed_papers)} 篇")
            print(f"   ⭐ 平均引用: {result['average_citations']:.1f}")
            
            return result
            
        except Exception as e:
            print(f"   ❌ 文献分析失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _stage_2_expert_analysis(self, research_topic: str, literature_results: Dict[str, Any]) -> Dict[str, Any]:
        """阶段2: 专家协作分析"""
        try:
            print("👨‍💼 启动多专家协作分析...")
            
            # 专家协作分析 - 真实API调用
            expert_analyses = self.agent_manager.collaborative_analysis(research_topic)
            
            if not expert_analyses or len(expert_analyses) == 0:
                return {'success': False, 'error': 'No expert analyses generated'}
            
            # 分析专家意见
            expert_summary = []
            total_confidence = 0
            
            for analysis in expert_analyses:
                expert_summary.append({
                    'expert': analysis.agent_name,
                    'confidence': analysis.confidence,
                    'analysis_length': len(str(analysis.analysis)),
                    'key_points': str(analysis.analysis)[:150] + "..."
                })
                total_confidence += analysis.confidence
            
            result = {
                'success': True,
                'experts_participated': len(expert_analyses),
                'average_confidence': total_confidence / len(expert_analyses),
                'expert_summaries': expert_summary,
                'consensus_topics': self._extract_consensus_topics(expert_analyses)
            }
            
            print(f"   ✅ 专家参与: {len(expert_analyses)} 位")
            print(f"   📊 平均置信度: {result['average_confidence']:.2f}")
            print(f"   🎯 共识主题: {len(result['consensus_topics'])} 个")
            
            return result
            
        except Exception as e:
            print(f"   ❌ 专家分析失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _stage_3_reasoning_analysis(self, research_topic: str, expert_results: Dict[str, Any]) -> Dict[str, Any]:
        """阶段3: 推理分析"""
        try:
            print("🧠 执行推理工作流...")
            
            # 推理分析 - 包括实验设计、实现计划等
            reasoning_result = self.reasoning_workflow.execute_full_reasoning_pipeline(
                research_topic=research_topic,
                expert_insights=expert_results.get('expert_summaries', [])
            )
            
            if not reasoning_result:
                return {'success': False, 'error': 'Reasoning pipeline failed'}
            
            result = {
                'success': True,
                'deliverables_generated': len(reasoning_result.get('deliverables', {})),
                'experiment_design': reasoning_result.get('deliverables', {}).get('experiment_design'),
                'implementation_plan': reasoning_result.get('deliverables', {}).get('implementation_plan'),
                'visualization_plan': reasoning_result.get('deliverables', {}).get('visualization_plan'),
                'feasibility_score': reasoning_result.get('overall_feasibility', 0)
            }
            
            print(f"   ✅ 推理交付物: {result['deliverables_generated']} 个")
            print(f"   📊 可行性评分: {result['feasibility_score']:.2f}")
            print("   🧪 实验设计: ✅" if result['experiment_design'] else "   🧪 实验设计: ❌")
            print("   🛠️ 实现计划: ✅" if result['implementation_plan'] else "   🛠️ 实现计划: ❌")
            
            return result
            
        except Exception as e:
            print(f"   ❌ 推理分析失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _stage_4_paper_generation(self, research_topic: str, target_conference: str, 
                                 literature_results: Dict[str, Any], expert_results: Dict[str, Any], 
                                 reasoning_results: Dict[str, Any]) -> Dict[str, Any]:
        """阶段4: 论文生成"""
        try:
            paper_results = {}
            
            # 4.1 引用收集
            print("📚 收集智能引用...")
            citations_result = self.citation_manager.collect_intelligent_citations(
                research_topic=research_topic,
                target_count=20,
                quality_threshold=0.6
            )
            
            if citations_result and citations_result.get('citations'):
                paper_results['citations'] = {
                    'success': True,
                    'count': len(citations_result['citations']),
                    'average_quality': sum(c.get('quality_score', 0) for c in citations_result['citations']) / len(citations_result['citations']),
                    'bibtex_generated': bool(citations_result.get('bibtex'))
                }
                print(f"   ✅ 收集引用: {len(citations_result['citations'])} 个")
            else:
                paper_results['citations'] = {'success': False}
                print("   ❌ 引用收集失败")
            
            # 4.2 论文写作
            print("📝 生成论文内容...")
            paper_content = self.paper_writer.generate_paper(
                topic=research_topic,
                research_insights=expert_results,
                experiment_plan=reasoning_results.get('experiment_design'),
                literature_review=literature_results.get('top_papers', [])
            )
            
            if paper_content:
                paper_results['content'] = {
                    'success': True,
                    'length': len(paper_content),
                    'sections_count': paper_content.count('\\section')
                }
                print(f"   ✅ 论文生成: {len(paper_content):,} 字符")
            else:
                paper_results['content'] = {'success': False}
                print("   ❌ 论文生成失败")
            
            # 4.3 LaTeX格式优化
            if paper_content:
                print("🔧 优化LaTeX格式...")
                latex_result = self.latex_expert.optimize_latex_format(
                    paper_content, target_venue=target_conference
                )
                
                if latex_result and latex_result.get('optimized_content'):
                    paper_results['latex_optimization'] = {
                        'success': True,
                        'issues_fixed': len(latex_result.get('issues_fixed', [])),
                        'quality_score': latex_result.get('quality_score', 0),
                        'venue_compliant': latex_result.get('venue_compliance', {}).get('is_compliant', False)
                    }
                    print(f"   ✅ LaTeX优化: {latex_result.get('quality_score', 0):.1f}/10")
                    
                    # 使用优化后的内容
                    paper_content = latex_result['optimized_content']
                else:
                    paper_results['latex_optimization'] = {'success': False}
                    print("   ❌ LaTeX优化失败")
            
            # 4.4 多专家评审
            print("👥 进行多专家评审...")
            if paper_content:
                review_result = self.review_system.conduct_review(
                    paper_content={'title': research_topic, 'content': paper_content},
                    target_venue=target_conference,
                    quality_threshold=7.0
                )
                
                if review_result:
                    paper_results['review'] = {
                        'success': True,
                        'consensus_score': review_result.get('consensus_score', 0),
                        'meets_threshold': review_result.get('meets_threshold', False),
                        'experts_count': len(review_result.get('expert_reviews', []))
                    }
                    print(f"   ✅ 评审完成: {review_result.get('consensus_score', 0):.2f}/10")
                else:
                    paper_results['review'] = {'success': False}
                    print("   ❌ 评审失败")
            
            # 保存论文
            if paper_content:
                paper_file = f"generated_paper_{int(time.time())}.tex"
                with open(paper_file, 'w', encoding='utf-8') as f:
                    f.write(paper_content)
                paper_results['output_file'] = paper_file
                print(f"   📄 论文已保存: {paper_file}")
            
            return paper_results
            
        except Exception as e:
            print(f"   ❌ 论文生成失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _stage_5_priority_two_integration(self, research_topic: str, target_conference: str, 
                                        paper_results: Dict[str, Any]) -> Dict[str, Any]:
        """阶段5: 第二优先级集成"""
        try:
            priority_results = {}
            
            # 5.1 会议模板适配
            print("🏛️ 适配会议模板...")
            if paper_results.get('output_file') and os.path.exists(paper_results['output_file']):
                with open(paper_results['output_file'], 'r', encoding='utf-8') as f:
                    paper_content = f.read()
                
                # 转换为会议格式
                conference_content = {
                    'title': research_topic,
                    'abstract': '论文摘要内容',
                    'content': paper_content
                }
                
                try:
                    formatted_paper = self.conference_adapter.format_for_conference(
                        conference_content, target_conference
                    )
                    
                    priority_results['conference_template'] = {
                        'success': True,
                        'conference': target_conference,
                        'formatted_length': len(formatted_paper)
                    }
                    
                    # 保存会议格式论文
                    conference_file = f"conference_{target_conference.lower()}_{int(time.time())}.tex"
                    with open(conference_file, 'w', encoding='utf-8') as f:
                        f.write(formatted_paper)
                    priority_results['conference_template']['output_file'] = conference_file
                    
                    print(f"   ✅ {target_conference}格式适配完成")
                    
                except Exception as e:
                    priority_results['conference_template'] = {'success': False, 'error': str(e)}
                    print(f"   ⚠️ 使用基础模板格式")
            
            # 5.2 实验代码生成
            print("💻 生成实验代码...")
            try:
                # 生成实验规格
                experiment_spec = self.code_generator.generate_experiment_specification(
                    research_topic, target_conference
                )
                
                if experiment_spec:
                    # 生成完整实验代码
                    code_output_dir = f"experiment_code_{int(time.time())}"
                    files_created = self.code_generator.generate_complete_experiment(
                        experiment_spec, code_output_dir
                    )
                    
                    # 统计代码
                    total_code_size = 0
                    for file_path in files_created.values():
                        if os.path.exists(file_path):
                            total_code_size += os.path.getsize(file_path)
                    
                    priority_results['experiment_code'] = {
                        'success': True,
                        'experiment_name': experiment_spec.name,
                        'files_count': len(files_created),
                        'total_code_size': total_code_size,
                        'framework': experiment_spec.framework,
                        'output_dir': code_output_dir
                    }
                    
                    print(f"   ✅ 实验代码生成: {total_code_size:,} 字节")
                else:
                    priority_results['experiment_code'] = {'success': False, 'error': 'No experiment spec'}
                    print("   ❌ 实验代码生成失败")
                    
            except Exception as e:
                priority_results['experiment_code'] = {'success': False, 'error': str(e)}
                print(f"   ❌ 实验代码生成错误: {e}")
            
            return priority_results
            
        except Exception as e:
            print(f"   ❌ 第二优先级集成失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _extract_consensus_topics(self, expert_analyses: List) -> List[str]:
        """提取专家共识主题"""
        # 简化版本 - 实际应用中可以用更复杂的NLP分析
        topics = []
        for analysis in expert_analyses:
            # 提取关键词（简化版）
            text = str(analysis.analysis).lower()
            if 'neural network' in text or 'deep learning' in text:
                topics.append('深度学习')
            if 'computer vision' in text or 'image' in text:
                topics.append('计算机视觉')
            if 'attention' in text:
                topics.append('注意力机制')
        
        return list(set(topics))  # 去重
    
    def _generate_final_report(self, pipeline_results: Dict[str, Any]) -> None:
        """生成最终报告"""
        print("\n" + "=" * 80)
        print("📊 端到端系统执行报告")
        print("=" * 80)
        
        summary = pipeline_results.get('execution_summary', {})
        
        print(f"🎯 研究主题: {summary.get('research_topic', 'N/A')}")
        print(f"🏛️ 目标会议: {summary.get('target_conference', 'N/A')}")
        print(f"⏱️  执行耗时: {summary.get('total_duration', 0):.2f} 秒")
        
        # 各阶段执行情况
        stages = [
            ('文献分析', 'stage_1_literature'),
            ('专家协作', 'stage_2_experts'), 
            ('推理分析', 'stage_3_reasoning'),
            ('论文生成', 'stage_4_paper'),
            ('二级集成', 'stage_5_priority_two')
        ]
        
        print(f"\n📋 各阶段执行情况:")
        successful_stages = 0
        
        for stage_name, stage_key in stages:
            stage_result = pipeline_results.get(stage_key, {})
            if stage_result.get('success', False):
                print(f"   ✅ {stage_name}: 成功")
                successful_stages += 1
            elif stage_key in pipeline_results:
                print(f"   ❌ {stage_name}: 失败 - {stage_result.get('error', '未知错误')}")
            else:
                print(f"   ⏭️  {stage_name}: 未执行")
        
        success_rate = (successful_stages / len(stages)) * 100
        print(f"\n📈 整体成功率: {successful_stages}/{len(stages)} ({success_rate:.1f}%)")
        
        # 输出文件清单
        print(f"\n📄 生成文件:")
        output_files = []
        
        if pipeline_results.get('stage_4_paper', {}).get('output_file'):
            output_files.append(f"   📝 论文文件: {pipeline_results['stage_4_paper']['output_file']}")
        
        if pipeline_results.get('stage_5_priority_two', {}).get('conference_template', {}).get('output_file'):
            output_files.append(f"   🏛️ 会议格式: {pipeline_results['stage_5_priority_two']['conference_template']['output_file']}")
        
        if pipeline_results.get('stage_5_priority_two', {}).get('experiment_code', {}).get('output_dir'):
            output_files.append(f"   💻 实验代码: {pipeline_results['stage_5_priority_two']['experiment_code']['output_dir']}/")
        
        for file_info in output_files:
            print(file_info)
        
        if not output_files:
            print("   ⚠️  未生成输出文件")
        
        # 保存完整结果
        results_file = f"end_to_end_results_{int(time.time())}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(pipeline_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n💾 完整结果已保存: {results_file}")
        
        if success_rate >= 80:
            print(f"\n🎉 端到端系统运行成功！")
            print("✅ Brain AutoResearch Agent 已完成从研究想法到论文生成的完整流程")
        else:
            print(f"\n⚠️  端到端系统部分失败，建议检查失败的阶段")

def run_end_to_end_test():
    """运行端到端测试"""
    print("🚀 Brain AutoResearch Agent - 端到端系统测试")
    print("=" * 80)
    
    # 研究主题示例
    research_topics = [
        "Brain-inspired attention mechanisms for computer vision tasks",
        "Spiking neural networks for energy-efficient edge computing",
        "Multimodal fusion networks inspired by human cognitive processes"
    ]
    
    print("📋 可选研究主题:")
    for i, topic in enumerate(research_topics, 1):
        print(f"   {i}. {topic}")
    
    # 用户选择
    try:
        choice = input(f"\n请选择研究主题 (1-{len(research_topics)}) 或输入自定义主题: ").strip()
        
        if choice.isdigit() and 1 <= int(choice) <= len(research_topics):
            research_topic = research_topics[int(choice) - 1]
        elif choice:
            research_topic = choice
        else:
            research_topic = research_topics[0]  # 默认选择
        
        target_conference = input("目标会议 (默认ICML): ").strip() or "ICML"
        
        print(f"\n🎯 已选择:")
        print(f"   📋 研究主题: {research_topic}")
        print(f"   🏛️ 目标会议: {target_conference}")
        
        # 确认执行
        confirm = input(f"\n⚠️  此测试将使用真实API，可能产生费用。是否继续? (y/n): ")
        if confirm.lower() != 'y':
            print("❌ 用户取消测试")
            return
        
        # 执行端到端流程
        print(f"\n🚀 开始执行端到端流程...")
        system = EndToEndResearchSystem()
        results = system.execute_full_pipeline(research_topic, target_conference)
        
        return results
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_end_to_end_test()
