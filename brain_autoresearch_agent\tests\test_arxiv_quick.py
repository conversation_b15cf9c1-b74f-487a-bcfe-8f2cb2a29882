import requests
import xml.etree.ElementTree as ET

def test_arxiv_simple():
    url = "http://export.arxiv.org/api/query?search_query=all:neural+networks&start=0&max_results=3"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            root = ET.fromstring(response.text)
            entries = root.findall('.//{http://www.w3.org/2005/Atom}entry')
            print(f"找到 {len(entries)} 篇论文")
            
            for i, entry in enumerate(entries[:2], 1):
                title = entry.find('.//{http://www.w3.org/2005/Atom}title')
                if title is not None:
                    print(f"{i}. {title.text[:100]}...")
            
            return True
        else:
            print(f"请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    test_arxiv_simple()