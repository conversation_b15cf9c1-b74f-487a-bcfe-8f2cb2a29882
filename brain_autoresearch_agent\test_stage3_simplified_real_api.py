#!/usr/bin/env python3
"""
Stage 3 简化真实API测试
- 减少Mock依赖
- 重点测试真实API功能
- 简化测试逻辑
"""

import sys
import os
import unittest
import traceback
from unittest.mock import patch, MagicMock

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入核心组件
from core.unified_api_client import UnifiedAPIClient
from reasoning.enhanced_hypothesis_experiment_designer import EnhancedHypothesisExperimentDesigner
from core.experiment_code_generator import EnhancedExperimentCodeGenerator
from reasoning.enhanced_visualization_advisor import EnhancedVisualizationAdvisor
from reasoning.data_models import ResearchProblem, ExperimentPlan

class TestStage3SimplifiedRealAPI(unittest.TestCase):
    """Stage 3 简化真实API测试"""
    
    def setUp(self):
        """测试初始化"""
        print(f"\n🧪 {self._testMethodName}")
        
    def _create_simple_research_problem(self) -> ResearchProblem:
        """创建简单的研究问题"""
        return ResearchProblem(
            question="注意力机制改进研究",
            title="注意力机制改进研究", 
            description="研究新的注意力机制在图像分类中的效果",
            hypothesis=["新注意力机制能提升性能"],
            background={"domain": "computer vision", "context": "现有注意力机制在处理复杂图像时存在局限性"},
            objectives=["如何改进注意力机制？", "效果如何验证？"],
            domain="计算机视觉"
        )
    
    def _create_simple_experiment_plan(self) -> ExperimentPlan:
        """创建简单的实验计划"""
        return ExperimentPlan(
            research_question="注意力机制改进研究",
            hypothesis=["新注意力机制能提升性能"],
            experiment_type="controlled_experiment",
            variables=["attention_type", "learning_rate"],
            metrics=["accuracy", "f1_score"],
            baseline_methods=["ResNet", "VGG"],
            expected_outcomes=["准确率提升3-5%"]
        )
    
    # =============================================================================
    # 真实API测试
    # =============================================================================
    
    def test_01_real_api_connection(self):
        """测试1: 真实API连接"""
        try:
            api_client = UnifiedAPIClient()
            response = api_client.get_text_response(
                prompt="请简单回复：连接成功",
                system_message="你是一个测试助手",
                model_type="default",
                max_tokens=50
            )
            
            self.assertIsNotNone(response)
            self.assertIsNotNone(response.content)
            self.assertTrue(len(response.content.strip()) > 0)
            self.assertTrue(response.success)
            
            print("✅ 真实API连接测试通过")
            
        except Exception as e:
            print(f"❌ API连接测试失败: {e}")
            self.skipTest(f"API连接失败: {e}")
    
    def test_02_real_hypothesis_designer_basic(self):
        """测试2: 真实API假设设计器基础功能"""
        try:
            api_client = UnifiedAPIClient()
            
            # 创建设计器
            designer = EnhancedHypothesisExperimentDesigner(api_client)
            
            # 创建研究问题
            research_problem = self._create_simple_research_problem()
            
            # 使用现有的设计方法
            experiment_plan = designer.design_experiment(research_problem)
            
            # 验证基础功能
            self.assertIsNotNone(experiment_plan)
            self.assertIsNotNone(experiment_plan.research_problem)
            self.assertIsInstance(experiment_plan.variables, list)
            self.assertIsInstance(experiment_plan.metrics, list)
            
            print("✅ 真实API假设设计器基础测试通过")
            
        except Exception as e:
            print(f"❌ 假设设计器测试失败: {e}")
            print(traceback.format_exc())
            # 不跳过，而是失败，这样能看到具体问题
            self.fail(f"假设设计器测试失败: {e}")
    
    def test_03_real_code_generator_basic(self):
        """测试3: 真实API代码生成器基础功能"""
        try:
            api_client = UnifiedAPIClient()
            
            # 创建代码生成器
            generator = EnhancedExperimentCodeGenerator(api_client)
            
            # 使用现有的方法生成实验规格
            research_idea = "注意力机制改进研究：研究新的注意力机制在图像分类中的效果"
            experiment_spec = generator.generate_experiment_specification(research_idea)
            
            # 验证实验规格生成
            self.assertIsNotNone(experiment_spec)
            self.assertTrue(hasattr(experiment_spec, 'framework'))
            
            # 生成完整实验代码
            import tempfile
            with tempfile.TemporaryDirectory() as temp_dir:
                code_result = generator.generate_complete_experiment(experiment_spec, temp_dir)
                
                # 验证代码生成
                self.assertIsNotNone(code_result)
                self.assertIsInstance(code_result, dict)
                self.assertGreater(len(code_result), 0)
            
            print("✅ 真实API代码生成器基础测试通过")
            
        except Exception as e:
            print(f"❌ 代码生成器测试失败: {e}")
            print(traceback.format_exc())
            self.fail(f"代码生成器测试失败: {e}")
    
    def test_04_real_visualization_advisor_basic(self):
        """测试4: 真实API可视化顾问基础功能"""
        try:
            api_client = UnifiedAPIClient()
            
            # 创建可视化顾问
            advisor = EnhancedVisualizationAdvisor(api_client)
            
            # 创建模拟的实验结果
            experiment_results = {
                "accuracy": [0.85, 0.87, 0.89, 0.91],
                "loss": [0.35, 0.28, 0.22, 0.18],
                "epochs": [1, 2, 3, 4],
                "model_type": "attention_mechanism",
                "dataset": "CIFAR-10"
            }
            
            # 使用现有的可视化计划生成方法
            viz_plan = advisor.generate_visualization_plan(experiment_results)
            
            # 验证可视化计划
            self.assertIsNotNone(viz_plan)
            self.assertTrue(hasattr(viz_plan, 'charts'))
            
            print("✅ 真实API可视化顾问基础测试通过")
            
        except Exception as e:
            print(f"❌ 可视化顾问测试失败: {e}")
            print(traceback.format_exc())
            self.fail(f"可视化顾问测试失败: {e}")
    
    def test_05_real_api_integration_simple(self):
        """测试5: 真实API简单集成测试"""
        try:
            api_client = UnifiedAPIClient()
            
            # 创建所有组件
            designer = EnhancedHypothesisExperimentDesigner(api_client)
            generator = EnhancedExperimentCodeGenerator(api_client)
            advisor = EnhancedVisualizationAdvisor(api_client)
            
            # 创建研究问题
            research_problem = self._create_simple_research_problem()
            
            print("🔬 开始简单集成测试...")
            
            # 1. 实验设计
            print("   1. 设计实验方案...")
            experiment_plan = designer.design_experiment(research_problem)
            self.assertIsNotNone(experiment_plan)
            
            # 2. 代码生成  
            print("   2. 生成实验代码...")
            research_idea = "注意力机制改进研究：研究新的注意力机制在图像分类中的效果"
            experiment_spec = generator.generate_experiment_specification(research_idea)
            self.assertIsNotNone(experiment_spec)
            
            import tempfile
            with tempfile.TemporaryDirectory() as temp_dir:
                code_result = generator.generate_complete_experiment(experiment_spec, temp_dir)
                self.assertIsNotNone(code_result)
            
            # 3. 可视化建议
            print("   3. 生成可视化建议...")
            experiment_results = {
                "accuracy": [0.85, 0.87, 0.89],
                "loss": [0.35, 0.28, 0.22],
                "model_type": "attention_mechanism"
            }
            viz_plan = advisor.generate_visualization_plan(experiment_results)
            self.assertIsNotNone(viz_plan)
            
            print("✅ 真实API简单集成测试通过")
            
        except Exception as e:
            print(f"❌ 集成测试失败: {e}")
            print(traceback.format_exc())
            self.fail(f"集成测试失败: {e}")

def main():
    """运行测试"""
    print("🚀 启动 Stage 3 简化真实API测试...")
    print("📋 测试策略:")
    print("  - 重点测试真实API功能")
    print("  - 减少Mock依赖")  
    print("  - 简化测试逻辑")
    print("  - 验证基础功能")
    print()
    
    # 运行测试
    unittest.main(verbosity=2, argv=[''])

if __name__ == "__main__":
    main()
