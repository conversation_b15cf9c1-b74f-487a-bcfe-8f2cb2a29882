"""
真实API测试套件 - Brain AutoResearch Agent
分模块测试各个系统组件的真实API调用

测试策略：
1. 核心基础设施测试 - LLM客户端、文献工具
2. 多专家代理系统测试 - 各个专家代理的真实响应
3. 推理系统测试 - 完整推理流程 
4. 论文生成系统测试 - LaTeX生成、引用收集、评审
5. 第二优先级测试 - 会议模板、代码生成、工作流
6. 端到端集成测试 - 完整系统流程
"""

import sys
import os
import json
import time
from pathlib import Path

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入系统组件
from core.llm_client import LLMClient
from core.semantic_scholar_tool import SemanticScholarTool
from agents.agent_manager import AgentManager
from reasoning.reasoning_workflow import ReasoningWorkflow
from paper_generation.latex_format_expert_clean import LaTeXFormatExpert
from paper_generation.enhanced_citation_manager import EnhancedCitationManager
from paper_generation.multi_expert_review_system import MultiExpertReviewSystem

def test_core_infrastructure_real_api():
    """测试1: 核心基础设施 - 真实API"""
    print("🧪 测试1: 核心基础设施 - 真实API测试")
    print("-" * 60)
    
    results = {}
    
    # 测试LLM客户端真实API
    print("🤖 测试LLM客户端...")
    try:
        # 使用真实模型而非mock
        client = LLMClient(model="gpt-4o-mini")  # 或者claude-3-5-sonnet-20241022
        
        test_prompt = "请简要介绍人工智能在神经科学研究中的应用。"
        
        response = client.generate_response(test_prompt)
        
        if response and len(response) > 50:
            results['llm_client'] = {
                'success': True,
                'response_length': len(response),
                'model_used': client.model,
                'sample_response': response[:100] + "..." if len(response) > 100 else response
            }
            print(f"   ✅ LLM客户端测试成功")
            print(f"   📊 使用模型: {client.model}")
            print(f"   📝 响应长度: {len(response):,} 字符")
            print(f"   💬 响应预览: {response[:100]}...")
        else:
            results['llm_client'] = {'success': False, 'error': 'Response too short or empty'}
            print("   ❌ LLM客户端响应不足")
            
    except Exception as e:
        results['llm_client'] = {'success': False, 'error': str(e)}
        print(f"   ❌ LLM客户端测试失败: {e}")
    
    # 测试Semantic Scholar工具真实API
    print("\n📚 测试Semantic Scholar工具...")
    try:
        scholar_tool = SemanticScholarTool()
        
        # 搜索与AI相关的论文
        search_results = scholar_tool.search_papers(
            query="artificial intelligence neural networks", 
            limit=5
        )
        
        if search_results and len(search_results) > 0:
            results['semantic_scholar'] = {
                'success': True,
                'papers_found': len(search_results),
                'sample_paper': {
                    'title': search_results[0].get('title', 'N/A'),
                    'year': search_results[0].get('year', 'N/A'),
                    'citation_count': search_results[0].get('citationCount', 0)
                }
            }
            print(f"   ✅ Semantic Scholar测试成功")
            print(f"   📄 找到论文: {len(search_results)} 篇")
            print(f"   🏆 示例论文: {search_results[0].get('title', 'N/A')[:60]}...")
        else:
            results['semantic_scholar'] = {'success': False, 'error': 'No papers found'}
            print("   ❌ Semantic Scholar无搜索结果")
            
    except Exception as e:
        results['semantic_scholar'] = {'success': False, 'error': str(e)}
        print(f"   ❌ Semantic Scholar测试失败: {e}")
    
    print(f"\n📊 核心基础设施测试结果:")
    success_count = sum(1 for r in results.values() if r.get('success', False))
    total_count = len(results)
    print(f"   成功率: {success_count}/{total_count}")
    
    return results

def test_expert_agents_real_api():
    """测试2: 多专家代理系统 - 真实API"""
    print("\n🧪 测试2: 多专家代理系统 - 真实API测试")
    print("-" * 60)
    
    results = {}
    
    try:
        # 初始化代理管理器
        agent_manager = AgentManager()
        
        # 测试研究任务
        research_topic = "脑启发的神经网络架构在计算机视觉中的应用"
        
        print(f"🎯 研究主题: {research_topic}")
        print("👥 测试多专家协作分析...")
        
        # 执行协作分析 - 这会调用真实的LLM API
        analysis_results = agent_manager.collaborative_analysis(research_topic)
        
        if analysis_results and len(analysis_results) > 0:
            results['expert_collaboration'] = {
                'success': True,
                'experts_participated': len(analysis_results),
                'total_response_length': sum(len(str(r.analysis)) for r in analysis_results),
                'sample_expert_response': {
                    'expert': analysis_results[0].agent_name,
                    'confidence': analysis_results[0].confidence,
                    'response_preview': str(analysis_results[0].analysis)[:100] + "..."
                }
            }
            
            print(f"   ✅ 多专家协作测试成功")
            print(f"   👨‍💼 参与专家: {len(analysis_results)} 位")
            print(f"   📊 总响应长度: {sum(len(str(r.analysis)) for r in analysis_results):,} 字符")
            
            # 显示各专家结果预览
            for i, result in enumerate(analysis_results[:3], 1):  # 显示前3个
                print(f"   {i}. {result.agent_name}: 置信度 {result.confidence:.2f}")
                print(f"      💭 {str(result.analysis)[:80]}...")
                
        else:
            results['expert_collaboration'] = {'success': False, 'error': 'No analysis results'}
            print("   ❌ 多专家协作无结果")
            
    except Exception as e:
        results['expert_collaboration'] = {'success': False, 'error': str(e)}
        print(f"   ❌ 多专家代理系统测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    return results

def test_paper_generation_real_api():
    """测试3: 论文生成系统 - 真实API"""
    print("\n🧪 测试3: 论文生成系统 - 真实API测试")
    print("-" * 60)
    
    results = {}
    
    # 测试LaTeX格式专家
    print("📝 测试LaTeX格式专家...")
    try:
        latex_expert = LaTeXFormatExpert()
        
        # 测试论文内容
        test_paper_content = """
        \\documentclass{article}
        \\usepackage{amsmath}
        \\title{Brain-Inspired Neural Networks}
        \\author{Test Author}
        \\begin{document}
        \\maketitle
        \\section{Introduction}
        This paper presents a novel approach to brain-inspired neural networks.
        \\end{document}
        """
        
        # 进行专业级LaTeX优化 - 调用真实API
        optimized_result = latex_expert.optimize_latex_format(
            test_paper_content, 
            target_venue="ICML"
        )
        
        if optimized_result and optimized_result.get('optimized_content'):
            results['latex_expert'] = {
                'success': True,
                'issues_detected': len(optimized_result.get('issues_detected', [])),
                'issues_fixed': len(optimized_result.get('issues_fixed', [])),
                'quality_score': optimized_result.get('quality_score', 0),
                'venue_compliance': optimized_result.get('venue_compliance', {})
            }
            print(f"   ✅ LaTeX专家测试成功")
            print(f"   🔍 检测问题: {len(optimized_result.get('issues_detected', []))} 个")
            print(f"   🔧 修复问题: {len(optimized_result.get('issues_fixed', []))} 个")
            print(f"   📊 质量分数: {optimized_result.get('quality_score', 0):.1f}/10")
        else:
            results['latex_expert'] = {'success': False, 'error': 'No optimization result'}
            print("   ❌ LaTeX专家优化失败")
            
    except Exception as e:
        results['latex_expert'] = {'success': False, 'error': str(e)}
        print(f"   ❌ LaTeX格式专家测试失败: {e}")
    
    # 测试增强引用管理器
    print("\n📚 测试增强引用管理器...")
    try:
        citation_manager = EnhancedCitationManager()
        
        research_topic = "Brain-inspired neural networks for computer vision"
        
        # 智能引用收集 - 调用真实API
        citations_result = citation_manager.collect_intelligent_citations(
            research_topic=research_topic,
            target_count=10,
            quality_threshold=0.7
        )
        
        if citations_result and citations_result.get('citations'):
            citations = citations_result['citations']
            results['citation_manager'] = {
                'success': True,
                'citations_collected': len(citations),
                'average_quality': sum(c.get('quality_score', 0) for c in citations) / len(citations),
                'bibtex_generated': bool(citations_result.get('bibtex'))
            }
            
            print(f"   ✅ 引用管理器测试成功")
            print(f"   📄 收集引用: {len(citations)} 个")
            print(f"   ⭐ 平均质量: {sum(c.get('quality_score', 0) for c in citations) / len(citations):.2f}")
            print(f"   📋 BibTeX生成: {'✅' if citations_result.get('bibtex') else '❌'}")
        else:
            results['citation_manager'] = {'success': False, 'error': 'No citations collected'}
            print("   ❌ 引用收集失败")
            
    except Exception as e:
        results['citation_manager'] = {'success': False, 'error': str(e)}
        print(f"   ❌ 增强引用管理器测试失败: {e}")
    
    # 测试多专家评审系统
    print("\n👥 测试多专家评审系统...")
    try:
        review_system = MultiExpertReviewSystem()
        
        # 测试论文内容
        test_paper = {
            'title': 'Brain-Inspired Neural Networks for Computer Vision',
            'abstract': 'This paper presents a novel brain-inspired approach to neural networks that achieves state-of-the-art performance on computer vision tasks.',
            'content': 'Detailed paper content here...'
        }
        
        # 进行多专家评审 - 调用真实API
        review_result = review_system.conduct_review(
            paper_content=test_paper,
            target_venue="ICML",
            quality_threshold=7.0
        )
        
        if review_result and review_result.get('consensus_score'):
            results['review_system'] = {
                'success': True,
                'consensus_score': review_result['consensus_score'],
                'experts_participated': len(review_result.get('expert_reviews', [])),
                'meets_threshold': review_result.get('meets_threshold', False),
                'recommendation': review_result.get('recommendation', 'N/A')
            }
            
            print(f"   ✅ 多专家评审测试成功")
            print(f"   📊 共识分数: {review_result['consensus_score']:.2f}/10")
            print(f"   👨‍💼 参与专家: {len(review_result.get('expert_reviews', []))} 位")
            print(f"   🎯 质量达标: {'✅' if review_result.get('meets_threshold', False) else '❌'}")
        else:
            results['review_system'] = {'success': False, 'error': 'No review result'}
            print("   ❌ 多专家评审失败")
            
    except Exception as e:
        results['review_system'] = {'success': False, 'error': str(e)}
        print(f"   ❌ 多专家评审系统测试失败: {e}")
    
    print(f"\n📊 论文生成系统测试结果:")
    success_count = sum(1 for r in results.values() if r.get('success', False))
    total_count = len(results)
    print(f"   成功率: {success_count}/{total_count}")
    
    return results

def test_priority_two_real_api():
    """测试4: 第二优先级 - 真实API"""
    print("\n🧪 测试4: 第二优先级 - 真实API测试")
    print("-" * 60)
    
    results = {}
    
    # 测试实验代码生成器
    print("💻 测试实验代码生成器...")
    try:
        from core.experiment_code_generator import ExperimentCodeGenerator
        
        # 使用真实LLM客户端
        model_client = LLMClient(model="gpt-4o-mini")
        code_generator = ExperimentCodeGenerator(model_client)
        
        research_idea = "A novel attention mechanism that combines self-attention with convolutional features for improved image classification performance"
        
        # 生成实验规格 - 调用真实API
        spec = code_generator.generate_experiment_specification(research_idea, "ICML")
        
        if spec:
            # 生成完整实验代码
            output_dir = "./real_api_test_experiments"
            files_created = code_generator.generate_complete_experiment(spec, output_dir)
            
            # 统计生成的代码
            total_code_size = 0
            for file_path in files_created.values():
                if os.path.exists(file_path):
                    total_code_size += os.path.getsize(file_path)
            
            results['code_generator'] = {
                'success': True,
                'experiment_name': spec.name,
                'files_generated': len(files_created),
                'total_code_size': total_code_size,
                'framework': spec.framework,
                'experiment_type': spec.experiment_type
            }
            
            print(f"   ✅ 代码生成器测试成功")
            print(f"   🧪 实验名称: {spec.name}")
            print(f"   📄 生成文件: {len(files_created)} 个")
            print(f"   💾 代码总量: {total_code_size:,} 字节")
            print(f"   🛠️ 使用框架: {spec.framework}")
        else:
            results['code_generator'] = {'success': False, 'error': 'No experiment specification generated'}
            print("   ❌ 实验规格生成失败")
            
    except Exception as e:
        results['code_generator'] = {'success': False, 'error': str(e)}
        print(f"   ❌ 实验代码生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    return results

def run_comprehensive_real_api_tests():
    """运行综合真实API测试套件"""
    print("🚀 Brain AutoResearch Agent - 综合真实API测试")
    print("=" * 80)
    
    print("⚠️  注意: 此测试将使用真实API，可能产生费用")
    print("📝 测试策略: 分模块测试各个系统组件的真实API调用")
    print("🎯 目标: 验证所有组件在真实环境下的工作状态")
    
    # 等待用户确认
    user_input = input("\n是否继续真实API测试? (y/n): ")
    if user_input.lower() != 'y':
        print("❌ 用户取消测试")
        return
    
    start_time = time.time()
    all_results = {}
    
    # 按模块执行测试
    try:
        # 测试1: 核心基础设施
        all_results['core_infrastructure'] = test_core_infrastructure_real_api()
        
        # 测试2: 多专家代理系统  
        all_results['expert_agents'] = test_expert_agents_real_api()
        
        # 测试3: 论文生成系统
        all_results['paper_generation'] = test_paper_generation_real_api()
        
        # 测试4: 第二优先级
        all_results['priority_two'] = test_priority_two_real_api()
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断测试")
        return
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试总结
    end_time = time.time()
    duration = end_time - start_time
    
    print("\n" + "=" * 80)
    print("📊 综合真实API测试结果总结")
    print("=" * 80)
    
    total_tests = 0
    successful_tests = 0
    
    for category, category_results in all_results.items():
        if isinstance(category_results, dict):
            category_success = sum(1 for r in category_results.values() if r.get('success', False))
            category_total = len(category_results)
            
            total_tests += category_total
            successful_tests += category_success
            
            success_rate = (category_success / category_total * 100) if category_total > 0 else 0
            
            print(f"\n📦 {category.replace('_', ' ').title()}:")
            print(f"   ✅ 成功: {category_success}/{category_total} ({success_rate:.1f}%)")
            
            # 显示详细结果
            for test_name, result in category_results.items():
                status = "✅" if result.get('success', False) else "❌"
                print(f"   {status} {test_name}: {result.get('error', 'Success')}")
    
    overall_success_rate = (successful_tests / total_tests * 100) if total_tests > 0 else 0
    
    print(f"\n🎯 总体结果:")
    print(f"   📊 整体成功率: {successful_tests}/{total_tests} ({overall_success_rate:.1f}%)")
    print(f"   ⏱️  测试耗时: {duration:.2f} 秒")
    print(f"   💰 API调用: 已使用真实API（请检查账单）")
    
    # 保存测试结果
    results_file = f"real_api_test_results_{int(time.time())}.json"
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            'timestamp': time.time(),
            'duration': duration,
            'overall_success_rate': overall_success_rate,
            'results': all_results
        }, f, indent=2, ensure_ascii=False)
    
    print(f"   📄 结果已保存: {results_file}")
    
    if overall_success_rate >= 80:
        print("\n🎉 系统整体运行良好，可以进行端到端集成！")
        return True
    else:
        print("\n⚠️  部分组件需要修复后再进行端到端集成")
        return False

if __name__ == "__main__":
    success = run_comprehensive_real_api_tests()
    
    if success:
        print("\n🚀 准备就绪，可以开始端到端系统集成测试！")
    else:
        print("\n🔧 请先修复失败的组件，然后再进行完整系统测试")
