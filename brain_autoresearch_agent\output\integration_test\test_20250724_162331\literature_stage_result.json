{"papers": [{"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 1", "abstract": "This paper presents a novel approach to Brain-Inspired Neural Networks for Efficient Learning. Our research shows significant improvements over existing methods.", "authors": ["Researcher 1A", "Researcher 1B"], "year": 2022, "venue": "Nature Machine Intelligence", "url": "https://example.com/paper1", "citation_count": 95, "source": "semantic_scholar", "paper_id": "mock-paper-id-1", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 2", "abstract": "This paper presents a novel approach to Brain-Inspired Neural Networks for Efficient Learning. Our research shows significant improvements over existing methods.", "authors": ["Researcher 2A", "Researcher 2B"], "year": 2021, "venue": "Conference 2", "url": "https://example.com/paper2", "citation_count": 90, "source": "semantic_scholar", "paper_id": "mock-paper-id-2", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 3", "abstract": "This paper presents a novel approach to Brain-Inspired Neural Networks for Efficient Learning. Our research shows significant improvements over existing methods.", "authors": ["Researcher 3A", "Researcher 3B"], "year": 2023, "venue": "Conference 3", "url": "https://example.com/paper3", "citation_count": 85, "source": "semantic_scholar", "paper_id": "mock-paper-id-3", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 4", "abstract": "This paper presents a novel approach to Brain-Inspired Neural Networks for Efficient Learning. Our research shows significant improvements over existing methods.", "authors": ["Researcher 4A", "Researcher 4B"], "year": 2022, "venue": "Conference 4", "url": "https://example.com/paper4", "citation_count": 80, "source": "semantic_scholar", "paper_id": "mock-paper-id-4", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 5", "abstract": "This paper presents a novel approach to Brain-Inspired Neural Networks for Efficient Learning. Our research shows significant improvements over existing methods.", "authors": ["Researcher 5A", "Researcher 5B"], "year": 2021, "venue": "Conference 5", "url": "https://example.com/paper5", "citation_count": 75, "source": "semantic_scholar", "paper_id": "mock-paper-id-5", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 6", "abstract": "This paper presents a novel approach to Brain-Inspired Neural Networks for Efficient Learning. Our research shows significant improvements over existing methods.", "authors": ["Researcher 6A", "Researcher 6B"], "year": 2023, "venue": "Conference 6", "url": "https://example.com/paper6", "citation_count": 70, "source": "semantic_scholar", "paper_id": "mock-paper-id-6", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 7", "abstract": "This paper presents a novel approach to Brain-Inspired Neural Networks for Efficient Learning. Our research shows significant improvements over existing methods.", "authors": ["Researcher 7A", "Researcher 7B"], "year": 2022, "venue": "Conference 7", "url": "https://example.com/paper7", "citation_count": 65, "source": "semantic_scholar", "paper_id": "mock-paper-id-7", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 8", "abstract": "This paper presents a novel approach to Brain-Inspired Neural Networks for Efficient Learning. Our research shows significant improvements over existing methods.", "authors": ["Researcher 8A", "Researcher 8B"], "year": 2021, "venue": "Conference 8", "url": "https://example.com/paper8", "citation_count": 60, "source": "semantic_scholar", "paper_id": "mock-paper-id-8", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 9", "abstract": "This paper presents a novel approach to Brain-Inspired Neural Networks for Efficient Learning. Our research shows significant improvements over existing methods.", "authors": ["Researcher 9A", "Researcher 9B"], "year": 2023, "venue": "Conference 9", "url": "https://example.com/paper9", "citation_count": 55, "source": "semantic_scholar", "paper_id": "mock-paper-id-9", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 10", "abstract": "This paper presents a novel approach to Brain-Inspired Neural Networks for Efficient Learning. Our research shows significant improvements over existing methods.", "authors": ["Researcher 10A", "Researcher 10B"], "year": 2022, "venue": "Conference 10", "url": "https://example.com/paper10", "citation_count": 50, "source": "semantic_scholar", "paper_id": "mock-paper-id-10", "keywords": null, "doi": null}, {"title": "Learning Rate Optimization for Deep Neural Networks Using Lipschitz\n  Bandits", "abstract": "Learning rate is a crucial parameter in training of neural networks. A\nproperly tuned learning rate leads to faster training and higher test accuracy.\nIn this paper, we propose a Lipschitz bandit-driven approach for tuning the\nlearning rate of neural networks. The proposed approach is compared with the\npopular HyperOpt technique used extensively for hyperparameter optimization and\nthe recently developed bandit-based algorithm BLiE. The results for multiple\nneural network architectures indicate that our method finds a better learning\nrate using a) fewer evaluations and b) lesser number of epochs per evaluation,\nwhen compared to both HyperOpt and BLiE. Thus, the proposed approach enables\nmore efficient training of neural networks, leading to lower training time and\nlesser computational cost.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "year": 2024, "venue": "arXiv", "url": "http://arxiv.org/abs/2409.09783v1", "citation_count": null, "source": "arxiv", "paper_id": "2409.09783v1", "keywords": null, "doi": ""}, {"title": "Effective and Efficient Computation with Multiple-timescale Spiking\n  Recurrent Neural Networks", "abstract": "The emergence of brain-inspired neuromorphic computing as a paradigm for edge\nAI is motivating the search for high-performance and efficient spiking neural\nnetworks to run on this hardware. However, compared to classical neural\nnetworks in deep learning, current spiking neural networks lack competitive\nperformance in compelling areas. Here, for sequential and streaming tasks, we\ndemonstrate how a novel type of adaptive spiking recurrent neural network\n(SRNN) is able to achieve state-of-the-art performance compared to other\nspiking neural networks and almost reach or exceed the performance of classical\nrecurrent neural networks (RNNs) while exhibiting sparse activity. From this,\nwe calculate a $>$100x energy improvement for our SRNNs over classical RNNs on\nthe harder tasks. To achieve this, we model standard and adaptive\nmultiple-timescale spiking neurons as self-recurrent neural units, and leverage\nsurrogate gradients and auto-differentiation in the PyTorch Deep Learning\nframework to efficiently implement backpropagation-through-time, including\nlearning of the important spiking neuron parameters to adapt our spiking\nneurons to the tasks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "year": 2020, "venue": "arXiv", "url": "http://arxiv.org/abs/2005.11633v2", "citation_count": null, "source": "arxiv", "paper_id": "2005.11633v2", "keywords": null, "doi": ""}, {"title": "Brain-Inspired Memristive Neural Networks for Unsupervised Learning", "abstract": "", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2019, "venue": "Handbook of Memristor Networks", "url": "https://doi.org/10.1007/978-3-319-76375-0_17", "citation_count": null, "source": "crossref", "paper_id": "10.1007/978-3-319-76375-0_17", "keywords": null, "doi": "10.1007/978-3-319-76375-0_17"}, {"title": "Brain-inspired Multimodal Learning Based on Neural Networks", "abstract": "<jats:p> Modern computational models have leveraged biological advances in human brain research. This study addresses the problem of multimodal learning with the help of brain-inspired models. Specifically, a unified multimodal learning architecture is proposed based on deep neural networks, which are inspired by the biology of the visual cortex of the human brain. This unified framework is validated by two practical multimodal learning tasks: image captioning, involving visual and natural language signals, and visual-haptic fusion, involving haptic and visual signals. Extensive experiments are conducted under the framework, and competitive results are achieved. </jats:p>", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "year": 2018, "venue": "Brain Science Advances", "url": "https://doi.org/10.26599/bsa.2018.9050004", "citation_count": null, "source": "crossref", "paper_id": "10.26599/bsa.2018.9050004", "keywords": null, "doi": "10.26599/bsa.2018.9050004"}, {"title": "Brain-Inspired Spiking Neural Networks", "abstract": "<jats:p>Brain is a very efficient computing system. It performs very complex tasks while occupying about 2 liters of volume and consuming very little energy. The computation tasks are performed by special cells in the brain called neurons. They compute using electrical pulses and exchange information between them through chemicals called neurotransmitters. With this as inspiration, there are several compute models which exist today trying to exploit the inherent efficiencies demonstrated by nature. The compute models representing spiking neural networks (SNNs) are biologically plausible, hence are used to study and understand the workings of brain and nervous system. More importantly, they are used to solve a wide variety of problems in the field of artificial intelligence (AI). They are uniquely suited to model temporal and spatio-temporal data paradigms. This chapter explores the fundamental concepts of SNNs, few of the popular neuron models, how the information is represented, learning methodologies, and state of the art platforms for implementing and evaluating SNNs along with a discussion on their applications and broader role in the field of AI and data networks.</jats:p>", "authors": ["<PERSON><PERSON><PERSON>"], "year": 2021, "venue": "Biomimetics", "url": "https://doi.org/10.5772/intechopen.93435", "citation_count": null, "source": "crossref", "paper_id": "10.5772/intechopen.93435", "keywords": null, "doi": "10.5772/intechopen.93435"}, {"title": "Energy-efficient Spiking Neural Network Equalization for IM/DD Systems\n  with Optimized Neural Encoding", "abstract": "We propose an energy-efficient equalizer for IM/DD systems based on spiking\nneural networks. We optimize a neural spike encoding that boosts the\nequalizer's performance while decreasing energy consumption.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "year": 2023, "venue": "arXiv", "url": "http://arxiv.org/abs/2312.12909v1", "citation_count": null, "source": "arxiv", "paper_id": "2312.12909v1", "keywords": null, "doi": ""}, {"title": "Exploring the Imposition of Synaptic Precision Restrictions For\n  Evolutionary Synthesis of Deep Neural Networks", "abstract": "A key contributing factor to incredible success of deep neural networks has\nbeen the significant rise on massively parallel computing devices allowing\nresearchers to greatly increase the size and depth of deep neural networks,\nleading to significant improvements in modeling accuracy. Although deeper,\nlarger, or complex deep neural networks have shown considerable promise, the\ncomputational complexity of such networks is a major barrier to utilization in\nresource-starved scenarios. We explore the synaptogenesis of deep neural\nnetworks in the formation of efficient deep neural network architectures within\nan evolutionary deep intelligence framework, where a probabilistic generative\nmodeling strategy is introduced to stochastically synthesize increasingly\nefficient yet effective offspring deep neural networks over generations,\nmimicking evolutionary processes such as heredity, random mutation, and natural\nselection in a probabilistic manner. In this study, we primarily explore the\nimposition of synaptic precision restrictions and its impact on the\nevolutionary synthesis of deep neural networks to synthesize more efficient\nnetwork architectures tailored for resource-starved scenarios. Experimental\nresults show significant improvements in synaptic efficiency (~10X decrease for\nGoogLeNet-based DetectNet) and inference speed (>5X increase for\nGoogLeNet-based DetectNet) while preserving modeling accuracy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "year": 2017, "venue": "arXiv", "url": "http://arxiv.org/abs/1707.00095v1", "citation_count": null, "source": "arxiv", "paper_id": "1707.00095v1", "keywords": null, "doi": ""}, {"title": "Convex Formulation of Overparameterized Deep Neural Networks", "abstract": "Analysis of over-parameterized neural networks has drawn significant\nattention in recentyears. It was shown that such systems behave like convex\nsystems under various restrictedsettings, such as for two-level neural\nnetworks, and when learning is only restricted locally inthe so-called neural\ntangent kernel space around specialized initializations. However, there areno\ntheoretical techniques that can analyze fully trained deep neural networks\nencountered inpractice. This paper solves this fundamental problem by\ninvestigating such overparameterizeddeep neural networks when fully trained. We\ngeneralize a new technique called neural feature repopulation, originally\nintroduced in (<PERSON> et al., 2019a) for two-level neural networks, to analyze\ndeep neural networks. It is shown that under suitable representations,\noverparameterized deep neural networks are inherently convex, and when\noptimized, the system can learn effective features suitable for the underlying\nlearning task under mild conditions. This new analysis is consistent with\nempirical observations that deep neural networks are capable of learning\nefficient feature representations. Therefore, the highly unexpected result of\nthis paper can satisfactorily explain the practical success of deep neural\nnetworks. Empirical studies confirm that predictions of our theory are\nconsistent with results observed in practice.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "year": 2019, "venue": "arXiv", "url": "http://arxiv.org/abs/1911.07626v1", "citation_count": null, "source": "arxiv", "paper_id": "1911.07626v1", "keywords": null, "doi": ""}, {"title": "Assessing Intelligence in Artificial Neural Networks", "abstract": "The purpose of this work was to develop of metrics to assess network\narchitectures that balance neural network size and task performance. To this\nend, the concept of neural efficiency is introduced to measure neural layer\nutilization, and a second metric called artificial intelligence quotient (aIQ)\nwas created to balance neural network performance and neural network\nefficiency. To study aIQ and neural efficiency, two simple neural networks were\ntrained on MNIST: a fully connected network (LeNet-300-100) and a convolutional\nneural network (LeNet-5). The LeNet-5 network with the highest aIQ was 2.32%\nless accurate but contained 30,912 times fewer parameters than the highest\naccuracy network. Both batch normalization and dropout layers were found to\nincrease neural efficiency. Finally, high aIQ networks are shown to be\nmemorization and overtraining resistant, capable of learning proper digit\nclassification with an accuracy of 92.51% even when 75% of the class labels are\nrandomized. These results demonstrate the utility of aIQ and neural efficiency\nas metrics for balancing network performance and size.", "authors": ["<PERSON>", "<PERSON>"], "year": 2020, "venue": "arXiv", "url": "http://arxiv.org/abs/2006.02909v1", "citation_count": null, "source": "arxiv", "paper_id": "2006.02909v1", "keywords": null, "doi": ""}, {"title": "Graph Structure of Neural Networks", "abstract": "Neural networks are often represented as graphs of connections between\nneurons. However, despite their wide use, there is currently little\nunderstanding of the relationship between the graph structure of the neural\nnetwork and its predictive performance. Here we systematically investigate how\ndoes the graph structure of neural networks affect their predictive\nperformance. To this end, we develop a novel graph-based representation of\nneural networks called relational graph, where layers of neural network\ncomputation correspond to rounds of message exchange along the graph structure.\nUsing this representation we show that: (1) a \"sweet spot\" of relational graphs\nleads to neural networks with significantly improved predictive performance;\n(2) neural network's performance is approximately a smooth function of the\nclustering coefficient and average path length of its relational graph; (3) our\nfindings are consistent across many different tasks and datasets; (4) the sweet\nspot can be identified efficiently; (5) top-performing neural networks have\ngraph structure surprisingly similar to those of real biological neural\nnetworks. Our work opens new directions for the design of neural architectures\nand the understanding on neural networks in general.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2020, "venue": "arXiv", "url": "http://arxiv.org/abs/2007.06559v2", "citation_count": null, "source": "arxiv", "paper_id": "2007.06559v2", "keywords": null, "doi": ""}], "workflows": {"paper_1": {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 1", "datasets": [], "network_architectures": ["Brain-Inspired Neural Networks"], "platforms_tools": [], "research_methods": ["Novel Approach"], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": ["Efficient Learning"]}, "paper_2": {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 2", "datasets": [], "network_architectures": ["Brain-Inspired Neural Networks"], "platforms_tools": [], "research_methods": ["Novel Approach 2"], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": []}, "paper_3": {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 3", "datasets": [], "network_architectures": ["Brain-Inspired Neural Networks"], "platforms_tools": [], "research_methods": ["Novel Approach 3"], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": []}, "paper_4": {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 4", "datasets": [], "network_architectures": ["Brain-Inspired Neural Networks"], "platforms_tools": [], "research_methods": ["Novel Approach 4"], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": ["Efficient Learning"]}, "paper_5": {"title": "Brain-Inspired Neural Networks for Efficient Learning Research: Novel Approach 5", "datasets": [], "network_architectures": ["Brain-Inspired Neural Networks"], "platforms_tools": [], "research_methods": ["Novel Approach 5"], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": ["Efficient Learning"]}}, "research_topic": "Brain-Inspired Neural Networks for Efficient Learning", "timestamp": "2025-07-24 16:25:25", "total_papers": 20}