"""
诊断论文内容生成问题
检查为何生成的内容为0字符
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置环境变量确保使用真实API
os.environ["DEEPSEEK_API_KEY"] = "***********************************"
os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
os.environ["MOCK_MODE"] = "false"
os.environ["ENABLE_MOCK_DATA"] = "false"

from paper_generation.brain_paper_writer import BrainPaperWriter
from core.llm_client import LLMClient

def run_debug_content_generation():
    """
    运行诊断内容生成的测试
    """
    print("\n" + "=" * 80)
    print("🔍 论文内容生成诊断")
    print("=" * 80)
    
    # 创建输出目录
    output_dir = "output/debug_content"
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建LLM客户端
    print("🔄 初始化LLM客户端...")
    llm_client = LLMClient(
        model="deepseek-chat", 
        temperature=0.7,
        api_key=os.environ["DEEPSEEK_API_KEY"],
        provider="deepseek"
    )
    
    if not llm_client.deepseek_mode:
        print("❌ LLMClient未使用DeepSeek模式，测试终止")
        return
    
    print(f"✅ LLM客户端初始化成功（DeepSeek模式: {llm_client.deepseek_mode}）")
    
    # 创建论文写作器
    paper_writer = BrainPaperWriter(
        model="deepseek-chat",
        temperature=0.7,
        llm_client=llm_client
    )
    
    print(f"✅ 论文写作器初始化成功")
    
    # 测试各个部分的生成
    sections_to_test = ["abstract", "introduction", "methodology", "conclusion"]
    context = {
        "research_topic": "Brain-Inspired Neural Networks for Efficient Learning",
        "target_venue": "ICML"
    }
    
    results = {}
    
    for section in sections_to_test:
        print(f"\n📝 诊断 {section} 部分生成...")
        
        # 运行诊断
        debug_result = paper_writer.debug_content_generation(section, context)
        
        # 汇总结果
        content_length = len(debug_result.get("final_content", ""))
        success = debug_result.get("success", False)
        error_count = len(debug_result.get("errors", []))
        
        status = "✅ 成功" if success and content_length > 0 else "❌ 失败"
        print(f"{status} - {section}: {content_length} 字符, {error_count} 错误")
        
        # 显示错误信息
        for error in debug_result.get("errors", []):
            print(f"  ⚠️ {error.get('phase')}: {error.get('error')}")
        
        # 存储结果
        results[section] = {
            "success": success,
            "content_length": content_length,
            "error_count": error_count,
            "debug_result": debug_result
        }
    
    # 保存完整诊断信息
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"content_diagnosis_{timestamp}.json")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n💾 完整诊断结果已保存至: {output_file}")
    
    # 总结建议
    print("\n📊 诊断总结:")
    successful_sections = sum(1 for s in results.values() if s["success"])
    print(f"- 成功生成内容的部分: {successful_sections}/{len(sections_to_test)}")
    
    if successful_sections < len(sections_to_test):
        print("- 建议检查以下问题:")
        print("  1. API响应格式是否与预期不符")
        print("  2. 专家代理的analyze方法返回格式")
        print("  3. 提示词是否需要优化")

if __name__ == "__main__":
    run_debug_content_generation() 