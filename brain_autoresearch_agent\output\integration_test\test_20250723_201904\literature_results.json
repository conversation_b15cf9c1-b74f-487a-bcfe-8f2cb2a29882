{"papers": [{"title": "Conversion of Continuous-Valued Deep Networks to Efficient Event-Driven Networks for Image Classification", "abstract": "Spiking neural networks (SNNs) can potentially offer an efficient way of doing inference because the neurons in the networks are sparsely activated and computations are event-driven. Previous work showed that simple continuous-valued deep Convolutional Neural Networks (CNNs) can be converted into accurate spiking equivalents. These networks did not include certain common operations such as max-pooling, softmax, batch-normalization and Inception-modules. This paper presents spiking equivalents of these operations therefore allowing conversion of nearly arbitrary CNN architectures. We show conversion of popular CNN architectures, including VGG-16 and Inception-v3, into SNNs that produce the best results reported to date on MNIST, CIFAR-10 and the challenging ImageNet dataset. SNNs can trade off classification error rate against the number of available operations whereas deep continuous-valued neural networks require a fixed number of operations to achieve their classification error rate. From the examples of LeNet for MNIST and BinaryNet for CIFAR-10, we show that with an increase in error rate of a few percentage points, the SNNs can achieve more than 2x reductions in operations compared to the original CNNs. This highlights the potential of SNNs in particular when deployed on power-efficient neuromorphic spiking neuron chips, for use in embedded applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Yuhuang Hu", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "year": 2017, "venue": "Frontiers in Neuroscience", "url": "https://www.semanticscholar.org/paper/915cc4b359863f256957485c8a60f2cceb78ab5f", "citation_count": 909, "source": "semantic_scholar", "paper_id": "915cc4b359863f256957485c8a60f2cceb78ab5f", "keywords": null, "doi": null}, {"title": "Evolving Deep Convolutional Neural Networks for Image Classification", "abstract": "Evolutionary paradigms have been successfully applied to neural network designs for two decades. Unfortunately, these methods cannot scale well to the modern deep neural networks due to the complicated architectures and large quantities of connection weights. In this paper, we propose a new method using genetic algorithms for evolving the architectures and connection weight initialization values of a deep convolutional neural network to address image classification problems. In the proposed algorithm, an efficient variable-length gene encoding strategy is designed to represent the different building blocks and the potentially optimal depth in convolutional neural networks. In addition, a new representation scheme is developed for effectively initializing connection weights of deep convolutional neural networks, which is expected to avoid networks getting stuck into local minimum that is typically a major issue in the backward gradient-based optimization. Furthermore, a novel fitness evaluation method is proposed to speed up the heuristic search with substantially less computational resource. The proposed algorithm is examined and compared with 22 existing algorithms on nine widely used image classification tasks, including the state-of-the-art methods. The experimental results demonstrate the remarkable superiority of the proposed algorithm over the state-of-the-art designs in terms of classification error rate and the number of parameters (weights).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2017, "venue": "IEEE Transactions on Evolutionary Computation", "url": "https://www.semanticscholar.org/paper/1b791b4031b921181243d12fe908273074eda5ed", "citation_count": 577, "source": "semantic_scholar", "paper_id": "1b791b4031b921181243d12fe908273074eda5ed", "keywords": null, "doi": null}, {"title": "Efficient convolutional neural networks on Raspberry Pi for image classification", "abstract": "With the good performance of deep learning in the field of computer vision (CV), the convolutional neural network (CNN) architectures have become main backbones of image recognition tasks. With the widespread use of mobile devices, neural network models based on platforms with low computing power are gradually being paid attention. However, due to the limitation of computing power, deep learning algorithms are usually not available on mobile devices. This paper proposes a lightweight convolutional neural network TripleNet, which can operate easily on Raspberry Pi. Adopted from the concept of block connections in ThreshNet, the newly proposed network model compresses and accelerates the network model, reduces the amount of parameters of the network, and shortens the inference time of each image while ensuring the accuracy. Our proposed TripleNet and other State-of-the-Art (SOTA) neural networks perform image classification experiments with the CIFAR-10 and SVHN datasets on Raspberry Pi. The experimental results show that, compared with GhostNet, MobileNet, ThreshNet, EfficientNet, and HarDNet, the inference time of TripleNet per image is shortened by 15%, 16%, 17%, 24%, and 30%, respectively. The detail codes of this work are available at https://github.com/RuiyangJu/TripleNet .", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "year": 2022, "venue": "Journal of Real-Time Image Processing", "url": "https://www.semanticscholar.org/paper/8d3af3c839d11d665e09aceaec7c2d3624693a7c", "citation_count": 16, "source": "semantic_scholar", "paper_id": "8d3af3c839d11d665e09aceaec7c2d3624693a7c", "keywords": null, "doi": null}, {"title": "Advanced Meta-Heuristics, Convolutional Neural Networks, and Feature Selectors for Efficient COVID-19 X-Ray Chest Image Classification", "abstract": "The chest X-ray is considered a significant clinical utility for basic examination and diagnosis. The human lung area can be affected by various infections, such as bacteria and viruses, leading to pneumonia. Efficient and reliable classification method facilities the diagnosis of such infections. Deep transfer learning has been introduced for pneumonia detection from chest X-rays in different models. However, there is still a need for further improvements in the feature extraction and advanced classification stages. This paper proposes a classification method with two stages to classify different cases from the chest X-ray images based on a proposed Advanced Squirrel Search Optimization Algorithm (ASSOA). The first stage is the feature learning and extraction processes based on a Convolutional Neural Network (CNN) model named ResNet-50 with image augmentation and dropout processes. The ASSOA algorithm is then applied to the extracted features for the feature selection process. Finally, the Multi-layer Perceptron (MLP) Neural Network’s connection weights are optimized by the proposed ASSOA algorithm (using the selected features) to classify input cases. A Kaggle chest X-ray images (Pneumonia) dataset consists of 5,863 X-rays is employed in the experiments. The proposed ASSOA algorithm is compared with the basic Squirrel Search (SS) optimization algorithm, Grey Wolf Optimizer (GWO), and Genetic Algorithm (GA) for feature selection to validate its efficiency. The proposed (ASSOA + MLP) is also compared with other classifiers, based on (SS + MLP), (GWO + MLP), and (GA + MLP), in performance metrics. The proposed (ASSOA + MLP) algorithm achieved a classification mean accuracy of (99.26%). The ASSOA + MLP algorithm also achieved a classification mean accuracy of (99.7%) for a chest X-ray COVID-19 dataset tested from GitHub. The results and statistical tests demonstrate the high effectiveness of the proposed method in determining the infected cases.", "authors": ["<PERSON><PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Rokaia <PERSON>", "<PERSON><PERSON>"], "year": 2021, "venue": "IEEE Access", "url": "https://www.semanticscholar.org/paper/9accd71d68cbe6c5817228f60093538595d18d33", "citation_count": 84, "source": "semantic_scholar", "paper_id": "9accd71d68cbe6c5817228f60093538595d18d33", "keywords": null, "doi": null}, {"title": "All You Need Is a Few Shifts: Designing Efficient Convolutional Neural Networks for Image Classification", "abstract": "Shift operation is an efficient alternative over depthwise separable convolution. However, it is still bottlenecked by its implementation manner, namely memory movement. To put this direction forward, a new and novel basic component named Sparse Shift Layer (SSL) is introduced in this paper to construct efficient convolutional neural networks. In this family of architectures, the basic block is only composed by 1x1 convolutional layers with only a few shift operations applied to the intermediate feature maps. To make this idea feasible, we introduce shift operation penalty during optimization and further propose a quantization-aware shift learning method to impose the learned displacement more friendly for inference. Extensive ablation studies indicate that only a few shift operations are sufficient to provide spatial information communication. Furthermore, to maximize the role of SSL, we redesign an improved network architecture to Fully Exploit the limited capacity of neural Network (FE-Net). Equipped with SSL, this network can achieve 75.0% top-1 accuracy on ImageNet with only 563M M-Adds. It surpasses other counterparts constructed by depthwise separable convolution and the networks searched by NAS in terms of accuracy and practical speed.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "year": 2019, "venue": "Computer Vision and Pattern Recognition", "url": "https://www.semanticscholar.org/paper/4b976c711eb211f67c5bb36c37175eec2f5bb738", "citation_count": 81, "source": "semantic_scholar", "paper_id": "4b976c711eb211f67c5bb36c37175eec2f5bb738", "keywords": null, "doi": null}], "workflows": {"915cc4b359863f256957485c8a60f2cceb78ab5f": {"title": "Conversion of Continuous-Valued Deep Networks to Efficient Event-Driven Networks for Image Classification", "abstract": "Spiking neural networks (SNNs) can potentially offer an efficient way of doing inference because the neurons in the networks are sparsely activated and computations are event-driven. Previous work showed that simple continuous-valued deep Convolutional Neural Networks (CNNs) can be converted into accurate spiking equivalents. These networks did not include certain common operations such as max-pooling, softmax, batch-normalization and Inception-modules. This paper presents spiking equivalents of...", "datasets": ["MNIST", "CIFAR-10", "ImageNet"], "network_architectures": ["VGG-16", "Inception-v3", "LeNet", "BinaryNet", "Spiking Neural Network (SNN)", "Convolutional Neural Network (CNN)"], "platforms_tools": ["neuromorphic spiking neuron chips"], "research_methods": ["conversion of continuous-valued deep networks to spiking equivalents", "event-driven computation", "sparse activation"], "evaluation_metrics": ["classification error rate", "number of operations", "accuracy"], "brain_inspiration": ["spiking neural networks", "event-driven computation", "sparse activation"], "ai_techniques": ["deep learning", "spiking neural networks", "Convolutional Neural Networks (CNNs)", "Inception-modules", "max-pooling", "softmax", "batch-normalization"]}, "1b791b4031b921181243d12fe908273074eda5ed": {"title": "Evolving Deep Convolutional Neural Networks for Image Classification", "abstract": "Evolutionary paradigms have been successfully applied to neural network designs for two decades. Unfortunately, these methods cannot scale well to the modern deep neural networks due to the complicated architectures and large quantities of connection weights. In this paper, we propose a new method using genetic algorithms for evolving the architectures and connection weight initialization values of a deep convolutional neural network to address image classification problems. In the proposed algo...", "datasets": [], "network_architectures": ["deep convolutional neural network"], "platforms_tools": [], "research_methods": ["genetic algorithms", "variable-length gene encoding strategy", "heuristic search", "backward gradient-based optimization"], "evaluation_metrics": ["classification error rate", "number of parameters (weights)"], "brain_inspiration": [], "ai_techniques": ["genetic algorithms", "deep convolutional neural network", "heuristic search", "gradient-based optimization"]}, "8d3af3c839d11d665e09aceaec7c2d3624693a7c": {"title": "Efficient convolutional neural networks on Raspberry Pi for image classification", "abstract": "With the good performance of deep learning in the field of computer vision (CV), the convolutional neural network (CNN) architectures have become main backbones of image recognition tasks. With the widespread use of mobile devices, neural network models based on platforms with low computing power are gradually being paid attention. However, due to the limitation of computing power, deep learning algorithms are usually not available on mobile devices. This paper proposes a lightweight convolution...", "datasets": ["CIFAR-10", "SVHN"], "network_architectures": ["TripleNet", "GhostNet", "MobileNet", "ThreshNet", "EfficientNet", "HarDNet"], "platforms_tools": ["Raspberry Pi"], "research_methods": ["block connections", "network model compression", "network model acceleration"], "evaluation_metrics": ["inference time per image", "accuracy"], "brain_inspiration": [], "ai_techniques": ["convolutional neural network", "deep learning", "image classification"]}, "9accd71d68cbe6c5817228f60093538595d18d33": {"title": "Advanced Meta-Heuristics, Convolutional Neural Networks, and Feature Selectors for Efficient COVID-19 X-Ray Chest Image Classification", "abstract": "The chest X-ray is considered a significant clinical utility for basic examination and diagnosis. The human lung area can be affected by various infections, such as bacteria and viruses, leading to pneumonia. Efficient and reliable classification method facilities the diagnosis of such infections. Deep transfer learning has been introduced for pneumonia detection from chest X-rays in different models. However, there is still a need for further improvements in the feature extraction and advanced ...", "datasets": ["Kaggle chest X-ray images (Pneumonia)", "chest X-ray COVID-19 dataset from GitHub"], "network_architectures": ["ResNet-50", "Multi-layer Perceptron (MLP) Neural Network"], "platforms_tools": [], "research_methods": ["Advanced Squirrel Search Optimization Algorithm (ASSOA)", "image augmentation", "dropout processes", "feature selection process", "Squirrel Search (SS) optimization algorithm", "Grey Wolf Optimizer (GWO)", "Genetic Algorithm (GA)"], "evaluation_metrics": ["classification mean accuracy"], "brain_inspiration": [], "ai_techniques": ["Deep transfer learning", "Convolutional Neural Network (CNN)", "Multi-layer Perceptron (MLP) Neural Network", "Advanced Squirrel Search Optimization Algorithm (ASSOA)", "Squirrel Search (SS) optimization algorithm", "Grey Wolf Optimizer (GWO)", "Genetic Algorithm (GA)"]}, "4b976c711eb211f67c5bb36c37175eec2f5bb738": {"title": "All You Need Is a Few Shifts: Designing Efficient Convolutional Neural Networks for Image Classification", "abstract": "Shift operation is an efficient alternative over depthwise separable convolution. However, it is still bottlenecked by its implementation manner, namely memory movement. To put this direction forward, a new and novel basic component named Sparse Shift Layer (SSL) is introduced in this paper to construct efficient convolutional neural networks. In this family of architectures, the basic block is only composed by 1x1 convolutional layers with only a few shift operations applied to the intermediate...", "datasets": ["ImageNet"], "network_architectures": ["Sparse Shift Layer (SSL)", "Fully Exploit the limited capacity of neural Network (FE-Net)"], "platforms_tools": [], "research_methods": ["shift operation penalty", "quantization-aware shift learning method"], "evaluation_metrics": ["top-1 accuracy", "M-Adds"], "brain_inspiration": [], "ai_techniques": ["depthwise separable convolution", "shift operation", "neural architecture search (NAS)"]}}, "timestamp": "2025-07-23 20:23:36"}