# 脑启发智能AutoResearch Agent依赖包
# 基于AI Scientist v2的要求并添加项目特定需求

# 核心依赖
pyyaml>=6.0
requests>=2.31.0
backoff>=2.2.1
tiktoken>=0.7.0

# LLM API支持
openai>=1.0.0
anthropic>=0.25.0

# 可选依赖（根据需要安装）
# transformers>=4.30.0  # 如果需要本地模型
# torch>=2.0.0         # PyTorch支持
# numpy>=1.24.0        # 数值计算
# matplotlib>=3.7.0    # 可视化
# seaborn>=0.12.0      # 统计可视化

# 开发依赖
pytest>=7.0.0         # 测试框架
black>=23.0.0         # 代码格式化
flake8>=6.0.0         # 代码检查

# 数据处理
pandas>=2.0.0         # 数据分析
pymupdf4llm>=0.0.1    # PDF处理
pypdf>=3.0.0          # PDF处理备选
