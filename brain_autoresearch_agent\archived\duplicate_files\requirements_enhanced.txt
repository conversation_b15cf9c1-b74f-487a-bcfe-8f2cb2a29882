# Enhanced Paper Generation System Requirements
# AI Scientist v2 Integration with Hybrid Models

# Core dependencies
openai>=1.0.0
requests>=2.28.0
numpy>=1.21.0
pandas>=1.3.0

# Visualization and Image Processing
matplotlib>=3.5.0
pillow>=9.0.0
plotly>=5.0.0

# LaTeX and Document Processing
jinja2>=3.0.0

# Async and Performance
aiohttp>=3.8.0
asyncio-throttle>=1.0.2

# Data Processing
python-dateutil>=2.8.0
pydantic>=1.9.0

# Optional LaTeX compilation (requires system TeX installation)
# pdflatex (system package)

# Development and Testing
pytest>=7.0.0
pytest-asyncio>=0.20.0

# Configuration and Logging
pyyaml>=6.0
colorlog>=6.0.0
