"""
LaTeX格式优化专家模块 - 清理版本
专门负责论文的LaTeX格式优化，确保符合顶级会议标准
完全重写以避免字符串转义问题
"""

import re
import os
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class LaTeXFormatIssue:
    """LaTeX格式问题"""
    issue_type: str  # 问题类型
    line_number: int  # 行号
    description: str  # 问题描述
    severity: str  # 严重程度: critical, warning, suggestion
    fix_suggestion: str  # 修复建议

@dataclass
class LaTeXOptimizationResult:
    """LaTeX优化结果"""
    original_content: str
    optimized_content: str
    issues_found: List[LaTeXFormatIssue]
    issues_fixed: List[LaTeXFormatIssue]
    venue_compliance: Dict[str, bool]  # 会议格式合规性
    quality_score: float  # 格式质量分数

class LaTeXFormatExpert:
    """LaTeX格式优化专家 - 专业级别会议格式优化（清理版）"""
    
    def __init__(self, hybrid_client=None):
        self.hybrid_client = hybrid_client
        self.venue_templates = self._load_venue_templates()
        
    def _load_venue_templates(self) -> Dict[str, Dict]:
        """加载会议模板配置 - 安全版本"""
        return {
            'ICML': {
                'documentclass': 'documentclass[accepted]{icml2024}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'subfigure'],
                'title_format': 'icmltitle',
                'author_format': 'icmlauthor',
                'abstract_env': 'abstract',
                'keywords_cmd': 'icmlkeywords'
            },
            'NeurIPS': {
                'documentclass': 'documentclass{neurips_2024}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'natbib'],
                'title_format': 'title',
                'author_format': 'author',
                'abstract_env': 'abstract',
                'keywords_cmd': 'keywords'
            },
            'ICLR': {
                'documentclass': 'documentclass{iclr2024_conference}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx'],
                'title_format': 'title',
                'author_format': 'author',
                'abstract_env': 'abstract',
                'keywords_cmd': 'keywords'
            }
        }
    
    def detect_format_issues(self, content: str, venue: str = 'ICML') -> List[LaTeXFormatIssue]:
        """检测格式问题 - 安全版本"""
        issues = []
        
        try:
            # 检查文档类声明
            if 'documentclass' not in content:
                issues.append(LaTeXFormatIssue(
                    issue_type='missing_documentclass',
                    line_number=1,
                    description=f'缺少文档类声明，建议使用{venue}格式',
                    severity='critical',
                    fix_suggestion='添加文档类声明'
                ))
            
            venue_template = self.venue_templates.get(venue, self.venue_templates['ICML'])
            
            # 检查必需包
            for pkg in venue_template['required_packages'][:5]:  # 检查前5个主要包
                if f'usepackage{{{pkg}}}' not in content and f'usepackage[' not in content or pkg not in content:
                    issues.append(LaTeXFormatIssue(
                        issue_type='missing_package',
                        line_number=0,
                        description=f'缺少必需包: {pkg}',
                        severity='warning',
                        fix_suggestion=f'添加包导入: {pkg}'
                    ))
            
            # 检查标题
            if 'title{' not in content:
                issues.append(LaTeXFormatIssue(
                    issue_type='missing_title',
                    line_number=0,
                    description='缺少标题声明',
                    severity='critical',
                    fix_suggestion='添加标题声明'
                ))
            
            # 检查作者
            if 'author{' not in content:
                issues.append(LaTeXFormatIssue(
                    issue_type='missing_author',
                    line_number=0,
                    description='缺少作者声明',
                    severity='critical',
                    fix_suggestion='添加作者信息'
                ))
            
            # 检查摘要
            if 'begin{abstract}' not in content:
                issues.append(LaTeXFormatIssue(
                    issue_type='missing_abstract',
                    line_number=0,
                    description='缺少摘要环境',
                    severity='critical',
                    fix_suggestion='添加摘要环境'
                ))
            
            # 检查文档结构
            if 'begin{document}' not in content:
                issues.append(LaTeXFormatIssue(
                    issue_type='missing_begin_document',
                    line_number=0,
                    description='缺少文档开始标记',
                    severity='critical',
                    fix_suggestion='添加begin{document}'
                ))
                
            if 'end{document}' not in content:
                issues.append(LaTeXFormatIssue(
                    issue_type='missing_end_document',
                    line_number=0,
                    description='缺少文档结束标记',
                    severity='critical',
                    fix_suggestion='添加end{document}'
                ))
            
        except Exception as e:
            print(f"   ⚠️ 格式检测过程中出现异常: {e}")
            issues.append(LaTeXFormatIssue(
                issue_type='detection_error',
                line_number=0,
                description=f'检测过程出错: {str(e)}',
                severity='warning',
                fix_suggestion='请检查LaTeX语法'
            ))
        
        return issues
    
    def fix_format_issues(self, content: str, issues: List[LaTeXFormatIssue], venue: str = 'ICML') -> Tuple[str, List[LaTeXFormatIssue]]:
        """修复格式问题 - 安全版本"""
        fixed_content = content
        fixed_issues = []
        
        try:
            venue_template = self.venue_templates.get(venue, self.venue_templates['ICML'])
            
            for issue in issues:
                if issue.issue_type == 'missing_documentclass':
                    # 在内容开始处添加文档类
                    documentclass_line = f"\\{venue_template['documentclass']}\n"
                    fixed_content = documentclass_line + fixed_content
                    fixed_issues.append(issue)
                
                elif issue.issue_type == 'missing_package':
                    # 添加缺少的包
                    pkg_name = issue.description.split(': ')[1] if ': ' in issue.description else 'unknown'
                    usepackage_line = f"\\usepackage{{{pkg_name}}}\n"
                    
                    # 在documentclass后添加
                    if 'documentclass' in fixed_content:
                        lines = fixed_content.split('\n')
                        for i, line in enumerate(lines):
                            if 'documentclass' in line:
                                lines.insert(i + 1, f"\\usepackage{{{pkg_name}}}")
                                break
                        fixed_content = '\n'.join(lines)
                        fixed_issues.append(issue)
                
                elif issue.issue_type == 'missing_abstract':
                    # 添加摘要环境
                    abstract_content = "\n\\begin{abstract}\n这里是论文摘要内容。\n\\end{abstract}\n"
                    if 'begin{document}' in fixed_content:
                        fixed_content = fixed_content.replace(
                            'begin{document}',
                            'begin{document}' + abstract_content
                        )
                        fixed_issues.append(issue)
        
        except Exception as e:
            print(f"   ⚠️ 格式修复过程中出现异常: {e}")
        
        return fixed_content, fixed_issues
    
    def optimize_latex_format(self, content: str, venue: str = 'ICML') -> LaTeXOptimizationResult:
        """优化LaTeX格式 - 主要接口（安全版本）"""
        try:
            print(f"🔧 开始专业级LaTeX格式优化 (目标会议: {venue})")
            
            # 1. 检测格式问题
            issues_found = self.detect_format_issues(content, venue)
            print(f"   📊 检测到 {len(issues_found)} 个格式问题")
            
            # 2. 修复格式问题
            optimized_content, issues_fixed = self.fix_format_issues(content, issues_found, venue)
            
            # 3. 检查合规性
            venue_compliance = self._check_venue_compliance_safe(optimized_content, venue)
            
            # 4. 计算质量分数
            quality_score = self._calculate_quality_score_safe(optimized_content, issues_found, issues_fixed)
            
            print(f"   ✅ 格式优化完成，质量分数: {quality_score:.1f}/10")
            
            return LaTeXOptimizationResult(
                original_content=content,
                optimized_content=optimized_content,
                issues_found=issues_found,
                issues_fixed=issues_fixed,
                venue_compliance=venue_compliance,
                quality_score=quality_score
            )
            
        except Exception as e:
            print(f"   ❌ 格式优化失败: {str(e)}")
            # 返回原始内容，避免崩溃
            return LaTeXOptimizationResult(
                original_content=content,
                optimized_content=content,
                issues_found=[],
                issues_fixed=[],
                venue_compliance={'error': False},
                quality_score=5.0  # 中等分数
            )
    
    def _check_venue_compliance_safe(self, content: str, venue: str) -> Dict[str, bool]:
        """检查会议格式合规性 - 安全版本"""
        compliance = {}
        
        try:
            venue_template = self.venue_templates.get(venue, self.venue_templates['ICML'])
            
            # 检查文档类
            compliance['documentclass'] = venue_template['documentclass'] in content or 'documentclass' in content
            
            # 检查必需包（检查前3个主要包）
            required_packages = venue_template['required_packages'][:3]
            package_compliance = 0
            for pkg in required_packages:
                if f'usepackage{{{pkg}}}' in content or (f'usepackage[' in content and pkg in content):
                    package_compliance += 1
            compliance['required_packages'] = package_compliance >= 2
            
            # 检查标题
            compliance['title_format'] = 'title{' in content
            
            # 检查摘要
            compliance['abstract'] = 'begin{abstract}' in content
            
            # 检查基本结构
            compliance['document_structure'] = 'begin{document}' in content and 'end{document}' in content
            
        except Exception as e:
            print(f"   ⚠️ 合规性检查出现异常: {e}")
            compliance['error'] = False
        
        return compliance
    
    def _calculate_quality_score_safe(self, content: str, issues_found: List[LaTeXFormatIssue], issues_fixed: List[LaTeXFormatIssue]) -> float:
        """计算格式质量分数 - 安全版本"""
        try:
            base_score = 8.0  # 起始分数
            
            # 根据问题严重程度扣分
            for issue in issues_found:
                if issue.severity == 'critical':
                    base_score -= 1.5
                elif issue.severity == 'warning':
                    base_score -= 0.8
                elif issue.severity == 'suggestion':
                    base_score -= 0.3
            
            # 修复问题加分
            for issue in issues_fixed:
                if issue.severity == 'critical':
                    base_score += 1.0
                elif issue.severity == 'warning':
                    base_score += 0.6
                elif issue.severity == 'suggestion':
                    base_score += 0.3
            
            # 内容质量加分
            if len(content) > 1000:  # 有实质内容
                base_score += 0.5
            
            if 'section{' in content:  # 有章节结构
                base_score += 0.3
            
            if 'cite{' in content:  # 有引用
                base_score += 0.2
            
            # 确保分数在合理范围内
            return max(3.0, min(10.0, base_score))
            
        except Exception as e:
            print(f"   ⚠️ 质量分数计算出现异常: {e}")
            return 6.0  # 返回中等分数
    
    def validate_latex_compilation(self, content: str) -> Dict[str, any]:
        """验证LaTeX编译能力 - 安全版本"""
        validation_result = {
            'can_compile': True,
            'warnings': [],
            'errors': [],
            'suggestions': []
        }
        
        try:
            # 检查基本结构
            if 'documentclass' not in content:
                validation_result['errors'].append('缺少文档类声明')
                validation_result['can_compile'] = False
            
            if 'begin{document}' not in content:
                validation_result['errors'].append('缺少begin{document}')
                validation_result['can_compile'] = False
            
            if 'end{document}' not in content:
                validation_result['errors'].append('缺少end{document}')
                validation_result['can_compile'] = False
            
            # 检查括号匹配（简单检查）
            open_braces = content.count('{')
            close_braces = content.count('}')
            if open_braces != close_braces:
                validation_result['warnings'].append('括号可能不匹配')
            
            print(f"   📋 编译验证完成: {'✅ 可编译' if validation_result['can_compile'] else '❌ 无法编译'}")
            
        except Exception as e:
            validation_result['errors'].append(f'验证过程出错: {str(e)}')
            validation_result['can_compile'] = False
        
        return validation_result

def main():
    """测试LaTeX格式专家"""
    # 测试内容
    test_content = """
    \\documentclass{article}
    \\begin{document}
    \\title{Test Paper}
    \\author{Test Author}
    \\begin{abstract}
    This is a test abstract.
    \\end{abstract}
    \\section{Introduction}
    This is the introduction.
    \\end{document}
    """
    
    # 创建专家实例
    expert = LaTeXFormatExpert()
    
    # 优化格式
    result = expert.optimize_latex_format(test_content, 'ICML')
    
    print("格式优化结果:")
    print(f"发现问题: {len(result.issues_found)}")
    print(f"修复问题: {len(result.issues_fixed)}")
    print(f"质量分数: {result.quality_score}")
    print(f"会议合规性: {result.venue_compliance}")

if __name__ == "__main__":
    main()
