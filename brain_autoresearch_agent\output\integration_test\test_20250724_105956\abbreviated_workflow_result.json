{"papers": [{"title": "Conversion of Continuous-Valued Deep Networks to Efficient Event-Driven Networks for Image Classification", "abstract": "Spiking neural networks (SNNs) can potentially offer an efficient way of doing inference because the neurons in the networks are sparsely activated and computations are event-driven. Previous work showed that simple continuous-valued deep Convolutional Neural Networks (CNNs) can be converted into accurate spiking equivalents. These networks did not include certain common operations such as max-pooling, softmax, batch-normalization and Inception-modules. This paper presents spiking equivalents of these operations therefore allowing conversion of nearly arbitrary CNN architectures. We show conversion of popular CNN architectures, including VGG-16 and Inception-v3, into SNNs that produce the best results reported to date on MNIST, CIFAR-10 and the challenging ImageNet dataset. SNNs can trade off classification error rate against the number of available operations whereas deep continuous-valued neural networks require a fixed number of operations to achieve their classification error rate. From the examples of LeNet for MNIST and BinaryNet for CIFAR-10, we show that with an increase in error rate of a few percentage points, the SNNs can achieve more than 2x reductions in operations compared to the original CNNs. This highlights the potential of SNNs in particular when deployed on power-efficient neuromorphic spiking neuron chips, for use in embedded applications.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Yuhuang Hu", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "year": 2017, "venue": "Frontiers in Neuroscience", "url": "https://www.semanticscholar.org/paper/915cc4b359863f256957485c8a60f2cceb78ab5f", "citation_count": 909, "source": "semantic_scholar", "paper_id": "915cc4b359863f256957485c8a60f2cceb78ab5f", "keywords": null, "doi": null}, {"title": "Evolving Deep Convolutional Neural Networks for Image Classification", "abstract": "Evolutionary paradigms have been successfully applied to neural network designs for two decades. Unfortunately, these methods cannot scale well to the modern deep neural networks due to the complicated architectures and large quantities of connection weights. In this paper, we propose a new method using genetic algorithms for evolving the architectures and connection weight initialization values of a deep convolutional neural network to address image classification problems. In the proposed algorithm, an efficient variable-length gene encoding strategy is designed to represent the different building blocks and the potentially optimal depth in convolutional neural networks. In addition, a new representation scheme is developed for effectively initializing connection weights of deep convolutional neural networks, which is expected to avoid networks getting stuck into local minimum that is typically a major issue in the backward gradient-based optimization. Furthermore, a novel fitness evaluation method is proposed to speed up the heuristic search with substantially less computational resource. The proposed algorithm is examined and compared with 22 existing algorithms on nine widely used image classification tasks, including the state-of-the-art methods. The experimental results demonstrate the remarkable superiority of the proposed algorithm over the state-of-the-art designs in terms of classification error rate and the number of parameters (weights).", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2017, "venue": "IEEE Transactions on Evolutionary Computation", "url": "https://www.semanticscholar.org/paper/1b791b4031b921181243d12fe908273074eda5ed", "citation_count": 577, "source": "semantic_scholar", "paper_id": "1b791b4031b921181243d12fe908273074eda5ed", "keywords": null, "doi": null}, {"title": "Efficient convolutional neural networks on Raspberry Pi for image classification", "abstract": "With the good performance of deep learning in the field of computer vision (CV), the convolutional neural network (CNN) architectures have become main backbones of image recognition tasks. With the widespread use of mobile devices, neural network models based on platforms with low computing power are gradually being paid attention. However, due to the limitation of computing power, deep learning algorithms are usually not available on mobile devices. This paper proposes a lightweight convolutional neural network TripleNet, which can operate easily on Raspberry Pi. Adopted from the concept of block connections in ThreshNet, the newly proposed network model compresses and accelerates the network model, reduces the amount of parameters of the network, and shortens the inference time of each image while ensuring the accuracy. Our proposed TripleNet and other State-of-the-Art (SOTA) neural networks perform image classification experiments with the CIFAR-10 and SVHN datasets on Raspberry Pi. The experimental results show that, compared with GhostNet, MobileNet, ThreshNet, EfficientNet, and HarDNet, the inference time of TripleNet per image is shortened by 15%, 16%, 17%, 24%, and 30%, respectively. The detail codes of this work are available at https://github.com/RuiyangJu/TripleNet .", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "year": 2022, "venue": "Journal of Real-Time Image Processing", "url": "https://www.semanticscholar.org/paper/8d3af3c839d11d665e09aceaec7c2d3624693a7c", "citation_count": 16, "source": "semantic_scholar", "paper_id": "8d3af3c839d11d665e09aceaec7c2d3624693a7c", "keywords": null, "doi": null}, {"title": "Advanced Meta-Heuristics, Convolutional Neural Networks, and Feature Selectors for Efficient COVID-19 X-Ray Chest Image Classification", "abstract": "The chest X-ray is considered a significant clinical utility for basic examination and diagnosis. The human lung area can be affected by various infections, such as bacteria and viruses, leading to pneumonia. Efficient and reliable classification method facilities the diagnosis of such infections. Deep transfer learning has been introduced for pneumonia detection from chest X-rays in different models. However, there is still a need for further improvements in the feature extraction and advanced classification stages. This paper proposes a classification method with two stages to classify different cases from the chest X-ray images based on a proposed Advanced Squirrel Search Optimization Algorithm (ASSOA). The first stage is the feature learning and extraction processes based on a Convolutional Neural Network (CNN) model named ResNet-50 with image augmentation and dropout processes. The ASSOA algorithm is then applied to the extracted features for the feature selection process. Finally, the Multi-layer Perceptron (MLP) Neural Network’s connection weights are optimized by the proposed ASSOA algorithm (using the selected features) to classify input cases. A Kaggle chest X-ray images (Pneumonia) dataset consists of 5,863 X-rays is employed in the experiments. The proposed ASSOA algorithm is compared with the basic Squirrel Search (SS) optimization algorithm, Grey Wolf Optimizer (GWO), and Genetic Algorithm (GA) for feature selection to validate its efficiency. The proposed (ASSOA + MLP) is also compared with other classifiers, based on (SS + MLP), (GWO + MLP), and (GA + MLP), in performance metrics. The proposed (ASSOA + MLP) algorithm achieved a classification mean accuracy of (99.26%). The ASSOA + MLP algorithm also achieved a classification mean accuracy of (99.7%) for a chest X-ray COVID-19 dataset tested from GitHub. The results and statistical tests demonstrate the high effectiveness of the proposed method in determining the infected cases.", "authors": ["<PERSON><PERSON><PERSON><PERSON> <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Rokaia <PERSON>", "<PERSON><PERSON>"], "year": 2021, "venue": "IEEE Access", "url": "https://www.semanticscholar.org/paper/9accd71d68cbe6c5817228f60093538595d18d33", "citation_count": 84, "source": "semantic_scholar", "paper_id": "9accd71d68cbe6c5817228f60093538595d18d33", "keywords": null, "doi": null}, {"title": "All You Need Is a Few Shifts: Designing Efficient Convolutional Neural Networks for Image Classification", "abstract": "Shift operation is an efficient alternative over depthwise separable convolution. However, it is still bottlenecked by its implementation manner, namely memory movement. To put this direction forward, a new and novel basic component named Sparse Shift Layer (SSL) is introduced in this paper to construct efficient convolutional neural networks. In this family of architectures, the basic block is only composed by 1x1 convolutional layers with only a few shift operations applied to the intermediate feature maps. To make this idea feasible, we introduce shift operation penalty during optimization and further propose a quantization-aware shift learning method to impose the learned displacement more friendly for inference. Extensive ablation studies indicate that only a few shift operations are sufficient to provide spatial information communication. Furthermore, to maximize the role of SSL, we redesign an improved network architecture to Fully Exploit the limited capacity of neural Network (FE-Net). Equipped with SSL, this network can achieve 75.0% top-1 accuracy on ImageNet with only 563M M-Adds. It surpasses other counterparts constructed by depthwise separable convolution and the networks searched by NAS in terms of accuracy and practical speed.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "year": 2019, "venue": "Computer Vision and Pattern Recognition", "url": "https://www.semanticscholar.org/paper/4b976c711eb211f67c5bb36c37175eec2f5bb738", "citation_count": 81, "source": "semantic_scholar", "paper_id": "4b976c711eb211f67c5bb36c37175eec2f5bb738", "keywords": null, "doi": null}, {"title": "Efficient Convolutional Neural Networks for Multi-Spectral Image Classification", "abstract": "While a great deal of research has been directed towards developing neural network architectures for RGB images, there is a relative dearth of research directed towards developing neural network architectures specifically for multi-spectral and hyper-spectral imagery. We have adapted recent developments in small efficient convolutional neural networks (CNNs), to create a small CNN architecture capable of being trained from scratch to classify 10 band multi-spectral images, using much fewer parameters than popular deep architectures, such as the ResNet or DenseNet architectures. We show that this network provides higher classification accuracy and greater sample efficiency than the same network using RGB images. Further, using a Bayesian version of our CNN architecture we show that a network that is capable of working with multi-spectral imagery significantly reduces the uncertainty associated with class predictions compared to using RGB images.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "year": 2019, "venue": "IEEE International Joint Conference on Neural Network", "url": "https://www.semanticscholar.org/paper/90f644185e02aac379910b118b79f3f1101220bd", "citation_count": 17, "source": "semantic_scholar", "paper_id": "90f644185e02aac379910b118b79f3f1101220bd", "keywords": null, "doi": null}, {"title": "Multi-Scale Dense Networks for Resource Efficient Image Classification", "abstract": null, "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2017, "venue": "International Conference on Learning Representations", "url": "https://www.semanticscholar.org/paper/125ccd810f43f1cba83c6681836d000f83d1886d", "citation_count": 769, "source": "semantic_scholar", "paper_id": "125ccd810f43f1cba83c6681836d000f83d1886d", "keywords": null, "doi": null}, {"title": "ANTNets: Mobile Convolutional Neural Networks for Resource Efficient Image Classification", "abstract": "Deep convolutional neural networks have achieved remarkable success in computer vision. However, deep neural networks require large computing resources to achieve high performance. Although depthwise separable convolution can be an efficient module to approximate a standard convolution, it often leads to reduced representational power of networks. In this paper, under budget constraints such as computational cost (MAdds) and the parameter count, we propose a novel basic architectural block, ANTBlock. It boosts the representational power by modeling, in a high dimensional space, interdependency of channels between a depthwise convolution layer and a projection layer in the ANTBlocks. Our experiments show that ANTNet built by a sequence of ANTBlocks, consistently outperforms state-of-the-art low-cost mobile convolutional neural networks across multiple datasets. On CIFAR100, our model achieves 75.7% top-1 accuracy, which is 1.5% higher than MobileNetV2 with 8.3% fewer parameters and 19.6% less computational cost. On ImageNet, our model achieves 72.8% top-1 accuracy, which is 0.8% improvement, with 157.7ms (20% faster) on iPhone 5s over MobileNetV2.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "year": 2019, "venue": "arXiv.org", "url": "https://www.semanticscholar.org/paper/5f6ec91e7de9a53942d3b168b3b856cb6ea0c5bf", "citation_count": 19, "source": "semantic_scholar", "paper_id": "5f6ec91e7de9a53942d3b168b3b856cb6ea0c5bf", "keywords": null, "doi": null}, {"title": "Quantum Methods for Neural Networks and Application to Medical Image Classification", "abstract": "Quantum machine learning techniques have been proposed as a way to potentially enhance performance in machine learning applications.\nIn this paper, we introduce two new quantum methods for neural networks. The first one is a quantum orthogonal neural network, which is based on a quantum pyramidal circuit as the building block for implementing orthogonal matrix multiplication. We provide an efficient way for training such orthogonal neural networks; novel algorithms are detailed for both classical and quantum hardware, where both are proven to scale asymptotically better than previously known training algorithms.\nThe second method is quantum-assisted neural networks, where a quantum computer is used to perform inner product estimation for inference and training of classical neural networks.\nWe then present extensive experiments applied to medical image classification tasks using current state of the art quantum hardware, where we compare different quantum methods with classical ones, on both real quantum hardware and simulators. Our results show that quantum and classical neural networks generates similar level of accuracy, supporting the promise that quantum methods can be useful in solving visual tasks, given the advent of better quantum hardware.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "year": 2022, "venue": "Quantum", "url": "https://www.semanticscholar.org/paper/056699324dcdc93e3bdc8e2d3d0f88d133cffd1f", "citation_count": 54, "source": "semantic_scholar", "paper_id": "056699324dcdc93e3bdc8e2d3d0f88d133cffd1f", "keywords": null, "doi": null}, {"title": "Semisupervised Neural Networks for Efficient Hyperspectral Image Classification", "abstract": null, "authors": ["<PERSON><PERSON>", "Gustau Camps-Valls", "<PERSON><PERSON>"], "year": 2010, "venue": "IEEE Transactions on Geoscience and Remote Sensing", "url": "https://www.semanticscholar.org/paper/58c239e6c2c38dea061310ed5d719c3221d4428f", "citation_count": 317, "source": "semantic_scholar", "paper_id": "58c239e6c2c38dea061310ed5d719c3221d4428f", "keywords": null, "doi": null}, {"title": "Exploring Highly Efficient Compact Neural Networks For Image Classification", "abstract": "Academic paper published in 2020 IEEE International Conference on Image Processing (ICIP). Full text available via DOI.", "authors": ["<PERSON><PERSON> Xi<PERSON>", "<PERSON>", "Sun-Yuan Kung"], "year": 2020, "venue": "2020 IEEE International Conference on Image Processing (ICIP)", "url": "https://doi.org/10.1109/icip40778.2020.9191334", "citation_count": 6, "source": "crossref", "paper_id": "10.1109/icip40778.2020.9191334", "keywords": null, "doi": "10.1109/icip40778.2020.9191334"}, {"title": "Creating Deep Convolutional Neural Networks for Image Classification", "abstract": "<jats:p>\n            This lesson provides a beginner-friendly introduction to convolutional neural networks (CNNs) for image classification. The tutorial provides a conceptual understanding of how neural networks work by using Google's Teachable Machine to train a model on paintings from the ArtUK database. This lesson also demonstrates how to use Javascript to embed the model in a live website.\n          </jats:p>", "authors": ["<PERSON><PERSON><PERSON>"], "year": 2023, "venue": "Programming Historian", "url": "https://doi.org/10.46430/phen0108", "citation_count": 2, "source": "crossref", "paper_id": "10.46430/phen0108", "keywords": null, "doi": "10.46430/phen0108"}, {"title": "Particle Swarm optimisation for Evolving Deep Neural Networks for Image Classification by Evolving and Stacking Transferable Blocks", "abstract": "Deep Convolutional Neural Networks (CNNs) have been widely used in image classification tasks, but the process of designing CNN architectures is very complex, so Neural Architecture Search (NAS), automatically searching for optimal CNN architectures, has attracted more and more research interests. However, the computational cost of NAS is often too high to be applied to real-life applications. In this paper, an efficient particle swarm optimisation method named EPSOCNN is proposed to evolve CNN architectures inspired by the idea of transfer learning. EPSOCNN successfully reduces the computation cost by minimising the search space to a single block and utilising a small subset of the training set to evaluate CNNs during the evolutionary process. Meanwhile, EPSOCNN also keeps very competitive classification accuracy by stacking the evolved block multiple times to fit the whole training dataset. The proposed EPSOCNN algorithm is evaluated on CIFAR-10 dataset and compared with 13 peer competitors including deep CNNs crafted by hand, learned by reinforcement learning methods and evolved by evolutionary computation approaches. It shows very promising results with regard to the classification accuracy, the number of parameters and the computational cost. Besides, the evolved transferable block from CIFAR-10 is transferred and evaluated on two other datasets — CIFAR-100 and SVHN. It shows promising results on both of the datasets, which demonstrate the transferability of the evolved block. All of the experiments have been performed multiple times and Student’s t-test is used to compare the proposed method with peer competitors from the statistical point of view.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "year": 2019, "venue": "IEEE Congress on Evolutionary Computation", "url": "https://www.semanticscholar.org/paper/e2d099cdbec575fad2710b9aa733200dd08e420c", "citation_count": 51, "source": "semantic_scholar", "paper_id": "e2d099cdbec575fad2710b9aa733200dd08e420c", "keywords": null, "doi": null}, {"title": "Provably efficient neural network representation for image\n  classification", "abstract": "The state-of-the-art approaches for image classification are based on neural\nnetworks. Mathematically, the task of classifying images is equivalent to\nfinding the function that maps an image to the label it is associated with. To\nrigorously establish the success of neural network methods, we should first\nprove that the function has an efficient neural network representation, and\nthen design provably efficient training algorithms to find such a\nrepresentation. Here, we achieve the first goal based on a set of assumptions\nabout the patterns in the images. The validity of these assumptions is very\nintuitive in many image classification problems, including but not limited to,\nrecognizing handwritten digits.", "authors": ["<PERSON><PERSON>"], "year": 2017, "venue": "arXiv", "url": "http://arxiv.org/abs/1711.04606v1", "citation_count": 0, "source": "arxiv", "paper_id": "1711.04606v1", "keywords": null, "doi": null}, {"title": "Classifier Ensemble for Efficient Uncertainty Calibration of Deep Neural Networks for Image Classification", "abstract": "Academic paper published in Proceedings of the 20th International Joint Conference on Computer Vision, Imaging and Computer Graphics Theory and Applications. Full text available via DOI.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "year": 2025, "venue": "Proceedings of the 20th International Joint Conference on Computer Vision, Imaging and Computer Graphics Theory and Applications", "url": "https://doi.org/10.5220/0013129000003912", "citation_count": 0, "source": "crossref", "paper_id": "10.5220/0013129000003912", "keywords": null, "doi": "10.5220/0013129000003912"}, {"title": "High Efficient Deep Feature Extraction and Classification of Spectral-Spatial Hyperspectral Image Using Cross Domain Convolutional Neural Networks", "abstract": "Recently, numerous remote sensing applications highly depend on the hyperspectral image (HSI). HSI classification, as a fundamental issue, has attracted increasing attention and become a hot topic in the remote sensing community. We implemented a regularized convolutional neural network (CNN), which adopted dropout and regularization strategies to address the overfitting problem of limited training samples. Although many kinds of the literature have confirmed that it is an effective way for HSI classification to integrate spectrum with spatial context, the scaling issue is not fully exploited. In this paper, we propose a high efficient deep feature extraction and the classification method for the spectral-spatial HSI, which can make full use of multiscale spatial feature obtained by guided filter. The proposed approach is the first attempt to lean a CNN for spectral and multiscale spatial features. Compared to its counterparts, experimental results show that the proposed method can achieve 3% improvement in accuracy, according to various datasets such as Indian Pines, Pavia University, and Salinas.", "authors": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2019, "venue": "IEEE Journal of Selected Topics in Applied Earth Observations and Remote Sensing", "url": "https://www.semanticscholar.org/paper/2f875a3e2c9933d6f09c5434af73f967d11a6e19", "citation_count": 33, "source": "semantic_scholar", "paper_id": "2f875a3e2c9933d6f09c5434af73f967d11a6e19", "keywords": null, "doi": null}, {"title": "Efficient Multiple Instance Convolutional Neural Networks for Gigapixel Resolution Image Classification", "abstract": null, "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "year": 2015, "venue": "arXiv.org", "url": "https://www.semanticscholar.org/paper/c8f4a7dc9e98ea0eb9313df3904f47c5357335a5", "citation_count": 47, "source": "semantic_scholar", "paper_id": "c8f4a7dc9e98ea0eb9313df3904f47c5357335a5", "keywords": null, "doi": null}, {"title": "SpectralMamba: Efficient Mamba for Hyperspectral Image Classification", "abstract": "Recurrent neural networks and Transformers have recently dominated most applications in hyperspectral (HS) imaging, owing to their capability to capture long-range dependencies from spectrum sequences. However, despite the success of these sequential architectures, the non-ignorable inefficiency caused by either difficulty in parallelization or computationally prohibitive attention still hinders their practicality, especially for large-scale observation in remote sensing scenarios. To address this issue, we herein propose SpectralMamba -- a novel state space model incorporated efficient deep learning framework for HS image classification. SpectralMamba features the simplified but adequate modeling of HS data dynamics at two levels. First, in spatial-spectral space, a dynamical mask is learned by efficient convolutions to simultaneously encode spatial regularity and spectral peculiarity, thus attenuating the spectral variability and confusion in discriminative representation learning. Second, the merged spectrum can then be efficiently operated in the hidden state space with all parameters learned input-dependent, yielding selectively focused responses without reliance on redundant attention or imparallelizable recurrence. To explore the room for further computational downsizing, a piece-wise scanning mechanism is employed in-between, transferring approximately continuous spectrum into sequences with squeezed length while maintaining short- and long-term contextual profiles among hundreds of bands. Through extensive experiments on four benchmark HS datasets acquired by satellite-, aircraft-, and UAV-borne imagers, SpectralMamba surprisingly creates promising win-wins from both performance and efficiency perspectives.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2024, "venue": "arXiv.org", "url": "https://www.semanticscholar.org/paper/f442d39e6cba53bc67c53e07759c0a90deda8ac9", "citation_count": 47, "source": "semantic_scholar", "paper_id": "f442d39e6cba53bc67c53e07759c0a90deda8ac9", "keywords": null, "doi": null}, {"title": "Hybrid technique for colour image classification and efficient retrieval based on fuzzy logic and neural networks", "abstract": "Academic paper published in The 2012 International Joint Conference on Neural Networks (IJCNN). Full text available via DOI.", "authors": ["<PERSON><PERSON>", "Siddhivina<PERSON><PERSON>"], "year": 2012, "venue": "The 2012 International Joint Conference on Neural Networks (IJCNN)", "url": "https://doi.org/10.1109/ijcnn.2012.6252587", "citation_count": 4, "source": "crossref", "paper_id": "10.1109/ijcnn.2012.6252587", "keywords": null, "doi": "10.1109/ijcnn.2012.6252587"}, {"title": "Deep Convolutional Spiking Neural Networks for Image Classification", "abstract": "<p>Spiking neural networks are biologically plausible counterparts of artificial neural networks. Artificial neural networks are usually trained with stochastic gradient descent (SGD) and spiking neural networks are trained with bioinspired spike timing dependent plasticity (STDP). Spiking networks could potentially help in reducing power usage owing to their binary activations. In this work, we use unsupervised STDP in the feature extraction layers of a neural network with instantaneous neurons to extract meaningful features. The extracted binary feature vectors are then classified using classification layers containing neurons with binary activations. Gradient descent (backpropagation) is used only on the output layer to perform training for classification. Surrogate gradients are proposed to perform backpropagation with binary gradients. The accuracies obtained for MNIST and the balanced EMNIST data set compare favorably with other approaches. The effect of the stochastic gradient descent (SGD) approximations on learning capabilities of our network are also explored. We also studied catastrophic forgetting and its effect on spiking neural networks (SNNs). For the experiments regarding catastrophic forgetting, in the classification sections of the network we use a modified synaptic intelligence that we refer to as cost per synapse metric as a regularizer to immunize the network against catastrophic forgetting in a Single-Incremental-Task scenario (SIT). In catastrophic forgetting experiments, we use MNIST and EMNIST handwritten digits datasets that were divided into five and ten incremental subtasks respectively. We also examine behavior of the spiking neural network and empirically study the effect of various hyperparameters on its learning capabilities using the software tool SPYKEFLOW that we developed. We employ MNIST, EMNIST and NMNIST data sets to produce our results.</p>", "authors": ["<PERSON><PERSON> Vaila"], "year": null, "venue": "Boise State University", "url": "https://doi.org/10.18122/td.1782.boisestate", "citation_count": 5, "source": "crossref", "paper_id": "10.18122/td.1782.boisestate", "keywords": null, "doi": "10.18122/td.1782.boisestate"}], "workflows": {"paper_1": {"title": "Conversion of Continuous-Valued Deep Networks to Efficient Event-Driven Networks for Image Classification", "datasets": ["MNIST", "CIFAR-10", "ImageNet"], "network_architectures": ["VGG-16", "Inception-v3", "LeNet", "BinaryNet", "Spiking Neural Network (SNN)", "Convolutional Neural Network (CNN)"], "platforms_tools": ["neuromorphic spiking neuron chips"], "research_methods": ["conversion of continuous-valued deep networks to spiking equivalents", "event-driven computation", "sparse activation"], "evaluation_metrics": ["classification error rate", "number of operations", "accuracy"], "brain_inspiration": ["spiking neural networks (SNNs)", "event-driven computation", "sparse activation"], "ai_techniques": ["deep learning", "spiking neural networks (SNNs)", "Convolutional Neural Networks (CNNs)", "max-pooling", "softmax", "batch-normalization", "Inception-modules"]}, "paper_2": {"title": "Evolving Deep Convolutional Neural Networks for Image Classification", "datasets": [], "network_architectures": ["deep convolutional neural network"], "platforms_tools": [], "research_methods": ["genetic algorithms", "variable-length gene encoding strategy", "heuristic search", "backward gradient-based optimization"], "evaluation_metrics": ["classification error rate", "number of parameters (weights)"], "brain_inspiration": [], "ai_techniques": ["genetic algorithms", "deep convolutional neural network", "heuristic search", "gradient-based optimization"]}, "paper_3": {"title": "Efficient convolutional neural networks on Raspberry Pi for image classification", "datasets": ["CIFAR-10", "SVHN"], "network_architectures": ["TripleNet", "GhostNet", "MobileNet", "ThreshNet", "EfficientNet", "HarDNet"], "platforms_tools": ["Raspberry Pi"], "research_methods": ["block connections", "network model compression", "network acceleration"], "evaluation_metrics": ["inference time per image", "accuracy"], "brain_inspiration": [], "ai_techniques": ["convolutional neural network (CNN)", "deep learning", "image classification"]}, "paper_4": {"title": "Advanced Meta-Heuristics, Convolutional Neural Networks, and Feature Selectors for Efficient COVID-19 X-Ray Chest Image Classification", "datasets": ["Kaggle chest X-ray images (Pneumonia)", "chest X-ray COVID-19 dataset from GitHub"], "network_architectures": ["ResNet-50", "Multi-layer Perceptron (MLP) Neural Network"], "platforms_tools": [], "research_methods": ["Advanced Squirrel Search Optimization Algorithm (ASSOA)", "image augmentation", "dropout processes", "feature selection", "Squirrel Search (SS) optimization algorithm", "Grey Wolf Optimizer (GWO)", "Genetic Algorithm (GA)"], "evaluation_metrics": ["classification mean accuracy"], "brain_inspiration": [], "ai_techniques": ["Deep transfer learning", "Convolutional Neural Network (CNN)", "Multi-layer Perceptron (MLP) Neural Network", "Advanced Squirrel Search Optimization Algorithm (ASSOA)", "Squirrel Search (SS) optimization algorithm", "Grey Wolf Optimizer (GWO)", "Genetic Algorithm (GA)"]}, "paper_5": {"title": "All You Need Is a Few Shifts: Designing Efficient Convolutional Neural Networks for Image Classification", "datasets": ["ImageNet"], "network_architectures": ["Sparse Shift Layer (SSL)", "FE-Net"], "platforms_tools": [], "research_methods": ["shift operation penalty", "quantization-aware shift learning method"], "evaluation_metrics": ["top-1 accuracy", "M-Adds"], "brain_inspiration": [], "ai_techniques": ["depthwise separable convolution", "shift operation", "neural architecture search (NAS)"]}}, "research_topic": "Efficient Neural Networks for Image Classification", "timestamp": "2025-07-24 11:12:21", "total_papers": 20}