"""
LaTeX格式优化专家模块 - 增强版
专门负责论文的LaTeX格式优化，确保符合顶级会议标准
"""

import re
import os
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class LaTeXFormatIssue:
    """LaTeX格式问题"""
    issue_type: str  # 问题类型
    line_number: int  # 行号
    description: str  # 问题描述
    severity: str  # 严重程度: critical, warning, suggestion
    fix_suggestion: str  # 修复建议

@dataclass
class LaTeXOptimizationResult:
    """LaTeX优化结果"""
    original_content: str
    optimized_content: str
    issues_found: List[LaTeXFormatIssue]
    issues_fixed: List[LaTeXFormatIssue]
    venue_compliance: Dict[str, bool]  # 会议格式合规性
    quality_score: float  # 格式质量分数

class LaTeXFormatExpert:
    """LaTeX格式优化专家 - 专业级别会议格式优化"""
    
    def __init__(self, hybrid_client=None):
        self.hybrid_client = hybrid_client
        self.venue_templates = self._load_venue_templates()
        self.format_rules = self._load_format_rules()
        self.citation_patterns = self._load_citation_patterns()
        
    def _load_venue_templates(self) -> Dict[str, Dict]:
        """加载会议模板配置 - 扩展版"""
        return {
            'ICML': {
                'documentclass': r'\\documentclass[accepted]{icml2024}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'subfigure'],
                'title_format': r'\\icmltitle{{{title}}}',
                'author_format': r'\\icmlauthor{{{author}}}{{{affiliation}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': r'\\icmlkeywords{{{keywords}}}',
                'section_numbering': True,
                'figure_placement': 'htbp',
                'table_style': 'booktabs',
                'algorithm_package': 'algorithm2e',
                'math_packages': ['amsmath', 'amssymb', 'amsthm'],
                'references_style': 'icml2024',
                'page_limit': 8,
                'appendix_allowed': True
            },
            'NeurIPS': {
                'documentclass': r'\\documentclass{neurips_2024}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'natbib'],
                'title_format': r'\\title{{{title}}}',
                'author_format': r'\\author{{{author}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': None,
                'section_numbering': True,
                'figure_placement': 'htbp',
                'table_style': 'booktabs',
                'algorithm_package': 'algorithm',
                'math_packages': ['amsmath', 'amssymb', 'amsthm'],
                'references_style': 'neurips_2024',
                'page_limit': 9,
                'appendix_allowed': True
            },
            'ICLR': {
                'documentclass': r'\\documentclass{iclr2024_conference}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx'],
                'title_format': r'\\title{{{title}}}',
                'author_format': r'\\author{{{author}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': r'\\keywords{{{keywords}}}',
                'section_numbering': True,
                'figure_placement': 'htbp',
                'table_style': 'booktabs',
                'algorithm_package': 'algorithm2e',
                'math_packages': ['amsmath', 'amssymb', 'amsthm'],
                'references_style': 'iclr2024',
                'page_limit': 9,
                'appendix_allowed': True
            },
            'AAAI': {
                'documentclass': r'\\documentclass[letterpaper]{aaai24}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'times'],
                'title_format': r'\\title{{{title}}}',
                'author_format': r'\\author{{{author}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': None,
                'section_numbering': True,
                'figure_placement': 'htbp',
                'table_style': 'booktabs',
                'algorithm_package': 'algorithm2e',
                'math_packages': ['amsmath', 'amssymb', 'amsthm'],
                'references_style': 'aaai24',
                'page_limit': 7,
                'appendix_allowed': False
            },
            'IEEE': {
                'documentclass': r'\\documentclass[conference]{IEEEtran}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'cite'],
                'title_format': r'\\title{{{title}}}',
                'author_format': r'\\author{{{author}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': r'\\begin{{IEEEkeywords}}{keywords}\\end{{IEEEkeywords}}',
                'section_numbering': True,
                'figure_placement': 'htbp',
                'table_style': 'booktabs',
                'algorithm_package': 'algorithmic',
                'math_packages': ['amsmath', 'amssymb', 'amsthm'],
                'references_style': 'IEEEtran',
                'page_limit': 6,
                'appendix_allowed': True
            }
        }
    
    def _load_citation_patterns(self) -> Dict[str, str]:
        """加载引用模式配置"""
        return {
            'ICML': r'\\cite\{[^}]+\}',
            'NeurIPS': r'\\citep?\{[^}]+\}',
            'ICLR': r'\\cite\{[^}]+\}',
            'AAAI': r'\\cite\{[^}]+\}',
            'IEEE': r'\\cite\{[^}]+\}'
        }
        
    def _load_format_rules(self) -> Dict[str, Dict]:
        """加载格式规则配置"""
        return {
            'critical_issues': {
                'missing_documentclass': r'Missing \\documentclass',
                'missing_begin_document': r'Missing \\begin{document}',
                'missing_end_document': r'Missing \\end{document}',
                'unmatched_braces': r'Unmatched braces',
                'undefined_commands': r'Undefined commands',
                'missing_packages': r'Missing required packages'
            },
            'warning_issues': {
                'long_lines': r'Lines longer than 80 characters',
                'missing_labels': r'Missing labels for figures/tables',
                'inconsistent_spacing': r'Inconsistent spacing',
                'deprecated_commands': r'Deprecated LaTeX commands',
                'improper_citations': r'Improper citation format'
            },
            'style_suggestions': {
                'section_formatting': r'Section formatting improvements',
                'figure_positioning': r'Figure positioning optimization',
                'table_formatting': r'Table formatting improvements',
                'equation_formatting': r'Equation formatting suggestions',
                'reference_formatting': r'Reference formatting improvements'
            }
        }
    
    def optimize_latex_format(self, content: str, venue: str = 'ICML') -> LaTeXOptimizationResult:
        """优化LaTeX格式 - 专业级别"""
        print(f"🔧 开始专业级LaTeX格式优化 (目标会议: {venue})")
        
        # 1. 检测格式问题
        issues_found = self._detect_format_issues(content, venue)
        print(f"   📊 检测到 {len(issues_found)} 个格式问题")
        
        # 2. 自动修复问题
        optimized_content, issues_fixed = self._auto_fix_issues(content, issues_found, venue)
        print(f"   🔧 自动修复了 {len(issues_fixed)} 个问题")
        
        # 3. 应用会议模板
        optimized_content = self._apply_venue_template(optimized_content, venue)
        print(f"   🎯 应用了 {venue} 会议模板")
        
        # 4. 检查会议合规性
        venue_compliance = self._check_venue_compliance(optimized_content, venue)
        print(f"   ✅ 会议合规性检查完成")
        
        # 5. 计算质量分数
        quality_score = self._calculate_quality_score(optimized_content, venue_compliance)
        print(f"   📈 LaTeX格式质量分数: {quality_score:.1f}/10")
        
        return LaTeXOptimizationResult(
            original_content=content,
            optimized_content=optimized_content,
            issues_found=issues_found,
            issues_fixed=issues_fixed,
            venue_compliance=venue_compliance,
            quality_score=quality_score
        )
    
    def _detect_format_issues(self, content: str, venue: str) -> List[LaTeXFormatIssue]:
        """检测格式问题 - 增强版"""
        issues = []
        lines = content.split('\n')
        
        # 检查文档类
        if not re.search(r'\\documentclass', content):
            issues.append(LaTeXFormatIssue(
                issue_type='missing_documentclass',
                line_number=1,
                description='缺少\\documentclass声明',
                severity='critical',
                fix_suggestion=f'添加 {self.venue_templates[venue]["documentclass"]}'
            ))
        
        # 检查必需包
        required_packages = self.venue_templates[venue]['required_packages']
        for pkg in required_packages:
            if not re.search(r'\\usepackage.*' + re.escape(pkg), content):
                issues.append(LaTeXFormatIssue(
                    issue_type='missing_package',
                    line_number=0,
                    description=f'缺少必需包: {pkg}',
                    severity='warning',
                    fix_suggestion=f'添加 \\\\usepackage{{{pkg}}}'
                ))
        
        # 检查标题格式
        title_format = self.venue_templates[venue]['title_format']
        if '\\\\icmltitle' in title_format and not re.search(r'\\\\icmltitle', content):
            issues.append(LaTeXFormatIssue(
                issue_type='wrong_title_format',
                line_number=0,
                description='标题格式不符合会议要求',
                severity='warning',
                fix_suggestion=f'使用 {title_format}'
            ))
        
        # 检查摘要环境
        if not re.search(r'\\begin\{abstract\}', content):
            issues.append(LaTeXFormatIssue(
                issue_type='missing_abstract',
                line_number=0,
                description='缺少摘要环境',
                severity='critical',
                fix_suggestion='添加 \\begin{abstract}...\\end{abstract}'
            ))
        
        # 检查参考文献
        if not re.search(r'\\bibliography|\\begin\{thebibliography\}', content):
            issues.append(LaTeXFormatIssue(
                issue_type='missing_bibliography',
                line_number=0,
                description='缺少参考文献',
                severity='warning',
                fix_suggestion='添加 \\bibliography{references} 或 \\begin{thebibliography}'
            ))
        
        # 检查图表环境
        figure_count = len(re.findall(r'\\begin\{figure\}', content))
        table_count = len(re.findall(r'\\begin\{table\}', content))
        
        if figure_count > 0:
            # 检查图表位置参数
            if not re.search(r'\\begin\{figure\}\[.*\]', content):
                issues.append(LaTeXFormatIssue(
                    issue_type='missing_figure_placement',
                    line_number=0,
                    description='图表缺少位置参数',
                    severity='suggestion',
                    fix_suggestion='添加位置参数如 [htbp]'
                ))
        
        # 检查算法环境
        if re.search(r'\\begin\\{algorithm\\}', content):
            algorithm_pkg = self.venue_templates[venue]['algorithm_package']
            if not re.search(r'\\usepackage.*' + re.escape(algorithm_pkg), content):
                issues.append(LaTeXFormatIssue(
                    issue_type='missing_algorithm_package',
                    line_number=0,
                    description=f'缺少算法包: {algorithm_pkg}',
                    severity='warning',
                    fix_suggestion=f'添加 \\\\usepackage{{{algorithm_pkg}}}'
                ))
        
        return issues
    
    def _auto_fix_issues(self, content: str, issues: List[LaTeXFormatIssue], venue: str) -> Tuple[str, List[LaTeXFormatIssue]]:
        """自动修复问题 - 增强版"""
        fixed_content = content
        fixed_issues = []
        
        for issue in issues:
            if issue.issue_type == 'missing_documentclass':
                # 在开头添加文档类
                venue_template = self.venue_templates[venue]
                fixed_content = venue_template['documentclass'] + '\n' + fixed_content
                fixed_issues.append(issue)
            
            elif issue.issue_type == 'missing_package':
                # 添加缺少的包
                pkg_name = issue.description.split(': ')[1]
                usepackage_line = f'\\usepackage{{{pkg_name}}}'
                
                # 找到合适的位置插入
                if '\\usepackage' in fixed_content:
                    # 在最后一个usepackage后插入
                    fixed_content = re.sub(
                        r'(\\usepackage\{[^}]+\})',
                        r'\\1\\n' + usepackage_line,
                        fixed_content,
                        count=1
                    )
                else:
                    # 在documentclass后插入
                    fixed_content = re.sub(
                        r'(\\documentclass\{[^}]+\})',
                        r'\\1\\n' + usepackage_line,
                        fixed_content,
                        count=1
                    )
                fixed_issues.append(issue)
            
            elif issue.issue_type == 'missing_abstract':
                # 添加摘要环境
                if '\\begin{document}' in fixed_content:
                    abstract_template = """
\\begin{abstract}
This paper presents...
\\end{abstract}
"""
                    fixed_content = fixed_content.replace(
                        '\\begin{document}',
                        '\\begin{document}' + abstract_template
                    )
                    fixed_issues.append(issue)
            
            elif issue.issue_type == 'missing_bibliography':
                # 添加参考文献
                if '\\end{document}' in fixed_content:
                    bib_template = """
\\bibliographystyle{""" + self.venue_templates[venue]['references_style'] + """}
\\bibliography{references}
"""
                    fixed_content = fixed_content.replace(
                        '\\end{document}',
                        bib_template + '\\end{document}'
                    )
                    fixed_issues.append(issue)
        
        return fixed_content, fixed_issues
    
    def _apply_venue_template(self, content: str, venue: str) -> str:
        """应用会议模板 - 专业版"""
        template = self.venue_templates[venue]
        optimized_content = content
        
        # 1. 确保正确的文档类
        if template['documentclass'] not in content:
            optimized_content = re.sub(
                r'\\documentclass\{[^}]+\}',
                template['documentclass'],
                optimized_content
            )
        
        # 2. 添加必需的包
        for pkg in template['required_packages']:
            if f'\\usepackage{{{pkg}}}' not in optimized_content:
                # 在第一个usepackage之前插入
                if '\\usepackage' in optimized_content:
                    optimized_content = re.sub(
                        r'(\\documentclass\{[^}]+\})',
                        r'\\1\\n\\usepackage{' + pkg + '}',
                        optimized_content,
                        count=1
                    )
        
        # 3. 优化标题格式
        if venue == 'ICML' and '\\icmltitle' not in optimized_content:
            optimized_content = re.sub(
                r'\\title\{([^}]+)\}',
                r'\\icmltitle{\1}',
                optimized_content
            )
        
        # 4. 优化作者格式
        if venue == 'ICML' and '\\icmlauthor' not in optimized_content:
            optimized_content = re.sub(
                r'\\author\{([^}]+)\}',
                r'\\icmlauthor{\1}{Affiliation}',
                optimized_content
            )
        
        # 5. 添加关键词（如果支持）
        if template['keywords_cmd'] and '\\keywords' not in optimized_content:
            if '\\end{abstract}' in optimized_content:
                keywords_cmd = template['keywords_cmd'].replace('{keywords}', '{brain-inspired intelligence, neural networks, machine learning}')
                optimized_content = optimized_content.replace(
                    '\\end{abstract}',
                    '\\end{abstract}\n' + keywords_cmd
                )
        
        return optimized_content
    
    def _check_venue_compliance(self, content: str, venue: str) -> Dict[str, bool]:
        """检查会议合规性 - 详细版"""
        template = self.venue_templates[venue]
        compliance = {}
        
        # 检查文档类
        compliance['documentclass'] = template['documentclass'] in content
        
        # 检查必需包
        required_packages = template['required_packages']
        missing_packages = []
        for pkg in required_packages:
            if not re.search(r'\\usepackage.*' + re.escape(pkg), content):
                missing_packages.append(pkg)
        compliance['required_packages'] = len(missing_packages) == 0
        
        # 检查标题格式
        title_format = template['title_format']
        if '\\icmltitle' in title_format:
            compliance['title_format'] = '\\icmltitle' in content
        else:
            compliance['title_format'] = '\\title' in content
        
        # 检查作者格式
        author_format = template['author_format']
        if '\\icmlauthor' in author_format:
            compliance['author_format'] = '\\icmlauthor' in content
        else:
            compliance['author_format'] = '\\author' in content
        
        # 检查摘要
        compliance['abstract'] = '\\begin{abstract}' in content
        
        # 检查参考文献
        compliance['bibliography'] = bool(re.search(r'\\bibliography|\\begin\{thebibliography\}', content))
        
        # 检查数学包
        math_packages = template.get('math_packages', [])
        math_compliance = True
        for pkg in math_packages:
            if not re.search(r'\\usepackage.*' + re.escape(pkg), content):
                math_compliance = False
                break
        compliance['math_packages'] = math_compliance
        
        # 检查图表包
        if 'graphicx' in required_packages:
            compliance['graphics_support'] = '\\usepackage{graphicx}' in content or '\\usepackage{graphics}' in content
        
        return compliance
    
    def _calculate_quality_score(self, content: str, venue_compliance: Dict[str, bool]) -> float:
        """计算质量分数 - 详细版"""
        # 基础分数
        base_score = 5.0
        
        # 会议合规性加分
        compliance_score = sum(venue_compliance.values()) / len(venue_compliance) * 3.0
        
        # 内容质量加分
        content_score = 0.0
        
        # 检查章节结构
        sections = len(re.findall(r'\\section\{', content))
        if sections >= 4:  # 引言、方法、实验、结论
            content_score += 1.0
        
        # 检查图表数量
        figures = len(re.findall(r'\\begin\{figure\}', content))
        tables = len(re.findall(r'\\begin\{table\}', content))
        if figures >= 2 and tables >= 1:
            content_score += 0.5
        
        # 检查引用数量
        citations = len(re.findall(r'\\cite\{[^}]+\}', content))
        if citations >= 10:
            content_score += 0.5
        
        # 检查算法环境
        if '\\begin{algorithm}' in content:
            content_score += 0.5
        
        # 检查数学公式
        equations = len(re.findall(r'\\begin\{equation\}', content))
        if equations >= 2:
            content_score += 0.5
        
        total_score = min(10.0, base_score + compliance_score + content_score)
        return total_score
    
    # 兼容性别名方法
    def detect_format_issues(self, content: str, venue: str = 'ICML') -> List[LaTeXFormatIssue]:
        """检测格式问题 - 兼容性别名"""
        return self._detect_format_issues(content, venue)
    
    def check_venue_compliance(self, content: str, venue: str = 'ICML') -> Dict[str, bool]:
        """检查会议合规性 - 兼容性别名"""
        return self._check_venue_compliance(content, venue)
    
    def fix_format_issues(self, content: str, venue: str = 'ICML') -> str:
        """修复格式问题 - 兼容性别名"""
        issues = self._detect_format_issues(content, venue)
        fixed_content, _ = self._auto_fix_issues(content, issues, venue)
        return self._apply_venue_template(fixed_content, venue)
    
    def generate_venue_template(self, venue: str, title: str, author: str, affiliation: str = "") -> str:
        """生成会议模板 - 新功能"""
        template = self.venue_templates.get(venue, self.venue_templates['ICML'])
        
        # 基础模板
        latex_template = f"""
{template['documentclass']}

% 必需包
"""
        
        # 添加必需包
        for pkg in template['required_packages']:
            latex_template += f"\\usepackage{{{pkg}}}\n"
        
        # 添加数学包
        for pkg in template.get('math_packages', []):
            latex_template += f"\\usepackage{{{pkg}}}\n"
        
        latex_template += f"""
% 标题和作者
{template['title_format'].format(title=title)}
{template['author_format'].format(author=author, affiliation=affiliation)}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
This paper presents a novel approach to brain-inspired intelligence...
\\end{{abstract}}

"""
        
        # 添加关键词
        if template['keywords_cmd']:
            keywords = template['keywords_cmd'].replace('{keywords}', '{brain-inspired intelligence, neural networks, machine learning}')
            latex_template += f"{keywords}\n\n"
        
        latex_template += f"""
\\section{{Introduction}}
Introduction content...

\\section{{Related Work}}
Related work content...

\\section{{Methodology}}
Methodology content...

\\section{{Experiments}}
Experimental results...

\\section{{Conclusion}}
Conclusion content...

\\bibliographystyle{{{template['references_style']}}}
\\bibliography{{references}}

\\end{{document}}
"""
        
        return latex_template.strip()
    
    def validate_latex_compilation(self, content: str) -> Dict[str, any]:
        """验证LaTeX编译 - 新功能"""
        validation_result = {
            'can_compile': True,
            'errors': [],
            'warnings': [],
            'suggestions': []
        }
        
        # 检查基本结构
        if '\\documentclass' not in content:
            validation_result['can_compile'] = False
            validation_result['errors'].append('Missing \\documentclass declaration')
        
        if '\\begin{document}' not in content:
            validation_result['can_compile'] = False
            validation_result['errors'].append('Missing \\begin{document}')
        
        if '\\end{document}' not in content:
            validation_result['can_compile'] = False
            validation_result['errors'].append('Missing \\end{document}')
        
        # 检查匹配的括号
        open_braces = content.count('{')
        close_braces = content.count('}')
        if open_braces != close_braces:
            validation_result['warnings'].append(f'Unmatched braces: {open_braces} open, {close_braces} close')
        
        # 检查环境匹配
        begin_count = len(re.findall(r'\\begin\{([^}]+)\}', content))
        end_count = len(re.findall(r'\\end\{([^}]+)\}', content))
        if begin_count != end_count:
            validation_result['warnings'].append(f'Unmatched environments: {begin_count} begin, {end_count} end')
        
        # 检查引用
        cite_count = len(re.findall(r'\\cite\{[^}]+\}', content))
        if cite_count > 0 and not re.search(r'\\bibliography|\\begin\{thebibliography\}', content):
            validation_result['warnings'].append('Citations found but no bibliography')
        
        # 建议
        if not re.search(r'\\label\{[^}]+\}', content):
            validation_result['suggestions'].append('Consider adding labels to figures, tables, and equations')
        
        return validation_result
