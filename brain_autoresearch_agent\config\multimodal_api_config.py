"""
多模态API配置 - 支持DeepSeek和Qwen视觉模型
"""

import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

@dataclass
class APIConfig:
    """API配置类"""
    provider: str
    api_key: str
    base_url: str
    models: List[str]
    capabilities: List[str]
    
class MultimodalAPIManager:
    """多模态API管理器"""
    
    def __init__(self):
        self.configs = {}
        self._setup_default_configs()
    
    def _setup_default_configs(self):
        """设置默认API配置"""
        
        # DeepSeek配置 - 文本生成和推理
        self.configs['deepseek'] = APIConfig(
            provider="deepseek",
            api_key="***********************************",
            base_url="https://api.deepseek.com",
            models=[
                "deepseek-chat",
                "deepseek-reasoner"
            ],
            capabilities=[
                "text_generation",
                "reasoning",
                "code_generation",
                "mathematical_analysis"
            ]
        )
        
        # Qwen配置 - 视觉模型
        self.configs['qwen'] = APIConfig(
            provider="qwen",
            api_key="sk-f8559ea97bad4d638416d20db63bc643",
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            models=[
                "qwen-plus",
                "qwen-max", 
                "qwen-vl-plus",
                "qwen-vl-max",
                "qvq-max-latest"
            ],
            capabilities=[
                "vision_analysis",
                "layout_optimization",
                "visual_reasoning",
                "paper_formatting"
            ]
        )
    
    def get_config(self, provider: str) -> Optional[APIConfig]:
        """获取指定provider的配置"""
        return self.configs.get(provider)
    
    def get_vision_model_config(self) -> APIConfig:
        """获取视觉模型配置"""
        return self.configs['qwen']
    
    def get_text_model_config(self) -> APIConfig:
        """获取文本生成模型配置"""
        return self.configs['deepseek']
    
    def setup_environment_variables(self):
        """设置环境变量"""
        for provider, config in self.configs.items():
            # 设置API密钥
            if provider == 'qwen':
                # Qwen使用DASHSCOPE_API_KEY环境变量
                os.environ["DASHSCOPE_API_KEY"] = config.api_key
                os.environ[f"{provider.upper()}_BASE_URL"] = config.base_url
            else:
                os.environ[f"{provider.upper()}_API_KEY"] = config.api_key
                os.environ[f"{provider.upper()}_BASE_URL"] = config.base_url
            
            print(f"✅ {provider.capitalize()} API配置完成")
            print(f"   🔑 API密钥: {config.api_key[:8]}...{config.api_key[-4:]}")
            print(f"   🌐 基础URL: {config.base_url}")
            print(f"   🤖 支持模型: {', '.join(config.models)}")
            print(f"   🎯 功能: {', '.join(config.capabilities)}")
            print()
    
    def get_optimal_model_for_task(self, task_type: str) -> Dict[str, Any]:
        """根据任务类型获取最优模型"""
        
        task_model_mapping = {
            # 文本生成任务
            "text_generation": {
                "provider": "deepseek",
                "model": "deepseek-chat",
                "config": self.configs['deepseek']
            },
            "paper_writing": {
                "provider": "deepseek", 
                "model": "deepseek-chat",
                "config": self.configs['deepseek']
            },
            "reasoning": {
                "provider": "deepseek",
                "model": "deepseek-reasoner", 
                "config": self.configs['deepseek']
            },
            "code_generation": {
                "provider": "deepseek",
                "model": "deepseek-chat",
                "config": self.configs['deepseek']
            },
            
            # 视觉任务
            "layout_analysis": {
                "provider": "qwen",
                "model": "qwen-vl-max",
                "config": self.configs['qwen']
            },
            "visual_optimization": {
                "provider": "qwen",
                "model": "qwen-vl-max",
                "config": self.configs['qwen']
            },
            "visual_reasoning": {
                "provider": "qwen",
                "model": "qvq-max-latest",
                "config": self.configs['qwen']
            },
            "paper_layout_optimization": {
                "provider": "qwen",
                "model": "qvq-max-latest",  # 视觉推理用于复杂布局优化
                "config": self.configs['qwen']
            }
        }
        
        return task_model_mapping.get(task_type, task_model_mapping["text_generation"])
    
    def validate_apis(self) -> Dict[str, bool]:
        """验证所有API连接"""
        results = {}
        
        for provider, config in self.configs.items():
            try:
                if provider == 'deepseek':
                    results[provider] = self._test_deepseek_connection(config)
                elif provider == 'qwen':
                    results[provider] = self._test_qwen_connection(config)
                else:
                    results[provider] = False
            except Exception as e:
                print(f"❌ {provider} API验证失败: {e}")
                results[provider] = False
        
        return results
    
    def _test_deepseek_connection(self, config: APIConfig) -> bool:
        """测试DeepSeek连接"""
        try:
            from openai import OpenAI
            
            client = OpenAI(
                api_key=config.api_key,
                base_url=config.base_url
            )
            
            response = client.chat.completions.create(
                model="deepseek-chat",
                messages=[{"role": "user", "content": "Hello, please respond with 'API test successful'"}],
                max_tokens=50,
                temperature=0.1
            )
            
            result = response.choices[0].message.content
            print(f"✅ DeepSeek连接测试成功: {result[:50]}...")
            return True
            
        except Exception as e:
            print(f"❌ DeepSeek连接测试失败: {e}")
            return False
    
    def _test_qwen_connection(self, config: APIConfig) -> bool:
        """测试Qwen连接"""
        try:
            from openai import OpenAI
            
            # 使用正确的Qwen API调用方式
            client = OpenAI(
                api_key=config.api_key,  # 直接使用config中的api_key
                base_url=config.base_url  # 使用兼容模式的base_url
            )
            
            # 使用文本模型测试连接
            response = client.chat.completions.create(
                model="qwen-plus",  # 使用基础文本模型测试连接
                messages=[{
                    "role": "user", 
                    "content": [{"type": "text", "text": "请回复'API测试成功'"}]
                }],
                max_tokens=50
            )
            
            result = response.choices[0].message.content
            print(f"✅ Qwen连接测试成功: {result}")
            return True
                
        except Exception as e:
            print(f"❌ Qwen连接测试失败: {e}")
            return False

# 全局API管理器实例
api_manager = MultimodalAPIManager()

def get_api_manager() -> MultimodalAPIManager:
    """获取API管理器实例"""
    return api_manager

def setup_all_apis():
    """设置所有API"""
    manager = get_api_manager()
    manager.setup_environment_variables()
    return manager

def validate_all_apis() -> Dict[str, bool]:
    """验证所有API"""
    manager = get_api_manager()
    return manager.validate_apis()

if __name__ == "__main__":
    print("🚀 多模态API配置测试")
    print("=" * 50)
    
    # 设置API
    manager = setup_all_apis()
    
    # 验证API
    print("🧪 验证API连接...")
    results = validate_all_apis()
    
    print(f"\n📊 API验证结果:")
    for provider, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {provider}: {status}")
    
    print(f"\n🎯 任务模型推荐:")
    tasks = [
        "paper_writing",
        "reasoning", 
        "layout_analysis",
        "visual_reasoning",
        "paper_layout_optimization"
    ]
    
    for task in tasks:
        model_info = manager.get_optimal_model_for_task(task)
        print(f"   {task}: {model_info['provider']} - {model_info['model']}")
