#!/usr/bin/env python3
"""
Agent修复验证测试
验证所有专家代理是否能够正确初始化
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

print("🔧 Agent修复验证测试")
print("="*50)

def test_agent_imports():
    """测试所有Agent模块的导入"""
    print("\n📦 测试1: Agent模块导入")
    try:
        from core.unified_api_client import UnifiedAPIClient
        print("  ✅ UnifiedAPIClient 导入成功")
        
        from agents.base_agent import BaseAgent
        print("  ✅ BaseAgent 导入成功")
        
        from agents.agent_manager import AgentManager
        print("  ✅ AgentManager 导入成功")
        
        from agents.expert_agents.ai_technology_expert import AITechnologyExpert
        print("  ✅ AITechnologyExpert 导入成功")
        
        from agents.expert_agents.neuroscience_expert import NeuroscienceExpert
        print("  ✅ NeuroscienceExpert 导入成功")
        
        from agents.expert_agents.data_analysis_expert import DataAnalysisExpert
        print("  ✅ DataAnalysisExpert 导入成功")
        
        from agents.expert_agents.experiment_design_expert import ExperimentDesignExpert
        print("  ✅ ExperimentDesignExpert 导入成功")
        
        from agents.expert_agents.paper_writing_expert import PaperWritingExpert
        print("  ✅ PaperWritingExpert 导入成功")
        
        print("  🎉 所有Agent模块导入测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ Agent模块导入失败: {e}")
        return False

def test_agent_initialization():
    """测试Agent初始化"""
    print("\n🤖 测试2: Agent初始化")
    try:
        from core.unified_api_client import UnifiedAPIClient
        from agents.agent_manager import AgentManager
        
        # 创建统一API客户端 (API密钥已硬编码在类中)
        api_client = UnifiedAPIClient()
        print("  ✅ UnifiedAPIClient 初始化成功")
        
        # 创建Agent管理器
        agent_manager = AgentManager(api_client)
        print("  ✅ AgentManager 初始化成功")
        
        # 检查注册的agents
        agents = agent_manager.list_agents()
        print(f"  ✅ 注册的专家数量: {len(agents)}")
        
        for agent_id, agent_info in agents.items():
            print(f"    - {agent_id}: {agent_info['agent_type']}")
        
        print("  🎉 Agent初始化测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ Agent初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_agents():
    """测试各个专家代理的单独初始化"""
    print("\n👥 测试3: 各专家代理独立初始化")
    try:
        from core.unified_api_client import UnifiedAPIClient
        from agents.expert_agents.ai_technology_expert import AITechnologyExpert
        from agents.expert_agents.neuroscience_expert import NeuroscienceExpert
        from agents.expert_agents.data_analysis_expert import DataAnalysisExpert
        from agents.expert_agents.experiment_design_expert import ExperimentDesignExpert
        from agents.expert_agents.paper_writing_expert import PaperWritingExpert
        
        # 创建统一API客户端
        api_client = UnifiedAPIClient()
        
        experts = [
            ("AI技术专家", AITechnologyExpert),
            ("神经科学专家", NeuroscienceExpert),
            ("数据分析专家", DataAnalysisExpert),
            ("实验设计专家", ExperimentDesignExpert),
            ("论文写作专家", PaperWritingExpert)
        ]
        
        for name, expert_class in experts:
            try:
                expert = expert_class(api_client)
                status = expert.get_status()
                print(f"  ✅ {name} 初始化成功 - 类型: {status['agent_type']}")
            except Exception as e:
                print(f"  ❌ {name} 初始化失败: {e}")
                return False
        
        print("  🎉 各专家代理独立初始化测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 专家代理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print(f"时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("目标: 验证Agent修复是否成功")
    
    results = []
    
    # 运行所有测试
    results.append(test_agent_imports())
    results.append(test_agent_initialization())
    results.append(test_individual_agents())
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 Agent修复验证结果总结")
    print("="*50)
    
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    print(f"测试数量: {total}")
    print(f"通过测试: {passed}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print("\n✅ Agent修复验证完全通过!")
        print("🎯 所有专家代理已正确更新，可以运行完整测试")
        print("\n💡 下一步建议:")
        print("1. 重新运行快速验证: python tests/test_stage1_quick_validation.py")
        print("2. 运行完整测试: python tests/test_stage1_literature_workflow_complete.py")
    else:
        print(f"\n⚠️ Agent修复验证部分通过 ({success_rate:.1f}%)")
        print("需要进一步检查失败的组件")
    
    return success_rate == 100.0

if __name__ == "__main__":
    import time
    main()
