"""
脑启发智能AutoResearch Agent - AI技术专家代理
专门负责AI技术分析、算法评估、技术路线规划等任务
"""

from typing import Dict, List, Any, Optional
import json
import time

from agents.base_agent import BaseAgent, AgentResponse, AgentCapabilities, register_expert
from core.unified_api_client import UnifiedAPIClient


@register_expert("ai_technology_expert")
class AITechnologyExpert(BaseAgent):
    """AI技术专家代理"""
    
    def __init__(self, unified_client: UnifiedAPIClient, temperature: float = 0.2):
        super().__init__(
            agent_type="AI技术专家",
            unified_client=unified_client,
            specialization="人工智能技术、机器学习算法、深度学习架构",
            temperature=temperature
        )
        
        # AI技术专家的核心能力
        self.capabilities = [
            AgentCapabilities.AI_TECHNOLOGY,
            AgentCapabilities.ANALYSIS,
            AgentCapabilities.EVALUATION,
            AgentCapabilities.REASONING
        ]
        
        # 初始化AI技术知识库
        self._init_ai_knowledge_base()
    
    def _init_ai_knowledge_base(self):
        """初始化AI技术知识库"""
        self.knowledge_base = {
            "neural_architectures": [
                "CNN", "RNN", "LSTM", "GRU", "Transformer", "Vision Transformer",
                "ResNet", "DenseNet", "EfficientNet", "BERT", "GPT", "Spiking Neural Networks"
            ],
            "learning_methods": [
                "Supervised Learning", "Unsupervised Learning", "Reinforcement Learning",
                "Transfer Learning", "Few-shot Learning", "Meta-learning", "Self-supervised Learning"
            ],
            "optimization_algorithms": [
                "Adam", "SGD", "RMSprop", "AdaGrad", "LAMB", "Lion"
            ],
            "evaluation_metrics": {
                "classification": ["Accuracy", "Precision", "Recall", "F1-Score", "AUC-ROC"],
                "regression": ["MSE", "MAE", "R²", "RMSE"],
                "generative": ["BLEU", "ROUGE", "FID", "IS"],
                "efficiency": ["FLOPs", "Parameters", "Latency", "Memory Usage"]
            },
            "ai_frameworks": [
                "PyTorch", "TensorFlow", "JAX", "Keras", "Hugging Face", "OpenAI API"
            ],
            "brain_inspired_techniques": [
                "Attention Mechanisms", "Spiking Neural Networks", "Neuromorphic Computing",
                "Synaptic Plasticity", "Hebbian Learning", "Cortical Columns", "Neuromodulation"
            ]
        }
    
    def _build_system_prompt(self) -> str:
        """Build system prompt for the AI technology expert"""
        return """You are a senior AI technology expert with deep expertise in artificial intelligence, machine learning, and deep learning.

Expertise Areas:
- Neural network architecture design and optimization
- Machine learning algorithms and applications
- Brain-inspired artificial intelligence technologies
- AI system performance evaluation and improvement
- Technology trend analysis and prediction

Analysis Principles:
1. Technical accuracy: Based on latest AI research and practices
2. Practical orientation: Consider real-world applications and constraints
3. Innovative thinking: Identify technological breakthroughs and opportunities
4. Comprehensive evaluation: Multi-dimensional analysis covering algorithms, performance, scalability
5. Brain-inspired perspective: Special focus on biologically-inspired AI techniques

Output Requirements:
- Provide clear technical analysis and recommendations
- Include specific algorithms, models, and framework suggestions
- Assess technical feasibility and implementation difficulty
- Provide quantitative technical metrics and performance expectations
- Output structured analysis results in JSON format

Always maintain professional, objective, and forward-looking analytical perspectives."""
    
    def analyze(self, input_data: Dict[str, Any]) -> AgentResponse:
        """
        AI技术专家分析主方法
        
        Args:
            input_data: 输入数据，可包含：
                - research_topic: 研究主题
                - paper_content: 论文内容  
                - technical_requirements: 技术需求
                - workflow_data: 工作流数据
                - input_text: 原始分析文本（用于研究问题评估）
                - analysis_type: 分析类型
                
        Returns:
            AgentResponse: AI技术分析结果
        """
        try:
            print(f"🤖 {self.agent_type}开始技术分析...")
            
            # 检查是否是研究问题评估
            if input_data.get("analysis_type") == "research_question_evaluation":
                return self._evaluate_research_question(input_data)
            # 根据输入数据类型选择分析策略
            elif "paper_content" in input_data:
                return self._analyze_paper_technology(input_data)
            elif "research_topic" in input_data:
                return self._analyze_research_topic(input_data)
            elif "workflow_data" in input_data:
                return self._analyze_workflow_technology(input_data)
            else:
                return self._general_ai_analysis(input_data)
                
        except Exception as e:
            print(f"❌ AI技术分析失败: {e}")
            return self._create_error_response(str(e))
    
    def _analyze_paper_technology(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析论文中的AI技术"""
        paper_content = input_data.get("paper_content", "")
        paper_title = input_data.get("title", "")
        
        prompt = f"""
        As an AI technology expert, please conduct in-depth analysis of the technical elements in the following research paper:
        
        Paper Title: {paper_title}
        Paper Content: {paper_content}
        
        Please analyze from the following dimensions:
        
        1. Core AI technology identification
        2. Algorithm innovation assessment
        3. Technical implementation difficulty analysis
        4. Performance metrics prediction
        5. Technology development potential
        6. Brain-inspired elements evaluation
        7. Practical application prospects
        8. Technical improvement suggestions
        
        Please return detailed analysis in JSON format:
        {{
            "core_technologies": ["technology1", "technology2"],
            "innovation_level": "innovation score (1-10)",
            "implementation_difficulty": "difficulty level (simple/moderate/complex)",
            "performance_prediction": {{
                "accuracy_expectation": "expected accuracy",
                "efficiency_rating": "efficiency rating",
                "scalability": "scalability assessment"
            }},
            "brain_inspiration_score": "brain-inspired score (1-10)",
            "technical_strengths": ["strength1", "strength2"],
            "technical_limitations": ["limitation1", "limitation2"],
            "improvement_suggestions": ["suggestion1", "suggestion2"],
            "application_domains": ["domain1", "domain2"],
            "confidence": 0.85
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            # 安全获取列表长度（处理Mock对象）
            def safe_len(obj, default=0):
                try:
                    if hasattr(obj, '__len__'):
                        return len(obj)
                    elif isinstance(obj, list):
                        return len(obj)
                    else:
                        return default
                except:
                    return default
            
            core_tech_list = json_data.get('core_technologies', [])
            core_tech_count = safe_len(core_tech_list, 0)
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"AI技术分析完成。识别出{core_tech_count}项核心技术，创新程度评分：{json_data.get('innovation_level', 'N/A')}",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于论文内容进行深度技术分析，评估了算法创新性、实现难度、性能预期等关键指标",
                metadata={
                    "analysis_type": "paper_technology",
                    "technical_analysis": json_data,
                    "core_tech_count": core_tech_count,
                    "innovation_score": json_data.get('innovation_level', 0)
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("技术分析JSON解析失败")
    
    def _analyze_research_topic(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析研究主题的技术可行性"""
        research_topic = input_data.get("research_topic", "")
        requirements = input_data.get("requirements", [])
        
        prompt = f"""
        As an AI technology expert, please analyze the technical feasibility and implementation path for the following research topic:
        
        Research Topic: {research_topic}
        Specific Requirements: {requirements}
        
        Please provide technical feasibility analysis:
        
        1. Required core technology stack
        2. Technical implementation pathway
        3. Key technical challenges
        4. Solution approaches
        5. Resource requirement assessment
        6. Timeline estimation
        7. Risk factor identification
        8. Success probability evaluation
        
        Please return in JSON format:
        {{
            "required_technologies": ["tech1", "tech2"],
            "implementation_path": ["step1", "step2", "step3"],
            "technical_challenges": ["challenge1", "challenge2"],
            "solution_approaches": ["approach1", "approach2"],
            "resource_requirements": {{
                "computational": "computational resource needs",
                "data": "data requirements",
                "expertise": "expertise requirements"
            }},
            "timeline_estimate": "estimated timeline",
            "risk_factors": ["risk1", "risk2"],
            "success_probability": 0.75,
            "recommended_frameworks": ["framework1", "framework2"],
            "confidence": 0.80
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            # 安全获取列表长度（处理Mock对象）
            def safe_len(obj, default=0):
                try:
                    if hasattr(obj, '__len__'):
                        return len(obj)
                    elif isinstance(obj, list):
                        return len(obj)
                    else:
                        return default
                except:
                    return default
            
            required_tech_list = json_data.get('required_technologies', [])
            tech_count = safe_len(required_tech_list, 0)
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"技术可行性分析完成。成功概率：{json_data.get('success_probability', 0)*100:.0f}%，预估时间：{json_data.get('timeline_estimate', '未知')}",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于研究主题进行全面技术可行性评估，考虑了技术栈、实现路径、资源需求等因素",
                metadata={
                    "analysis_type": "research_feasibility",
                    "feasibility_analysis": json_data,
                    "success_probability": json_data.get('success_probability', 0),
                    "tech_count": tech_count
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("可行性分析JSON解析失败")
    
    def _analyze_workflow_technology(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析工作流中的技术要素"""
        workflow_data = input_data.get("workflow_data", {})
        
        # 提取工作流中的技术信息
        datasets = workflow_data.get("datasets", [])
        architectures = workflow_data.get("network_architectures", [])
        platforms = workflow_data.get("platforms_tools", [])
        ai_techniques = workflow_data.get("ai_techniques", [])
        
        prompt = f"""
        As an AI technology expert, please analyze the technical configuration in the following workflow:
        
        Datasets: {datasets}
        Network Architectures: {architectures}
        Platforms & Tools: {platforms}
        AI Techniques: {ai_techniques}
        
        Please evaluate this workflow from a technical perspective:
        
        1. Technology stack coherence
        2. Architecture selection rationality
        3. Platform tool compatibility
        4. Performance optimization suggestions
        5. Technology upgrade path
        6. Potential technical risks
        7. Alternative technical solutions
        8. Overall technical rating
        
        Please return in JSON format:
        {{
            "workflow_coherence": "technology stack coherence score (1-10)",
            "architecture_assessment": {{
                "suitability": "architecture suitability",
                "performance_expectation": "performance expectation",
                "scalability": "scalability assessment"
            }},
            "platform_compatibility": "platform compatibility score (1-10)",
            "optimization_suggestions": ["optimization1", "optimization2"],
            "upgrade_path": ["upgrade_path1", "upgrade_path2"],
            "technical_risks": ["risk1", "risk2"],
            "alternative_solutions": ["alternative1", "alternative2"],
            "overall_rating": "overall technical rating (A/B/C/D)",
            "confidence": 0.85
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"工作流技术分析完成。整体评级：{json_data.get('overall_rating', 'N/A')}，协调性评分：{json_data.get('workflow_coherence', 'N/A')}",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"对工作流的技术栈、架构选择、平台配置进行综合评估",
                metadata={
                    "analysis_type": "workflow_technology",
                    "workflow_analysis": json_data,
                    "coherence_score": json_data.get('workflow_coherence', 0),
                    "overall_rating": json_data.get('overall_rating', 'N/A')
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("工作流技术分析JSON解析失败")
    
    def _general_ai_analysis(self, input_data: Dict[str, Any]) -> AgentResponse:
        """通用AI技术分析"""
        prompt = f"""
        As an AI technology expert, please analyze the following data from a technical perspective:
        
        {json.dumps(input_data, ensure_ascii=False, indent=2)}
        
        Please provide general AI technical insights and recommendations.
        
        Return in JSON format:
        {{
            "technical_insights": ["insight1", "insight2"],
            "ai_recommendations": ["recommendation1", "recommendation2"],
            "technology_trends": ["trend1", "trend2"],
            "confidence": 0.75
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            # 安全获取列表长度（处理Mock对象）
            def safe_len(obj, default=0):
                try:
                    if hasattr(obj, '__len__'):
                        return len(obj)
                    elif isinstance(obj, list):
                        return len(obj)
                    else:
                        return default
                except:
                    return default
            
            insights_list = json_data.get('technical_insights', [])
            insights_count = safe_len(insights_list, 0)
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"通用AI技术分析完成。提供了{insights_count}个技术洞察",
                confidence=float(json_data.get('confidence', 0.7)),
                reasoning="基于输入数据进行通用AI技术分析",
                metadata={
                    "analysis_type": "general_ai",
                    "analysis_result": json_data
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("通用AI分析JSON解析失败")
    
    def recommend_architecture(self, task_type: str, constraints: Dict[str, Any]) -> Dict[str, Any]:
        """推荐适合的神经网络架构"""
        prompt = f"""
        基于以下任务类型和约束条件，推荐最适合的神经网络架构：
        
        任务类型：{task_type}
        约束条件：{json.dumps(constraints, ensure_ascii=False, indent=2)}
        
        请推荐3个最佳架构选择，并说明理由。
        
        以JSON格式返回：
        {{
            "recommendations": [
                {{
                    "architecture": "架构名称",
                    "rationale": "推荐理由",
                    "pros": ["优点1", "优点2"],
                    "cons": ["缺点1", "缺点2"],
                    "suitability_score": 0.9
                }}
            ],
            "confidence": 0.85
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "架构推荐失败"}
    
    def evaluate_ai_innovation(self, technique_description: str) -> Dict[str, Any]:
        """评估AI技术创新性"""
        prompt = f"""
        请评估以下AI技术的创新性：
        
        技术描述：{technique_description}
        
        从以下维度评估：
        1. 新颖性 (1-10)
        2. 技术影响力 (1-10)  
        3. 实用性 (1-10)
        4. 可扩展性 (1-10)
        5. 脑启发程度 (1-10)
        
        以JSON格式返回评估结果。
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "创新性评估失败"}
    
    def get_technology_roadmap(self, research_goal: str) -> Dict[str, Any]:
        """生成技术发展路线图"""
        prompt = f"""
        为以下研究目标制定技术发展路线图：
        
        研究目标：{research_goal}
        
        请制定分阶段的技术发展计划，包括：
        - 短期目标（3-6个月）
        - 中期目标（6-12个月）  
        - 长期目标（1-2年）
        
        每个阶段包含具体的技术里程碑和关键技术点。
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "技术路线图生成失败"}
    
    def collaborate(self, collaboration_input: Dict[str, Any], *args, **kwargs) -> Dict[str, Any]:
        """
        与其他专家协作分析
        
        Args:
            collaboration_input: 包含协作上下文的字典
                - task: 协作任务类型
                - own_analysis: 自己的初始分析
                - other_expert_opinions: 其他专家的意见
                - research_topic: 研究主题
            *args, **kwargs: 额外参数，用于兼容性
            
        Returns:
            协作分析结果
        """
        print("🤖 AI技术专家开始协作分析...")
        
        # 提取协作上下文
        task = collaboration_input.get('task', 'collaborative_analysis')
        own_analysis = collaboration_input.get('own_analysis', {})
        other_opinions = collaboration_input.get('other_expert_opinions', {})
        research_topic = collaboration_input.get('research_topic', '')
        
        # 构建协作分析提示词
        collaboration_prompt = f"""
As an AI Technology Expert, provide collaborative analysis by integrating insights from other experts.

COLLABORATION CONTEXT:
- Research Topic: {research_topic}
- Task: {task}
- My Initial Analysis: {json.dumps(own_analysis, indent=2) if own_analysis else 'None'}

OTHER EXPERT OPINIONS:
{self._format_other_opinions(other_opinions)}

COLLABORATION REQUIREMENTS:
1. Review and integrate insights from other experts
2. Identify complementary perspectives and synergies
3. Address any conflicts or disagreements constructively
4. Provide enhanced analysis that combines multiple viewpoints
5. Focus on AI/ML technical aspects while considering other domains

Please provide a comprehensive collaborative analysis that:
- Acknowledges valuable insights from other experts
- Integrates different perspectives coherently
- Resolves technical conflicts with evidence-based reasoning
- Enhances the overall analysis quality

Format your response as JSON with the following structure:
{{
    "collaborative_analysis": "Enhanced analysis integrating multiple expert perspectives",
    "key_insights_integrated": ["insight1", "insight2", "insight3"],
    "technical_synergies": ["synergy1", "synergy2"],
    "resolved_conflicts": ["conflict1_resolution", "conflict2_resolution"],
    "enhanced_recommendations": ["rec1", "rec2", "rec3"],
    "confidence": 0.85,
    "collaboration_quality": "high/medium/low",
    "next_collaboration_steps": ["step1", "step2"]
}}
"""
        
        try:
            # 获取LLM响应
            response, json_data = self.get_llm_response(collaboration_prompt, extract_json=True)
            
            if json_data:
                # 添加元数据
                json_data['expert_type'] = 'ai_technology'
                json_data['collaboration_timestamp'] = time.time()
                json_data['task_type'] = task
                
                print(f"✅ AI技术专家协作完成，置信度: {json_data.get('confidence', 0.0):.2f}")
                return json_data
            else:
                # 回退响应
                return self._create_fallback_collaboration_response(task, own_analysis, other_opinions)
                
        except Exception as e:
            print(f"❌ AI技术专家协作失败: {e}")
            return self._create_fallback_collaboration_response(task, own_analysis, other_opinions)
    
    def _format_other_opinions(self, other_opinions: Dict[str, Any]) -> str:
        """格式化其他专家的意见"""
        if not other_opinions:
            return "No other expert opinions provided."
        
        formatted = []
        for expert, opinion in other_opinions.items():
            if isinstance(opinion, dict):
                confidence = opinion.get('confidence', 'N/A')
                analysis = opinion.get('analysis', str(opinion))
                formatted.append(f"- {expert}: (Confidence: {confidence}) {analysis}")
            else:
                formatted.append(f"- {expert}: {str(opinion)}")
        
        return "\n".join(formatted)
    
    def _create_fallback_collaboration_response(self, task: str, own_analysis: Dict[str, Any], 
                                              other_opinions: Dict[str, Any]) -> Dict[str, Any]:
        """创建回退协作响应"""
        return {
            "collaborative_analysis": f"AI technology perspective on {task} with consideration of other expert inputs",
            "key_insights_integrated": ["Technical feasibility assessment", "AI/ML methodology evaluation"],
            "technical_synergies": ["Cross-domain knowledge integration", "Multi-perspective validation"],
            "resolved_conflicts": ["Technical approach alignment"],
            "enhanced_recommendations": ["Adopt hybrid AI approach", "Implement iterative development"],
            "confidence": 0.75,
            "collaboration_quality": "medium",
            "next_collaboration_steps": ["Refine technical specifications", "Plan implementation phases"],
            "expert_type": "ai_technology",
            "collaboration_timestamp": time.time(),
            "task_type": task
        }
    
    def _ensure_structured_response(self, raw_response: Any, analysis_type: str = "general") -> Dict[str, Any]:
        """
        确保响应是结构化的字典格式
        
        Args:
            raw_response: 原始响应
            analysis_type: 分析类型
            
        Returns:
            结构化的响应字典
        """
        if isinstance(raw_response, dict):
            return raw_response
        
        if isinstance(raw_response, str):
            # 尝试从字符串中提取JSON
            try:
                import json
                import re
                
                # 查找JSON模式
                json_pattern = r'\{[^}]*\}'
                matches = re.findall(json_pattern, raw_response, re.DOTALL)
                if matches:
                    try:
                        return json.loads(matches[0])
                    except json.JSONDecodeError:
                        pass
            except:
                pass
            
            # 如果无法解析JSON，创建结构化响应
            return {
                'analysis': raw_response,
                'analysis_type': analysis_type,
                'confidence': 0.7,
                'structured': False,
                'raw_content': raw_response
            }
        
        # 其他类型的响应
        return {
            'analysis': str(raw_response),
            'analysis_type': analysis_type,
            'confidence': 0.5,
            'structured': False,
            'raw_content': raw_response
        }
    
    def _evaluate_research_question(self, input_data: Dict[str, Any]) -> AgentResponse:
        """评估研究问题 - 专门处理增强prompt格式"""
        input_text = input_data.get("input_text", "")
        
        # 直接使用传入的增强prompt，它已经包含了完整的评估指导
        response, _ = self.get_llm_response(input_text, extract_json=False)
        
        if response:
            return AgentResponse(
                agent_type=self.agent_type,
                content=response,  # 返回完整的LLM响应，包含JSON格式的评估
                confidence=0.8,
                reasoning="AI技术专家基于增强prompt进行研究问题评估",
                metadata={
                    "analysis_type": "research_question_evaluation",
                    "prompt_type": "enhanced_ai_scientist_v2"
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("研究问题评估失败")
    