"""
第一优先级综合测试
测试LaTeX格式专家、引用管理器和多专家评审系统的集成效果
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入专家系统
from paper_generation.paper_quality_optimizer import PaperQualityOptimizer
from paper_generation.latex_format_expert import LaTeXFormatExpert
from paper_generation.enhanced_citation_manager import EnhancedCitationManager
from paper_generation.multi_expert_review_system import MultiExpertReviewSystem

# 导入混合模型客户端
from core.hybrid_model_client import HybridModelClient

class PriorityOneIntegrationTest:
    """第一优先级集成测试"""
    
    def __init__(self):
        self.test_results = {
            'start_time': datetime.now(),
            'tests_run': 0,
            'tests_passed': 0,
            'tests_failed': 0,
            'detailed_results': []
        }
        
        # 初始化混合模型客户端
        try:
            self.hybrid_client = HybridModelClient()
            print("✅ 混合模型客户端初始化成功")
        except Exception as e:
            print(f"⚠️  混合模型客户端初始化失败: {e}")
            self.hybrid_client = None
    
    def log_test_result(self, test_name: str, passed: bool, details: str, metrics: dict = None):
        """记录测试结果"""
        self.test_results['tests_run'] += 1
        if passed:
            self.test_results['tests_passed'] += 1
            status = "✅ PASSED"
        else:
            self.test_results['tests_failed'] += 1
            status = "❌ FAILED"
        
        result = {
            'test_name': test_name,
            'status': status,
            'passed': passed,
            'details': details,
            'metrics': metrics or {},
            'timestamp': datetime.now()
        }
        
        self.test_results['detailed_results'].append(result)
        print(f"{status}: {test_name}")
        print(f"   详情: {details}")
        if metrics:
            print(f"   指标: {metrics}")
        print()
    
    async def test_latex_format_expert(self):
        """测试LaTeX格式专家"""
        print("🔧 测试LaTeX格式专家...")
        
        # 测试用LaTeX内容
        test_content = """
\\documentclass{article}
\\begin{document}
\\title{Test Paper}
\\author{Test Author}
\\maketitle

\\begin{abstract}
This is a test abstract.
\\end{abstract}

\\section{Introduction}
This is the introduction section.

\\section{Methods}
This is the methods section.

\\section{Results}
This is the results section.

\\section{Conclusion}
This is the conclusion section.

\\end{document}
"""
        
        try:
            latex_expert = LaTeXFormatExpert(self.hybrid_client)
            result = await latex_expert.optimize_latex_format(test_content, "ICML")
            
            # 检查结果
            success = result.get('success', False)
            quality_score = result.get('quality_score', 0)
            fixed_issues = result.get('fixed_issues', [])
            
            metrics = {
                'quality_score': quality_score,
                'issues_fixed': len(fixed_issues),
                'has_optimized_content': bool(result.get('optimized_content'))
            }
            
            if success and quality_score >= 7.0:
                self.log_test_result(
                    "LaTeX格式专家", 
                    True, 
                    f"成功优化LaTeX格式，质量分数: {quality_score:.1f}/10",
                    metrics
                )
            else:
                self.log_test_result(
                    "LaTeX格式专家", 
                    False, 
                    f"优化失败或质量分数过低: {quality_score:.1f}/10",
                    metrics
                )
                
        except Exception as e:
            self.log_test_result(
                "LaTeX格式专家", 
                False, 
                f"测试异常: {str(e)}"
            )
    
    async def test_citation_manager(self):
        """测试引用管理器"""
        print("📚 测试引用管理器...")
        
        # 测试用内容
        test_content = """
This paper presents a novel approach to deep learning.
Deep learning has achieved remarkable success in recent years.
Neural networks are inspired by biological neural systems.
"""
        
        test_metadata = {
            'title': 'Test Paper on Deep Learning',
            'abstract': 'This is a test abstract about deep learning.',
            'keywords': ['deep learning', 'neural networks', 'machine learning']
        }
        
        try:
            citation_manager = EnhancedCitationManager(self.hybrid_client)
            result = await citation_manager.enhance_citations(test_content, test_metadata)
            
            # 检查结果
            success = result.get('success', False)
            citation_count = result.get('citation_count', 0)
            quality_score = result.get('quality_score', 0)
            
            metrics = {
                'citation_count': citation_count,
                'quality_score': quality_score,
                'has_enhanced_content': bool(result.get('enhanced_content'))
            }
            
            if success and citation_count >= 10 and quality_score >= 7.0:
                self.log_test_result(
                    "引用管理器", 
                    True, 
                    f"成功增强引用，数量: {citation_count}，质量: {quality_score:.1f}/10",
                    metrics
                )
            else:
                self.log_test_result(
                    "引用管理器", 
                    False, 
                    f"增强失败或质量不足: 数量{citation_count}，质量{quality_score:.1f}/10",
                    metrics
                )
                
        except Exception as e:
            self.log_test_result(
                "引用管理器", 
                False, 
                f"测试异常: {str(e)}"
            )
    
    async def test_multi_expert_review(self):
        """测试多专家评审系统"""
        print("🔍 测试多专家评审系统...")
        
        # 测试用内容
        test_content = """
This paper presents a neural plasticity-inspired deep learning architecture.
We propose a novel approach that combines spike-timing dependent plasticity
with meta-learning for improved neural network performance.

The methodology involves creating a hybrid network architecture that
incorporates biological learning mechanisms. Our experiments demonstrate
significant improvements over traditional deep learning approaches.
"""
        
        test_metadata = {
            'title': 'Neural Plasticity-Inspired Deep Learning Architecture',
            'abstract': 'This paper introduces a novel deep learning architecture inspired by neural plasticity mechanisms.',
            'authors': ['Test Author'],
            'keywords': ['neural plasticity', 'deep learning', 'meta-learning']
        }
        
        try:
            review_system = MultiExpertReviewSystem(self.hybrid_client)
            result = await review_system.conduct_review(test_content, test_metadata)
            
            # 检查结果
            consensus_score = result.consensus_score
            num_reviews = len(result.reviews)
            key_issues = len(result.key_issues)
            suggestions = len(result.improvement_suggestions)
            
            metrics = {
                'consensus_score': consensus_score,
                'num_reviews': num_reviews,
                'key_issues': key_issues,
                'suggestions': suggestions,
                'recommendation': result.final_recommendation
            }
            
            if consensus_score >= 6.0 and num_reviews >= 3:
                self.log_test_result(
                    "多专家评审系统", 
                    True, 
                    f"成功完成评审，共识分数: {consensus_score:.1f}/10，{num_reviews}位专家",
                    metrics
                )
            else:
                self.log_test_result(
                    "多专家评审系统", 
                    False, 
                    f"评审不完整或分数过低: {consensus_score:.1f}/10，{num_reviews}位专家",
                    metrics
                )
                
        except Exception as e:
            self.log_test_result(
                "多专家评审系统", 
                False, 
                f"测试异常: {str(e)}"
            )
    
    async def test_integrated_optimizer(self):
        """测试综合优化器"""
        print("🚀 测试综合优化器...")
        
        # 测试用内容
        test_content = """
\\documentclass{article}
\\begin{document}
\\title{Neural Plasticity-Inspired Deep Learning Architecture}
\\author{Test Author}
\\maketitle

\\begin{abstract}
This paper presents a novel approach to deep learning inspired by neural plasticity mechanisms.
\\end{abstract}

\\section{Introduction}
Deep learning has achieved remarkable success in recent years.

\\section{Methodology}
We propose a neural plasticity-inspired architecture.

\\section{Experiments}
We evaluate our approach on several benchmarks.

\\section{Conclusion}
Our results demonstrate the effectiveness of the proposed approach.

\\end{document}
"""
        
        test_metadata = {
            'title': 'Neural Plasticity-Inspired Deep Learning Architecture',
            'abstract': 'This paper presents a novel approach to deep learning inspired by neural plasticity mechanisms.',
            'authors': ['Test Author'],
            'keywords': ['neural plasticity', 'deep learning', 'meta-learning']
        }
        
        try:
            optimizer = PaperQualityOptimizer(self.hybrid_client)
            result = await optimizer.optimize_paper(
                test_content, 
                test_metadata,
                output_dir="output/test_optimization",
                target_venue="ICML"
            )
            
            # 检查结果
            success = result.get('success', False)
            final_quality = result.get('final_quality', 0)
            total_improvement = result.get('total_improvement', 0)
            
            metrics = {
                'final_quality': final_quality,
                'total_improvement': total_improvement,
                'optimization_rounds': len(result.get('optimization_log', {}).get('optimization_rounds', [])),
                'has_optimized_content': bool(result.get('optimized_content'))
            }
            
            if final_quality >= 7.0 and total_improvement > 0:
                self.log_test_result(
                    "综合优化器", 
                    True, 
                    f"成功优化论文，最终质量: {final_quality:.1f}/10，改进: {total_improvement:+.1f}",
                    metrics
                )
            else:
                self.log_test_result(
                    "综合优化器", 
                    False, 
                    f"优化效果不理想: 质量{final_quality:.1f}/10，改进{total_improvement:+.1f}",
                    metrics
                )
                
        except Exception as e:
            self.log_test_result(
                "综合优化器", 
                False, 
                f"测试异常: {str(e)}"
            )
    
    async def test_system_integration(self):
        """测试系统集成"""
        print("⚡ 测试系统集成...")
        
        try:
            # 测试所有组件是否可以同时工作
            latex_expert = LaTeXFormatExpert(self.hybrid_client)
            citation_manager = EnhancedCitationManager(self.hybrid_client)
            review_system = MultiExpertReviewSystem(self.hybrid_client)
            optimizer = PaperQualityOptimizer(self.hybrid_client)
            
            # 检查所有组件是否正确初始化
            components = [
                ('LaTeX专家', latex_expert),
                ('引用管理器', citation_manager),
                ('评审系统', review_system),
                ('优化器', optimizer)
            ]
            
            all_initialized = True
            for name, component in components:
                if component is None:
                    all_initialized = False
                    break
            
            if all_initialized:
                self.log_test_result(
                    "系统集成", 
                    True, 
                    "所有组件成功初始化并可以协同工作",
                    {'components_count': len(components)}
                )
            else:
                self.log_test_result(
                    "系统集成", 
                    False, 
                    "部分组件初始化失败"
                )
                
        except Exception as e:
            self.log_test_result(
                "系统集成", 
                False, 
                f"集成测试异常: {str(e)}"
            )
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始第一优先级综合测试")
        print("="*60)
        
        # 运行各个测试
        await self.test_latex_format_expert()
        await self.test_citation_manager()
        await self.test_multi_expert_review()
        await self.test_integrated_optimizer()
        await self.test_system_integration()
        
        # 完成测试
        self.test_results['end_time'] = datetime.now()
        self.test_results['duration'] = (
            self.test_results['end_time'] - self.test_results['start_time']
        ).total_seconds()
        
        # 生成报告
        self.generate_test_report()
    
    def generate_test_report(self):
        """生成测试报告"""
        results = self.test_results
        
        print("\n" + "="*60)
        print("🎯 第一优先级综合测试报告")
        print("="*60)
        
        print(f"📊 测试统计:")
        print(f"   总测试数: {results['tests_run']}")
        print(f"   通过数: {results['tests_passed']}")
        print(f"   失败数: {results['tests_failed']}")
        print(f"   成功率: {(results['tests_passed'] / max(1, results['tests_run']) * 100):.1f}%")
        print(f"   测试时长: {results['duration']:.1f}秒")
        
        print(f"\n📋 详细结果:")
        for result in results['detailed_results']:
            print(f"   {result['status']}: {result['test_name']}")
            if result['details']:
                print(f"      {result['details']}")
            if result['metrics']:
                for key, value in result['metrics'].items():
                    print(f"      {key}: {value}")
        
        # 评估整体状态
        success_rate = results['tests_passed'] / max(1, results['tests_run'])
        if success_rate >= 0.8:
            overall_status = "✅ 优秀"
        elif success_rate >= 0.6:
            overall_status = "⚠️  良好"
        else:
            overall_status = "❌ 需要改进"
        
        print(f"\n🏆 整体评估: {overall_status}")
        
        # 保存报告
        self.save_test_report()
    
    def save_test_report(self):
        """保存测试报告"""
        import json
        
        output_dir = Path("output/test_reports")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON报告
        json_file = output_dir / f"priority_one_test_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存文本报告
        txt_file = output_dir / f"priority_one_test_{timestamp}.txt"
        with open(txt_file, 'w', encoding='utf-8') as f:
            f.write(self.generate_text_report())
        
        print(f"\n📁 测试报告已保存:")
        print(f"   JSON: {json_file}")
        print(f"   TXT: {txt_file}")
    
    def generate_text_report(self) -> str:
        """生成文本报告"""
        results = self.test_results
        
        report = f"""
第一优先级综合测试报告
{'='*50}

测试时间: {results['start_time']} - {results['end_time']}
测试时长: {results['duration']:.1f}秒

统计信息:
- 总测试数: {results['tests_run']}
- 通过数: {results['tests_passed']}
- 失败数: {results['tests_failed']}
- 成功率: {(results['tests_passed'] / max(1, results['tests_run']) * 100):.1f}%

详细结果:
{'-'*30}
"""
        
        for result in results['detailed_results']:
            report += f"\n{result['status']}: {result['test_name']}\n"
            report += f"时间: {result['timestamp']}\n"
            report += f"详情: {result['details']}\n"
            
            if result['metrics']:
                report += "指标:\n"
                for key, value in result['metrics'].items():
                    report += f"  {key}: {value}\n"
            
            report += "\n"
        
        return report

async def main():
    """主测试函数"""
    print("🚀 启动第一优先级综合测试")
    
    # 创建测试实例
    test_runner = PriorityOneIntegrationTest()
    
    # 运行所有测试
    await test_runner.run_all_tests()
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    asyncio.run(main())
