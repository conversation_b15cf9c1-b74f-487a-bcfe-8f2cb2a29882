from dataclasses import dataclass, field
from typing import Optional, Dict, List, Any, Union
# ImplementationPlan 数据模型补充
@dataclass
class ImplementationStep:
    title: str = ""
    description: str = ""
    estimated_time: Optional[str] = None

@dataclass
class ImplementationPlan:
    experiment_plan: Optional['ExperimentPlan'] = None
    steps: List[ImplementationStep] = field(default_factory=list)
    programming_language: str = "Python"
    frameworks: List[str] = field(default_factory=list)
    libraries: List[str] = field(default_factory=list)
    code_templates: Dict[str, str] = field(default_factory=dict)
    architecture: Dict[str, Any] = field(default_factory=dict)
    algorithms: List[str] = field(default_factory=list)
    data_pipeline: Dict[str, Any] = field(default_factory=dict)
    training_strategy: Dict[str, Any] = field(default_factory=dict)
    evaluation_pipeline: Dict[str, Any] = field(default_factory=dict)
    resource_requirements: Dict[str, Any] = field(default_factory=dict)
    timeline: Dict[str, Any] = field(default_factory=dict)
    collaboration_insights: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        return {
            "experiment_plan": self.experiment_plan.to_dict() if self.experiment_plan else None,
            "steps": [vars(step) for step in self.steps],
            "programming_language": self.programming_language,
            "frameworks": self.frameworks,
            "libraries": self.libraries,
            "code_templates": self.code_templates,
            "architecture": self.architecture,
            "algorithms": self.algorithms,
            "data_pipeline": self.data_pipeline,
            "training_strategy": self.training_strategy,
            "evaluation_pipeline": self.evaluation_pipeline,
            "resource_requirements": self.resource_requirements,
            "timeline": self.timeline,
            "collaboration_insights": self.collaboration_insights
        }
"""
实验推理模块 - 核心数据模型定义
专门用于脑启发智能研究的实验设计和推理流程
"""

from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import json


@dataclass
class ResearchProblem:
    """研究问题数据结构"""
    question: str = ""  # 设置默认值
    hypothesis: List[str] = field(default_factory=list)  # 设置默认值
    background: Dict[str, Any] = field(default_factory=dict)  # 设置默认值
    domain: str = "brain-inspired intelligence"
    
    # 兼容性字段
    title: Optional[str] = None
    description: Optional[str] = None
    objectives: List[str] = field(default_factory=list)
    constraints: List[str] = field(default_factory=list)
    
    # 评估结果
    value_score: Optional[float] = None
    innovation_score: Optional[float] = None
    feasibility_score: Optional[float] = None
    impact_score: Optional[float] = None
    novelty_score: Optional[float] = None
    
    # 新增字段用于增强协作
    evaluation_summary: Optional[str] = None
    key_challenges: List[str] = field(default_factory=list)
    suggested_approaches: List[str] = field(default_factory=list)
    
    # 评估详情
    evaluation_details: Dict[str, Any] = field(default_factory=dict)
    expert_opinions: List[Dict] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "question": self.question,
            "hypothesis": self.hypothesis,
            "background": self.background,
            "domain": self.domain,
            "value_score": self.value_score,
            "innovation_score": self.innovation_score,
            "feasibility_score": self.feasibility_score,
            "impact_score": self.impact_score,
            "evaluation_details": self.evaluation_details,
            "expert_opinions": self.expert_opinions
        }


@dataclass 
class ExperimentVariable:
    """实验变量定义"""
    name: str
    type: str  # 'independent', 'dependent', 'control', 'confounding'
    description: str
    values: List[Any]
    measurement_method: str
    
    
@dataclass
class ExperimentPlan:
    """实验方案数据结构"""
    research_question: str
    hypothesis: List[str]
    
    # 实验设计
    experiment_type: str = "controlled"  # 'controlled', 'comparative', 'ablation', 'benchmark'
    design: Dict[str, Any] = field(default_factory=dict)
    methodology: Union[Dict[str, Any], str] = field(default_factory=dict)
    
    # 变量定义（兼容新旧格式）
    variables: Union[List[ExperimentVariable], Dict[str, List]] = field(default_factory=list)
    
    # 评估指标
    metrics: List[str] = field(default_factory=list)
    evaluation_criteria: Dict[str, Any] = field(default_factory=dict)
    
    # 新增字段用于协作
    baseline_methods: List[str] = field(default_factory=list)
    expected_outcomes: List[str] = field(default_factory=list)
    collaboration_insights: List[str] = field(default_factory=list)
    
    # 合理性论证
    rationale: str = ""
    logical_connection: str = ""
    validity_analysis: Dict[str, Any] = field(default_factory=dict)
    
    # 实验配置
    sample_size: Optional[int] = None
    duration: Optional[str] = None
    resources_needed: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "research_question": self.research_question,
            "hypothesis": self.hypothesis,
            "experiment_type": self.experiment_type,
            "design": self.design,
            "methodology": self.methodology,
            "variables": [vars(v) for v in self.variables],
            "metrics": self.metrics,
            "evaluation_criteria": self.evaluation_criteria,
            "rationale": self.rationale,
            "logical_connection": self.logical_connection,
            "validity_analysis": self.validity_analysis,
            "sample_size": self.sample_size,
            "duration": self.duration,
            "resources_needed": self.resources_needed
        }


@dataclass
class ExperimentPlan:
    """实验方案数据结构"""
    research_problem: Optional[ResearchProblem] = None
    research_question: str = ""
    hypothesis: List[str] = field(default_factory=list)
    # 实验设计
    experiment_type: str = "controlled"  # 'controlled', 'comparative', 'ablation', 'benchmark'
    design: Dict[str, Any] = field(default_factory=dict)
    methodology: Union[Dict[str, Any], str] = field(default_factory=dict)
    # 变量定义（兼容新旧格式）
    variables: Union[List[ExperimentVariable], Dict[str, List]] = field(default_factory=list)
    # 评估指标
    metrics: List[str] = field(default_factory=list)
    evaluation_criteria: Dict[str, Any] = field(default_factory=dict)
    # 新增字段用于协作
    baseline_methods: List[str] = field(default_factory=list)
    expected_outcomes: List[str] = field(default_factory=list)
    collaboration_insights: List[str] = field(default_factory=list)
    # 合理性论证
    rationale: str = ""
    logical_connection: str = ""
    validity_analysis: Dict[str, Any] = field(default_factory=dict)
    # 实验配置
    sample_size: Optional[int] = None
    duration: Optional[str] = None
    resources_needed: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "research_problem": self.research_problem.to_dict() if self.research_problem else None,
            "research_question": self.research_question,
            "hypothesis": self.hypothesis,
            "experiment_type": self.experiment_type,
            "design": self.design,
            "methodology": self.methodology,
            "variables": [vars(v) for v in self.variables] if isinstance(self.variables, list) else self.variables,
            "metrics": self.metrics,
            "evaluation_criteria": self.evaluation_criteria,
            "baseline_methods": self.baseline_methods,
            "expected_outcomes": self.expected_outcomes,
            "collaboration_insights": self.collaboration_insights,
            "rationale": self.rationale,
            "logical_connection": self.logical_connection,
            "validity_analysis": self.validity_analysis,
            "sample_size": self.sample_size,
            "duration": self.duration,
            "resources_needed": self.resources_needed,
            "code_structure": self.code_structure,
            "environment": self.environment,
            "hardware_requirements": self.hardware_requirements,
            "recommended_datasets": self.recommended_datasets,
            "data_preprocessing": self.data_preprocessing,
            "code_templates": self.code_templates
        }
    
    # 代码结构
    code_structure: Dict[str, Any] = field(default_factory=dict)
    
    # 环境配置
    environment: Dict[str, Any] = field(default_factory=dict)
    hardware_requirements: Dict[str, Any] = field(default_factory=dict)
    
    # 数据集建议
    recommended_datasets: List[str] = field(default_factory=list)
    data_preprocessing: List[str] = field(default_factory=list)
    
    # 代码生成相关
    code_templates: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "experiment_plan_id": self.experiment_plan_id,
            "programming_language": self.programming_language,
            "frameworks": self.frameworks,
            "libraries": self.libraries,
            "steps": [vars(step) for step in self.steps],
            "code_structure": self.code_structure,
            "environment": self.environment,
            "hardware_requirements": self.hardware_requirements,
            "recommended_datasets": self.recommended_datasets,
            "data_preprocessing": self.data_preprocessing,
            "code_templates": self.code_templates
        }


@dataclass
class VisualizationChart:
    """单个图表定义"""
    chart_type: str  # 'line', 'bar', 'scatter', 'heatmap', 'box', 'violin', etc.
    title: str
    description: str
    data_requirements: List[str] = field(default_factory=list)  # 设置默认值
    recommended_tool: str = 'matplotlib'  # 设置默认值
    code_template: Optional[str] = None
    best_practices: List[str] = field(default_factory=list)
    
    # 兼容性字段
    data_source: Optional[str] = None
    x_axis: Optional[str] = None
    y_axis: Optional[str] = None


@dataclass  
class VisualizationPlan:
    """可视化展示方案"""
    implementation_plan: Optional['ImplementationPlan'] = None  # 关联的实现方案，设为可选
    
    # 兼容性字段
    experiment_name: Optional[str] = None
    charts: List['VisualizationChart'] = field(default_factory=list)
    recommended_tools: List[str] = field(default_factory=list)
    export_formats: List[str] = field(default_factory=list)
    styling_suggestions: List[str] = field(default_factory=list)
    interactive_features: List[str] = field(default_factory=list)
    code_examples: Dict[str, str] = field(default_factory=dict)
    
    # 新增字段用于协作
    chart_types: List[str] = field(default_factory=list)
    design_principles: List[str] = field(default_factory=list)
    data_stories: List[str] = field(default_factory=list)
    interactive_elements: List[str] = field(default_factory=list)
    academic_standards: List[str] = field(default_factory=list)
    accessibility_features: List[str] = field(default_factory=list)
    collaboration_insights: List[str] = field(default_factory=list)
    
    # 兼容字段
    experiment_plan_id: Optional[str] = None
    
    # 图表方案
    charts: List[VisualizationChart] = field(default_factory=list)
    
    # 工具推荐
    recommended_tools: List[str] = field(default_factory=list)
    tool_comparison: Dict[str, Any] = field(default_factory=dict)
    
    # 设计指导
    color_schemes: List[str] = field(default_factory=list)
    layout_suggestions: Dict[str, Any] = field(default_factory=dict)
    
    # 期刊规范
    journal_requirements: Dict[str, Any] = field(default_factory=dict)
    figure_standards: Dict[str, Any] = field(default_factory=dict)
    
    # 代码生成
    code_templates: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "experiment_plan_id": self.experiment_plan_id,
            "charts": [vars(chart) for chart in self.charts],
            "recommended_tools": self.recommended_tools,
            "tool_comparison": self.tool_comparison,
            "design_principles": self.design_principles,
            "color_schemes": self.color_schemes,
            "layout_suggestions": self.layout_suggestions,
            "journal_requirements": self.journal_requirements,
            "figure_standards": self.figure_standards,
            "code_templates": self.code_templates
        }


@dataclass
class ReasoningSession:
    """推理会话记录"""
    session_id: str
    timestamp: datetime
    research_problem: ResearchProblem
    experiment_plan: Optional[ExperimentPlan] = None
    implementation_plan: Optional[ImplementationPlan] = None
    visualization_plan: Optional[VisualizationPlan] = None
    
    # 新增字段用于增强工作流
    start_time: Optional[datetime] = None
    completion_time: Optional[datetime] = None
    status: str = "in_progress"  # 'in_progress', 'completed', 'error'
    error_message: Optional[str] = None
    
    # 推理过程记录
    reasoning_log: List[Dict[str, Any]] = field(default_factory=list)
    expert_interactions: List[Dict[str, Any]] = field(default_factory=list)
    
    # 状态管理
    current_stage: str = "problem_evaluation"  # 'problem_evaluation', 'experiment_design', 'implementation_planning', 'visualization_design', 'completed'
    completion_status: Dict[str, bool] = field(default_factory=lambda: {
        "problem_evaluated": False,
        "experiment_designed": False, 
        "implementation_planned": False,
        "visualization_designed": False
    })
    
    def __post_init__(self):
        """初始化后处理"""
        if self.start_time is None:
            self.start_time = self.timestamp
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "session_id": self.session_id,
            "timestamp": self.timestamp.isoformat(),
            "research_problem": self.research_problem.to_dict(),
            "experiment_plan": self.experiment_plan.to_dict() if self.experiment_plan else None,
            "implementation_plan": self.implementation_plan.to_dict() if self.implementation_plan else None,
            "visualization_plan": self.visualization_plan.to_dict() if self.visualization_plan else None,
            "reasoning_log": self.reasoning_log,
            "expert_interactions": self.expert_interactions,
            "current_stage": self.current_stage,
            "completion_status": self.completion_status
        }
        
    def save_to_file(self, filepath: str):
        """保存到文件"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
            
    @classmethod
    def load_from_file(cls, filepath: str) -> 'ReasoningSession':
        """从文件加载"""
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 重构对象
        session = cls(
            session_id=data["session_id"],
            timestamp=datetime.fromisoformat(data["timestamp"]),
            research_problem=ResearchProblem(**data["research_problem"])
        )
        
        if data.get("experiment_plan"):
            session.experiment_plan = ExperimentPlan(**data["experiment_plan"])
        if data.get("implementation_plan"):
            session.implementation_plan = ImplementationPlan(**data["implementation_plan"])
        if data.get("visualization_plan"):
            session.visualization_plan = VisualizationPlan(**data["visualization_plan"])
            
        session.reasoning_log = data.get("reasoning_log", [])
        session.expert_interactions = data.get("expert_interactions", [])
        session.current_stage = data.get("current_stage", "problem_evaluation")
        session.completion_status = data.get("completion_status", {})
        
        return session


# 常用的实验类型模板
EXPERIMENT_TEMPLATES = {
    "controlled_experiment": {
        "name": "对照实验",
        "description": "设置实验组和对照组，控制变量进行比较",
        "required_components": ["control_group", "experimental_group", "controlled_variables"],
        "metrics": ["accuracy", "performance", "efficiency"]
    },
    
    "ablation_study": {
        "name": "消融实验",
        "description": "逐步移除系统组件，分析各部分的贡献",
        "required_components": ["baseline_model", "component_variations", "performance_metrics"],
        "metrics": ["component_contribution", "model_performance", "feature_importance"]
    },
    
    "benchmark_comparison": {
        "name": "基准对比实验",
        "description": "与现有方法在标准数据集上进行对比",
        "required_components": ["baseline_methods", "benchmark_datasets", "evaluation_metrics"],
        "metrics": ["comparative_accuracy", "computational_efficiency", "generalization"]
    },
    
    "parameter_sensitivity": {
        "name": "参数敏感性分析",
        "description": "分析关键参数对系统性能的影响",
        "required_components": ["parameter_ranges", "sensitivity_metrics", "stability_analysis"],
        "metrics": ["parameter_impact", "robustness", "optimal_ranges"]
    }
}

# 可视化图表类型模板
VISUALIZATION_TEMPLATES = {
    "performance_comparison": {
        "chart_type": "bar",
        "description": "性能对比图",
        "suitable_for": ["accuracy", "f1_score", "precision", "recall"],
        "tools": ["matplotlib", "seaborn", "plotly"]
    },
    
    "training_curves": {
        "chart_type": "line", 
        "description": "训练曲线",
        "suitable_for": ["loss", "accuracy", "learning_rate"],
        "tools": ["matplotlib", "tensorboard", "wandb"]
    },
    
    "parameter_sensitivity": {
        "chart_type": "heatmap",
        "description": "参数敏感性热图",
        "suitable_for": ["hyperparameters", "sensitivity_analysis"],
        "tools": ["seaborn", "plotly", "matplotlib"]
    },
    
    "distribution_analysis": {
        "chart_type": "violin",
        "description": "数据分布分析",
        "suitable_for": ["error_distribution", "performance_variance"],
        "tools": ["seaborn", "plotly"]
    }
}


@dataclass
class DiscussionRound:
    """讨论轮次数据结构"""
    round_number: int
    topic: str
    participants: List[str]
    responses: Dict[str, Any]  # 使用Any避免循环导入
    consensus_score: float
    key_insights: List[str]
    conflicts: List[str]
    timestamp: str


@dataclass
class CollaborationSession:
    """协作会话数据结构"""
    session_id: str
    research_topic: str
    participants: List[str]
    rounds: List['DiscussionRound'] = field(default_factory=list)
    final_consensus: Dict[str, Any] = field(default_factory=dict)
    quality_score: float = 0.0
    insights: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    duration: float = 0.0
    success: bool = False  # 添加缺失的属性


@dataclass
class CollaborationResult:
    """协作结果数据结构"""
    session_id: str
    success: bool
    final_consensus: Dict[str, Any]
    quality_metrics: Dict[str, float]
    participant_contributions: Dict[str, Any]
    iterations_completed: int
    total_duration: float
    key_insights: List[str]
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "session_id": self.session_id,
            "success": self.success,
            "final_consensus": self.final_consensus,
            "quality_metrics": self.quality_metrics,
            "participant_contributions": self.participant_contributions,
            "iterations_completed": self.iterations_completed,
            "total_duration": self.total_duration,
            "key_insights": self.key_insights,
            "recommendations": self.recommendations
        }
