# 增强论文撰写系统使用指南

## 🎯 系统概述

Brain AutoResearch Agent 的增强论文撰写系统是一个集成了多专家评审、自动修订和工作流分析的智能学术论文生成平台。该系统能够：

- ✅ **工作流提取**: 从研究主题中提取最佳实践工作流
- ✅ **多专家评审**: 5类专家并行评审，提供专业反馈
- ✅ **自动修订**: 基于评审意见的智能内容改进
- ✅ **质量控制**: 迭代优化直到达到发表标准
- ✅ **多格式输出**: Markdown、LaTeX、JSON多种格式

## 🚀 快速开始

### 基础使用

```python
from paper_generation.unified_paper_workflow import (
    UnifiedPaperGenerationWorkflow, 
    UnifiedWorkflowConfig, 
    PaperGenerationConfig
)

# 1. 创建配置
paper_config = PaperGenerationConfig(
    target_venue="ICML",                    # 目标会议
    paper_type="research",                  # 论文类型
    max_review_iterations=3,                # 最大评审轮次
    quality_threshold=7.0,                  # 质量阈值
    enable_multi_expert_review=True,        # 启用多专家评审
    enable_auto_revision=True,              # 启用自动修订
    latex_output=True                       # 生成LaTeX输出
)

unified_config = UnifiedWorkflowConfig(
    enable_workflow_extraction=True,        # 启用工作流提取
    paper_generation_config=paper_config,
    output_formats=['markdown', 'latex', 'json']
)

# 2. 初始化系统
workflow = UnifiedPaperGenerationWorkflow(config=unified_config)

# 3. 生成论文
research_topic = "Brain-Inspired Continual Learning for Dynamic Environments"
result = workflow.generate_complete_research_paper(research_topic)

# 4. 查看结果
print(f"成功: {result.success}")
print(f"最终评分: {result.paper_generation.quality_metrics.overall_score:.2f}/10")
print(f"输出文件: {list(result.output_files.keys())}")
```

### 高级配置

```python
# 详细的研究上下文
research_context = {
    "domain": "machine learning",
    "subfield": "continual learning", 
    "motivation": "Address catastrophic forgetting in neural networks",
    "target_applications": ["computer vision", "robotics"],
    "constraints": ["computational efficiency", "memory limitations"],
    "related_work": ["EWC", "PackNet", "Progressive Neural Networks"],
    "datasets": ["CIFAR-10", "ImageNet", "CORe50"],
    "evaluation_metrics": ["accuracy", "forgetting measure", "transfer"]
}

# 执行带上下文的生成
result = workflow.generate_complete_research_paper(
    research_topic=research_topic,
    research_context=research_context
)
```

## 📊 系统架构

### 核心组件

1. **工作流提取器 (PaperWorkflowExtractor)**
   - 提取现有研究的工作流模式
   - 识别研究空白和创新机会
   - 生成方法论洞察

2. **多专家评审系统 (MultiExpertReviewSystem)**
   - AI技术专家：技术创新和算法贡献
   - 神经科学专家：生物合理性和机制分析
   - 数据分析专家：实验设计和统计分析
   - 实验设计专家：研究方法和可重现性
   - 学术写作专家：表达清晰度和组织结构

3. **自动修订引擎 (AutoRevisionEngine)**
   - 基于评审反馈的智能修订
   - 多种修订策略：重写、增强、重构、添加内容
   - 质量改进跟踪和评估

4. **增强论文撰写器 (EnhancedBrainPaperWriter)**
   - 集成所有组件的主控制器
   - 质量控制循环管理
   - 多格式输出生成

### 工作流程

```
研究主题输入
    ↓
工作流提取和分析
    ↓
初始论文生成
    ↓
多专家评审 ←→ 自动修订 (迭代循环)
    ↓
质量达标判断
    ↓
多格式输出生成
    ↓
综合分析报告
```

## ⚙️ 配置选项

### PaperGenerationConfig

```python
config = PaperGenerationConfig(
    # 基础设置
    target_venue="ICML",           # ICML, NeurIPS, ICLR, AAAI 等
    paper_type="research",         # research, survey, position
    
    # 质量控制
    max_review_iterations=3,       # 最大评审轮次 (1-5)
    quality_threshold=7.0,         # 质量阈值 (5.0-9.0)
    
    # 功能开关
    enable_multi_expert_review=True,  # 多专家评审
    enable_auto_revision=True,        # 自动修订
    latex_output=True,                # LaTeX输出
)
```

### UnifiedWorkflowConfig

```python
unified_config = UnifiedWorkflowConfig(
    # 工作流设置
    enable_workflow_extraction=True,
    workflow_analysis_depth="comprehensive",  # basic, standard, comprehensive
    
    # 输出设置
    output_formats=['markdown', 'latex', 'json'],
    save_intermediate_results=True,
    output_directory="output"
)
```

## 📈 质量评估体系

### 评审维度

1. **新颖性 (25%权重)**
   - 研究贡献的原创性
   - 创新思路和突破性见解

2. **技术质量 (25%权重)**
   - 方法论的严谨性
   - 实验设计和执行质量

3. **清晰度 (20%权重)**
   - 写作质量和组织结构
   - 图表的清晰性和信息量

4. **重要性 (15%权重)**
   - 对领域的贡献重要性
   - 对未来研究的潜在影响

5. **可重现性 (15%权重)**
   - 方法描述的清晰度
   - 实现细节的可获得性

### 质量阈值

- **9.0-10.0**: 顶级论文，重大贡献
- **7.5-8.9**: 优秀论文，值得发表
- **6.5-7.4**: 良好论文，小修后接受
- **5.5-6.4**: 需要大幅修订
- **<5.5**: 质量不足，需重新设计

## 🔧 高级功能

### 1. 自定义专家权重

```python
# 为特定领域调整专家权重
custom_weights = {
    'ai_technology': 0.3,
    'neuroscience': 0.3,
    'data_analysis': 0.2,
    'experiment_design': 0.15,
    'paper_writing': 0.05
}
```

### 2. 修订策略控制

```python
revision_strategies = {
    'novelty': 'enhance',        # enhance, rewrite, restructure
    'technical_quality': 'restructure',
    'clarity': 'rewrite',
    'significance': 'enhance',
    'reproducibility': 'add_content'
}
```

### 3. 输出自定义

```python
# 自定义输出格式
output_config = {
    'markdown': {
        'include_metrics': True,
        'include_review_history': True,
        'include_workflow_insights': True
    },
    'latex': {
        'template': 'icml2024',
        'include_bibliography': True,
        'compile_pdf': False
    },
    'json': {
        'include_raw_data': True,
        'compact_format': False
    }
}
```

## 📋 最佳实践

### 1. 研究主题描述

✅ **好的例子**:
```
"Brain-Inspired Continual Learning for Dynamic Environments: 
A Meta-Learning Approach with Synaptic Plasticity Mechanisms"
```

❌ **避免的例子**:
```
"Machine Learning"  # 太宽泛
"New Algorithm"     # 不具体
```

### 2. 研究上下文提供

```python
# 提供丰富的上下文信息
research_context = {
    "motivation": "具体的问题陈述",
    "related_work": ["关键参考文献"],
    "datasets": ["使用的数据集"],
    "baselines": ["对比方法"],
    "metrics": ["评估指标"],
    "constraints": ["限制条件"]
}
```

### 3. 质量控制策略

- 设置合理的质量阈值 (6.5-7.5 for most venues)
- 允许2-3轮评审迭代
- 监控改进历史，避免过度修订
- 定期保存中间结果

## 🐛 故障排除

### 常见问题

1. **生成质量低**
   - 检查研究主题描述是否具体
   - 增加研究上下文信息
   - 适当降低质量阈值

2. **评审失败**
   - 确认LLM客户端配置正确
   - 检查网络连接
   - 启用备用评审机制

3. **修订效果差**
   - 增加修订策略多样性
   - 调整专家权重分配
   - 检查评审反馈质量

4. **输出格式错误**
   - 确认输出目录可写
   - 检查LaTeX依赖安装
   - 验证JSON序列化兼容性

### 调试模式

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 保存中间结果
config.save_intermediate_results = True

# 单步执行
workflow.debug_mode = True
```

## 📚 扩展开发

### 自定义专家

```python
class CustomExpert:
    def analyze(self, input_data):
        # 实现自定义分析逻辑
        return analysis_result

# 注册自定义专家
agent_manager.register_agent('custom_expert', CustomExpert())
```

### 自定义评审标准

```python
custom_criteria = {
    'domain_relevance': ReviewCriterion(
        name='Domain Relevance',
        description='Relevance to brain-inspired computing',
        weight=0.2
    )
}
```

### 自定义输出格式

```python
class CustomFormatter:
    def format_paper(self, paper_content):
        # 实现自定义格式化
        return formatted_output
```

## 📞 支持和贡献

- **问题报告**: 在项目Issues中提交
- **功能请求**: 通过Pull Request贡献
- **文档改进**: 欢迎提交文档修正

## 📄 许可证

本项目遵循项目根目录的LICENSE文件中的许可条款。

---

*更新时间: 2024-12-28*  
*版本: v2.0 - Enhanced Paper Writing System*
