"""
快速测试LaTeX格式专家
"""

from paper_generation.latex_format_expert_clean import LaTeXFormatExpert

def test_latex_expert():
    print("🧪 快速测试LaTeX格式专家")
    
    test_content = """
\\documentclass{article}
\\usepackage{amsmath}
\\begin{document}
\\title{Brain-Inspired Artificial Intelligence: A Novel Approach}
\\author{Test Author}
\\begin{abstract}
This paper presents a novel brain-inspired artificial intelligence approach.
\\end{abstract}
\\section{Introduction}
This is the introduction.
\\end{document}
"""
    
    try:
        expert = LaTeXFormatExpert()
        result = expert.optimize_latex_format(test_content, 'ICML')
        
        print(f"✅ LaTeX专家测试成功")
        print(f"   📊 发现问题: {len(result.issues_found)}")
        print(f"   🔧 修复问题: {len(result.issues_fixed)}")
        print(f"   📈 质量分数: {result.quality_score:.1f}/10")
        print(f"   🎯 会议合规性: {sum(result.venue_compliance.values())}/{len(result.venue_compliance)} 项")
        
        return True
        
    except Exception as e:
        print(f"❌ LaTeX专家测试失败: {e}")
        return False

if __name__ == "__main__":
    test_latex_expert()
