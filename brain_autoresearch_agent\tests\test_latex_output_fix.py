"""
LaTeX输出修复验证测试
专门测试修复后的BrainPaperWriter是否正确生成LaTeX格式，无元组泄露
"""

import os
import sys
import time
import re
from datetime import datetime

# 添加项目根路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from paper_generation.brain_paper_writer import BrainPaperWriter
from paper_generation.latex_generator import LaTeXGenerator


def test_latex_generator_standalone():
    """测试LaTeX生成器独立功能"""
    print("\n🧪 测试LaTeX生成器独立功能")
    print("-" * 50)
    
    try:
        latex_gen = LaTeXGenerator()
        
        # 测试样本论文数据
        sample_paper = {
            'title': 'Brain-Inspired Neural Network Architectures',
            'abstract': 'This paper presents novel brain-inspired neural network architectures.',
            'introduction': 'Brain-inspired computing has emerged as a promising paradigm.',
            'related_work': 'Previous work in neuromorphic computing includes...',
            'methodology': 'Our approach incorporates three key principles...',
            'experiments': 'We evaluated our method on standard benchmarks...',
            'results': 'The experimental results demonstrate significant improvements...',
            'discussion': 'These findings suggest that brain-inspired approaches...',
            'conclusion': 'In conclusion, this work presents a novel framework...'
        }
        
        # 测试不同会议模板
        venues = ['ICML', 'NeurIPS', 'ICLR', 'generic']
        
        for venue in venues:
            print(f"  📄 测试 {venue} 模板...")
            latex_content = latex_gen.generate_latex_paper(sample_paper, venue)
            
            # 验证LaTeX内容
            if latex_content and len(latex_content) > 1000:
                # 检查是否包含基本LaTeX结构
                if '\\documentclass' in latex_content and '\\begin{document}' in latex_content:
                    print(f"    ✅ {venue} 模板生成成功 ({len(latex_content)} 字符)")
                else:
                    print(f"    ⚠️ {venue} 模板缺少LaTeX结构")
            else:
                print(f"    ❌ {venue} 模板生成失败")
        
        return True
        
    except Exception as e:
        print(f"❌ LaTeX生成器测试失败: {e}")
        return False


def test_safe_string_extraction():
    """测试_safe_string_extract方法是否正确处理各种输入"""
    print("\n🧪 测试字符串提取安全机制")
    print("-" * 50)
    
    try:
        writer = BrainPaperWriter()
        
        # 测试各种输入类型
        test_cases = [
            ("普通字符串", "test string", "test string"),
            ("元组输入", ("content", "extra"), "content"),
            ("字典输入", {"content": "dict content"}, "dict content"),
            ("空输入", None, ""),
            ("复杂元组", ("main content", [{"role": "user", "content": "prompt"}]), "main content"),
        ]
        
        all_passed = True
        for test_name, input_obj, expected in test_cases:
            result = writer._safe_string_extract(input_obj)
            if result == expected:
                print(f"  ✅ {test_name}: 通过")
            else:
                print(f"  ❌ {test_name}: 失败 (期望: {expected}, 实际: {result})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 字符串提取测试失败: {e}")
        return False


def test_generate_methods_output():
    """测试_generate_*方法是否返回纯字符串而非元组"""
    print("\n🧪 测试生成方法输出格式")
    print("-" * 50)
    
    try:
        writer = BrainPaperWriter()
        
        # 准备测试用的虚拟数据
        sample_structure = {
            'research_topic': 'brain-inspired computing',
            'target_venue': 'ICML'
        }
        sample_analysis = {
            'research_significance': 'Novel approach to bio-inspired AI',
            'value_assessment': {'innovation': 0.8, 'feasibility': 0.7}
        }
        sample_literature = {
            'key_insights': {},
            'research_gaps': {}
        }
        
        # 测试各个生成方法
        methods_to_test = [
            ('_generate_abstract', [sample_structure, sample_analysis]),
            ('_generate_introduction', [sample_literature, sample_analysis]),
            ('_generate_related_work', [sample_literature]),
            ('_generate_methodology', [sample_analysis]),
            ('_generate_experiments', [sample_analysis]),
            ('_generate_results', [sample_analysis]),
            ('_generate_conclusion', [sample_analysis])
        ]
        
        all_passed = True
        for method_name, args in methods_to_test:
            print(f"  🔍 测试 {method_name}...")
            
            try:
                method = getattr(writer, method_name)
                result = method(*args)
                
                # 检查输出类型
                if isinstance(result, str):
                    # 检查是否包含Python元组字符串
                    if "('content'," in result or "('analysis'," in result:
                        print(f"    ⚠️ {method_name}: 包含疑似元组泄露")
                        all_passed = False
                    elif len(result) > 50:  # 合理的内容长度
                        print(f"    ✅ {method_name}: 返回纯字符串 ({len(result)} 字符)")
                    else:
                        print(f"    ⚠️ {method_name}: 内容过短 ({len(result)} 字符)")
                else:
                    print(f"    ❌ {method_name}: 返回类型错误 ({type(result)})")
                    all_passed = False
                    
            except Exception as e:
                print(f"    ❌ {method_name}: 执行失败 - {e}")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 生成方法测试失败: {e}")
        return False


def test_integrated_paper_generation():
    """测试集成后的论文生成是否正确产生LaTeX输出"""
    print("\n🧪 测试集成论文生成")
    print("-" * 50)
    
    try:
        writer = BrainPaperWriter()
        
        # 使用简单的研究主题进行快速测试
        research_topic = "energy-efficient neuromorphic computing"
        target_venue = "ICML"
        
        print(f"  📝 生成主题: {research_topic}")
        print(f"  🎯 目标会议: {target_venue}")
        
        # 生成论文
        start_time = time.time()
        result = writer.generate_paper(
            research_topic=research_topic,
            target_venue=target_venue
        )
        generation_time = time.time() - start_time
        
        print(f"  ⏱️ 生成耗时: {generation_time:.1f} 秒")
        
        # 验证结果结构
        required_fields = ['title', 'abstract', 'introduction', 'methodology', 
                          'experiments', 'results', 'conclusion', 'latex']
        
        missing_fields = []
        for field in required_fields:
            if field not in result or not result[field]:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"    ⚠️ 缺失字段: {missing_fields}")
        else:
            print(f"    ✅ 所有必需字段存在")
        
        # 验证LaTeX输出
        latex_content = result.get('latex', '')
        if latex_content:
            if '\\documentclass' in latex_content and '\\begin{document}' in latex_content:
                print(f"    ✅ LaTeX格式正确 ({len(latex_content)} 字符)")
                
                # 检查是否有元组泄露
                if "('content'," in latex_content or "('analysis'," in latex_content:
                    print(f"    ⚠️ LaTeX中发现元组泄露")
                    return False
                else:
                    print(f"    ✅ LaTeX内容干净，无元组泄露")
            else:
                print(f"    ❌ LaTeX格式不正确")
                return False
        else:
            print(f"    ❌ 未生成LaTeX内容")
            return False
        
        # 保存测试输出
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"test_paper_{timestamp}.tex"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(latex_content)
        print(f"    💾 测试论文保存至: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_fix_tests():
    """运行所有修复验证测试"""
    print("🔧 LaTeX输出修复验证测试")
    print("=" * 80)
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("LaTeX生成器独立功能", test_latex_generator_standalone),
        ("字符串提取安全机制", test_safe_string_extraction),
        ("生成方法输出格式", test_generate_methods_output),
        ("集成论文生成", test_integrated_paper_generation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔄 运行测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = "PASS" if result else "FAIL"
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  结果: {status}")
        except Exception as e:
            results[test_name] = f"ERROR: {e}"
            print(f"  结果: ❌ ERROR - {e}")
    
    # 测试总结
    print("\n" + "=" * 80)
    print("📊 修复验证测试总结")
    print("=" * 80)
    
    passed = sum(1 for r in results.values() if r == "PASS")
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅" if result == "PASS" else "❌"
        print(f"{status} {test_name}: {result}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有修复验证通过! LaTeX输出系统正常工作.")
    else:
        print("⚠️ 部分测试失败，需要进一步修复.")
    
    return results


if __name__ == "__main__":
    results = run_all_fix_tests()
    
    # 保存测试结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"latex_fix_test_results_{timestamp}.json"
    
    import json
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump({
            'test_timestamp': datetime.now().isoformat(),
            'test_results': results,
            'summary': {
                'total_tests': len(results),
                'passed_tests': sum(1 for r in results.values() if r == "PASS"),
                'failed_tests': sum(1 for r in results.values() if r != "PASS")
            }
        }, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 测试结果保存至: {results_file}")
