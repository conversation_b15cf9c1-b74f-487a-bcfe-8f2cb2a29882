"""
AI Scientist v2集成论文写作器

集成AI-Scientist-v2的功能，增强论文生成流程
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
import importlib.util
import shutil

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from paper_generation.brain_paper_writer import BrainPaperWriter
from paper_generation.latex_format_expert import LaTeXFormatExpert
from paper_generation.enhanced_citation_manager import EnhancedCitationManager
from paper_generation.multi_expert_review_system_v2 import MultiExpertReviewSystemV2
from core.unified_api_client import get_unified_client
from paper_generation.improved_latex_generator import ImprovedLaTeXGenerator

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('AIScientistV2Writer')

class AIScientistV2IntegratedWriter:
    """
    集成AI Scientist v2的论文写作器
    
    融合brain_autoresearch_agent和AI-Scientist-v2两个项目的功能
    """
    
    def __init__(self, ai_scientist_path: Optional[str] = None):
        """
        初始化写作器
        
        Args:
            ai_scientist_path: AI-Scientist-v2项目路径
        """
        self.api_client = get_unified_client()
        
        # 初始化核心组件
        self.brain_writer = BrainPaperWriter(model="deepseek-chat", temperature=0.7)
        self.latex_expert = LaTeXFormatExpert(self.api_client)
        self.citation_manager = EnhancedCitationManager(self.api_client)
        self.review_system = MultiExpertReviewSystemV2(self.api_client)
        self.latex_generator = ImprovedLaTeXGenerator()
        
        # AI Scientist v2集成
        self.ai_scientist_path = ai_scientist_path or os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
            'AI-Scientist-v2'
        )
        
        self.ai_scientist_available = self._check_ai_scientist_availability()
        self.ai_scientist_modules = {}
        
        if self.ai_scientist_available:
            self._load_ai_scientist_modules()
        
        # 输出路径
        self.output_dir = "output/integrated_papers"
        os.makedirs(self.output_dir, exist_ok=True)
        
        logger.info("✅ AI Scientist v2集成论文写作器初始化完成")
        if self.ai_scientist_available:
            logger.info("✅ AI Scientist v2模块加载成功")
        else:
            logger.warning("⚠️ AI Scientist v2模块未找到或加载失败")
    
    def _check_ai_scientist_availability(self) -> bool:
        """检查AI Scientist v2是否可用"""
        return os.path.exists(self.ai_scientist_path) and \
               os.path.exists(os.path.join(self.ai_scientist_path, 'ai_scientist'))
    
    def _load_ai_scientist_modules(self) -> None:
        """加载AI Scientist v2模块"""
        try:
            # 添加AI Scientist路径到系统路径
            if self.ai_scientist_path not in sys.path:
                sys.path.append(self.ai_scientist_path)
            
            # 加载核心模块
            self._load_module('perform_icbinb_writeup', 
                           os.path.join(self.ai_scientist_path, 'ai_scientist', 'perform_icbinb_writeup.py'))
            
            self._load_module('perform_writeup',
                           os.path.join(self.ai_scientist_path, 'ai_scientist', 'perform_writeup.py'))
            
            self._load_module('llm',
                           os.path.join(self.ai_scientist_path, 'ai_scientist', 'llm.py'))
            
            logger.info("✅ AI Scientist v2核心模块加载成功")
        except Exception as e:
            logger.error(f"❌ AI Scientist v2模块加载失败: {e}")
            self.ai_scientist_available = False
    
    def _load_module(self, name: str, path: str) -> None:
        """加载单个模块"""
        try:
            spec = importlib.util.spec_from_file_location(name, path)
            if spec and spec.loader:
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                self.ai_scientist_modules[name] = module
                logger.info(f"✅ 模块 {name} 加载成功")
            else:
                logger.warning(f"⚠️ 无法为 {name} 创建规范")
        except Exception as e:
            logger.error(f"❌ 模块 {name} 加载失败: {e}")
    
    def generate_paper(self, 
                     research_topic: str, 
                     target_venue: str = "ICML",
                     use_ai_scientist: bool = True,
                     paper_type: str = "research",
                     optimize_paper: bool = True) -> Dict[str, Any]:
        """
        生成论文
        
        Args:
            research_topic: 研究主题
            target_venue: 目标会议
            use_ai_scientist: 是否使用AI Scientist v2
            paper_type: 论文类型
            optimize_paper: 是否优化论文
            
        Returns:
            生成的论文
        """
        logger.info(f"🚀 开始为主题 '{research_topic}' 生成论文")
        logger.info(f"📝 目标会议: {target_venue}, 论文类型: {paper_type}")
        logger.info(f"🔧 使用AI Scientist v2: {use_ai_scientist}, 优化论文: {optimize_paper}")
        
        result = {
            'title': '',
            'paper_content': {},
            'latex_content': '',
            'quality_score': 0.0,
            'optimization_results': None,
            'review_results': None,
            'output_files': {},
            'generation_time': datetime.now().isoformat(),
            'use_ai_scientist': use_ai_scientist,
            'target_venue': target_venue
        }
        
        try:
            # 根据设置选择使用哪个引擎生成论文
            if use_ai_scientist and self.ai_scientist_available:
                paper_content = self._generate_with_ai_scientist(research_topic, target_venue)
            else:
                paper_content = self._generate_with_brain_writer(research_topic, target_venue, paper_type)
            
            if not paper_content:
                logger.error("❌ 论文生成失败")
                return result
            
            # 提取标题
            title = paper_content.get('title', research_topic)
            result['title'] = title
            result['paper_content'] = paper_content
            
            # 生成LaTeX
            logger.info("📄 生成LaTeX内容...")
            latex_content = self.latex_generator.generate_latex_paper(
                paper_content, venue=target_venue
            )
            result['latex_content'] = latex_content
            
            # 保存初始LaTeX
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_title = self._safe_filename(title)
            initial_latex_file = os.path.join(self.output_dir, f"{safe_title}_{timestamp}_initial.tex")
            with open(initial_latex_file, 'w', encoding='utf-8') as f:
                f.write(latex_content)
            result['output_files']['initial_latex'] = initial_latex_file
            
            # 评审论文
            logger.info("🔍 进行论文评审...")
            paper_metadata = {
                'title': title,
                'target_venue': target_venue,
                'abstract': paper_content.get('abstract', '')
            }
            
            review_result = self.review_system.conduct_review_sync(
                latex_content, paper_metadata
            )
            result['review_results'] = review_result
            result['quality_score'] = review_result.get('consensus_score', 0.0)
            
            logger.info(f"📊 初始质量评分: {result['quality_score']:.2f}/10")
            
            # 如果需要优化
            if optimize_paper:
                logger.info("🔧 开始论文优化...")
                optimization_result = self._optimize_paper(latex_content, paper_metadata)
                
                if optimization_result and optimization_result.get('success'):
                    result['optimization_results'] = optimization_result
                    result['quality_score'] = optimization_result.get('final_quality', result['quality_score'])
                    
                    # 更新LaTeX内容
                    if 'optimized_content' in optimization_result:
                        result['latex_content'] = optimization_result['optimized_content']
                        
                    # 添加输出文件
                    if 'output_files' in optimization_result:
                        for file_type, file_path in optimization_result['output_files'].items():
                            result['output_files'][file_type] = file_path
                            
                    logger.info(f"📊 优化后质量评分: {result['quality_score']:.2f}/10")
                else:
                    logger.warning("⚠️ 论文优化失败，使用初始版本")
            
            # 保存最终结果
            final_output = {
                'title': result['title'],
                'target_venue': target_venue,
                'quality_score': result['quality_score'],
                'generation_time': result['generation_time'],
                'use_ai_scientist': use_ai_scientist,
                'paper_content': result['paper_content'],
                'review_summary': review_result.get('review_summary', ''),
                'quality_metrics': review_result.get('quality_metrics', {})
            }
            
            final_file = os.path.join(self.output_dir, f"{safe_title}_{timestamp}_complete.json")
            with open(final_file, 'w', encoding='utf-8') as f:
                json.dump(final_output, f, ensure_ascii=False, indent=2, default=str)
            result['output_files']['complete_result'] = final_file
            
            # 保存最终LaTeX
            final_latex_file = os.path.join(self.output_dir, f"{safe_title}_{timestamp}_final.tex")
            with open(final_latex_file, 'w', encoding='utf-8') as f:
                f.write(result['latex_content'])
            result['output_files']['final_latex'] = final_latex_file
            
            logger.info("🎉 论文生成完成")
            logger.info(f"📄 标题: {result['title']}")
            logger.info(f"📊 最终质量评分: {result['quality_score']:.2f}/10")
            logger.info(f"💾 结果保存在: {self.output_dir}")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 论文生成过程中发生错误: {e}")
            import traceback
            logger.error(traceback.format_exc())
            
            result['error'] = str(e)
            return result
    
    def _generate_with_brain_writer(self, research_topic: str, 
                                  target_venue: str, 
                                  paper_type: str) -> Dict[str, Any]:
        """使用Brain Writer生成论文"""
        logger.info("🧠 使用Brain Writer生成论文...")
        
        try:
            paper = self.brain_writer.generate_paper(
                research_topic=research_topic,
                target_venue=target_venue,
                paper_type=paper_type
            )
            
            logger.info("✅ Brain Writer论文生成完成")
            return paper
            
        except Exception as e:
            logger.error(f"❌ Brain Writer生成失败: {e}")
            return {}
    
    def _generate_with_ai_scientist(self, research_topic: str, target_venue: str) -> Dict[str, Any]:
        """使用AI Scientist v2生成论文"""
        logger.info("🔬 使用AI Scientist v2生成论文...")
        
        try:
            # 检查模块是否加载
            if not self.ai_scientist_modules.get('perform_icbinb_writeup'):
                logger.error("❌ AI Scientist v2 perform_icbinb_writeup模块未加载")
                return self._generate_with_brain_writer(research_topic, target_venue, "research")
                
            # 准备参数
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_dir = os.path.join(self.output_dir, f"ai_scientist_{timestamp}")
            os.makedirs(temp_dir, exist_ok=True)
            
            # 准备输入
            input_data = {
                "idea": research_topic,
                "output_dir": temp_dir
            }
            
            # 调用AI Scientist生成
            writeup_module = self.ai_scientist_modules['perform_icbinb_writeup']
            
            # 修改API密钥
            llm_module = self.ai_scientist_modules.get('llm')
            if llm_module:
                # 保存原始API密钥
                original_api_key = getattr(llm_module, 'ANTHROPIC_API_KEY', None)
                # 设置为DeepSeek API密钥
                setattr(llm_module, 'ANTHROPIC_API_KEY', self.api_client.deepseek_api_key)
            
            try:
                # 执行论文生成
                writeup_module.main(research_topic, temp_dir)
                
                # 恢复原始API密钥
                if llm_module and original_api_key:
                    setattr(llm_module, 'ANTHROPIC_API_KEY', original_api_key)
                
                # 加载结果
                json_file = os.path.join(temp_dir, "complete_result.json")
                tex_file = os.path.join(temp_dir, "paper.tex")
                
                paper_content = {}
                
                # 读取JSON结果
                if os.path.exists(json_file):
                    with open(json_file, 'r', encoding='utf-8') as f:
                        paper_content = json.load(f)
                
                # 如果没有JSON或JSON不完整，尝试从TEX读取
                if not paper_content or 'paper_content' not in paper_content:
                    if os.path.exists(tex_file):
                        with open(tex_file, 'r', encoding='utf-8') as f:
                            tex_content = f.read()
                        paper_content = self._extract_content_from_tex(tex_content, research_topic)
                
                # 添加目标会议
                paper_content['metadata'] = paper_content.get('metadata', {})
                paper_content['metadata']['target_venue'] = target_venue
                
                # 复制生成的文件到输出目录
                for filename in os.listdir(temp_dir):
                    if filename.endswith(('.tex', '.json')):
                        src = os.path.join(temp_dir, filename)
                        dst = os.path.join(self.output_dir, f"ai_scientist_{timestamp}_{filename}")
                        shutil.copy2(src, dst)
                
                logger.info("✅ AI Scientist v2论文生成完成")
                return paper_content
                
            except Exception as e:
                logger.error(f"❌ AI Scientist v2执行失败: {e}")
                
                # 恢复原始API密钥
                if llm_module and original_api_key:
                    setattr(llm_module, 'ANTHROPIC_API_KEY', original_api_key)
                
                # 回退到Brain Writer
                return self._generate_with_brain_writer(research_topic, target_venue, "research")
                
        except Exception as e:
            logger.error(f"❌ AI Scientist v2集成失败: {e}")
            # 回退到Brain Writer
            return self._generate_with_brain_writer(research_topic, target_venue, "research")
    
    def _extract_content_from_tex(self, tex_content: str, research_topic: str) -> Dict[str, Any]:
        """从TEX文件提取内容"""
        paper_content = {
            'title': research_topic,
            'abstract': '',
            'introduction': '',
            'methodology': '',
            'experiments': '',
            'results': '',
            'discussion': '',
            'conclusion': '',
            'references': '',
            'metadata': {
                'generation_time': datetime.now().isoformat()
            }
        }
        
        # 提取标题
        title_match = re.search(r'\\title\{(.*?)\}', tex_content, re.DOTALL)
        if title_match:
            paper_content['title'] = title_match.group(1).strip()
        
        # 提取摘要
        abstract_match = re.search(r'\\begin\{abstract\}(.*?)\\end\{abstract\}', tex_content, re.DOTALL)
        if abstract_match:
            paper_content['abstract'] = abstract_match.group(1).strip()
        
        # 提取章节
        sections = {
            'introduction': r'\\section\{(?:Introduction|INTRODUCTION)\}(.*?)(?=\\section\{|\\end\{document\})',
            'background': r'\\section\{(?:Background|BACKGROUND|Related Work|RELATED WORK)\}(.*?)(?=\\section\{|\\end\{document\})',
            'methodology': r'\\section\{(?:Method|METHOD|Methodology|METHODOLOGY|Approach|APPROACH)\}(.*?)(?=\\section\{|\\end\{document\})',
            'experiments': r'\\section\{(?:Experiments|EXPERIMENTS|Experimental Results)\}(.*?)(?=\\section\{|\\end\{document\})',
            'results': r'\\section\{(?:Results|RESULTS)\}(.*?)(?=\\section\{|\\end\{document\})',
            'discussion': r'\\section\{(?:Discussion|DISCUSSION)\}(.*?)(?=\\section\{|\\end\{document\})',
            'conclusion': r'\\section\{(?:Conclusion|CONCLUSION|Conclusions|CONCLUSIONS)\}(.*?)(?=\\section\{|\\end\{document\})',
        }
        
        for section, pattern in sections.items():
            match = re.search(pattern, tex_content, re.DOTALL)
            if match:
                paper_content[section] = match.group(1).strip()
        
        # 提取参考文献
        bib_match = re.search(r'\\begin\{thebibliography\}(.*?)\\end\{thebibliography\}', tex_content, re.DOTALL)
        if bib_match:
            paper_content['references'] = bib_match.group(1).strip()
        
        return paper_content
    
    def _optimize_paper(self, latex_content: str, paper_metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        优化论文
        
        Args:
            latex_content: LaTeX内容
            paper_metadata: 论文元数据
            
        Returns:
            优化结果
        """
        try:
            # 导入异步优化器
            from paper_generation.paper_quality_optimizer import PaperQualityOptimizer
            import asyncio
            
            # 创建优化器
            optimizer = PaperQualityOptimizer(self.api_client)
            
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 执行优化
                optimization_result = loop.run_until_complete(
                    optimizer.optimize_paper(
                        latex_content,
                        paper_metadata,
                        output_dir=os.path.join(self.output_dir, "optimized"),
                        target_venue=paper_metadata.get('target_venue', 'ICML')
                    )
                )
                
                return optimization_result
                
            finally:
                # 关闭事件循环
                loop.close()
                
        except Exception as e:
            logger.error(f"❌ 论文优化失败: {e}")
            return None
    
    def _safe_filename(self, title: str) -> str:
        """创建安全的文件名"""
        import re
        safe_title = re.sub(r'[^\w\s-]', '', title)
        safe_title = re.sub(r'[-\s]+', '-', safe_title)
        return safe_title[:50]  # 限制长度
    
    def create_icbinb_paper(self, research_topic: str, target_venue: str = "ICML") -> Dict[str, Any]:
        """
        创建"I Can't Believe It's Not Better"类型的论文
        
        Args:
            research_topic: 研究主题
            target_venue: 目标会议
            
        Returns:
            生成的论文
        """
        logger.info(f"🔬 创建ICBINB论文: '{research_topic}'")
        
        # 检查是否可用
        if not self.ai_scientist_available or 'perform_icbinb_writeup' not in self.ai_scientist_modules:
            logger.warning("⚠️ AI Scientist v2 ICBINB模块不可用，使用标准模式")
            return self.generate_paper(research_topic, target_venue, use_ai_scientist=False)
        
        # 使用AI Scientist v2
        return self.generate_paper(research_topic, target_venue, use_ai_scientist=True)
        
    def integrate_experiment_code(self, paper_content: Dict[str, Any], 
                               experiment_code: Dict[str, Any]) -> Dict[str, Any]:
        """
        将实验代码集成到论文中
        
        Args:
            paper_content: 论文内容
            experiment_code: 实验代码
            
        Returns:
            更新后的论文内容
        """
        logger.info("🧪 将实验代码集成到论文中...")
        
        # 复制论文内容
        updated_content = paper_content.copy()
        
        # 更新实验部分
        if 'experiments' in updated_content and 'code_implementation' in experiment_code:
            experiment_section = updated_content['experiments']
            code_details = experiment_code.get('code_implementation', {}).get('description', '')
            
            # 添加代码实现细节
            code_section = "\n\n\\subsection{Implementation Details}\n"
            code_section += code_details
            
            updated_content['experiments'] = experiment_section + code_section
        
        # 更新结果部分
        if 'results' in updated_content and 'experimental_results' in experiment_code:
            results_section = updated_content['results']
            exp_results = experiment_code.get('experimental_results', {}).get('description', '')
            
            # 添加实验结果
            results_update = "\n\n\\subsection{Experimental Outcomes}\n"
            results_update += exp_results
            
            updated_content['results'] = results_section + results_update
        
        logger.info("✅ 实验代码已集成到论文中")
        return updated_content
    
    def merge_icbinb_sections(self, target_file: str, icbinb_content: Dict[str, Any]) -> None:
        """
        合并ICBINB内容到目标文件
        
        Args:
            target_file: 目标文件
            icbinb_content: ICBINB内容
        """
        if not os.path.exists(target_file):
            logger.error(f"❌ 目标文件不存在: {target_file}")
            return
        
        try:
            # 读取目标文件
            with open(target_file, 'r', encoding='utf-8') as f:
                tex_content = f.read()
            
            # 查找结论部分
            conclusion_match = re.search(r'(\\section\{(?:Conclusion|CONCLUSION|Conclusions|CONCLUSIONS)\}.*?)(?=\\section\{|\\end\{document\}|$)', tex_content, re.DOTALL)
            
            if conclusion_match:
                # 准备ICBINB部分
                icbinb_section = "\n\n\\section{Why Isn't This Better?}\n"
                icbinb_section += icbinb_content.get('analysis', 'This approach seems promising but several factors limit its performance.')
                
                # 插入到结论之前
                start_pos = conclusion_match.start()
                new_content = tex_content[:start_pos] + icbinb_section + "\n\n" + tex_content[start_pos:]
                
                # 写回文件
                with open(target_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                logger.info(f"✅ ICBINB部分已添加到 {target_file}")
                
            else:
                logger.warning(f"⚠️ 在 {target_file} 中未找到结论部分")
                
        except Exception as e:
            logger.error(f"❌ 合并ICBINB内容失败: {e}")

# 测试代码
if __name__ == "__main__":
    # 初始化写作器
    writer = AIScientistV2IntegratedWriter()
    
    # 测试论文生成
    print("🧪 测试AI Scientist v2集成论文写作器")
    print("=" * 50)
    
    # 测试主题
    research_topic = "Neural Plasticity-Inspired Meta-Learning for Adaptive Neural Networks"
    
    # 生成论文
    print(f"\n🚀 为主题 '{research_topic}' 生成论文...")
    result = writer.generate_paper(
        research_topic=research_topic,
        target_venue="ICML",
        use_ai_scientist=True,
        optimize_paper=True
    )
    
    # 输出结果
    print("\n📊 生成结果:")
    print(f"📄 标题: {result['title']}")
    print(f"📊 质量评分: {result['quality_score']:.2f}/10")
    print(f"💾 输出文件:")
    for file_type, file_path in result['output_files'].items():
        print(f"   - {file_type}: {file_path}")
    
    print("\n✅ 测试完成")
