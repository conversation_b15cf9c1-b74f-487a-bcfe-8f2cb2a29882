"""
Literature Research Manager for Academic Paper Generation

This module provides advanced literature research capabilities
using Semantic Scholar API and intelligent citation management.
"""

import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging
import re

# Import core tools
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.semantic_scholar_tool import SemanticScholarTool
from core.llm_client import LLMClient


class LiteratureManager:
    """
    Advanced literature research and management system
    
    Provides intelligent literature search, analysis, and citation generation
    for brain-inspired intelligence research papers.
    """
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        Initialize Literature Manager
        
        Args:
            llm_client: LLM client for content analysis
        """
        self.semantic_scholar = SemanticScholarTool()
        self.llm_client = llm_client or LLMClient(model="deepseek-chat")
        self.logger = self._setup_logger()
        
        # Cache for avoiding repeated API calls
        self.paper_cache = {}
        self.search_cache = {}
        
    def _setup_logger(self) -> logging.Logger:
        """Setup logging for literature research"""
        logger = logging.getLogger('LiteratureManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def conduct_comprehensive_review(self, research_topic: str, 
                                   max_papers_per_query: int = 15,
                                   include_recent_only: bool = True) -> Dict[str, Any]:
        """
        Conduct comprehensive literature review for a research topic
        
        Args:
            research_topic: Main research topic
            max_papers_per_query: Maximum papers to retrieve per search query
            include_recent_only: Whether to focus on recent papers (last 5 years)
            
        Returns:
            Comprehensive literature review data
        """
        self.logger.info(f"Starting comprehensive literature review for: {research_topic}")
        
        # Generate intelligent search queries
        search_queries = self._generate_search_strategies(research_topic)
        
        # Search for papers using multiple strategies
        all_papers = {}
        paper_summaries = {}
        
        for query_type, queries in search_queries.items():
            self.logger.info(f"Searching {query_type} papers...")
            category_papers = []
            
            for query in queries:
                try:
                    # Check cache first
                    cache_key = f"{query}_{max_papers_per_query}_{include_recent_only}"
                    if cache_key in self.search_cache:
                        papers = self.search_cache[cache_key]
                    else:
                        papers = self.semantic_scholar.search_papers(
                            query, 
                            max_results=max_papers_per_query
                        )
                        
                        # Filter by recency if requested
                        if include_recent_only:
                            papers = self._filter_recent_papers(papers, years_back=5)
                        
                        # Cache results
                        self.search_cache[cache_key] = papers
                    
                    category_papers.extend(papers)
                    self.logger.info(f"  Found {len(papers)} papers for: {query}")
                    
                    # Rate limiting
                    time.sleep(0.5)
                    
                except Exception as e:
                    self.logger.warning(f"Error searching '{query}': {e}")
                    continue
            
            # Remove duplicates and store
            unique_papers = self._deduplicate_papers(category_papers)
            all_papers[query_type] = unique_papers
            
            # Generate summaries for this category
            if unique_papers:
                summaries = self._analyze_paper_collection(unique_papers, query_type)
                paper_summaries[query_type] = summaries
        
        # Comprehensive analysis
        comprehensive_analysis = self._perform_comprehensive_analysis(
            all_papers, paper_summaries, research_topic
        )
        
        # Generate research landscape map
        research_landscape = self._map_research_landscape(all_papers, research_topic)
        
        # Identify key papers and citations
        key_papers = self._identify_key_papers(all_papers)
        
        # Generate citation recommendations
        citation_recommendations = self._generate_citation_strategy(
            all_papers, research_topic
        )
        
        literature_review = {
            'research_topic': research_topic,
            'search_strategies': search_queries,
            'papers_by_category': all_papers,
            'category_summaries': paper_summaries,
            'comprehensive_analysis': comprehensive_analysis,
            'research_landscape': research_landscape,
            'key_papers': key_papers,
            'citation_recommendations': citation_recommendations,
            'metadata': {
                'total_papers_found': sum(len(papers) for papers in all_papers.values()),
                'search_date': datetime.now().isoformat(),
                'coverage_analysis': self._analyze_coverage(all_papers)
            }
        }
        
        self.logger.info(f"Literature review completed. Found {literature_review['metadata']['total_papers_found']} total papers")
        
        return literature_review
    
    def _generate_search_strategies(self, research_topic: str) -> Dict[str, List[str]]:
        """
        Generate intelligent search strategies for comprehensive coverage
        """
        strategies_prompt = f"""
        Generate comprehensive search strategies for literature review on: "{research_topic}"
        
        Create search queries for these categories:
        1. foundational: Core foundational papers and seminal works
        2. recent_advances: Latest developments and state-of-the-art
        3. methodological: Technical methods and algorithms  
        4. applications: Real-world applications and use cases
        5. neuroscience_basis: Biological and neuroscience foundations
        6. comparative: Comparison studies and benchmarks
        
        For each category, provide 3-4 specific search queries that would find relevant academic papers.
        Focus on terms that appear in paper titles and abstracts.
        
        Return as JSON format:
        {{
            "foundational": ["query1", "query2", ...],
            "recent_advances": ["query1", "query2", ...],
            ...
        }}
        """
        
        try:
            response = self.llm_client.get_response(strategies_prompt)
            
            # Handle tuple response from LLM client
            if isinstance(response, tuple):
                response = response[0] if response else ""
            elif not isinstance(response, str):
                response = str(response)
            
            # Try to parse as JSON
            if response.strip().startswith('{'):
                strategies = json.loads(response)
            else:
                # Fallback parsing
                strategies = self._parse_strategies_from_text(response)
            
            return strategies
            
        except Exception as e:
            self.logger.warning(f"Error generating search strategies: {e}")
            # Fallback strategies
            return self._get_fallback_strategies(research_topic)
    
    def _get_fallback_strategies(self, research_topic: str) -> Dict[str, List[str]]:
        """Fallback search strategies when LLM fails"""
        topic_keywords = research_topic.lower().split()
        
        return {
            'foundational': [
                f"{research_topic} foundations",
                f"{research_topic} survey",
                "brain-inspired computing",
                "neuromorphic computing"
            ],
            'recent_advances': [
                f"{research_topic} 2023",
                f"{research_topic} 2024", 
                f"{research_topic} state-of-the-art",
                f"recent advances {research_topic}"
            ],
            'methodological': [
                f"{research_topic} algorithm",
                f"{research_topic} method",
                f"{research_topic} approach",
                f"{research_topic} framework"
            ],
            'applications': [
                f"{research_topic} application",
                f"{research_topic} real-world",
                f"{research_topic} deployment",
                f"{research_topic} practical"
            ],
            'neuroscience_basis': [
                f"{research_topic} neuroscience",
                f"{research_topic} biological",
                f"{research_topic} brain",
                "neural networks biological"
            ],
            'comparative': [
                f"{research_topic} comparison",
                f"{research_topic} benchmark",
                f"{research_topic} evaluation",
                f"{research_topic} performance"
            ]
        }
    
    def _parse_strategies_from_text(self, text: str) -> Dict[str, List[str]]:
        """Parse search strategies from text response"""
        strategies = {}
        current_category = None
        
        lines = text.split('\\n')
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Check for category headers
            if any(cat in line.lower() for cat in ['foundational', 'recent', 'methodological', 'applications', 'neuroscience', 'comparative']):
                for cat in ['foundational', 'recent_advances', 'methodological', 'applications', 'neuroscience_basis', 'comparative']:
                    if cat.replace('_', ' ') in line.lower() or cat in line.lower():
                        current_category = cat
                        strategies[cat] = []
                        break
            elif current_category and (line.startswith('-') or line.startswith('•') or line.startswith('"')):
                # Extract query
                query = line.lstrip('-•').strip().strip('"')
                if query:
                    strategies[current_category].append(query)
        
        return strategies
    
    def _filter_recent_papers(self, papers: List[Dict], years_back: int = 5) -> List[Dict]:
        """Filter papers to include only recent ones"""
        current_year = datetime.now().year
        cutoff_year = current_year - years_back
        
        recent_papers = []
        for paper in papers:
            try:
                year = paper.get('year')
                if year and int(year) >= cutoff_year:
                    recent_papers.append(paper)
            except (ValueError, TypeError):
                # Include papers with unknown years
                recent_papers.append(paper)
        
        return recent_papers
    
    def _deduplicate_papers(self, papers: List[Dict]) -> List[Dict]:
        """Remove duplicate papers based on title similarity"""
        if not papers:
            return []
        
        unique_papers = []
        seen_titles = set()
        
        for paper in papers:
            title = paper.get('title', '').lower().strip()
            if not title:
                continue
                
            # Simple deduplication by title
            title_words = set(title.split())
            is_duplicate = False
            
            for seen_title in seen_titles:
                seen_words = set(seen_title.split())
                # Check for high overlap
                overlap = len(title_words & seen_words)
                if overlap > 0.8 * min(len(title_words), len(seen_words)):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_papers.append(paper)
                seen_titles.add(title)
        
        return unique_papers
    
    def _analyze_paper_collection(self, papers: List[Dict], category: str) -> Dict[str, Any]:
        """Analyze a collection of papers for insights"""
        if not papers:
            return {'summary': 'No papers found', 'key_insights': []}
        
        # Extract key information
        titles = [p.get('title', '') for p in papers if p.get('title')]
        abstracts = [p.get('abstract', '') for p in papers if p.get('abstract')]
        years = [p.get('year') for p in papers if p.get('year')]
        
        # Generate analysis using LLM
        analysis_prompt = f"""
        Analyze this collection of {category} papers for brain-inspired intelligence research:
        
        Paper Titles: {json.dumps(titles[:20], indent=2)}
        
        Sample Abstracts: {json.dumps(abstracts[:5], indent=2)}
        
        Publication Years: {years}
        
        Provide:
        1. Key research themes and trends
        2. Most influential approaches or methods
        3. Research gaps or opportunities
        4. Evolution of the field over time
        5. Recommended papers for citation
        
        Be concise and focus on actionable insights.
        """
        
        try:
            analysis = self.llm_client.get_response(analysis_prompt)
            
            return {
                'category': category,
                'paper_count': len(papers),
                'year_range': f"{min(years) if years else 'Unknown'}-{max(years) if years else 'Unknown'}",
                'analysis': analysis,
                'top_papers': papers[:10],  # Top 10 papers
                'key_insights': self._extract_key_insights_from_analysis(analysis)
            }
            
        except Exception as e:
            self.logger.warning(f"Error analyzing {category} papers: {e}")
            return {
                'category': category,
                'paper_count': len(papers),
                'error': str(e),
                'top_papers': papers[:10]
            }
    
    def _perform_comprehensive_analysis(self, all_papers: Dict[str, List], 
                                      summaries: Dict[str, Any],
                                      research_topic: str) -> Dict[str, Any]:
        """Perform comprehensive cross-category analysis"""
        
        total_papers = sum(len(papers) for papers in all_papers.values())
        
        analysis_prompt = f"""
        Perform comprehensive analysis of literature review results for: "{research_topic}"
        
        Categories and counts:
        {json.dumps({cat: len(papers) for cat, papers in all_papers.items()}, indent=2)}
        
        Category insights:
        {json.dumps({cat: summary.get('key_insights', []) for cat, summary in summaries.items()}, indent=2)}
        
        Provide:
        1. Overall state of the field assessment
        2. Major research directions and paradigms
        3. Convergence/divergence of approaches
        4. Critical research gaps requiring attention
        5. Future research opportunities
        6. Recommendations for positioning new research
        
        Total papers analyzed: {total_papers}
        """
        
        try:
            comprehensive_analysis = self.llm_client.get_response(analysis_prompt)
            
            return {
                'field_assessment': comprehensive_analysis,
                'total_papers_analyzed': total_papers,
                'category_distribution': {cat: len(papers) for cat, papers in all_papers.items()},
                'research_maturity': self._assess_research_maturity(all_papers),
                'trend_analysis': self._analyze_temporal_trends(all_papers),
                'gap_analysis': self._identify_research_gaps(summaries)
            }
            
        except Exception as e:
            self.logger.warning(f"Error in comprehensive analysis: {e}")
            return {
                'error': str(e),
                'total_papers_analyzed': total_papers,
                'category_distribution': {cat: len(papers) for cat, papers in all_papers.items()}
            }
    
    def _map_research_landscape(self, all_papers: Dict[str, List], 
                               research_topic: str) -> Dict[str, Any]:
        """Map the research landscape and identify key areas"""
        
        # Extract all authors for network analysis
        author_network = {}
        venue_distribution = {}
        keyword_frequency = {}
        
        for category, papers in all_papers.items():
            for paper in papers:
                # Author network
                authors = paper.get('authors', [])
                for author in authors:
                    name = author.get('name', '')
                    if name:
                        if name not in author_network:
                            author_network[name] = {'papers': 0, 'categories': set()}
                        author_network[name]['papers'] += 1
                        author_network[name]['categories'].add(category)
                
                # Venue analysis
                venue = paper.get('venue', '')
                if venue:
                    venue_distribution[venue] = venue_distribution.get(venue, 0) + 1
                
                # Keyword extraction from titles
                title = paper.get('title', '').lower()
                for word in title.split():
                    if len(word) > 3:  # Skip short words
                        keyword_frequency[word] = keyword_frequency.get(word, 0) + 1
        
        # Identify top entities
        top_authors = sorted(author_network.items(), 
                           key=lambda x: x[1]['papers'], reverse=True)[:20]
        top_venues = sorted(venue_distribution.items(), 
                          key=lambda x: x[1], reverse=True)[:15]
        top_keywords = sorted(keyword_frequency.items(), 
                            key=lambda x: x[1], reverse=True)[:30]
        
        return {
            'research_landscape_summary': f"Landscape analysis for {research_topic}",
            'top_authors': top_authors,
            'top_venues': top_venues,
            'trending_keywords': top_keywords,
            'research_clusters': self._identify_research_clusters(all_papers),
            'interdisciplinary_connections': self._find_interdisciplinary_connections(all_papers)
        }
    
    def _identify_key_papers(self, all_papers: Dict[str, List]) -> Dict[str, Any]:
        """Identify key papers across all categories"""
        
        all_papers_flat = []
        for papers in all_papers.values():
            all_papers_flat.extend(papers)
        
        # Sort by citation count if available
        highly_cited = sorted(
            [p for p in all_papers_flat if p.get('citationCount', 0) > 0],
            key=lambda x: x.get('citationCount', 0),
            reverse=True
        )[:20]
        
        # Sort by recent and highly cited
        recent_important = sorted(
            [p for p in all_papers_flat if p.get('year', 0) >= 2020],
            key=lambda x: x.get('citationCount', 0),
            reverse=True
        )[:15]
        
        # Survey papers (based on title keywords)
        survey_papers = [
            p for p in all_papers_flat 
            if any(keyword in p.get('title', '').lower() 
                  for keyword in ['survey', 'review', 'overview', 'comprehensive'])
        ][:10]
        
        return {
            'highly_cited_papers': highly_cited,
            'recent_important_papers': recent_important,
            'survey_papers': survey_papers,
            'foundation_papers': self._identify_foundation_papers(all_papers_flat),
            'breakthrough_papers': self._identify_breakthrough_papers(all_papers_flat)
        }
    
    def _generate_citation_strategy(self, all_papers: Dict[str, List], 
                                  research_topic: str) -> Dict[str, Any]:
        """Generate strategic citation recommendations"""
        
        citation_prompt = f"""
        Generate a strategic citation plan for a research paper on: "{research_topic}"
        
        Available paper categories and counts:
        {json.dumps({cat: len(papers) for cat, papers in all_papers.items()}, indent=2)}
        
        Recommend:
        1. Essential foundational papers (5-8 papers)
        2. Recent state-of-the-art papers (3-5 papers)
        3. Methodological comparisons (4-6 papers)
        4. Application examples (2-4 papers)
        5. Survey/review papers for comprehensive coverage (1-2 papers)
        
        Provide citation strategy explaining why each category is important.
        """
        
        try:
            citation_strategy = self.llm_client.get_response(citation_prompt)
            
            # Select specific papers for each category
            recommended_papers = self._select_papers_for_citation(all_papers)
            
            return {
                'citation_strategy': citation_strategy,
                'recommended_papers': recommended_papers,
                'citation_balance': self._analyze_citation_balance(all_papers),
                'coverage_assessment': self._assess_citation_coverage(all_papers)
            }
            
        except Exception as e:
            self.logger.warning(f"Error generating citation strategy: {e}")
            return {
                'error': str(e),
                'fallback_recommendations': self._get_fallback_citations(all_papers)
            }
    
    # Helper methods for analysis
    
    def _extract_key_insights_from_analysis(self, analysis_text: str) -> List[str]:
        """Extract key insights from analysis text"""
        # Simple extraction - look for numbered lists or bullet points
        insights = []
        lines = analysis_text.split('\\n')
        
        for line in lines:
            line = line.strip()
            if (line.startswith(('1.', '2.', '3.', '4.', '5.')) or 
                line.startswith(('•', '-', '*')) or
                'key' in line.lower() or 'important' in line.lower()):
                insights.append(line)
        
        return insights[:10]  # Limit to top 10 insights
    
    def _assess_research_maturity(self, all_papers: Dict[str, List]) -> str:
        """Assess the maturity of research field"""
        total_papers = sum(len(papers) for papers in all_papers.values())
        
        if total_papers > 200:
            return "Mature field with extensive literature"
        elif total_papers > 100:
            return "Developing field with good coverage"
        elif total_papers > 50:
            return "Emerging field with growing interest"
        else:
            return "Early-stage field with limited literature"
    
    def _analyze_temporal_trends(self, all_papers: Dict[str, List]) -> Dict[str, Any]:
        """Analyze temporal trends in the literature"""
        all_papers_flat = []
        for papers in all_papers.values():
            all_papers_flat.extend(papers)
        
        years = [p.get('year') for p in all_papers_flat if p.get('year')]
        year_counts = {}
        for year in years:
            year_counts[year] = year_counts.get(year, 0) + 1
        
        return {
            'publication_trend': year_counts,
            'peak_years': sorted(year_counts.items(), key=lambda x: x[1], reverse=True)[:5],
            'recent_growth': len([y for y in years if y >= 2020]),
            'field_age': max(years) - min(years) if years else 0
        }
    
    def _identify_research_gaps(self, summaries: Dict[str, Any]) -> List[str]:
        """Identify research gaps from category summaries"""
        gaps = []
        for category, summary in summaries.items():
            analysis = summary.get('analysis', '')
            if 'gap' in analysis.lower() or 'limitation' in analysis.lower():
                # Extract sentences containing gaps/limitations
                sentences = analysis.split('.')
                for sentence in sentences:
                    if any(word in sentence.lower() for word in ['gap', 'limitation', 'missing', 'lack']):
                        gaps.append(sentence.strip())
        
        return gaps[:15]  # Limit to top 15 gaps
    
    def _identify_research_clusters(self, all_papers: Dict[str, List]) -> List[str]:
        """Identify main research clusters/themes"""
        # Simple clustering based on common keywords in titles
        all_titles = []
        for papers in all_papers.values():
            all_titles.extend([p.get('title', '') for p in papers])
        
        # Extract common themes
        word_freq = {}
        for title in all_titles:
            words = title.lower().split()
            for word in words:
                if len(word) > 4:  # Focus on meaningful words
                    word_freq[word] = word_freq.get(word, 0) + 1
        
        # Get top themes
        top_themes = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        return [theme[0] for theme in top_themes]
    
    def _find_interdisciplinary_connections(self, all_papers: Dict[str, List]) -> List[str]:
        """Find interdisciplinary connections"""
        # Look for papers that span multiple categories
        connections = []
        categories = list(all_papers.keys())
        
        for i, cat1 in enumerate(categories):
            for cat2 in categories[i+1:]:
                # Find common authors or similar titles
                cat1_authors = set()
                cat2_authors = set()
                
                for paper in all_papers[cat1]:
                    for author in paper.get('authors', []):
                        cat1_authors.add(author.get('name', ''))
                
                for paper in all_papers[cat2]:
                    for author in paper.get('authors', []):
                        cat2_authors.add(author.get('name', ''))
                
                common_authors = cat1_authors & cat2_authors
                if common_authors:
                    connections.append(f"{cat1} ↔ {cat2}: {len(common_authors)} shared authors")
        
        return connections
    
    def _identify_foundation_papers(self, papers: List[Dict]) -> List[Dict]:
        """Identify foundational papers"""
        # Papers with high citation counts and early publication
        foundation_candidates = [
            p for p in papers 
            if p.get('citationCount', 0) > 100 and p.get('year', 9999) < 2020
        ]
        
        return sorted(foundation_candidates, 
                     key=lambda x: x.get('citationCount', 0), 
                     reverse=True)[:10]
    
    def _identify_breakthrough_papers(self, papers: List[Dict]) -> List[Dict]:
        """Identify breakthrough papers"""
        # Recent papers with very high citation counts
        breakthrough_candidates = [
            p for p in papers 
            if p.get('citationCount', 0) > 50 and p.get('year', 0) >= 2020
        ]
        
        return sorted(breakthrough_candidates, 
                     key=lambda x: x.get('citationCount', 0), 
                     reverse=True)[:8]
    
    def _select_papers_for_citation(self, all_papers: Dict[str, List]) -> Dict[str, List]:
        """Select specific papers for citation recommendations"""
        recommendations = {}
        
        for category, papers in all_papers.items():
            # Select top papers from each category
            if papers:
                # Sort by citation count
                sorted_papers = sorted(
                    papers, 
                    key=lambda x: x.get('citationCount', 0), 
                    reverse=True
                )
                recommendations[category] = sorted_papers[:5]  # Top 5 from each category
        
        return recommendations
    
    def _analyze_citation_balance(self, all_papers: Dict[str, List]) -> Dict[str, Any]:
        """Analyze citation balance across categories"""
        category_counts = {cat: len(papers) for cat, papers in all_papers.items()}
        total_papers = sum(category_counts.values())
        
        balance = {}
        for category, count in category_counts.items():
            balance[category] = {
                'count': count,
                'percentage': (count / total_papers * 100) if total_papers > 0 else 0,
                'recommended_citations': max(2, count // 10)  # Rough guideline
            }
        
        return balance
    
    def _assess_citation_coverage(self, all_papers: Dict[str, List]) -> Dict[str, Any]:
        """Assess citation coverage quality"""
        all_papers_flat = []
        for papers in all_papers.values():
            all_papers_flat.extend(papers)
        
        total_papers = len(all_papers_flat)
        highly_cited = len([p for p in all_papers_flat if p.get('citationCount', 0) > 50])
        recent_papers = len([p for p in all_papers_flat if p.get('year', 0) >= 2020])
        
        return {
            'total_coverage': total_papers,
            'quality_papers': highly_cited,
            'recent_coverage': recent_papers,
            'coverage_score': min(100, (total_papers / 50) * 100),  # Scale to 100
            'quality_ratio': (highly_cited / total_papers * 100) if total_papers > 0 else 0,
            'recency_ratio': (recent_papers / total_papers * 100) if total_papers > 0 else 0
        }
    
    def _get_fallback_citations(self, all_papers: Dict[str, List]) -> Dict[str, List]:
        """Get fallback citation recommendations"""
        fallback = {}
        for category, papers in all_papers.items():
            if papers:
                fallback[category] = papers[:3]  # Just take first 3 papers
        return fallback
    
    def _analyze_coverage(self, all_papers: Dict[str, List]) -> Dict[str, Any]:
        """Analyze the coverage of the literature review"""
        category_counts = {cat: len(papers) for cat, papers in all_papers.items()}
        total_unique_papers = sum(category_counts.values())
        
        return {
            'categories_covered': len(category_counts),
            'total_unique_papers': total_unique_papers,
            'average_papers_per_category': total_unique_papers / len(category_counts) if category_counts else 0,
            'coverage_balance': category_counts,
            'coverage_quality': 'Comprehensive' if total_unique_papers > 100 else 'Good' if total_unique_papers > 50 else 'Basic'
        }
    
    def save_literature_review(self, literature_review: Dict[str, Any], 
                              filename: str = None) -> str:
        """
        Save literature review to file
        
        Args:
            literature_review: Complete literature review data
            filename: Output filename (optional)
            
        Returns:
            Path to saved file
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            topic = literature_review.get('research_topic', 'unknown').replace(' ', '_')
            filename = f"literature_review_{topic}_{timestamp}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(literature_review, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"Literature review saved to: {filename}")
        return filename


if __name__ == "__main__":
    # Example usage
    print("🔍 Literature Research Manager")
    print("=" * 50)
    
    # Initialize manager
    lit_manager = LiteratureManager()
    
    # Conduct literature review
    research_topic = "Brain-Inspired Visual Recognition"
    
    try:
        literature_review = lit_manager.conduct_comprehensive_review(
            research_topic=research_topic,
            max_papers_per_query=10,
            include_recent_only=True
        )
        
        print(f"\\n📊 Literature Review Summary:")
        print(f"Topic: {literature_review['research_topic']}")
        print(f"Total Papers: {literature_review['metadata']['total_papers_found']}")
        print(f"Categories: {list(literature_review['papers_by_category'].keys())}")
        print(f"Coverage Quality: {literature_review['metadata']['coverage_analysis']['coverage_quality']}")
        
        # Save results
        output_file = lit_manager.save_literature_review(literature_review)
        print(f"\\n💾 Results saved to: {output_file}")
        
    except Exception as e:
        print(f"\\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
