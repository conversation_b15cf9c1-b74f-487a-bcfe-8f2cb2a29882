"""
Quick Stage 4 Test - Fast validation of paper writing components
Tests core Stage 4 functionality with reduced scope for faster validation
"""

import os
import sys
import asyncio
from datetime import datetime

# Add project path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LL<PERSON>lient
from stage3_simulation import create_stage3_simulation


async def quick_stage4_test():
    """Quick validation of Stage 4 core components"""
    print("⚡ Quick Stage 4 Test - Core Components")
    print("=" * 50)
    
    # Setup
    api_key = "sk-1b1d72e2e10643029de548b655e1f93e"
    os.environ["DEEPSEEK_API_KEY"] = api_key
    os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
    
    # Load Stage 3 data
    stage3_data = create_stage3_simulation()
    topic = stage3_data['research_topic']
    
    print(f"🎯 Topic: {topic}")
    
    # Create clients
    writer = LLMClient(provider="deepseek", model="deepseek-chat", temperature=0.7)
    reasoner = LLMClient(provider="deepseek", model="deepseek-reasoner", temperature=0.6)
    
    print(f"✅ DeepSeek clients ready")
    
    # Test 1: Abstract generation with Stage 3 integration
    print(f"\\n📝 Test 1: Abstract generation")
    abstract_prompt = f"""
    Write a comprehensive abstract for the research paper: "{topic}"
    
    Based on this research assessment:
    - Overall research value: {stage3_data['research_value_assessment']['overall_score']}/10
    - Key innovation: {stage3_data['innovation_highlights']['technical_contributions'][0]}
    
    Include: Background, Method, Results, Conclusion (150-200 words)
    """
    
    try:
        abstract = writer.generate_response(abstract_prompt)
        print(f"✅ Abstract: {len(abstract)} chars")
        print(f"Preview: {abstract[:150]}...")
    except Exception as e:
        print(f"❌ Abstract failed: {e}")
        return False
    
    # Test 2: Literature review with reasoning
    print(f"\\n📚 Test 2: Literature review")
    lit_prompt = f"""
    Provide a focused literature review for "{topic}".
    
    Context: {stage3_data['literature_integration']['research_gaps'][0]}
    Key references: {', '.join(stage3_data['literature_integration']['key_references'][:2])}
    
    Structure: 1) Current state, 2) Gaps, 3) Our positioning (100-150 words)
    """
    
    try:
        literature = reasoner.generate_response(lit_prompt)
        print(f"✅ Literature: {len(literature)} chars")
    except Exception as e:
        print(f"❌ Literature failed: {e}")
        return False
    
    # Test 3: Methodology based on Stage 3 experimental design
    print(f"\\n🔬 Test 3: Methodology")
    method_prompt = f"""
    Write a methodology section for "{topic}".
    
    Experimental framework: {stage3_data['experimental_design']['experimental_framework']['phase1']['methodology']}
    Datasets: {', '.join(stage3_data['experimental_design']['experimental_validation']['datasets'][:2])}
    
    Include: Approach, Implementation, Evaluation (100-150 words)
    """
    
    try:
        methodology = writer.generate_response(method_prompt)
        print(f"✅ Methodology: {len(methodology)} chars")
    except Exception as e:
        print(f"❌ Methodology failed: {e}")
        return False
    
    # Test 4: Multi-expert review simulation
    print(f"\\n👥 Test 4: Expert review")
    review_prompt = f"""
    As an ICML reviewer, evaluate this research on "{topic}".
    
    Innovation: {stage3_data['innovation_highlights']['technical_contributions'][0]}
    Expected impact: {stage3_data['innovation_highlights']['expected_impact']['academic']}
    
    Provide: Score (1-10), Strengths (2), Weaknesses (2), Decision (50-100 words)
    """
    
    try:
        review = reasoner.generate_response(review_prompt)
        print(f"✅ Review: {len(review)} chars")
        
        # Extract score if possible
        if "score" in review.lower() or "/10" in review:
            print(f"📊 Review contains scoring information")
    except Exception as e:
        print(f"❌ Review failed: {e}")
        return False
    
    # Test 5: Paper structure generation
    print(f"\\n📋 Test 5: Paper structure")
    structure_prompt = f"""
    Create a detailed paper outline for "{topic}".
    
    Target venue: ICML (8 pages)
    Focus areas: {', '.join(stage3_data['experimental_design']['experimental_validation']['metrics'][:3])}
    
    Provide: Section titles, key points for each section, estimated word counts
    """
    
    try:
        structure = writer.generate_response(structure_prompt)
        print(f"✅ Structure: {len(structure)} chars")
        
        # Check for standard sections
        standard_sections = ['abstract', 'introduction', 'method', 'experiment', 'result', 'conclusion']
        found_sections = [s for s in standard_sections if s in structure.lower()]
        print(f"📋 Standard sections found: {len(found_sections)}/{len(standard_sections)}")
    except Exception as e:
        print(f"❌ Structure failed: {e}")
        return False
    
    print(f"\\n🎉 Quick Stage 4 test completed!")
    print(f"✅ All core components functional")
    print(f"✅ Stage 3 integration working")
    print(f"✅ Ready for full Stage 4 test")
    
    return True


if __name__ == "__main__":
    success = asyncio.run(quick_stage4_test())
    if success:
        print(f"\\n💡 Next: Run python test_stage4_complete.py for full test")
    else:
        print(f"\\n⚠️ Check API connection and try again")
