"""
Enhanced Reasoning Workflow Coordinator
Integrates multi-expert collaboration system for high-quality end-to-end reasoning
"""

import os
import sys
import json
import time
import uuid
import logging
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime
from pathlib import Path

# Add project path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.unified_api_client import UnifiedAPIClient
from core.enhanced_literature_manager import EnhancedLiteratureManager
from agents.agent_manager import <PERSON><PERSON>anager
from reasoning.enhanced_multi_agent_collaborator import EnhancedMultiAgentCollaborator
from reasoning.data_models import (
    ResearchProblem, ExperimentPlan, ImplementationPlan, 
    VisualizationPlan, ReasoningSession
)


class EnhancedExperimentReasoningWorkflow:
    """Enhanced Experiment Reasoning Workflow Coordinator"""
    
    def __init__(self, unified_client: Optional[UnifiedAPIClient] = None):
        """
        Initialize enhanced workflow coordinator
        
        Args:
            unified_client: Unified API client
        """
        # Initialize unified client
        if unified_client is None:
            unified_client = UnifiedAPIClient()
        
        self.unified_client = unified_client
        
        # Initialize core components
        self.literature_manager = EnhancedLiteratureManager(unified_client=unified_client)
        self.agent_manager = AgentManager(unified_client=unified_client)
        self.collaborator = EnhancedMultiAgentCollaborator(unified_client=unified_client)
        
        # Workflow status
        self.current_session: Optional[ReasoningSession] = None
        self.session_history: List[ReasoningSession] = []
        
        # Enhanced workflow configuration
        self.workflow_config = {
            "collaboration_rounds": 3,
            "auto_save": True,
            "save_directory": "./reasoning_sessions",
            "enable_multi_agent_collaboration": True,
            "quality_threshold": 0.8,
            "consensus_threshold": 0.75
        }
        
        # Ensure save directory exists
        Path(self.workflow_config["save_directory"]).mkdir(parents=True, exist_ok=True)
        
        print("✅ Enhanced experiment reasoning workflow initialized")
        print(f"   💡 Multi-expert collaboration: {'enabled' if self.workflow_config['enable_multi_agent_collaboration'] else 'disabled'}")
    
    def run_enhanced_reasoning_flow(self, 
                                  research_question: str,
                                  hypothesis: List[str],
                                  background: Dict[str, Any] = None,
                                  workflow_context: Dict[str, Any] = None,
                                  target_venue: str = "ICML",
                                  progress_callback: Optional[Callable] = None) -> ReasoningSession:
        """
        Run enhanced multi-expert collaborative reasoning process
        
        Args:
            research_question: Research question
            hypothesis: List of hypotheses
            background: Background information
            workflow_context: Workflow context extracted from papers
            target_venue: Target journal/conference
            progress_callback: Progress callback function
            
        Returns:
            Complete reasoning session record
        """
        
        print(f"\n🚀 Starting enhanced multi-expert collaborative reasoning process")
        print(f"📋 Research question: {research_question}")
        print(f"🎯 Target venue: {target_venue}")
        print(f"🤖 Participating experts: {list(self.agent_manager.agents.keys())}")
        print("=" * 80)
        
        # Create new reasoning session
        session = self._create_new_session(research_question, hypothesis, background or {})
        self.current_session = session
        
        try:
            # Stage 1: Multi-expert collaborative evaluation of research question
            if progress_callback:
                progress_callback("Stage 1: Multi-expert collaborative evaluation", 0.1)
            
            evaluation_result = self._run_collaborative_evaluation(
                research_question, hypothesis, background or {}
            )
            session.research_problem = evaluation_result
            
            # Stage 2: Collaborative experiment design
            if progress_callback:
                progress_callback("Stage 2: Collaborative experiment design", 0.3)
            
            experiment_result = self._run_collaborative_experiment_design(
                session.research_problem, workflow_context or {}
            )
            session.experiment_plan = experiment_result
            
            # Stage 3: Collaborative implementation planning
            if progress_callback:
                progress_callback("Stage 3: Collaborative implementation planning", 0.6)
            
            implementation_result = self._run_collaborative_implementation_planning(
                session.experiment_plan
            )
            session.implementation_plan = implementation_result
            
            # Stage 4: Collaborative visualization planning
            if progress_callback:
                progress_callback("Stage 4: Collaborative visualization planning", 0.8)
            
            visualization_result = self._run_collaborative_visualization_planning(
                session.implementation_plan
            )
            session.visualization_plan = visualization_result
            
            # Complete reasoning process
            session.status = "completed"
            session.completion_time = datetime.now()
            
            if progress_callback:
                progress_callback("Reasoning process completed", 1.0)
            
            # Save session
            if self.workflow_config["auto_save"]:
                self._save_session(session)
            
            print(f"\n✅ Enhanced reasoning process completed")
            print(f"   📊 Session ID: {session.session_id}")
            print(f"   ⏱️ Total duration: {session.completion_time - session.start_time}")
            
            return session
            
        except Exception as e:
            session.status = "error"
            session.error_message = str(e)
            print(f"\n❌ Reasoning process error: {e}")
            raise
    
    def _create_new_session(self, research_question: str, 
                          hypothesis: List[str], 
                          background: Dict[str, Any]) -> ReasoningSession:
        """Create new reasoning session"""
        
        session_id = f"enhanced_reasoning_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        
        research_problem = ResearchProblem(
            question=research_question,
            hypothesis=hypothesis,
            background=background
        )
        
        current_time = datetime.now()
        
        session = ReasoningSession(
            session_id=session_id,
            timestamp=current_time,  # For compatibility
            start_time=current_time,
            research_problem=research_problem,
            status="in_progress"
        )
        
        print(f"✅ Created new reasoning session: {session_id}")
        return session
    
    def _run_collaborative_evaluation(self, 
                                    research_question: str,
                                    hypothesis: List[str],
                                    background: Dict[str, Any]) -> ResearchProblem:
        """Run collaborative evaluation stage"""
        print("\n📊 Stage 1: Multi-expert collaborative evaluation")
        print("-" * 50)
        
        # Prepare discussion points
        discussion_points = [
            f"Scientific value and innovation of the research question: {research_question}",
            f"Reasonableness and verifiability of hypotheses: {hypothesis}",
            "Limitations of existing research and research gaps",
            "Technical feasibility and resource requirements",
            "Expected contributions and impact assessment"
        ]
        
        # Create collaboration session
        collab_session = self.collaborator.create_collaboration_session(
            research_topic=f"Research question evaluation: {research_question}",
            specific_questions=discussion_points,
            required_experts=["ai_technology", "data_analysis"]
        )
        
        # Execute multi-round discussion
        completed_session = self.collaborator.conduct_multi_round_discussion(
            collab_session, discussion_points
        )
        
        # Generate research problem based on collaboration results
        consensus = completed_session.final_consensus
        
        research_problem = ResearchProblem(
            question=research_question,
            hypothesis=hypothesis,
            background=background,
            novelty_score=consensus.get("novelty_score", 0.8),
            feasibility_score=consensus.get("feasibility_score", 0.7),
            impact_score=consensus.get("impact_score", 0.8),
            evaluation_summary=consensus.get("core_conclusions", "Multi-expert consensus reached"),
            key_challenges=consensus.get("risks_limitations", []),
            suggested_approaches=consensus.get("next_actions", [])
        )
        
        print(f"   ✅ Evaluation completed - Quality score: {completed_session.quality_score:.2f}")
        return research_problem
    
    def _run_collaborative_experiment_design(self,
                                           research_problem: ResearchProblem,
                                           workflow_context: Dict[str, Any]) -> ExperimentPlan:
        """Run collaborative experiment design stage"""
        print("\n🔬 Stage 2: Multi-expert collaborative experiment design")
        print("-" * 50)
        
        # Prepare experiment design discussion points
        discussion_points = [
            f"Experimental design principles and methodology: {research_problem.question}",
            "Design of control and experimental groups",
            "Definition of key variables and evaluation metrics",
            "Data collection and analysis strategy",
            "Ensuring experiment reproducibility and validity"
        ]
        
        # Create experiment design collaboration session
        collab_session = self.collaborator.create_collaboration_session(
            research_topic=f"Experiment design: {research_problem.question}",
            specific_questions=discussion_points,
            required_experts=["experiment_design", "data_analysis", "ai_technology"]
        )
        
        # Execute collaborative discussion
        completed_session = self.collaborator.conduct_multi_round_discussion(
            collab_session, discussion_points
        )
        
        # Generate experiment plan
        consensus = completed_session.final_consensus
        
        experiment_plan = ExperimentPlan(
            research_question=research_problem.question,
            hypothesis=research_problem.hypothesis,
            methodology=consensus.get("methodology", "Collaboratively developed methodology"),
            variables={
                "independent": consensus.get("independent_variables", []),
                "dependent": consensus.get("dependent_variables", []),
                "controlled": consensus.get("controlled_variables", [])
            },
            metrics=consensus.get("key_metrics", []),
            baseline_methods=consensus.get("baseline_methods", []),
            evaluation_criteria=consensus.get("evaluation_criteria", []),
            expected_outcomes=consensus.get("expected_outcomes", []),
            collaboration_insights=completed_session.insights
        )
        
        print(f"   ✅ Experiment design completed - Quality score: {completed_session.quality_score:.2f}")
        return experiment_plan
    
    def _run_collaborative_implementation_planning(self,
                                                 experiment_plan: ExperimentPlan) -> ImplementationPlan:
        """Run collaborative implementation planning stage"""
        print("\n⚙️ Stage 3: Multi-expert collaborative implementation planning")
        print("-" * 50)
        
        # Prepare implementation planning discussion points
        discussion_points = [
            "Technical architecture and implementation framework selection",
            "Design of key algorithms and modules",
            "Data processing and model training strategy",
            "Performance optimization and resource management",
            "Testing and validation plan"
        ]
        
        # Create implementation planning collaboration session
        collab_session = self.collaborator.create_collaboration_session(
            research_topic=f"Implementation planning: {experiment_plan.research_question}",
            specific_questions=discussion_points,
            required_experts=["ai_technology", "data_analysis"]
        )
        
        # Execute collaborative discussion
        completed_session = self.collaborator.conduct_multi_round_discussion(
            collab_session, discussion_points
        )
        
        # Generate implementation plan
        consensus = completed_session.final_consensus
        
        implementation_plan = ImplementationPlan(
            experiment_plan=experiment_plan,
            architecture=consensus.get("technical_architecture", {}),
            algorithms=consensus.get("key_algorithms", []),
            frameworks=consensus.get("frameworks", []),
            data_pipeline=consensus.get("data_processing", {}),
            training_strategy=consensus.get("training_strategy", {}),
            evaluation_pipeline=consensus.get("evaluation_methods", {}),
            resource_requirements=consensus.get("resource_requirements", {}),
            timeline=consensus.get("implementation_timeline", {}),
            collaboration_insights=completed_session.insights
        )
        
        print(f"   ✅ Implementation planning completed - Quality score: {completed_session.quality_score:.2f}")
        return implementation_plan
    
    def _run_collaborative_visualization_planning(self,
                                                implementation_plan: ImplementationPlan) -> VisualizationPlan:
        """Run collaborative visualization planning stage"""
        print("\n📊 Stage 4: Multi-expert collaborative visualization planning")
        print("-" * 50)
        
        # Prepare visualization discussion points
        discussion_points = [
            "Visualization strategy for key results and findings",
            "Chart types and design principles",
            "Clarity and readability of data presentation",
            "Academic paper chart standards",
            "Possibilities for interactive visualization"
        ]
        
        # Create visualization collaboration session
        collab_session = self.collaborator.create_collaboration_session(
            research_topic=f"Visualization planning: {implementation_plan.experiment_plan.research_question}",
            specific_questions=discussion_points,
            required_experts=["data_analysis", "paper_writing"]
        )
        
        # Execute collaborative discussion
        completed_session = self.collaborator.conduct_multi_round_discussion(
            collab_session, discussion_points
        )
        
        # Generate visualization plan
        consensus = completed_session.final_consensus
        
        visualization_plan = VisualizationPlan(
            implementation_plan=implementation_plan,
            chart_types=consensus.get("recommended_charts", []),
            design_principles=consensus.get("design_guidelines", []),
            data_stories=consensus.get("data_narratives", []),
            interactive_elements=consensus.get("interactive_features", []),
            academic_standards=consensus.get("academic_requirements", []),
            accessibility_features=consensus.get("accessibility", []),
            collaboration_insights=completed_session.insights
        )
        
        print(f"   ✅ Visualization planning completed - Quality score: {completed_session.quality_score:.2f}")
        return visualization_plan
    
    def _save_session(self, session: ReasoningSession) -> None:
        """Save session to file"""
        try:
            file_path = f"{self.workflow_config['save_directory']}/{session.session_id}.json"
            session_data = {
                "session_id": session.session_id,
                "start_time": session.start_time.isoformat(),
                "completion_time": session.completion_time.isoformat() if session.completion_time else None,
                "status": session.status,
                "research_problem": session.research_problem.__dict__ if session.research_problem else None,
                "experiment_plan": session.experiment_plan.__dict__ if session.experiment_plan else None,
                "implementation_plan": session.implementation_plan.__dict__ if session.implementation_plan else None,
                "visualization_plan": session.visualization_plan.__dict__ if session.visualization_plan else None
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Session saved: {file_path}")
            
        except Exception as e:
            print(f"❌ Failed to save session: {e}")
    
    def get_session_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session summary"""
        session = next((s for s in self.session_history if s.session_id == session_id), None)
        if not session:
            return None
        
        return {
            "session_id": session.session_id,
            "start_time": session.start_time,
            "status": session.status,
            "research_question": session.research_problem.question if session.research_problem else None,
            "completion_stages": [
                "Problem evaluation" if session.research_problem else None,
                "Experiment design" if session.experiment_plan else None,
                "Implementation planning" if session.implementation_plan else None,
                "Visualization design" if session.visualization_plan else None
            ]
        }
    
    def list_recent_sessions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """List recent sessions"""
        recent_sessions = sorted(
            self.session_history, 
            key=lambda x: x.start_time, 
            reverse=True
        )[:limit]
        
        return [self.get_session_summary(session.session_id) for session in recent_sessions]
    
    def generate_workflow_report(self, session: ReasoningSession) -> str:
        """Generate workflow report"""
        report = f"""
# Enhanced Multi-Expert Collaborative Reasoning Workflow Report

## Session Overview
- **Session ID**: {session.session_id}
- **Start Time**: {session.start_time.strftime('%Y-%m-%d %H:%M:%S')}
- **Completion Time**: {session.completion_time.strftime('%Y-%m-%d %H:%M:%S') if session.completion_time else 'In Progress'}
- **Status**: {session.status}

## Research Problem Analysis
- **Question**: {session.research_problem.question if session.research_problem else 'N/A'}
- **Evaluation Summary**: {session.research_problem.evaluation_summary if session.research_problem else 'N/A'}

## Experiment Plan
- **Methodology**: {session.experiment_plan.methodology if session.experiment_plan else 'N/A'}
- **Collaboration Insights**: {len(session.experiment_plan.collaboration_insights) if session.experiment_plan and hasattr(session.experiment_plan, 'collaboration_insights') else 0} items

## Implementation Plan
- **Architecture**: {'Defined' if session.implementation_plan and session.implementation_plan.architecture else 'Undefined'}
- **Collaboration Insights**: {len(session.implementation_plan.collaboration_insights) if session.implementation_plan and hasattr(session.implementation_plan, 'collaboration_insights') else 0} items

## Visualization Plan
- **Chart Types**: {len(session.visualization_plan.chart_types) if session.visualization_plan and session.visualization_plan.chart_types else 0} types
- **Collaboration Insights**: {len(session.visualization_plan.collaboration_insights) if session.visualization_plan and hasattr(session.visualization_plan, 'collaboration_insights') else 0} items

---
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        return report


# Add the EnhancedReasoningWorkflow class for compatibility
class EnhancedReasoningWorkflow(EnhancedExperimentReasoningWorkflow):
    """
    Enhanced reasoning workflow (simplified compatibility layer)
    """
    
    def __init__(self, unified_client: Optional[UnifiedAPIClient] = None):
        """Initialize enhanced reasoning workflow"""
        super().__init__(unified_client)
        print("✅ Enhanced reasoning workflow initialized (compatibility mode)")
    
    def analyze_research_topic(self, research_topic: str, papers: List[Dict] = None, workflows: Dict = None) -> ResearchProblem:
        """
        Analyze research topic - compatibility function
        
        Args:
            research_topic: Research topic to analyze
            papers: Optional list of papers for context
            workflows: Optional dictionary of workflows
            
        Returns:
            Research problem object
        """
        # Create background context
        background = {
            "papers": papers or [],
            "workflows": workflows or {},
            "description": f"Analysis of {research_topic}"
        }
        
        # Create hypothesis list
        hypothesis = [
            f"The research on {research_topic} will lead to significant improvements in AI systems",
            f"Novel methodologies for {research_topic} can outperform existing approaches"
        ]
        
        try:
            # Run collaboration evaluation
            research_problem = self._run_collaborative_evaluation(
                research_question=research_topic,
                hypothesis=hypothesis,
                background=background
            )
            return research_problem
        except Exception as e:
            print(f"⚠️ Error in research topic analysis: {e}")
            # Fallback - create a basic research problem
            return ResearchProblem(
                question=research_topic,
                hypothesis=hypothesis,
                background=background,
                significance="Potentially significant for advancing AI capabilities",
                constraints={"timeline": "6 months", "resources": "Standard"},
                evaluation_criteria={"performance": "accuracy and efficiency"}
            )
            
    def analyze_experiment_logic(self, research_problem: ResearchProblem, experiment_plan: ExperimentPlan) -> Dict[str, Any]:
        """
        Analyze the logical relationship between research problem and experiment design
        
        Args:
            research_problem: The research problem definition
            experiment_plan: The experiment plan to analyze
            
        Returns:
            Analysis results with logical validity and improvement suggestions
        """
        print("🧠 Analyzing experiment logic and relationship to research questions...")
        
        try:
            # Get paper writing expert for logical analysis
            writing_expert = self.agent_manager.get_agent('paper_writing')
            
            if not writing_expert:
                return self._create_default_logic_analysis(research_problem, experiment_plan)
            
            # Extract key details for analysis
            research_question = research_problem.question if hasattr(research_problem, 'question') else str(research_problem)
            
            hypothesis = []
            if hasattr(experiment_plan, 'hypothesis'):
                if isinstance(experiment_plan.hypothesis, list):
                    hypothesis = experiment_plan.hypothesis
                else:
                    hypothesis = [str(experiment_plan.hypothesis)]
            
            methodology = ""
            if hasattr(experiment_plan, 'methodology'):
                methodology = experiment_plan.methodology
            
            # Create prompt for logical analysis
            prompt = f"""
            Analyze the logical relationship between the research question and experiment design:
            
            RESEARCH QUESTION: {research_question}
            
            EXPERIMENT HYPOTHESIS: {' '.join(hypothesis)}
            
            METHODOLOGY: {methodology}
            
            Please evaluate:
            1. The logical validity of the experiment design for addressing the research question
            2. Whether the experiment can effectively test the hypothesis
            3. The alignment between measurements and research objectives
            4. Potential logical gaps or methodological weaknesses
            5. Suggestions for improving the experimental logic
            
            Format your response as a JSON with the following structure:
            {{
                "logical_validity": 0.8,  # score from 0 to 1
                "alignment_analysis": "analysis of alignment between experiment and question",
                "logical_strengths": ["strength 1", "strength 2"],
                "logical_weaknesses": ["weakness 1", "weakness 2"],
                "improvement_suggestions": ["suggestion 1", "suggestion 2"]
            }}
            """
            
            # Get analysis from writing expert
            response = writing_expert.analyze({
                "input_text": prompt,
                "analysis_type": "logical_analysis"
            })
            
            # Extract content
            if hasattr(response, 'content'):
                content = response.content
            else:
                content = str(response)
                
            # Try to extract JSON
            try:
                import re
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    analysis = json.loads(json_match.group(0))
                    return analysis
            except Exception as e:
                print(f"⚠️ Failed to parse logic analysis: {e}")
            
            # Fallback
            return self._create_default_logic_analysis(research_problem, experiment_plan)
            
        except Exception as e:
            print(f"❌ Experiment logic analysis failed: {e}")
            return self._create_default_logic_analysis(research_problem, experiment_plan)
    
    def _create_default_logic_analysis(self, research_problem, experiment_plan) -> Dict[str, Any]:
        """Create a default logic analysis as fallback"""
        return {
            "logical_validity": 0.75,
            "alignment_analysis": f"The experiment appears to be reasonably aligned with the research question, though there may be room for improvement in directly addressing all aspects of the problem statement.",
            "logical_strengths": [
                "Clear connection between hypothesis and experiment design",
                "Measurable outcomes that relate to the research goals",
                "Appropriate methodology for the research domain"
            ],
            "logical_weaknesses": [
                "Some aspects of the research question may not be fully addressed",
                "Potential confounding variables not fully controlled"
            ],
            "improvement_suggestions": [
                "More explicitly connect experimental measurements to research question components",
                "Consider additional control conditions to strengthen causal inferences",
                "Include more diverse evaluation metrics to capture different aspects of performance"
            ]
        }


# Convenience function
def create_enhanced_reasoning_workflow(unified_client: Optional[UnifiedAPIClient] = None) -> EnhancedReasoningWorkflow:
    """Create enhanced reasoning workflow instance"""
    return EnhancedReasoningWorkflow(unified_client)


if __name__ == "__main__":
    # Demo enhanced reasoning workflow
    print("🧠 Enhanced Multi-Expert Collaborative Reasoning Workflow Demo")
    print("=" * 60)
    
    # Create enhanced workflow
    unified_client = UnifiedAPIClient()
    workflow = EnhancedReasoningWorkflow(unified_client)
    
    # Example research question
    research_question = "How to design an efficient neural network architecture inspired by brain mechanisms?"
    hypothesis = [
        "Sparse connectivity in biological neural networks can improve computational efficiency",
        "Simulating neural plasticity mechanisms can enhance learning performance",
        "Hierarchical information processing can improve model generalization"
    ]
    
    background = {
        "domain": "Deep Learning and Neuroscience",
        "previous_work": "Existing neural network architectures are primarily based on mathematical optimization principles",
        "motivation": "Aim to improve artificial neural networks by drawing inspiration from biological brain mechanisms"
    }
    
    print(f"\n📋 Research Question: {research_question}")
    print(f"🔬 Number of Hypotheses: {len(hypothesis)}")
    print(f"🎯 Application Domain: {background['domain']}")
    
    try:
        # Run enhanced reasoning process
        print("\n🚀 Starting enhanced multi-expert collaborative reasoning process...")
        print("(Actual execution requires valid API connection)")
        
        # Create session for demo
        session = workflow._create_new_session(research_question, hypothesis, background)
        print(f"\n✅ Demo session created successfully: {session.session_id}")
        
        # Generate report demo
        report = workflow.generate_workflow_report(session)
        print(f"\n📊 Workflow Report Preview:")
        print(report[:500] + "...")
        
    except Exception as e:
        print(f"\n❌ Demo process error: {e}")
        print("💡 Please ensure API configuration is correct before trying again")
