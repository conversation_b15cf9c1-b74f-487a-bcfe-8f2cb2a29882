"""
End-to-End System Integration Test
Tests the complete workflow from literature research to paper generation
"""

import os
import sys
import json
import unittest
import logging
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the necessary components
from core.unified_api_client import get_unified_client
from workflow.complete_research_workflow import CompleteResearchWorkflow
from paper_generation.brain_paper_writer import BrainPaperWriter
from paper_generation.version_management_system import VersionManagementSystem

class TestSystemIntegration(unittest.TestCase):
    """Test the complete research workflow"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment"""
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        cls.logger = logging.getLogger("TestSystemIntegration")
        
        # Set up API keys for testing
        if "DEEPSEEK_API_KEY" not in os.environ:
            os.environ["DEEPSEEK_API_KEY"] = "***********************************"
        if "DEEPSEEK_BASE_URL" not in os.environ:
            os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
        
        # Disable mock mode
        os.environ["MOCK_MODE"] = "false"
        os.environ["ENABLE_MOCK_DATA"] = "false"
        
        # Set up output directory
        cls.output_dir = os.path.join("output", "integration_test", f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(cls.output_dir, exist_ok=True)
        
        # Create unified API client
        cls.api_client = get_unified_client()
    
    def setUp(self):
        """Set up each test"""
        self.workflow = CompleteResearchWorkflow(
            output_dir=self.output_dir,
            config_path=None  # Use default config
        )
    
    def test_01_api_connection(self):
        """Test API connection"""
        self.logger.info("Testing API connection")
        
        # Test unified API client
        self.assertIsNotNone(self.api_client)
        
        # Test DeepSeek API connection
        response = self.api_client.get_text_response(
            prompt="Respond with 'API test successful'",
            system_message="You are a helpful assistant."
        )
        
        self.assertIsNotNone(response)
        
        # 处理APIResponse对象或字符串
        if hasattr(response, 'content'):
            # 如果是APIResponse对象，使用其content属性
            content = response.content
            self.assertTrue(response.success, "API response indicates failure")
            self.assertIn("successful", content.lower())
        else:
            # 如果是字符串，直接使用
            self.assertIn("successful", response.lower())
        
        self.logger.info("✅ API connection test passed")
    
    def test_02_workflow_initialization(self):
        """Test workflow initialization"""
        self.logger.info("Testing workflow initialization")
        
        # Check if workflow components are initialized correctly
        self.assertIsNotNone(self.workflow.literature_manager)
        self.assertIsNotNone(self.workflow.collaborator)
        self.assertIsNotNone(self.workflow.experiment_designer)
        self.assertIsNotNone(self.workflow.paper_writer)
        
        # Check if status is initialized correctly
        self.assertEqual(self.workflow.status["current_stage"], 0)
        self.assertEqual(self.workflow.status["progress"], 0.0)
        self.assertEqual(len(self.workflow.status["errors"]), 0)
        
        self.logger.info("✅ Workflow initialization test passed")
    
    def test_03_literature_stage(self):
        """Test literature research stage"""
        self.logger.info("Testing literature research stage")
        
        # 导入序列化工具
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from utils.serialization_utils import EnhancedJSONEncoder
        
        # Run literature stage
        research_topic = "Brain-Inspired Neural Networks for Efficient Learning"
        result = self.workflow.run_literature_stage(research_topic)
        
        # Verify result structure
        self.assertIsNotNone(result)
        self.assertIn("papers", result)
        self.assertIn("workflows", result)
        
        # Verify papers were found
        self.assertGreater(len(result["papers"]), 0)
        
        # Save result for inspection
        result_path = os.path.join(self.output_dir, "literature_stage_result.json")
        with open(result_path, "w", encoding="utf-8") as f:
            json.dump(result, f, cls=EnhancedJSONEncoder, ensure_ascii=False, indent=2)
        
        self.logger.info(f"✅ Literature stage test passed. Results saved to {result_path}")
        
        # Update workflow status
        self.workflow.status["stage1_completed"] = True
    
    def test_04_paper_generation(self):
        """Test paper generation"""
        self.logger.info("Testing paper generation")
        
        # 导入序列化工具
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from utils.serialization_utils import save_to_json_file, serialize_agent_response
        
        # Generate a paper on a simple topic
        paper_writer = BrainPaperWriter(
            model="deepseek-chat", 
            temperature=0.7,
            llm_client=self.workflow.unified_client
        )
        
        paper_content = paper_writer.generate_paper(
            research_topic="Brain-Inspired Meta-Learning for Efficient Neural Networks",
            target_venue="ICML"
        )
        
        # Process paper content to make it JSON serializable
        serializable_paper = {}
        
        for key, value in paper_content.items():
            # Handle AgentResponse objects and nested dictionaries
            if hasattr(value, 'content') and hasattr(value, 'confidence'):
                serializable_paper[key] = serialize_agent_response(value)
            elif isinstance(value, dict):
                # Process nested dictionary
                serializable_paper[key] = {}
                for subkey, subvalue in value.items():
                    if hasattr(subvalue, 'content') and hasattr(subvalue, 'confidence'):
                        serializable_paper[key][subkey] = serialize_agent_response(subvalue)
                    else:
                        serializable_paper[key][subkey] = subvalue
            else:
                serializable_paper[key] = value
        
        # 输出论文内容摘要
        print("\n📄 生成论文摘要:")
        if "abstract" in paper_content:
            abstract = paper_content["abstract"]
            if hasattr(abstract, 'content'):
                print(f"  {abstract.content[:200]}...")
            else:
                print(f"  {str(abstract)[:200]}...")
        
        if "sections" in paper_content and isinstance(paper_content["sections"], dict):
            sections = paper_content["sections"]
            self.assertTrue(all(section in sections for section in 
                             ["abstract", "introduction", "related_work", "methodology", 
                              "experiments", "results", "conclusion"]))
        else:
            # 直接检查是否包含关键部分
            self.assertTrue(any(key in paper_content for key in 
                             ["abstract", "introduction", "related_work", "methodology", 
                              "experiments", "results", "conclusion"]))
        
        # 检查元数据
        self.assertIn("metadata", paper_content)
        # 检查LaTeX内容
        self.assertIn("latex", paper_content)
        
        # Save paper content
        paper_path = os.path.join(self.output_dir, "generated_paper.json")
        save_to_json_file(serializable_paper, paper_path)
        print(f"📄 论文JSON保存到: {paper_path}")
        
        # Save LaTeX content
        latex_content = paper_content["latex"]
        if hasattr(latex_content, 'content'):
            latex_content = latex_content.content
            
        latex_path = os.path.join(self.output_dir, "generated_paper.tex")
        with open(latex_path, "w", encoding="utf-8") as f:
            f.write(str(latex_content))
        print(f"📄 论文LaTeX保存到: {latex_path}")
        
        self.logger.info(f"✅ Paper generation test passed. Paper saved to {paper_path}")
    
    def test_05_version_management(self):
        """Test version management"""
        self.logger.info("Testing version management")
        
        # Create a simple paper content
        paper_content = {
            "sections": {
                "abstract": "This is a test abstract",
                "introduction": "This is a test introduction"
            },
            "metadata": {
                "title": "Test Paper",
                "authors": ["Test Author"]
            }
        }
        
        # Generate LaTeX content
        latex_content = f"""\\documentclass{{article}}
\\title{{{paper_content['metadata']['title']}}}
\\author{{{paper_content['metadata']['authors'][0]}}}

\\begin{{document}}
\\maketitle

\\begin{{abstract}}
{paper_content['sections']['abstract']}
\\end{{abstract}}

\\section{{Introduction}}
{paper_content['sections']['introduction']}

\\end{{document}}
"""
        
        # Create versions
        version1 = self.workflow.version_manager.create_version(
            latex_content=latex_content,
            metadata=paper_content["metadata"],
            quality_score=6.5,
            comment="Initial version"
        )
        
        # Modify content for second version
        modified_latex = latex_content.replace(
            "This is a test introduction",
            "This is an improved test introduction with additional content"
        )
        
        version2 = self.workflow.version_manager.create_version(
            latex_content=modified_latex,
            metadata=paper_content["metadata"],
            quality_score=7.2,
            comment="Improved version"
        )
        
        # List versions
        versions = self.workflow.version_manager.list_versions()
        self.assertEqual(len(versions), 2)
        
        # Compare versions
        diff, similarity = self.workflow.version_manager.compare_versions(
            version1.version_id, version2.version_id
        )
        
        self.assertIsNotNone(diff)
        self.assertLess(similarity, 100.0)
        
        self.logger.info("✅ Version management test passed")
    
    def test_06_complete_workflow_short(self):
        """Test abbreviated complete workflow with simplified research topic"""
        self.logger.info("Testing abbreviated complete workflow")
        
        try:
            # Run literature stage only
            research_topic = "Efficient Neural Networks for Image Classification"
            result = self.workflow.run_literature_stage(research_topic)
            
            # Verify result structure
            self.assertIsNotNone(result)
            self.assertIn("papers", result)
            self.assertIn("workflows", result)
            
            # Verify papers were found
            self.assertGreater(len(result["papers"]), 0)
            
            # Manually set status to completed
            self.workflow.status["stage1_completed"] = True
            
            # Verify status
            self.assertTrue(self.workflow.status["stage1_completed"])
            
            # Save result
            result_path = os.path.join(self.output_dir, "abbreviated_workflow_result.json")
            with open(result_path, "w", encoding="utf-8") as f:
                from utils.serialization_utils import EnhancedJSONEncoder
                json.dump(result, f, cls=EnhancedJSONEncoder, ensure_ascii=False, indent=2)
            
            self.logger.info(f"✅ Abbreviated workflow test passed. Results saved to {result_path}")
            
        except Exception as e:
            self.fail(f"Workflow test failed: {e}")


if __name__ == "__main__":
    unittest.main() 