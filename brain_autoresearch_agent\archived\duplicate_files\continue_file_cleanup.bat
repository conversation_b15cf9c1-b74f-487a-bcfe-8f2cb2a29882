@echo off
echo 继续精简根目录文件...

REM 归档更多测试文件到archived/old_tests/
echo 移动更多测试文件...
move test_complete_paper_system.py archived\old_tests\ 2>NUL
move test_complete_real_api_system.py archived\old_tests\ 2>NUL
move test_deepseek_api.py archived\old_tests\ 2>NUL
move test_end_to_end_system.py archived\old_tests\ 2>NUL
move test_enhanced_prompts.py archived\old_tests\ 2>NUL
move test_enhanced_stage4.py archived\old_tests\ 2>NUL
move test_experiment_generator.py archived\old_tests\ 2>NUL
move test_experiment_generator_simple.py archived\old_tests\ 2>NUL
move test_integrated_system.py archived\old_tests\ 2>NUL
move test_latex_output_fix.py archived\old_tests\ 2>NUL
move test_modular_real_api_system.py archived\old_tests\ 2>NUL
move test_multimodal_api.py archived\old_tests\ 2>NUL
move test_priority_one_complete.py archived\old_tests\ 2>NUL
move test_qwen_api.py archived\old_tests\ 2>NUL
move test_real_api_comprehensive.py archived\old_tests\ 2>NUL
move test_reasoning_simple.py archived\old_tests\ 2>NUL

REM 归档测试结果文件到archived/output/
echo 移动测试结果文件...
move test_workflow_analysis_result.json archived\output\ 2>NUL
move test_workflow_extraction_result.json archived\output\ 2>NUL
move generated_paper_1752913237.tex archived\output\ 2>NUL
move multimodal_test_report_1752913237.txt archived\output\ 2>NUL
move PERFORMANCE_METRICS.json archived\output\ 2>NUL

REM 归档更多文档到archived/temp_docs/
echo 移动更多临时文档...
move ADVANCED_SYSTEM_TECHNICAL_COMPARISON.md archived\temp_docs\ 2>NUL
move ENHANCED_PAPER_WRITING_GUIDE.md archived\temp_docs\ 2>NUL
move FINAL_TASK_COMPLETION_REPORT.md archived\temp_docs\ 2>NUL
move REASONING_COMPLETION_REPORT.md archived\temp_docs\ 2>NUL
move TEST_EXECUTION_LOG.md archived\temp_docs\ 2>NUL
move README_NEW.md archived\temp_docs\ 2>NUL

REM 归档工具文件到archived/duplicate_files/
echo 移动工具和重复文件...
move comprehensive_test_runner.py archived\old_tests\ 2>NUL
move real_api_test.py archived\old_tests\ 2>NUL
move simple_test.py archived\old_tests\ 2>NUL
move move_files_to_archive.bat archived\duplicate_files\ 2>NUL

REM 归档分析报告文件到archived/temp_docs/
echo 移动分析报告文件...
move FILE_ANALYSIS_REPORT.md archived\temp_docs\ 2>NUL
move CODE_REORGANIZATION_PLAN.md archived\temp_docs\ 2>NUL

echo 第二轮文件精简完成！
pause
