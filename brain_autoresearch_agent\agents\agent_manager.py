"""
脑启发智能AutoResearch Agent - 代理管理器
负责管理多专家代理系统，协调代理间的协作和任务分配
"""

from typing import Dict, List, Any, Optional, Tuple
import json
import time
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed

from agents.base_agent import BaseAgent, AgentTask, AgentResponse
from core.unified_api_client import UnifiedAPIClient


class AgentManager:
    """多专家代理管理器"""
    
    # 静态变量，跟踪是否已经初始化过代理
    _agents_initialized = False
    
    def __init__(self, unified_client: UnifiedAPIClient):
        """
        初始化代理管理器
        
        Args:
            unified_client: 统一API客户端实例
        """
        self.unified_client = unified_client
        self.agents: Dict[str, BaseAgent] = {}
        self.task_queue: List[AgentTask] = []
        self.completed_tasks: List[AgentResponse] = []
        
        # 初始化默认专家代理
        self._initialize_default_agents()
        
        # 协作历史
        self.collaboration_history: List[Dict[str, Any]] = []
        
        # 性能统计
        self.stats = {
            "total_tasks": 0,
            "successful_tasks": 0,
            "collaboration_count": 0,
            "average_confidence": 0.0
        }
    
    def _initialize_default_agents(self):
        """初始化默认的专家代理"""
        # 如果已经初始化过，避免重复注册
        if AgentManager._agents_initialized and len(self.agents) > 0:
            print("⚠️ 代理已经初始化，避免重复注册")
            return
            
        try:
            # 导入所有专家代理
            from agents.expert_agents.ai_technology_expert import AITechnologyExpert
            from agents.expert_agents.neuroscience_expert import NeuroscienceExpert
            from agents.expert_agents.data_analysis_expert import DataAnalysisExpert
            from agents.expert_agents.experiment_design_expert import ExperimentDesignExpert
            from agents.expert_agents.paper_writing_expert import PaperWritingExpert
            
            # 注册所有专家代理
            experts = [
                ("ai_technology", AITechnologyExpert(self.unified_client)),
                ("neuroscience", NeuroscienceExpert(self.unified_client)),
                ("data_analysis", DataAnalysisExpert(self.unified_client)),
                ("experiment_design", ExperimentDesignExpert(self.unified_client)),
                ("paper_writing", PaperWritingExpert(self.unified_client))
            ]
            
            for agent_id, agent in experts:
                self.register_agent(agent_id, agent)
            
            print("✅ 代理管理器初始化完成")
            print(f"   已注册专家: {list(self.agents.keys())}")
            
            # 标记为已初始化
            AgentManager._agents_initialized = True
            
        except Exception as e:
            print(f"❌ 代理管理器初始化失败: {e}")
            # 至少注册AI技术专家作为备用
            try:
                ai_expert = AITechnologyExpert(self.unified_client)
                self.register_agent("ai_technology", ai_expert)
                print("✅ 至少注册了AI技术专家作为备用")
                
                # 标记为已初始化
                AgentManager._agents_initialized = True
            except Exception as backup_error:
                print(f"❌ 备用注册也失败: {backup_error}")
    
    def register_agent(self, agent_id: str, agent: BaseAgent) -> bool:
        """
        注册专家代理
        
        Args:
            agent_id: 代理唯一标识
            agent: 代理实例
            
        Returns:
            bool: 注册是否成功
        """
        try:
            if agent_id in self.agents:
                print(f"⚠️ 代理 {agent_id} 已存在，将覆盖")
            
            self.agents[agent_id] = agent
            print(f"✅ 代理注册成功: {agent_id} ({agent.agent_type})")
            return True
            
        except Exception as e:
            print(f"❌ 代理注册失败: {e}")
            return False
    
    def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """获取指定代理"""
        return self.agents.get(agent_id)
    
    def get_available_experts(self) -> List[str]:
        """获取所有可用专家代理的类型列表"""
        expert_types = []
        for agent_id, agent in self.agents.items():
            expert_types.append(agent.agent_type)
        return expert_types
    
    def get_registered_agents(self) -> List[str]:
        """
        获取所有已注册代理的ID列表
        
        Returns:
            List[str]: 已注册代理的ID列表
        """
        return list(self.agents.keys())
    
    def list_agents(self) -> Dict[str, Dict[str, Any]]:
        """列出所有注册的代理及其状态"""
        agent_info = {}
        for agent_id, agent in self.agents.items():
            agent_info[agent_id] = agent.get_status()
        return agent_info
    
    def create_task(self, 
                   task_type: str,
                   input_data: Dict[str, Any],
                   requirements: List[str] = None,
                   priority: int = 1) -> AgentTask:
        """
        创建新任务
        
        Args:
            task_type: 任务类型
            input_data: 输入数据
            requirements: 任务要求
            priority: 优先级 (1-5)
            
        Returns:
            AgentTask: 创建的任务
        """
        task_id = f"task_{int(time.time())}_{len(self.task_queue)}"
        
        task = AgentTask(
            task_id=task_id,
            task_type=task_type,
            input_data=input_data,
            requirements=requirements or [],
            priority=priority
        )
        
        self.task_queue.append(task)
        print(f"📋 任务创建成功: {task_id} (类型: {task_type})")
        
        return task
    
    def assign_task_to_agent(self, task: AgentTask, agent_id: str) -> Optional[AgentResponse]:
        """
        将任务分配给指定代理
        
        Args:
            task: 要执行的任务
            agent_id: 目标代理ID
            
        Returns:
            AgentResponse: 执行结果
        """
        try:
            agent = self.get_agent(agent_id)
            if not agent:
                print(f"❌ 代理不存在: {agent_id}")
                return None
            
            print(f"🎯 分配任务 {task.task_id} 给代理 {agent_id}")
            
            # 执行任务
            response = agent.process_task(task)
            
            # 更新统计
            self.stats["total_tasks"] += 1
            if response.confidence > 0.7:
                self.stats["successful_tasks"] += 1
            
            # 记录完成的任务
            self.completed_tasks.append(response)
            
            return response
            
        except Exception as e:
            print(f"❌ 任务分配失败: {e}")
            return None
    
    def auto_assign_task(self, task: AgentTask) -> Dict[str, AgentResponse]:
        """
        自动分配任务给最适合的代理
        
        Args:
            task: 要执行的任务
            
        Returns:
            Dict[agent_id, AgentResponse]: 各代理的执行结果
        """
        try:
            print(f"🤖 自动分配任务: {task.task_id}")
            
            # 根据任务类型选择合适的代理
            suitable_agents = self._select_suitable_agents(task)
            
            if not suitable_agents:
                print("❌ 没有找到合适的代理")
                return {}
            
            # 执行任务
            results = {}
            for agent_id in suitable_agents:
                response = self.assign_task_to_agent(task, agent_id)
                if response:
                    results[agent_id] = response
            
            return results
            
        except Exception as e:
            print(f"❌ 自动任务分配失败: {e}")
            return {}
    
    def _select_suitable_agents(self, task: AgentTask) -> List[str]:
        """选择适合任务的代理"""
        suitable_agents = []
        
        # 简单的任务类型匹配策略
        task_type = task.task_type.lower()
        
        if any(keyword in task_type for keyword in ["ai", "technology", "algorithm", "model"]):
            if "ai_technology" in self.agents:
                suitable_agents.append("ai_technology")
        
        # 如果没有特定匹配，使用所有可用代理
        if not suitable_agents:
            suitable_agents = list(self.agents.keys())
        
        return suitable_agents
    
    def collaborative_analysis(self, 
                             task: AgentTask, 
                             agent_ids: List[str] = None) -> Dict[str, Any]:
        """
        多专家协作分析
        
        Args:
            task: 分析任务
            agent_ids: 参与协作的代理ID列表（None表示所有代理）
            
        Returns:
            Dict: 协作分析结果
        """
        try:
            print(f"🤝 启动多专家协作分析: {task.task_id}")
            print("=" * 60)
            
            # 确定参与协作的代理
            if agent_ids is None:
                agent_ids = list(self.agents.keys())
            
            print(f"👥 参与协作的专家: {len(agent_ids)}个")
            for i, agent_id in enumerate(agent_ids, 1):
                agent = self.get_agent(agent_id)
                if agent:
                    print(f"   {i}. {agent_id}: {agent.agent_type} ({agent.specialization})")
            
            print("\n🔍 阶段1: 各专家独立分析")
            print("-" * 40)
            
            # 第一阶段：各专家独立分析
            individual_results = {}
            for i, agent_id in enumerate(agent_ids, 1):
                print(f"\n📊 专家{i} - {agent_id}正在分析...")
                response = self.assign_task_to_agent(task, agent_id)
                if response:
                    individual_results[agent_id] = response
                    print(f"   ✅ 分析完成，置信度: {response.confidence:.2f}")
                    print(f"   📝 结果摘要: {response.content[:100]}...")
                else:
                    print(f"   ❌ 分析失败")
            
            if not individual_results:
                return {"error": "没有代理成功完成分析"}
            
            print(f"\n✅ 独立分析阶段完成，{len(individual_results)}位专家提供了分析")
            
            # 第二阶段：专家间协作
            print(f"\n🤝 阶段2: 专家间协作讨论")
            print("-" * 40)
            
            collaboration_results = []
            agent_list = list(individual_results.keys())
            
            if len(agent_list) >= 2:
                print(f"🔄 开始{len(agent_list)}位专家的两两协作...")
                
                for i, agent_id1 in enumerate(agent_list):
                    for agent_id2 in agent_list[i+1:]:
                        print(f"\n🤝 协作对话: {agent_id1} ↔ {agent_id2}")
                        
                        agent1 = self.get_agent(agent_id1)
                        agent2 = self.get_agent(agent_id2)
                        
                        if agent1 and agent2:
                            # 准备协作数据
                            shared_data = {
                                "task": task.input_data,
                                "agent1_analysis": individual_results[agent_id1].metadata,
                                "agent2_analysis": individual_results[agent_id2].metadata
                            }
                            
                            print(f"   📤 {agent1.agent_type} 发起协作请求...")
                            # 执行协作
                            collab_result = agent1.collaborate_with(agent2, shared_data)
                            collaboration_results.append(collab_result)
                            
                            # 显示协作结果
                            if collab_result and 'result' in collab_result:
                                if 'error' not in collab_result['result']:
                                    print(f"   ✅ 协作成功")
                                    if 'collaboration_insights' in collab_result['result']:
                                        insight = collab_result['result']['collaboration_insights']
                                        # 安全处理insight，可能是字典、字符串或列表
                                        if isinstance(insight, str):
                                            print(f"   💡 协作洞察: {insight[:150]}...")
                                        elif isinstance(insight, dict):
                                            insight_str = json.dumps(insight, ensure_ascii=False)
                                            print(f"   💡 协作洞察: {insight_str[:150]}...")
                                        elif isinstance(insight, list):
                                            insight_str = str(insight)
                                            print(f"   💡 协作洞察: {insight_str[:150]}...")
                                        else:
                                            print(f"   💡 协作洞察: {str(insight)[:150]}...")
                                    if 'confidence' in collab_result['result']:
                                        conf = collab_result['result']['confidence']
                                        print(f"   📊 协作置信度: {conf}")
                                else:
                                    print(f"   ⚠️ 协作遇到问题: {collab_result['result']['error']}")
                            else:
                                print(f"   ❌ 协作失败")
                        
                        # 协作间隔，避免API过载
                        time.sleep(1)
                
                print(f"\n✅ 协作阶段完成，共进行了{len(collaboration_results)}对协作讨论")
            else:
                print("📝 只有1位专家参与，跳过协作阶段")
            
            # 第三阶段：结果融合
            print(f"\n🧠 阶段3: 知识融合与共识达成")
            print("-" * 40)
            
            print("🔄 正在融合所有专家的分析结果...")
            fusion_result = self._fuse_collaboration_results(
                individual_results, 
                collaboration_results
            )
            
            # 显示融合结果详情
            if fusion_result:
                summary = fusion_result.get("collaboration_summary", {})
                print(f"📊 融合结果统计:")
                print(f"   参与专家数量: {summary.get('participating_experts', 0)}")
                print(f"   协作对话数量: {summary.get('collaboration_pairs', 0)}")
                print(f"   整体置信度: {summary.get('overall_confidence', 0)}")
                
                consensus = fusion_result.get("consensus_level", 0)
                print(f"   专家共识水平: {consensus}")
                
                # 显示共识质量
                if consensus >= 0.9:
                    print("   🎯 专家意见高度一致")
                elif consensus >= 0.7:
                    print("   ✅ 专家意见基本一致")
                elif consensus >= 0.5:
                    print("   ⚠️ 专家意见存在分歧")
                else:
                    print("   ❌ 专家意见分歧较大")
                
                # 显示综合洞察
                insights = fusion_result.get("synthesized_insights", [])
                if insights:
                    print(f"\n💡 综合洞察 (共{len(insights)}条):")
                    for i, insight in enumerate(insights[:3], 1):  # 显示前3条
                        print(f"   {i}. {insight}")
                
                # 显示一致性建议
                recommendations = fusion_result.get("recommendations", [])
                if recommendations:
                    print(f"\n🎯 共识建议 (共{len(recommendations)}条):")
                    for i, rec in enumerate(recommendations[:3], 1):  # 显示前3条
                        print(f"   {i}. {rec}")
            
            print(f"\n✅ 多专家协作分析完成!")
            print("=" * 60)
            
            # 更新协作历史
            self.collaboration_history.append({
                "task_id": task.task_id,
                "participants": agent_ids,
                "individual_results": len(individual_results),
                "collaboration_pairs": len(collaboration_results),
                "timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
            })
            
            self.stats["collaboration_count"] += 1
            
            return fusion_result
            
        except Exception as e:
            print(f"❌ 协作分析失败: {e}")
            import traceback
            traceback.print_exc()
            return {"error": str(e)}
    
    def _fuse_collaboration_results(self, 
                                  individual_results: Dict[str, AgentResponse],
                                  collaboration_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """融合协作分析结果"""
        try:
            # 计算整体置信度
            confidences = [r.confidence for r in individual_results.values()]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0
            
            # 提取关键洞察
            all_insights = []
            for response in individual_results.values():
                if hasattr(response, 'metadata') and 'analysis_result' in response.metadata:
                    analysis = response.metadata['analysis_result']
                    if isinstance(analysis, dict):
                        # 提取各种洞察字段
                        for key in ['technical_insights', 'insights', 'key_findings']:
                            if key in analysis:
                                value = analysis[key]
                                if isinstance(value, list):
                                    all_insights.extend(value)
                                elif isinstance(value, str):
                                    all_insights.append(value)
                                elif isinstance(value, dict):
                                    # 如果是字典，尝试提取其值
                                    for v in value.values():
                                        if isinstance(v, str):
                                            all_insights.append(v)
                                        elif isinstance(v, list):
                                            all_insights.extend(v)
            
            # 提取协作洞察
            collaboration_insights = []
            for collab in collaboration_results:
                try:
                    if 'result' in collab and 'collaboration_insights' in collab['result']:
                        insight = collab['result']['collaboration_insights']
                        if isinstance(insight, list):
                            collaboration_insights.extend(insight)
                        elif isinstance(insight, str):
                            collaboration_insights.append(insight)
                        elif isinstance(insight, dict):
                            # 如果是字典，尝试提取值
                            for v in insight.values():
                                if isinstance(v, str):
                                    collaboration_insights.append(v)
                                elif isinstance(v, list):
                                    collaboration_insights.extend(v)
                except Exception as e:
                    print(f"⚠️ 协作洞察提取警告: {e}")
                    continue
            
            # 生成综合分析
            fusion_result = {
                "collaboration_summary": {
                    "participating_experts": len(individual_results),
                    "collaboration_pairs": len(collaboration_results),
                    "overall_confidence": round(avg_confidence, 2),
                    "analysis_timestamp": time.strftime('%Y-%m-%d %H:%M:%S')
                },
                "individual_analyses": {
                    agent_id: {
                        "agent_type": response.agent_type,
                        "confidence": response.confidence,
                        "key_content": response.content[:200] + "..." if len(response.content) > 200 else response.content
                    }
                    for agent_id, response in individual_results.items()
                },
                "synthesized_insights": all_insights[:10],  # 取前10个洞察
                "collaboration_insights": collaboration_insights,
                "consensus_level": self._calculate_consensus_level(individual_results),
                "recommendations": self._generate_consensus_recommendations(individual_results)
            }
            
            return fusion_result
            
        except Exception as e:
            print(f"❌ 结果融合失败: {e}")
            return {"error": f"结果融合失败: {e}"}
    
    def _calculate_consensus_level(self, results: Dict[str, AgentResponse]) -> float:
        """计算专家共识水平"""
        # 简单实现：基于置信度方差
        confidences = [r.confidence for r in results.values()]
        if len(confidences) < 2:
            return 1.0
        
        avg = sum(confidences) / len(confidences)
        variance = sum((c - avg) ** 2 for c in confidences) / len(confidences)
        consensus = 1.0 - min(variance, 1.0)  # 方差越小，共识越高
        
        return round(consensus, 2)
    
    def _generate_consensus_recommendations(self, results: Dict[str, AgentResponse]) -> List[str]:
        """生成共识性建议"""
        # 提取所有建议
        all_recommendations = []
        
        for response in results.values():
            # 从元数据中提取建议
            if hasattr(response, 'metadata'):
                metadata = response.metadata
                for key in ['recommendations', 'suggestions', 'advice']:
                    if key in metadata and isinstance(metadata[key], list):
                        all_recommendations.extend(metadata[key])
        
        # 简单去重和筛选
        unique_recommendations = list(set(all_recommendations))
        return unique_recommendations[:5]  # 返回前5个建议
    
    def parallel_execution(self, tasks: List[AgentTask]) -> Dict[str, List[AgentResponse]]:
        """并行执行多个任务"""
        try:
            print(f"⚡ 并行执行 {len(tasks)} 个任务")
            
            results = {}
            with ThreadPoolExecutor(max_workers=len(self.agents)) as executor:
                # 提交所有任务
                future_to_task = {}
                for task in tasks:
                    suitable_agents = self._select_suitable_agents(task)
                    for agent_id in suitable_agents:
                        future = executor.submit(self.assign_task_to_agent, task, agent_id)
                        future_to_task[future] = (task.task_id, agent_id)
                
                # 收集结果
                for future in as_completed(future_to_task):
                    task_id, agent_id = future_to_task[future]
                    try:
                        response = future.result()
                        if response:
                            if task_id not in results:
                                results[task_id] = []
                            results[task_id].append(response)
                    except Exception as e:
                        print(f"❌ 任务 {task_id} 在代理 {agent_id} 上执行失败: {e}")
            
            return results
            
        except Exception as e:
            print(f"❌ 并行执行失败: {e}")
            return {}
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统整体状态"""
        # 更新平均置信度
        if self.completed_tasks:
            avg_confidence = sum(t.confidence for t in self.completed_tasks) / len(self.completed_tasks)
            self.stats["average_confidence"] = round(avg_confidence, 2)
        
        return {
            "agent_manager": {
                "registered_agents": len(self.agents),
                "active_agents": sum(1 for a in self.agents.values() if a.is_active),
                "task_queue_size": len(self.task_queue),
                "completed_tasks": len(self.completed_tasks)
            },
            "performance_stats": self.stats,
            "agent_details": self.list_agents(),
            "recent_collaborations": len(self.collaboration_history)
        }
    
    def clear_completed_tasks(self):
        """清理已完成的任务记录"""
        cleared_count = len(self.completed_tasks)
        self.completed_tasks.clear()
        print(f"🧹 已清理 {cleared_count} 个完成的任务记录")
    
    def shutdown(self):
        """关闭代理管理器"""
        print("🔄 正在关闭代理管理器...")
        
        for agent_id, agent in self.agents.items():
            agent.is_active = False
            print(f"   ✅ 代理 {agent_id} 已停用")
        
        self.clear_completed_tasks()
        print("✅ 代理管理器已关闭")
