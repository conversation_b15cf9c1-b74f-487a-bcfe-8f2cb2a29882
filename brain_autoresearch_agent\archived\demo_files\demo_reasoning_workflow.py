"""
完整的实验推理工作流演示
展示端到端的推理流程和交付物生成
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from reasoning.reasoning_workflow import ExperimentReasoningWorkflow


def demonstrate_reasoning_workflow():
    """演示完整的推理工作流"""
    
    print("🎯 实验推理工作流演示")
    print("=" * 80)
    
    # 创建工作流协调器
    print("🔧 初始化推理工作流...")
    workflow = ExperimentReasoningWorkflow()
    print("✅ 初始化完成")
    
    # 定义研究问题和假设
    research_question = "如何基于大脑神经可塑性机制设计自适应深度神经网络？"
    hypothesis = [
        "Hebbian学习规则可以指导神经网络权重的动态调整",
        "突触可塑性机制能够提高网络的泛化能力", 
        "多尺度时间窗口的可塑性更新能够捕获复杂的时序依赖关系"
    ]
    
    background = {
        "domain": "brain-inspired machine learning",
        "related_work": [
            "神经可塑性与机器学习",
            "自适应神经网络架构",
            "生物启发的学习算法"
        ],
        "motivation": "现有深度学习模型缺乏生物神经系统的自适应和可塑性特征",
        "technical_context": "PyTorch, 神经科学, 计算神经科学"
    }
    
    workflow_context = {
        "paper_structure": "实验驱动的论文结构",
        "computational_resources": "GPU集群可用",
        "timeline": "3个月研究周期"
    }
    
    # 定义进度追踪
    progress_stages = []
    
    def track_progress(stage: str, description: str, progress: float):
        """追踪进度"""
        progress_info = f"{stage}: {description} ({progress*100:.0f}%)"
        progress_stages.append(progress_info)
        print(f"📊 {progress_info}")
    
    try:
        # 运行完整推理流程
        print(f"\n🚀 开始推理流程")
        print(f"🔬 研究问题: {research_question}")
        print(f"💡 假设数量: {len(hypothesis)}")
        
        session = workflow.run_complete_reasoning_flow(
            research_question=research_question,
            hypothesis=hypothesis,
            background=background,
            workflow_context=workflow_context,
            target_venue="NeurIPS",
            progress_callback=track_progress
        )
        
        print(f"\n🎉 推理流程成功完成!")
        print(f"📋 会话ID: {session.session_id}")
        
        # 显示关键结果
        print(f"\n📊 关键结果摘要:")
        if session.research_problem:
            print(f"  📈 研究价值评分: {session.research_problem.value_score:.2f}/10")
        
        if session.experiment_plan:
            print(f"  🔬 实验类型: {session.experiment_plan.experiment_type}")
            print(f"  📏 变量数量: {len(session.experiment_plan.variables)}")
            print(f"  📊 评估指标: {len(session.experiment_plan.metrics)}")
        
        if session.implementation_plan:
            print(f"  🐍 编程语言: {session.implementation_plan.programming_language}")
            print(f"  📚 框架: {', '.join(session.implementation_plan.frameworks)}")
            print(f"  🔧 实现步骤: {len(session.implementation_plan.steps)}")
        
        if session.visualization_plan:
            print(f"  📈 图表数量: {len(session.visualization_plan.charts)}")
            print(f"  🛠️ 推荐工具: {', '.join(session.visualization_plan.recommended_tools[:3])}")
        
        # 生成交付物
        print(f"\n📦 生成交付物...")
        deliverables = workflow.generate_final_deliverables(session)
        
        print(f"\n✅ 演示完成!")
        print(f"📄 生成了 {len(deliverables)} 个交付物:")
        for name, path in deliverables.items():
            print(f"  📋 {name}: {Path(path).name}")
        
        return session, deliverables
        
    except Exception as e:
        print(f"\n❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None


def analyze_session_results(session):
    """分析会话结果"""
    
    if not session:
        print("❌ 没有有效的会话数据")
        return
    
    print(f"\n🔍 会话结果分析")
    print("-" * 50)
    
    # 分析完成状态
    completion_count = sum(session.completion_status.values())
    total_stages = len(session.completion_status)
    completion_rate = completion_count / total_stages * 100
    
    print(f"📊 完成率: {completion_rate:.0f}% ({completion_count}/{total_stages})")
    
    for stage, completed in session.completion_status.items():
        status = "✅" if completed else "❌"
        print(f"  {status} {stage}")
    
    # 分析推理质量
    if session.research_problem and session.research_problem.value_score:
        score = session.research_problem.value_score
        if score >= 8:
            quality = "优秀"
        elif score >= 6:
            quality = "良好"
        elif score >= 4:
            quality = "一般"
        else:
            quality = "需要改进"
        
        print(f"\n🎯 研究质量: {quality} ({score:.1f}/10)")
    
    # 分析专家交互
    expert_count = len(session.expert_interactions)
    print(f"\n👥 专家交互: {expert_count} 次")
    
    # 分析推理日志
    log_count = len(session.reasoning_log)
    print(f"📝 推理记录: {log_count} 条")


if __name__ == "__main__":
    # 运行演示
    session, deliverables = demonstrate_reasoning_workflow()
    
    # 分析结果
    if session:
        analyze_session_results(session)
        
        # 显示部分交付物内容
        print(f"\n📖 部分交付物预览:")
        try:
            if "综合报告" in deliverables:
                report_path = deliverables["综合报告"]
                if os.path.exists(report_path):
                    with open(report_path, 'r', encoding='utf-8') as f:
                        content = f.read()[:500]  # 前500字符
                    print(f"\n📄 综合报告预览:")
                    print(content + "..." if len(content) == 500 else content)
        except Exception as e:
            print(f"📖 预览失败: {e}")
    
    print(f"\n🎊 演示结束!")
