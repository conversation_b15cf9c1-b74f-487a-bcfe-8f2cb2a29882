<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script>
      // Check if we're running under Live Server
      if (window.location.hostname === '127.0.0.1' || window.location.hostname === 'localhost') {
          let lastModified = '';

          // Check for file changes every second
          setInterval(async () => {
              try {
                  const response = await fetch(window.location.href, { method: 'HEAD' });
                  // get a timestamp that shows when the file was last changed
                  const currentModified = response.headers.get('last-modified');

                  if (lastModified && lastModified !== currentModified) {
                      window.location.reload();
                  }

                  lastModified = currentModified;
              } catch (e) {
                  console.error('Error checking for updates:', e);
              }
          }, 1000);
      }
  </script>
    <script
      id="p5scripttag"
      src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.9.0/p5.min.js"
      integrity="sha512-uaz5GpnQoE6t5echKlX8P52czvsIGgLPcvlzfvRubLZ1Hp8JemUDnbUiAahbVtPb+jUVrNETuXvAhDDF/N3M4w=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    ></script>

    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/atom-one-dark.min.css"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/python.min.js"></script>

    <script>
      <!-- placeholder -->
    </script>
    <title>AI Scientist-v2 Visualization</title>
    <style>
      body,
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      body {
        background-color: #ffffff;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      }
      #canvas-container {
        position: absolute;
        left: 0;
        top: 0;
        width: 40vw;
        height: 100vh;
        background-color: inherit;
        padding-top: 40px;
      }
      canvas {
        float: left;
        height: 100vh;
        width: 100vw;
      }
      #text-container {
        float: right;
        height: 100vh;
        width: 50vw;
        background-color: #282c34;
        overflow: auto;
      }
      #plan {
        /* border-left: 2px solid #282c34; */
        background-color: #282c34;
        color: #f2f0e7;
        min-height: 5rem;
        padding: 1em 0 1em 1em;
      }
      #plot_plan {
        background-color: #282c34;
        color: #f2f0e7;
        min-height: 5rem;
        padding: 1em 0 1em 1em;
        white-space: pre-wrap;
      }
      #exec_time_feedback {
        margin-top: 20px;
        padding: 10px;
        background-color: #282c34;
        border-left: 3px solid #ff5555;
        color: #f2f0e7;
      }
      #exec_time {
        margin-top: 20px;
        padding: 10px;
        background-color: #282c34;
        border-left: 3px solid #ff5555;
        color: #f2f0e7;
      }
      #exc_info {
        margin-top: 20px;
        padding: 10px;
        background-color: #2c1f1f;
        border-left: 3px solid #ff5555;
        color: #f2f0e7;
      }
      #metrics {
        margin-top: 20px;
        padding: 10px;
        background-color: #282c34;
        color: #f2f0e7;
      }
      #vlm_feedback {
        margin-top: 20px;
        padding: 10px;
        background-color: #1f2c2f;
        color: #f2f0e7;
        border-left: 3px solid #55ff55;
      }
      #vlm_feedback p {
        margin: 0.5em 0;
        white-space: pre-wrap;
      }
      .datasets_successfully_tested {
        margin-top: 20px;
        padding: 10px;
        background-color: #282c34;
        color: #f2f0e7;
        border-left: 3px solid #55ff55;
      }
      .plots-container {
        float: right;
        width: 50vw;
        padding: 1rem;
        background-color: #282c34;
        margin-top: 1rem;
      }

      .plot-item {
        flex: 1 1 300px;
        max-width: 100%;
        margin-bottom: 1rem;
        white-space: pre-wrap;
      }

      .plot-item img {
        width: 100%;
        height: auto;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        display: block;
      }

      .metric-group {
        margin-bottom: 20px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }

      .metric-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
      }

      .metric-table th,
      .metric-table td {
        padding: 8px;
        text-align: left;
        border: 1px solid #ddd;
      }

      .metric-table th {
        background-color: #363b44;
      }

      /* Styles for tabs */
      .tabs-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 49vw;
        background-color: #000000;
        z-index: 10;
        display: flex;
        padding: 0;
      }

      .tab {
        cursor: pointer;
        padding: 10px 15px;
        background-color: #333;
        color: #f2f0e7;
        border: none;
        outline: none;
        transition: background-color 0.3s;
        flex: 1;
        text-align: center;
      }

      .tab:hover {
        background-color: #444;
      }

      .tab.active {
        background-color: #4c76af;
        font-weight: bold;
      }

      .tab.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: #282c34;
      }

      .tab-content {
        display: none;
        padding-top: 40px; /* Space for tabs */
      }

      .tab-content.active {
        display: block;
      }

      .stage-info {
        padding: 10px;
        background-color: #282c34;
        color: #f2f0e7;
        margin-bottom: 10px;
        font-size: 0.9em;
      }

      .stage-status {
        display: inline-block;
        padding: 3px 6px;
        border-radius: 3px;
        margin-left: 8px;
        font-size: 0.8em;
      }

      .stage-status.completed {
        background-color: #4caf50;
      }

      .stage-status.in-progress {
        background-color: #2196f3;
      }

      .stage-status.not-started {
        background-color: #9e9e9e;
      }
    </style>
  </head>
  <body>
    <div class="tabs-container" id="stage-tabs">
      <button class="tab" data-stage="Stage_1" onclick="selectStage('Stage_1')">Stage 1</button>
      <button class="tab" data-stage="Stage_2" onclick="selectStage('Stage_2')">Stage 2</button>
      <button class="tab" data-stage="Stage_3" onclick="selectStage('Stage_3')">Stage 3</button>
      <button class="tab" data-stage="Stage_4" onclick="selectStage('Stage_4')">Stage 4</button>
    </div>

    <div id="canvas-container"></div>

    <pre id="text-container">
        <div id="stage-info" class="stage-info"></div>
        <div id="plan"></div>
        <hr>
        <div id="exc_info"></div>
        <hr>
        <div id="exec_time"></div>
        <hr>
        <div id="exec_time_feedback"></div>
        <hr>
        <div id="metrics"></div>
        <hr>
        <div id="plot_plan"></div>
        <hr>
        <div class="plots-container" id="plots"></div>
        <hr>
        <div id="vlm_feedback"></div>
        <hr>
        <div id="datasets_successfully_tested"></div>
        <hr>
        <code id="code" class="language-python"></code>
        <hr>
        <code id="plot_code" class="language-python"></code>
    </pre>
  </body>
</html>
