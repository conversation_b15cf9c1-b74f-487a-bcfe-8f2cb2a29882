"""
Workflow Data Models for Paper Generation System

定义论文工作流提取和分析相关的数据模型
"""

from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum


class ImplementationFramework(Enum):
    """实现框架枚举"""
    PYTORCH = "PyTorch"
    TENSORFLOW = "TensorFlow"
    JAX = "JAX"
    KERAS = "Keras"
    SKLEARN = "Scikit-learn"
    NUMPY = "NumPy"
    BRIAN2 = "Brian2"
    NEST = "NEST"
    NEURON = "NEURON"
    CUSTOM = "Custom"


class DatasetType(Enum):
    """数据集类型枚举"""
    IMAGE = "Image"
    TEXT = "Text"
    AUDIO = "Audio"
    VIDEO = "Video"
    TABULAR = "Tabular"
    GRAPH = "Graph"
    TIME_SERIES = "Time Series"
    MULTIMODAL = "Multimodal"
    SYNTHETIC = "Synthetic"


class NetworkArchitectureType(Enum):
    """网络架构类型枚举"""
    CNN = "Convolutional Neural Network"
    RNN = "Recurrent Neural Network"
    LSTM = "Long Short-Term Memory"
    GRU = "Gated Recurrent Unit"
    TRANSFORMER = "Transformer"
    ATTENTION = "Attention Mechanism"
    RESNET = "Residual Network"
    DENSENET = "Dense Network"
    UNET = "U-Net"
    GAN = "Generative Adversarial Network"
    VAE = "Variational Autoencoder"
    SNN = "Spiking Neural Network"
    BRAIN_INSPIRED = "Brain-Inspired Architecture"
    HYBRID = "Hybrid Architecture"
    CUSTOM = "Custom Architecture"


class ResearchMethodType(Enum):
    """研究方法类型枚举"""
    SUPERVISED_LEARNING = "Supervised Learning"
    UNSUPERVISED_LEARNING = "Unsupervised Learning"
    REINFORCEMENT_LEARNING = "Reinforcement Learning"
    TRANSFER_LEARNING = "Transfer Learning"
    META_LEARNING = "Meta Learning"
    FEDERATED_LEARNING = "Federated Learning"
    CONTINUAL_LEARNING = "Continual Learning"
    SELF_SUPERVISED = "Self-Supervised Learning"
    WEAKLY_SUPERVISED = "Weakly Supervised Learning"
    NEUROMORPHIC_COMPUTING = "Neuromorphic Computing"
    BRAIN_INSPIRED_LEARNING = "Brain-Inspired Learning"
    COMPARATIVE_STUDY = "Comparative Study"
    ABLATION_STUDY = "Ablation Study"
    THEORETICAL_ANALYSIS = "Theoretical Analysis"


@dataclass
class DatasetInfo:
    """数据集信息"""
    name: str
    type: DatasetType
    size: Optional[str] = None
    description: Optional[str] = None
    source_url: Optional[str] = None
    preprocessing_steps: List[str] = field(default_factory=list)
    split_strategy: Optional[str] = None  # train/val/test split
    characteristics: Dict[str, Any] = field(default_factory=dict)


@dataclass
class NetworkArchitecture:
    """网络架构信息"""
    name: str
    type: NetworkArchitectureType
    description: Optional[str] = None
    layers: List[str] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)
    innovations: List[str] = field(default_factory=list)  # 创新点
    brain_inspiration: Optional[str] = None  # 脑启发机制


@dataclass
class ImplementationDetails:
    """实现细节信息"""
    framework: ImplementationFramework
    version: Optional[str] = None
    hardware_requirements: List[str] = field(default_factory=list)
    software_dependencies: List[str] = field(default_factory=list)
    code_availability: bool = False
    code_url: Optional[str] = None
    computational_requirements: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ExperimentSetup:
    """实验设置信息"""
    objective: str
    hypotheses: List[str] = field(default_factory=list)
    independent_variables: List[str] = field(default_factory=list)
    dependent_variables: List[str] = field(default_factory=list)
    control_variables: List[str] = field(default_factory=list)
    evaluation_metrics: List[str] = field(default_factory=list)
    baseline_methods: List[str] = field(default_factory=list)
    experimental_conditions: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ResearchMethod:
    """研究方法信息"""
    name: str
    type: ResearchMethodType
    description: Optional[str] = None
    procedure: List[str] = field(default_factory=list)
    advantages: List[str] = field(default_factory=list)
    limitations: List[str] = field(default_factory=list)
    applicability: List[str] = field(default_factory=list)


@dataclass
class VisualizationSpec:
    """可视化规格信息"""
    chart_type: str
    purpose: str
    data_source: str
    tools_used: List[str] = field(default_factory=list)
    description: Optional[str] = None
    code_snippet: Optional[str] = None


@dataclass
class PaperWorkflow:
    """完整的论文工作流信息"""
    paper_id: str
    title: str
    authors: List[str] = field(default_factory=list)
    venue: Optional[str] = None
    year: Optional[int] = None
    
    # 核心工作流组件
    datasets: List[DatasetInfo] = field(default_factory=list)
    network_architectures: List[NetworkArchitecture] = field(default_factory=list)
    implementation_details: List[ImplementationDetails] = field(default_factory=list)
    research_methods: List[ResearchMethod] = field(default_factory=list)
    experiment_setup: Optional[ExperimentSetup] = None
    
    # 结果和可视化
    visualizations: List[VisualizationSpec] = field(default_factory=list)
    key_results: List[str] = field(default_factory=list)
    contributions: List[str] = field(default_factory=list)
    
    # 元数据
    extraction_confidence: float = 0.0  # 提取置信度
    extraction_timestamp: datetime = field(default_factory=datetime.now)
    brain_inspired_elements: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'paper_id': self.paper_id,
            'title': self.title,
            'authors': self.authors,
            'venue': self.venue,
            'year': self.year,
            'datasets': [
                {
                    'name': d.name,
                    'type': d.type.value,
                    'size': d.size,
                    'description': d.description,
                    'source_url': d.source_url,
                    'preprocessing_steps': d.preprocessing_steps,
                    'split_strategy': d.split_strategy,
                    'characteristics': d.characteristics
                } for d in self.datasets
            ],
            'network_architectures': [
                {
                    'name': na.name,
                    'type': na.type.value,
                    'description': na.description,
                    'layers': na.layers,
                    'parameters': na.parameters,
                    'innovations': na.innovations,
                    'brain_inspiration': na.brain_inspiration
                } for na in self.network_architectures
            ],
            'implementation_details': [
                {
                    'framework': impl.framework.value,
                    'version': impl.version,
                    'hardware_requirements': impl.hardware_requirements,
                    'software_dependencies': impl.software_dependencies,
                    'code_availability': impl.code_availability,
                    'code_url': impl.code_url,
                    'computational_requirements': impl.computational_requirements
                } for impl in self.implementation_details
            ],
            'research_methods': [
                {
                    'name': rm.name,
                    'type': rm.type.value,
                    'description': rm.description,
                    'procedure': rm.procedure,
                    'advantages': rm.advantages,
                    'limitations': rm.limitations,
                    'applicability': rm.applicability
                } for rm in self.research_methods
            ],
            'experiment_setup': {
                'objective': self.experiment_setup.objective,
                'hypotheses': self.experiment_setup.hypotheses,
                'independent_variables': self.experiment_setup.independent_variables,
                'dependent_variables': self.experiment_setup.dependent_variables,
                'control_variables': self.experiment_setup.control_variables,
                'evaluation_metrics': self.experiment_setup.evaluation_metrics,
                'baseline_methods': self.experiment_setup.baseline_methods,
                'experimental_conditions': self.experiment_setup.experimental_conditions
            } if self.experiment_setup else None,
            'visualizations': [
                {
                    'chart_type': vs.chart_type,
                    'purpose': vs.purpose,
                    'data_source': vs.data_source,
                    'tools_used': vs.tools_used,
                    'description': vs.description,
                    'code_snippet': vs.code_snippet
                } for vs in self.visualizations
            ],
            'key_results': self.key_results,
            'contributions': self.contributions,
            'extraction_confidence': self.extraction_confidence,
            'extraction_timestamp': self.extraction_timestamp.isoformat(),
            'brain_inspired_elements': self.brain_inspired_elements
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'PaperWorkflow':
        """从字典创建实例"""
        workflow = cls(
            paper_id=data.get('paper_id', ''),
            title=data.get('title', ''),
            authors=data.get('authors', []),
            venue=data.get('venue'),
            year=data.get('year')
        )
        
        # 解析数据集
        for d_data in data.get('datasets', []):
            dataset = DatasetInfo(
                name=d_data['name'],
                type=DatasetType(d_data['type']),
                size=d_data.get('size'),
                description=d_data.get('description'),
                source_url=d_data.get('source_url'),
                preprocessing_steps=d_data.get('preprocessing_steps', []),
                split_strategy=d_data.get('split_strategy'),
                characteristics=d_data.get('characteristics', {})
            )
            workflow.datasets.append(dataset)
        
        # 解析网络架构
        for na_data in data.get('network_architectures', []):
            arch = NetworkArchitecture(
                name=na_data['name'],
                type=NetworkArchitectureType(na_data['type']),
                description=na_data.get('description'),
                layers=na_data.get('layers', []),
                parameters=na_data.get('parameters', {}),
                innovations=na_data.get('innovations', []),
                brain_inspiration=na_data.get('brain_inspiration')
            )
            workflow.network_architectures.append(arch)
        
        # 解析实现细节
        for impl_data in data.get('implementation_details', []):
            impl = ImplementationDetails(
                framework=ImplementationFramework(impl_data['framework']),
                version=impl_data.get('version'),
                hardware_requirements=impl_data.get('hardware_requirements', []),
                software_dependencies=impl_data.get('software_dependencies', []),
                code_availability=impl_data.get('code_availability', False),
                code_url=impl_data.get('code_url'),
                computational_requirements=impl_data.get('computational_requirements', {})
            )
            workflow.implementation_details.append(impl)
        
        # 解析研究方法
        for rm_data in data.get('research_methods', []):
            method = ResearchMethod(
                name=rm_data['name'],
                type=ResearchMethodType(rm_data['type']),
                description=rm_data.get('description'),
                procedure=rm_data.get('procedure', []),
                advantages=rm_data.get('advantages', []),
                limitations=rm_data.get('limitations', []),
                applicability=rm_data.get('applicability', [])
            )
            workflow.research_methods.append(method)
        
        # 解析实验设置
        if data.get('experiment_setup'):
            exp_data = data['experiment_setup']
            workflow.experiment_setup = ExperimentSetup(
                objective=exp_data['objective'],
                hypotheses=exp_data.get('hypotheses', []),
                independent_variables=exp_data.get('independent_variables', []),
                dependent_variables=exp_data.get('dependent_variables', []),
                control_variables=exp_data.get('control_variables', []),
                evaluation_metrics=exp_data.get('evaluation_metrics', []),
                baseline_methods=exp_data.get('baseline_methods', []),
                experimental_conditions=exp_data.get('experimental_conditions', {})
            )
        
        # 解析可视化
        for vs_data in data.get('visualizations', []):
            vis = VisualizationSpec(
                chart_type=vs_data['chart_type'],
                purpose=vs_data['purpose'],
                data_source=vs_data['data_source'],
                tools_used=vs_data.get('tools_used', []),
                description=vs_data.get('description'),
                code_snippet=vs_data.get('code_snippet')
            )
            workflow.visualizations.append(vis)
        
        # 设置其他属性
        workflow.key_results = data.get('key_results', [])
        workflow.contributions = data.get('contributions', [])
        workflow.extraction_confidence = data.get('extraction_confidence', 0.0)
        
        if data.get('extraction_timestamp'):
            workflow.extraction_timestamp = datetime.fromisoformat(data['extraction_timestamp'])
        
        workflow.brain_inspired_elements = data.get('brain_inspired_elements', [])
        
        return workflow


@dataclass
class WorkflowExtractionResult:
    """工作流提取结果"""
    success: bool
    workflow: Optional[PaperWorkflow] = None
    confidence_score: float = 0.0
    extraction_time: float = 0.0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


# 预定义的工作流模板
WORKFLOW_TEMPLATES = {
    "brain_inspired_cnn": {
        "description": "脑启发卷积神经网络工作流",
        "typical_datasets": ["CIFAR-10", "ImageNet", "MNIST"],
        "typical_architectures": ["CNN", "ResNet", "Brain-Inspired CNN"],
        "typical_methods": ["Supervised Learning", "Transfer Learning"],
        "typical_frameworks": ["PyTorch", "TensorFlow"]
    },
    "spiking_neural_network": {
        "description": "脉冲神经网络工作流",
        "typical_datasets": ["N-MNIST", "DVS-CIFAR10", "SHD"],
        "typical_architectures": ["SNN", "CSNN", "Deep SNN"],
        "typical_methods": ["Spike-based Learning", "STDP"],
        "typical_frameworks": ["Brian2", "NEST", "SpykeTorch"]
    },
    "neuromorphic_computing": {
        "description": "神经形态计算工作流",
        "typical_datasets": ["Neuromorphic datasets", "Event-based data"],
        "typical_architectures": ["Neuromorphic chips", "Memristor networks"],
        "typical_methods": ["Event-driven processing", "In-memory computing"],
        "typical_frameworks": ["Loihi", "TrueNorth", "Custom hardware"]
    },
    "attention_mechanism": {
        "description": "注意力机制工作流",
        "typical_datasets": ["Text corpora", "Vision datasets"],
        "typical_architectures": ["Transformer", "Attention CNN", "Self-Attention"],
        "typical_methods": ["Self-Supervised Learning", "Transfer Learning"],
        "typical_frameworks": ["PyTorch", "TensorFlow", "Hugging Face"]
    }
}
