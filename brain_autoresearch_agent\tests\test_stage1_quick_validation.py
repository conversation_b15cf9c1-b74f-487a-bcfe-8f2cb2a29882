"""
阶段1快速验证脚本 - 基础功能测试
在不消耗大量API的情况下验证核心组件是否正常工作
"""

import sys
import os
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """测试关键模块导入"""
    print("🔍 测试1: 模块导入检查")
    
    try:
        from core.unified_api_client import UnifiedAPIClient
        print("  ✅ UnifiedAPIClient 导入成功")
    except Exception as e:
        print(f"  ❌ UnifiedAPIClient 导入失败: {e}")
        return False
    
    try:
        from core.enhanced_literature_manager import EnhancedLiteratureManager
        print("  ✅ EnhancedLiteratureManager 导入成功")
    except Exception as e:
        print(f"  ❌ EnhancedLiteratureManager 导入失败: {e}")
        return False
    
    try:
        from core.paper_workflow import PaperWorkflowExtractor
        print("  ✅ PaperWorkflowExtractor 导入成功")
    except Exception as e:
        print(f"  ❌ PaperWorkflowExtractor 导入失败: {e}")
        return False
        
    try:
        from reasoning.brain_multi_agent_reasoner import BrainInspiredMultiAgentReasoner
        print("  ✅ BrainInspiredMultiAgentReasoner 导入成功")
    except Exception as e:
        print(f"  ❌ BrainInspiredMultiAgentReasoner 导入失败: {e}")
        return False
    
    print("  🎉 所有核心模块导入测试通过\n")
    return True

def test_api_client_basic():
    """测试API客户端基础功能"""
    print("🤖 测试2: API客户端基础功能")
    
    try:
        from core.unified_api_client import UnifiedAPIClient
        
        # 初始化客户端
        client = UnifiedAPIClient()
        print("  ✅ 客户端初始化成功")
        
        # 测试任务模型推荐
        provider, model = client.get_optimal_model_for_task("paper_writing")
        print(f"  ✅ 任务模型推荐: {provider} - {model}")
        
        # 测试JSON提取功能
        test_json = '{"test": "success", "score": 8.5}'
        json_text = f"这里有一些JSON数据：\n```json\n{test_json}\n```\n其他文本。"
        
        extracted = client.extract_json(json_text)
        if extracted and extracted.get("test") == "success":
            print("  ✅ JSON提取功能正常")
        else:
            print("  ⚠️ JSON提取功能异常")
        
        print("  🎉 API客户端基础功能测试完成\n")
        return True
        
    except Exception as e:
        print(f"  ❌ API客户端测试失败: {e}\n")
        return False

def test_workflow_extractor_basic():
    """测试工作流提取器基础功能"""
    print("📄 测试3: 工作流提取器基础功能")
    
    try:
        from core.paper_workflow import PaperWorkflowExtractor
        
        # 初始化提取器（但不调用API）
        extractor = PaperWorkflowExtractor()
        print("  ✅ 工作流提取器初始化成功")
        
        # 测试后备解析功能
        test_text = """
        This paper uses MNIST dataset and CIFAR-10 for evaluation.
        We implement ResNet and Transformer architectures.
        The experiments are conducted using PyTorch framework.
        We measure accuracy and F1-score metrics.
        """
        
        fallback_result = extractor._fallback_text_parsing(test_text)
        
        if any(len(v) > 0 for v in fallback_result.values()):
            print("  ✅ 后备文本解析功能正常")
        else:
            print("  ⚠️ 后备文本解析需要优化")
        
        print("  🎉 工作流提取器基础测试完成\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 工作流提取器测试失败: {e}\n")
        return False

def test_literature_manager_basic():
    """测试文献管理器基础功能"""
    print("📚 测试4: 文献管理器基础功能")
    
    try:
        from core.enhanced_literature_manager import EnhancedLiteratureManager
        from core.enhanced_literature_manager import PaperInfo
        
        # 初始化管理器
        manager = EnhancedLiteratureManager()
        print("  ✅ 文献管理器初始化成功")
        
        # 测试查询增强功能
        enhanced_query = manager._enhance_query("neural networks")
        if "brain-inspired" in enhanced_query:
            print("  ✅ 查询增强功能正常")
        else:
            print("  ⚠️ 查询增强功能需要检查")
        
        # 测试去重功能
        test_papers = [
            PaperInfo("Paper A", "Abstract A", ["Author1"], 2023, "Venue1", "url1", 10, "test", "id1"),
            PaperInfo("Paper A", "Abstract A", ["Author2"], 2023, "Venue2", "url2", 15, "test", "id2"),
            PaperInfo("Paper B", "Abstract B", ["Author3"], 2023, "Venue3", "url3", 5, "test", "id3")
        ]
        
        unique_papers = manager._deduplicate_papers(test_papers)
        if len(unique_papers) == 2:
            print("  ✅ 论文去重功能正常")
        else:
            print(f"  ⚠️ 论文去重结果异常: 期望2篇，实际{len(unique_papers)}篇")
        
        print("  🎉 文献管理器基础测试完成\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 文献管理器测试失败: {e}\n")
        return False

def test_multi_agent_reasoner_basic():
    """测试多专家推理器基础功能"""
    print("🧠 测试5: 多专家推理器基础功能")
    
    try:
        from reasoning.brain_multi_agent_reasoner import BrainInspiredMultiAgentReasoner
        from reasoning.brain_multi_agent_reasoner import ResearchIdea
        
        # 初始化推理器
        reasoner = BrainInspiredMultiAgentReasoner()
        print("  ✅ 多专家推理器初始化成功")
        
        # 测试辅助功能
        test_idea = ResearchIdea(
            title="测试研究",
            description="这是一个测试研究想法",
            research_question="如何测试系统？",
            keywords=["test", "system"]
        )
        
        # 测试工作流知识整合
        mock_literature = type('MockLiterature', (), {
            'workflow_extractions': [
                type('MockWorkflow', (), {
                    'datasets': ['MNIST', 'CIFAR-10'],
                    'network_architectures': ['CNN', 'ResNet'],
                    'platforms_tools': ['PyTorch', 'TensorFlow'],
                    'research_methods': ['Supervised Learning']
                })()
            ]
        })()
        
        workflow_knowledge = reasoner._integrate_workflow_knowledge(mock_literature)
        if workflow_knowledge and len(workflow_knowledge['datasets']) > 0:
            print("  ✅ 工作流知识整合功能正常")
        else:
            print("  ⚠️ 工作流知识整合需要检查")
        
        # 测试评分提取
        test_opinion = "这个研究很有价值。评分：价值8分，可行性7分，创新性9分，影响力6分"
        scores = reasoner._extract_scores_from_opinion(test_opinion)
        
        if scores.get('value', 0) == 8.0 and scores.get('innovation', 0) == 9.0:
            print("  ✅ 评分提取功能正常")
        else:
            print("  ⚠️ 评分提取需要调整")
        
        print("  🎉 多专家推理器基础测试完成\n")
        return True
        
    except Exception as e:
        print(f"  ❌ 多专家推理器测试失败: {e}\n")
        return False

def main():
    """主测试函数"""
    print("🚀 阶段1快速验证测试")
    print("=" * 50)
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"目标: 验证核心组件基础功能，不调用外部API\n")
    
    start_time = time.time()
    
    # 运行所有测试
    tests = [
        test_imports,
        test_api_client_basic,
        test_workflow_extractor_basic, 
        test_literature_manager_basic,
        test_multi_agent_reasoner_basic
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed_tests += 1
            else:
                print(f"⚠️ {test_func.__name__} 未完全通过")
        except Exception as e:
            print(f"❌ {test_func.__name__} 执行异常: {e}\n")
    
    # 生成总结
    test_duration = time.time() - start_time
    success_rate = (passed_tests / total_tests) * 100
    
    print("=" * 50)
    print("📊 快速验证结果总结")
    print("=" * 50)
    print(f"测试数量: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"成功率: {success_rate:.1f}%")
    print(f"执行时间: {test_duration:.2f} 秒")
    
    if success_rate >= 80:
        print("\n✅ 阶段1基础功能验证通过!")
        print("🎯 核心组件准备就绪，可以进行完整测试")
        print("\n💡 下一步建议:")
        print("1. 运行完整测试: python tests/test_stage1_literature_workflow_complete.py")
        print("2. 检查API密钥配置是否正确")
        print("3. 确保网络连接正常")
    elif success_rate >= 60:
        print("\n⚠️ 阶段1基础功能部分通过")
        print("🔧 建议检查失败的组件并修复问题")
    else:
        print("\n❌ 阶段1基础功能验证失败")
        print("🚨 需要检查环境配置和依赖安装")
    
    print(f"\n📄 详细测试请运行: tests/test_stage1_literature_workflow_complete.py")
    
    return success_rate >= 80

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
