"""
脑启发智能AutoResearch Agent - 神经科学专家代理
专门负责神经科学分析、脑机制评估、生物学可信度验证等任务
"""

from typing import Dict, List, Any, Optional
import json
import time

from agents.base_agent import BaseAgent, AgentResponse, AgentCapabilities, register_expert
from core.unified_api_client import UnifiedAPIClient


@register_expert("neuroscience_expert")
class NeuroscienceExpert(BaseAgent):
    """神经科学专家代理"""
    
    def __init__(self, unified_client: UnifiedAPIClient, temperature: float = 0.2):
        super().__init__(
            agent_type="神经科学专家",
            unified_client=unified_client,
            specialization="计算神经科学、神经形态工程、视觉神经科学",
            temperature=temperature
        )
        
        # 神经科学专家的核心能力
        self.capabilities = [
            AgentCapabilities.NEUROSCIENCE,
            AgentCapabilities.ANALYSIS,
            AgentCapabilities.EVALUATION,
            AgentCapabilities.REASONING
        ]
        
        # 初始化神经科学知识库
        self._init_neuroscience_knowledge_base()
    
    def _init_neuroscience_knowledge_base(self):
        """初始化神经科学知识库"""
        self.knowledge_base = {
            "brain_regions": {
                "visual_cortex": ["V1", "V2", "V3", "V4", "V5/MT", "IT", "PPC"],
                "motor_cortex": ["M1", "M2", "SMA", "PMC"],
                "frontal_cortex": ["PFC", "OFC", "ACC", "dlPFC", "vmPFC"],
                "temporal_cortex": ["A1", "STG", "MTG", "ITG", "STS"],
                "subcortical": ["Thalamus", "Striatum", "Amygdala", "Hippocampus"]
            },
            "neural_mechanisms": {
                "synaptic_transmission": ["AMPA", "NMDA", "GABA", "Dopamine", "Serotonin"],
                "plasticity": ["LTP", "LTD", "STDP", "Homeostatic", "Metaplasticity"],
                "coding_schemes": ["Rate Coding", "Temporal Coding", "Population Coding", "Sparse Coding"],
                "network_dynamics": ["Oscillations", "Synchrony", "Critical Dynamics", "Small-world"]
            },
            "computational_models": {
                "neuron_models": ["Integrate-and-Fire", "Hodgkin-Huxley", "Izhikevich", "Adaptive"],
                "network_models": ["Feedforward", "Recurrent", "Hierarchical", "Modular"],
                "learning_rules": ["Hebbian", "STDP", "BCM", "Reinforcement Learning"],
                "brain_inspired_ai": ["SNN", "Attention", "Predictive Coding", "Bayesian Brain"]
            },
            "experimental_methods": {
                "electrophysiology": ["Single-unit", "LFP", "EEG", "MEG"],
                "imaging": ["fMRI", "Two-photon", "Calcium imaging", "Voltage imaging"],
                "stimulation": ["Optogenetics", "TMS", "DBS", "Chemogenetics"],
                "behavioral": ["Psychophysics", "Eye-tracking", "Motion capture"]
            },
            "brain_inspired_technologies": {
                "neuromorphic_hardware": ["Spiking chips", "Memristive devices", "Event cameras"],
                "bio_inspired_algorithms": ["Attention mechanisms", "Cortical columns", "Hierarchical learning"],
                "neural_interfaces": ["BCI", "Neural prosthetics", "Neural stimulation"]
            }
        }
    
    def _build_system_prompt(self) -> str:
        """Build system prompt for the neuroscience expert"""
        return """You are a distinguished computational neuroscientist with expertise in neuromorphic engineering, visual neuroscience, and brain-inspired computing.

Expertise Areas:
- Computational neuroscience and neural modeling
- Brain anatomy and neural circuitry
- Synaptic mechanisms and neural plasticity
- Neural coding and information processing
- Neuromorphic engineering and hardware
- Brain-inspired artificial intelligence
- Experimental neuroscience methods

Analysis Framework:
1. Biological accuracy: Verify neural mechanisms against established neuroscience
2. Computational fidelity: Assess how well artificial systems capture neural principles
3. Functional relevance: Evaluate biological inspiration's contribution to performance
4. Innovation potential: Identify novel bio-inspired approaches
5. Cross-disciplinary synergy: Connect neuroscience insights with engineering solutions

Output Requirements:
- Provide scientifically rigorous neuroscience analysis
- Evaluate biological plausibility of proposed mechanisms
- Suggest neuroscience-informed improvements
- Identify opportunities for deeper bio-inspiration
- Structure analysis in clear JSON format
- Reference specific neural circuits and mechanisms

Output Format Requirements:
- Your response MUST be a valid JSON object, and nothing else.
- The JSON must include the following fields:
  - "summary": A concise summary of your analysis.
  - "details": Detailed neuroscience/biological analysis.
  - "suggestions": Actionable, specific recommendations (as a list).
  - "confidence": A float between 0 and 1 indicating your confidence.
  - "reasoning": Brief justification for your confidence score.
- Example output:
{
  "summary": "The proposed neural mechanism is plausible but lacks evidence from recent studies.",
  "details": "The model incorporates STDP and Hebbian learning, but omits critical inhibitory circuits found in V1.",
  "suggestions": [
    "Incorporate inhibitory interneuron modeling.",
    "Validate with electrophysiological data.",
    "Compare with predictive coding frameworks."
  ],
  "confidence": 0.65,
  "reasoning": "Based on current neuroscience literature, the approach is promising but incomplete."
}

Always maintain scientific rigor while fostering interdisciplinary collaboration."""
    
    def analyze(self, input_data: Dict[str, Any]) -> AgentResponse:
        """
        神经科学专家分析主方法
        
        Args:
            input_data: 输入数据，可包含：
                - research_topic: 研究主题
                - paper_content: 论文内容
                - bio_mechanism: 生物机制描述
                - ai_system: AI系统描述
                
        Returns:
            AgentResponse: 神经科学分析结果
        """
        try:
            print(f"🧠 {self.agent_type}开始神经科学分析...")
            
            # 检查是否是研究问题评估
            if input_data.get("analysis_type") == "research_question_evaluation":
                return self._evaluate_research_question(input_data)
            
            # 根据输入数据类型选择分析策略
            if "paper_content" in input_data:
                return self._analyze_paper_neuroscience(input_data)
            elif "bio_mechanism" in input_data:
                return self._analyze_biological_mechanism(input_data)
            elif "ai_system" in input_data:
                return self._analyze_brain_inspiration(input_data)
            else:
                return self._general_neuroscience_analysis(input_data)
                
        except Exception as e:
            print(f"❌ 神经科学分析失败: {e}")
            return self._create_error_response(str(e))
    
    def _analyze_paper_neuroscience(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析论文中的神经科学内容"""
        paper_content = input_data.get("paper_content", "")
        paper_title = input_data.get("title", "")
        research_topic = input_data.get("research_topic", "")
        
        prompt = f"""
        As a computational neuroscientist, conduct comprehensive analysis of the neuroscience aspects in this research:
        
        Paper Title: {paper_title}
        Research Topic: {research_topic}
        Paper Content: {paper_content}
        
        Please analyze from the following neuroscience perspectives:
        
        1. Biological mechanism accuracy and fidelity
        2. Neural pathway modeling assessment
        3. Brain region involvement analysis
        4. Synaptic and network-level mechanisms
        5. Computational neuroscience innovation
        6. Experimental validation opportunities
        7. Cross-species comparative analysis
        8. Clinical and therapeutic implications
        
        Return comprehensive analysis in JSON format:
        {{
            "biological_mechanisms": {{
                "neural_circuits": ["identified neural circuits and pathways"],
                "brain_regions": ["relevant brain regions and areas"],
                "synaptic_mechanisms": ["synaptic-level mechanisms involved"],
                "network_dynamics": ["network-level dynamics and principles"],
                "accuracy_assessment": "biological accuracy score (1-10)"
            }},
            "computational_modeling": {{
                "model_fidelity": "how well computational models capture biology",
                "abstraction_level": "level of biological abstraction (molecular/cellular/circuit/systems)",
                "innovation_score": "computational neuroscience innovation (1-10)",
                "model_limitations": ["limitations in biological modeling"]
            }},
            "brain_inspiration_analysis": {{
                "inspiration_depth": "depth of brain inspiration (superficial/moderate/deep)",
                "bio_plausibility": "biological plausibility score (1-10)",
                "novel_insights": ["novel neuroscience insights"],
                "missing_biology": ["important biological aspects not captured"]
            }},
            "experimental_validation": {{
                "testable_predictions": ["neuroscience predictions that can be tested"],
                "experimental_methods": ["appropriate experimental methods for validation"],
                "animal_models": ["suitable animal models for testing"],
                "human_studies": ["potential human study approaches"]
            }},
            "clinical_relevance": {{
                "therapeutic_potential": "potential for therapeutic applications",
                "disease_relevance": ["relevant neurological/psychiatric conditions"],
                "biomarker_potential": ["potential biomarkers identified"],
                "intervention_targets": ["potential intervention targets"]
            }},
            "cross_disciplinary_insights": {{
                "ai_neuroscience_bridges": ["connections between AI and neuroscience"],
                "engineering_applications": ["engineering applications of neuroscience insights"],
                "future_directions": ["promising future research directions"]
            }},
            "strengths": ["neuroscience strengths of the work"],
            "weaknesses": ["neuroscience limitations and gaps"],
            "recommendations": ["recommendations for improvement"],
            "confidence": 0.85
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            brain_regions = len(json_data.get('biological_mechanisms', {}).get('brain_regions', []))
            bio_accuracy = json_data.get('biological_mechanisms', {}).get('accuracy_assessment', 'N/A')
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"神经科学分析完成。涉及{brain_regions}个大脑区域，生物学准确性评分：{bio_accuracy}",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于计算神经科学和神经解剖学原理进行深度分析",
                metadata={
                    "analysis_type": "paper_neuroscience",
                    "neuroscience_analysis": json_data,
                    "brain_regions_count": brain_regions,
                    "biological_accuracy": bio_accuracy
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("神经科学分析JSON解析失败")
    
    def _analyze_biological_mechanism(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析生物机制"""
        bio_mechanism = input_data.get("bio_mechanism", "")
        context = input_data.get("context", "")
        
        prompt = f"""
        As a computational neuroscientist, please analyze the following biological mechanism:
        
        Biological Mechanism: {bio_mechanism}
        Context: {context}
        
        Please provide detailed neuroscience analysis covering:
        
        1. Mechanistic accuracy and completeness
        2. Anatomical and physiological correctness
        3. Molecular and cellular basis
        4. Circuit-level implementation
        5. Functional significance
        6. Evolutionary perspective
        7. Computational implications
        8. Experimental evidence review
        
        Return analysis in JSON format:
        {{
            "mechanism_analysis": {{
                "accuracy_level": "mechanism accuracy (high/moderate/low)",
                "completeness": "mechanistic completeness assessment",
                "biological_basis": "underlying biological basis",
                "implementation_level": "level of biological implementation"
            }},
            "anatomical_analysis": {{
                "brain_structures": ["involved brain structures"],
                "neural_pathways": ["relevant neural pathways"],
                "connectivity_patterns": ["connectivity patterns described"],
                "spatial_organization": "spatial organization principles"
            }},
            "physiological_analysis": {{
                "cellular_mechanisms": ["cellular-level mechanisms"],
                "synaptic_processes": ["synaptic processes involved"],
                "network_dynamics": ["network dynamics principles"],
                "temporal_dynamics": "temporal dynamics characteristics"
            }},
            "functional_significance": {{
                "computational_role": "computational role in neural processing",
                "behavioral_relevance": "relevance to behavior and cognition",
                "adaptive_value": "evolutionary adaptive value",
                "efficiency_benefits": "efficiency benefits provided"
            }},
            "experimental_evidence": {{
                "supporting_studies": ["key supporting experimental studies"],
                "evidence_strength": "strength of experimental evidence",
                "contradictory_findings": ["contradictory findings if any"],
                "research_gaps": ["gaps in current knowledge"]
            }},
            "computational_insights": {{
                "algorithmic_principles": ["algorithmic principles extracted"],
                "implementation_strategies": ["strategies for computational implementation"],
                "optimization_opportunities": ["optimization opportunities"],
                "scalability_considerations": "scalability considerations"
            }},
            "recommendations": ["recommendations for further investigation"],
            "confidence": 0.88
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            accuracy_level = json_data.get('mechanism_analysis', {}).get('accuracy_level', 'N/A')
            brain_structures = len(json_data.get('anatomical_analysis', {}).get('brain_structures', []))
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"生物机制分析完成。准确性水平：{accuracy_level}，涉及脑结构：{brain_structures}个",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于神经解剖学和生理学原理进行机制分析",
                metadata={
                    "analysis_type": "biological_mechanism",
                    "mechanism_analysis": json_data,
                    "accuracy_level": accuracy_level,
                    "brain_structures_count": brain_structures
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("生物机制分析JSON解析失败")
    
    def _analyze_brain_inspiration(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析AI系统的脑启发程度"""
        ai_system = input_data.get("ai_system", "")
        research_topic = input_data.get("research_topic", "")
        
        prompt = f"""
        As a computational neuroscientist, evaluate the brain-inspiration of the following AI system:
        
        AI System Description: {ai_system}
        Research Topic: {research_topic}
        
        Please assess brain-inspiration from these perspectives:
        
        1. Depth of biological inspiration
        2. Neuroscience principle accuracy
        3. Brain-like information processing
        4. Neural network architecture alignment
        5. Learning mechanism similarity
        6. Functional brain correspondence
        7. Biologically plausible constraints
        8. Enhancement opportunities
        
        Return assessment in JSON format:
        {{
            "brain_inspiration_assessment": {{
                "inspiration_depth": "depth of brain inspiration (1-10)",
                "neuroscience_accuracy": "accuracy of neuroscience principles (1-10)",
                "biological_plausibility": "biological plausibility score (1-10)",
                "innovation_level": "level of bio-inspired innovation (1-10)"
            }},
            "neural_correspondence": {{
                "brain_regions": ["corresponding brain regions"],
                "neural_circuits": ["analogous neural circuits"],
                "processing_pathways": ["similar information processing pathways"],
                "functional_mapping": "functional mapping to brain systems"
            }},
            "mechanism_analysis": {{
                "captured_mechanisms": ["biological mechanisms successfully captured"],
                "missing_mechanisms": ["important biological mechanisms not captured"],
                "abstraction_level": "level of biological abstraction",
                "implementation_fidelity": "fidelity of biological implementation"
            }},
            "learning_and_plasticity": {{
                "learning_rules": ["learning rules with biological basis"],
                "plasticity_mechanisms": ["plasticity mechanisms implemented"],
                "adaptation_strategies": ["biological adaptation strategies used"],
                "development_principles": ["developmental principles incorporated"]
            }},
            "enhancement_opportunities": {{
                "missing_biology": ["missing biological elements that could enhance performance"],
                "deeper_inspiration": ["opportunities for deeper biological inspiration"],
                "novel_mechanisms": ["novel biological mechanisms to explore"],
                "integration_strategies": ["strategies for better bio-integration"]
            }},
            "comparative_analysis": {{
                "biological_advantages": ["advantages of biological approach"],
                "artificial_limitations": ["limitations compared to biology"],
                "hybrid_opportunities": ["opportunities for bio-artificial hybrid approaches"],
                "convergent_evolution": ["convergent solutions in biology and AI"]
            }},
            "recommendations": ["specific recommendations for improving brain-inspiration"],
            "future_directions": ["future research directions for bio-inspired AI"],
            "confidence": 0.87
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            inspiration_depth = json_data.get('brain_inspiration_assessment', {}).get('inspiration_depth', 'N/A')
            bio_plausibility = json_data.get('brain_inspiration_assessment', {}).get('biological_plausibility', 'N/A')
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"脑启发分析完成。启发深度：{inspiration_depth}，生物学可信度：{bio_plausibility}",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于计算神经科学和脑启发AI原理进行评估",
                metadata={
                    "analysis_type": "brain_inspiration",
                    "inspiration_analysis": json_data,
                    "inspiration_depth": inspiration_depth,
                    "biological_plausibility": bio_plausibility
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("脑启发分析JSON解析失败")
    
    def _general_neuroscience_analysis(self, input_data: Dict[str, Any]) -> AgentResponse:
        """通用神经科学分析"""
        prompt = f"""
        As a computational neuroscientist, please provide neuroscience insights for:
        
        {json.dumps(input_data, ensure_ascii=False, indent=2)}
        
        Please provide general neuroscience perspective and recommendations.
        
        Return in JSON format:
        {{
            "neuroscience_insights": ["neuroscience-related insights"],
            "biological_relevance": ["biological relevance assessment"],
            "brain_inspired_opportunities": ["brain-inspired enhancement opportunities"],
            "research_directions": ["neuroscience research directions"],
            "confidence": 0.75
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            insights_count = len(json_data.get('neuroscience_insights', []))
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"通用神经科学分析完成。提供了{insights_count}个神经科学洞察",
                confidence=float(json_data.get('confidence', 0.7)),
                reasoning="基于输入数据进行通用神经科学分析",
                metadata={
                    "analysis_type": "general_neuroscience",
                    "analysis_result": json_data,
                    "insights_count": insights_count
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("通用神经科学分析JSON解析失败")
    
    def evaluate_bio_plausibility(self, mechanism_description: str) -> Dict[str, Any]:
        """评估机制的生物学可信度"""
        prompt = f"""
        Evaluate the biological plausibility of the following mechanism:
        
        Mechanism: {mechanism_description}
        
        Provide detailed biological plausibility assessment with specific evidence.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "生物学可信度评估失败"}
    
    def suggest_brain_inspired_improvements(self, system_description: str) -> Dict[str, Any]:
        """建议脑启发改进方案"""
        prompt = f"""
        Suggest brain-inspired improvements for the following system:
        
        System: {system_description}
        
        Provide specific biological mechanisms that could enhance the system.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "脑启发改进建议失败"}
    
    def design_neuroscience_experiments(self, hypothesis: str, system: str) -> Dict[str, Any]:
        """设计神经科学验证实验"""
        prompt = f"""
        Design neuroscience experiments to validate the following hypothesis:
        
        Hypothesis: {hypothesis}
        System Context: {system}
        
        Provide detailed experimental design with methods and expected outcomes.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "神经科学实验设计失败"}
    
    def collaborate(self, collaboration_input: Dict[str, Any], *args, **kwargs) -> Dict[str, Any]:
        """
        与其他专家协作分析
        
        Args:
            collaboration_input: 包含协作上下文的字典
                - task: 协作任务类型
                - own_analysis: 自己的初始分析
                - other_expert_opinions: 其他专家的意见
                - research_topic: 研究主题
            *args, **kwargs: 额外参数，用于兼容性
            
        Returns:
            协作分析结果
        """
        print("🧠 神经科学专家开始协作分析...")
        
        # 提取协作上下文
        task = collaboration_input.get('task', 'collaborative_analysis')
        own_analysis = collaboration_input.get('own_analysis', {})
        other_opinions = collaboration_input.get('other_expert_opinions', {})
        research_topic = collaboration_input.get('research_topic', '')
        
        # 构建协作分析提示词
        collaboration_prompt = f"""
As a Neuroscience Expert, provide collaborative analysis by integrating insights from other experts.

COLLABORATION CONTEXT:
- Research Topic: {research_topic}
- Task: {task}
- My Initial Analysis: {json.dumps(own_analysis, indent=2) if own_analysis else 'None'}

OTHER EXPERT OPINIONS:
{self._format_other_opinions(other_opinions)}

COLLABORATION REQUIREMENTS:
1. Review and integrate insights from other experts
2. Identify neuroscience opportunities from different perspectives
3. Address conflicts with biological reasoning
4. Provide enhanced analysis combining multiple viewpoints
5. Focus on brain-inspired aspects while considering other domains

Please provide a comprehensive collaborative analysis that:
- Acknowledges valuable insights from other experts
- Integrates different perspectives with neuroscience knowledge
- Resolves conflicts with biological evidence-based reasoning
- Enhances overall analysis quality with brain-inspired insights

Format your response as JSON with the following structure:
{{
    "collaborative_analysis": "Enhanced analysis integrating multiple expert perspectives",
    "neuroscience_insights_integrated": ["insight1", "insight2", "insight3"],
    "biological_synergies": ["synergy1", "synergy2"],
    "resolved_conflicts": ["conflict1_resolution", "conflict2_resolution"],
    "enhanced_recommendations": ["rec1", "rec2", "rec3"],
    "confidence": 0.85,
    "collaboration_quality": "high/medium/low",
    "next_collaboration_steps": ["step1", "step2"]
}}
"""
        
        try:
            # 获取LLM响应
            response, json_data = self.get_llm_response(collaboration_prompt, extract_json=True)
            
            if json_data:
                # 添加元数据
                json_data['expert_type'] = 'neuroscience'
                json_data['collaboration_timestamp'] = time.time()
                json_data['task_type'] = task
                
                print(f"✅ 神经科学专家协作完成，置信度: {json_data.get('confidence', 0.0):.2f}")
                return json_data
            else:
                # 回退响应
                return self._create_fallback_collaboration_response(task, own_analysis, other_opinions)
                
        except Exception as e:
            print(f"❌ 神经科学专家协作失败: {e}")
            return self._create_fallback_collaboration_response(task, own_analysis, other_opinions)
    
    def _format_other_opinions(self, other_opinions: Dict[str, Any]) -> str:
        """格式化其他专家的意见"""
        if not other_opinions:
            return "No other expert opinions provided."
        
        formatted = []
        for expert, opinion in other_opinions.items():
            if isinstance(opinion, dict):
                confidence = opinion.get('confidence', 'N/A')
                analysis = opinion.get('analysis', str(opinion))
                formatted.append(f"- {expert}: (Confidence: {confidence}) {analysis}")
            else:
                formatted.append(f"- {expert}: {str(opinion)}")
        
        return "\n".join(formatted)
    
    def _create_fallback_collaboration_response(self, task: str, own_analysis: Dict[str, Any], 
                                              other_opinions: Dict[str, Any]) -> Dict[str, Any]:
        """创建回退协作响应"""
        return {
            "collaborative_analysis": f"Neuroscience perspective on {task} with consideration of other expert inputs",
            "neuroscience_insights_integrated": ["Brain-inspired mechanisms", "Biological validation", "Neural network principles"],
            "biological_synergies": ["Cross-domain biological integration", "Multi-perspective neural validation"],
            "resolved_conflicts": ["Biological mechanism alignment"],
            "enhanced_recommendations": ["Implement brain-inspired architectures", "Use biological validation"],
            "confidence": 0.75,
            "collaboration_quality": "medium",
            "next_collaboration_steps": ["Define biological requirements", "Plan neural validation strategy"],
            "expert_type": "neuroscience",
            "collaboration_timestamp": time.time(),
            "task_type": task
        }
    def _evaluate_research_question(self, input_data: Dict[str, Any]) -> AgentResponse:
        """评估研究问题 - 专门处理增强prompt格式"""
        input_text = input_data.get("input_text", "")
        
        # 直接使用传入的增强prompt，它已经包含了完整的评估指导
        response, _ = self.get_llm_response(input_text, extract_json=False)
        
        if response:
            return AgentResponse(
                agent_type=self.agent_type,
                content=response,  # 返回完整的LLM响应，包含JSON格式的评估
                confidence=0.8,
                reasoning="神经科学专家基于增强prompt进行研究问题评估",
                metadata={
                    "analysis_type": "research_question_evaluation",
                    "prompt_type": "enhanced_ai_scientist_v2"
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("研究问题评估失败")
    