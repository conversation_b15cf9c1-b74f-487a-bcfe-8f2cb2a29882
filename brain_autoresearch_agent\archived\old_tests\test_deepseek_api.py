"""
DeepSeek API 真实测试脚本
使用真实的DeepSeek API进行论文生成测试
"""

import os
import sys
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from paper_generation.unified_paper_workflow import (
    UnifiedPaperGenerationWorkflow, 
    UnifiedWorkflowConfig
)
from paper_generation.enhanced_brain_paper_writer import PaperGenerationConfig


def setup_apis():
    """配置DeepSeek和Qwen API"""
    print("🔧 配置多个API...")
    
    # 设置DeepSeek API密钥
    deepseek_api_key = "***********************************"
    os.environ["DEEPSEEK_API_KEY"] = deepseek_api_key
    os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
    
    # 设置Qwen视觉API密钥
    qwen_api_key = "sk-f8559ea97bad4d638416d20db63bc643"
    os.environ["DASHSCOPE_API_KEY"] = qwen_api_key  # Qwen使用DASHSCOPE_API_KEY
    os.environ["QWEN_BASE_URL"] = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    print(f"✅ API配置完成")
    print(f"🔑 DeepSeek API: {deepseek_api_key[:8]}...{deepseek_api_key[-4:]}")
    print(f"🔑 Qwen API: {qwen_api_key[:8]}...{qwen_api_key[-4:]}")
    print(f"🌐 DeepSeek URL: https://api.deepseek.com")
    print(f"🌐 Qwen URL: https://dashscope.aliyuncs.com/compatible-mode/v1")
    
    return deepseek_api_key, qwen_api_key


def test_deepseek_connection():
    """测试DeepSeek连接"""
    print("\\n🧪 测试DeepSeek API连接...")
    
    try:
        # 创建LLM客户端
        llm_client = LLMClient(
            provider="deepseek",
            model="deepseek-chat",
            temperature=0.7
        )
        
        # 简单测试 (使用英文以优化DeepSeek性能)
        test_prompt = "Please provide a brief introduction to the core concepts of brain-inspired artificial intelligence."
        response = llm_client.get_response(test_prompt)
        
        if response:
            print(f"✅ DeepSeek API连接成功!")
            print(f"📝 测试响应: {response[:100]}...")
            return True
        else:
            print(f"❌ DeepSeek API响应为空")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek API连接失败: {e}")
        return False


def create_deepseek_config() -> UnifiedWorkflowConfig:
    """创建DeepSeek配置"""
    print("\\n⚙️ 创建DeepSeek论文生成配置...")
    
    # 论文生成配置
    paper_config = PaperGenerationConfig(
        target_venue="ICML",
        paper_type="research",
        max_review_iterations=3,  # 减少轮次以节省API调用
        quality_threshold=7.5,   # 稍高的质量要求
        enable_auto_revision=True,
        enable_multi_expert_review=True,
        latex_output=True
    )
    
    # 统一工作流配置
    config = UnifiedWorkflowConfig(
        use_advanced_writer=True,  # 使用高级撰写器
        paper_generation_config=paper_config,
        output_formats=["markdown", "latex", "json"],
        output_directory=f"output/deepseek_papers_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        enable_workflow_extraction=True,
        enable_integration_analysis=True
    )
    
    print(f"✅ 配置创建完成")
    print(f"📄 论文类型: {paper_config.paper_type}")
    print(f"🎯 目标会议: {paper_config.target_venue}")
    print(f"🔄 最大评审轮次: {paper_config.max_review_iterations}")
    print(f"📊 质量阈值: {paper_config.quality_threshold}")
    
    return config


async def test_deepseek_paper_generation():
    """使用DeepSeek进行论文生成测试"""
    print("\\n🚀 开始DeepSeek论文生成测试...")
    
    # 研究主题 (使用英文以优化DeepSeek性能)
    research_topic = "Brain-Inspired Neural Networks with Adaptive Learning Mechanisms"
    research_requirements = {
        "target_conference": "ICML 2024",
        "paper_length": "8 pages",
        "focus_areas": [
            "neural plasticity modeling",
            "adaptive learning algorithms", 
            "biologically-inspired network architectures",
            "experimental validation methods"
        ],
        "innovation_requirements": [
            "propose novel brain-inspired learning mechanisms",
            "design effective network architectures", 
            "provide theoretical analysis and experimental validation"
        ]
    }
    
    print(f"📋 研究主题: {research_topic}")
    print(f"🎯 目标会议: {research_requirements['target_conference']}")
    print(f"📄 论文长度: {research_requirements['paper_length']}")
    print(f"🔬 重点领域: {', '.join(research_requirements['focus_areas'])}")
    
    try:
        # 创建配置和工作流
        config = create_deepseek_config()
        
        # 创建LLM客户端 (使用DeepSeek)
        llm_client = LLMClient(
            provider="deepseek",
            model="deepseek-chat",  # 使用chat模型
            temperature=0.7
        )
        
        # 创建统一工作流
        workflow = UnifiedPaperGenerationWorkflow(llm_client, config)
        
        print(f"\\n🔄 开始论文生成流程...")
        start_time = datetime.now()
        
        # 执行论文生成
        result = await workflow.generate_complete_paper(
            research_topic=research_topic,
            research_requirements=research_requirements,
            reference_papers=[]  # 可以添加参考论文
        )
        
        generation_time = (datetime.now() - start_time).total_seconds()
        
        # 显示结果
        if result.success:
            print(f"\\n✅ DeepSeek论文生成成功!")
            print(f"📁 输出目录: {config.output_directory}")
            print(f"📄 生成格式: {', '.join(config.output_formats)}")
            print(f"⏱️ 生成时间: {generation_time:.2f}秒")
            print(f"📊 论文质量评分: {result.paper_quality_score:.2f}/10")
            
            # 显示输出文件
            print(f"\\n📂 生成的文件:")
            for file_path in result.output_files.values():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    print(f"  - {os.path.basename(file_path)} ({file_size:.1f} KB)")
            
            # 显示论文摘要
            if hasattr(result, 'paper_summary') and result.paper_summary:
                print(f"\\n📋 论文摘要:")
                print(result.paper_summary[:500] + "..." if len(result.paper_summary) > 500 else result.paper_summary)
            
            return True
        else:
            print(f"\\n❌ DeepSeek论文生成失败")
            print(f"🔍 错误信息: {getattr(result, 'error_message', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"\\n💥 生成过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_deepseek_reasoning_model():
    """测试DeepSeek推理模型"""
    print("\\n🧠 测试DeepSeek推理模型...")
    
    try:
        # 创建推理模型客户端
        reasoner_client = LLMClient(
            provider="deepseek",
            model="deepseek-reasoner",  # 使用推理模型
            temperature=0.7
        )
        
        # 复杂推理测试 (使用英文以优化DeepSeek性能)
        reasoning_prompt = """
        Please analyze the following research question and provide in-depth reasoning:
        
        Research Question: How to design an adaptive neural network that incorporates brain neural plasticity mechanisms?
        
        Please analyze from the following perspectives:
        1. Biological Foundation: Core mechanisms of brain neural plasticity
        2. Algorithm Design: How to transform biological mechanisms into computational models
        3. Technical Challenges: Main difficulties that may be encountered during implementation
        4. Innovation Value: Potential advantages compared to existing methods
        5. Experimental Validation: How to design experiments to validate effectiveness
        
        Please provide detailed reasoning process and specific recommendations.
        """
        
        print("📝 发送推理请求...")
        response = reasoner_client.get_response(reasoning_prompt)
        
        if response:
            print(f"✅ DeepSeek推理模型响应成功!")
            print(f"📊 响应长度: {len(response)} 字符")
            print(f"\\n🧠 推理结果片段:")
            print("=" * 60)
            print(response[:800] + "..." if len(response) > 800 else response)
            print("=" * 60)
            return True
        else:
            print(f"❌ DeepSeek推理模型无响应")
            return False
            
    except Exception as e:
        print(f"❌ DeepSeek推理模型测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("=" * 80)
    print("🧠 DeepSeek API 真实测试")
    print("=" * 80)
    
    # 1. 配置API
    deepseek_key, qwen_key = setup_apis()
    
    # 2. 测试连接
    if not test_deepseek_connection():
        print("\\n❌ API连接测试失败，请检查API密钥和网络连接")
        return
    
    # 3. 测试推理模型
    test_deepseek_reasoning_model()
    
    # 4. 询问是否进行完整论文生成
    print("\\n" + "=" * 60)
    response = input("🤔 是否进行完整的论文生成测试？(这将消耗更多API配额) [y/N]: ")
    
    if response.lower() in ['y', 'yes', '是']:
        print("\\n🚀 开始完整论文生成测试...")
        success = await test_deepseek_paper_generation()
        
        if success:
            print("\\n🎉 所有测试完成！DeepSeek API工作正常！")
        else:
            print("\\n😞 论文生成测试失败，但基础API功能正常")
    else:
        print("\\n✅ 基础API测试完成！")
    
    print("\\n💡 提示：如果要在demo中使用DeepSeek，请修改配置文件中的模型设置")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
