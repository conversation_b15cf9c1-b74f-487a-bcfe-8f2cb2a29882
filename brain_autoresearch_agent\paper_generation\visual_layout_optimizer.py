"""
基于Qwen视觉模型的论文布局优化器
用于论文最终rollback阶段的视觉优化
"""

import os
import sys
import base64
import requests
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from dataclasses import dataclass
import tempfile
import subprocess

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.multimodal_api_config import get_api_manager

@dataclass
class LayoutAnalysisResult:
    """布局分析结果"""
    overall_score: float
    issues: List[str]
    recommendations: List[str]
    visual_problems: List[str]
    formatting_suggestions: List[str]
    
@dataclass
class LayoutOptimizationResult:
    """布局优化结果"""
    success: bool
    original_score: float
    optimized_score: float
    improvements: List[str]
    optimized_latex: str
    visual_feedback: str

class PaperLayoutOptimizer:
    """论文布局优化器"""
    
    def __init__(self):
        """初始化布局优化器"""
        self.api_manager = get_api_manager()
        self.qwen_config = self.api_manager.get_vision_model_config()
        self.temp_dir = tempfile.mkdtemp(prefix="paper_layout_")
        
        print("📐 论文布局优化器已初始化")
        print(f"   🎯 视觉模型: {self.qwen_config.models[0]}")
        print(f"   📁 临时目录: {self.temp_dir}")
    
    def analyze_paper_layout(self, latex_content: str, paper_title: str = "Research Paper") -> LayoutAnalysisResult:
        """分析论文布局"""
        print("🔍 分析论文布局...")
        
        try:
            # 1. 编译LaTeX为PDF
            pdf_path = self._compile_latex_to_pdf(latex_content, paper_title)
            
            if not pdf_path or not os.path.exists(pdf_path):
                print("❌ PDF编译失败，使用文本分析")
                return self._analyze_text_layout(latex_content)
            
            # 2. 转换PDF为图片
            image_paths = self._convert_pdf_to_images(pdf_path)
            
            if not image_paths:
                print("❌ PDF转图片失败，使用文本分析")
                return self._analyze_text_layout(latex_content)
            
            # 3. 使用视觉模型分析布局
            return self._analyze_visual_layout(image_paths, latex_content)
            
        except Exception as e:
            print(f"❌ 布局分析失败: {e}")
            return self._analyze_text_layout(latex_content)
    
    def optimize_paper_layout(self, latex_content: str, paper_title: str = "Research Paper",
                            max_iterations: int = 3) -> LayoutOptimizationResult:
        """优化论文布局"""
        print("🎨 开始论文布局优化...")
        
        # 初始分析
        initial_analysis = self.analyze_paper_layout(latex_content, paper_title)
        print(f"📊 初始布局评分: {initial_analysis.overall_score:.1f}/10")
        
        current_latex = latex_content
        current_score = initial_analysis.overall_score
        improvements = []
        
        for iteration in range(max_iterations):
            print(f"\n🔄 优化轮次 {iteration + 1}/{max_iterations}")
            
            if current_score >= 8.5:
                print("✅ 布局质量已达标，停止优化")
                break
            
            # 生成优化方案
            optimization_suggestions = self._generate_optimization_suggestions(
                current_latex, initial_analysis
            )
            
            if not optimization_suggestions:
                print("⚠️ 无法生成更多优化建议")
                break
            
            # 应用优化
            optimized_latex = self._apply_layout_optimizations(
                current_latex, optimization_suggestions
            )
            
            # 分析优化后的布局
            optimized_analysis = self.analyze_paper_layout(optimized_latex, paper_title)
            print(f"📊 优化后评分: {optimized_analysis.overall_score:.1f}/10")
            
            # 如果有改进，保存结果
            if optimized_analysis.overall_score > current_score:
                improvement = f"轮次{iteration + 1}: 评分从{current_score:.1f}提升到{optimized_analysis.overall_score:.1f}"
                improvements.append(improvement)
                print(f"✅ {improvement}")
                
                current_latex = optimized_latex
                current_score = optimized_analysis.overall_score
            else:
                print("❌ 本轮优化无改进")
        
        # 最终视觉反馈
        visual_feedback = self._get_final_visual_feedback(current_latex, paper_title)
        
        return LayoutOptimizationResult(
            success=current_score > initial_analysis.overall_score,
            original_score=initial_analysis.overall_score,
            optimized_score=current_score,
            improvements=improvements,
            optimized_latex=current_latex,
            visual_feedback=visual_feedback
        )
    
    def _compile_latex_to_pdf(self, latex_content: str, paper_title: str) -> Optional[str]:
        """编译LaTeX为PDF"""
        try:
            # 创建临时LaTeX文件
            latex_file = os.path.join(self.temp_dir, f"{paper_title.replace(' ', '_')}.tex")
            pdf_file = os.path.join(self.temp_dir, f"{paper_title.replace(' ', '_')}.pdf")
            
            with open(latex_file, 'w', encoding='utf-8') as f:
                f.write(latex_content)
            
            # 使用pdflatex编译
            result = subprocess.run([
                'pdflatex', 
                '-output-directory', self.temp_dir,
                '-interaction=nonstopmode',
                latex_file
            ], capture_output=True, text=True)
            
            if os.path.exists(pdf_file):
                return pdf_file
            else:
                print(f"⚠️ PDF编译失败: {result.stderr[:200]}...")
                return None
                
        except Exception as e:
            print(f"❌ LaTeX编译错误: {e}")
            return None
    
    def _convert_pdf_to_images(self, pdf_path: str) -> List[str]:
        """转换PDF为图片"""
        try:
            from PIL import Image
            import pdf2image
            
            # 转换PDF为图片
            images = pdf2image.convert_from_path(pdf_path, dpi=300)
            image_paths = []
            
            for i, image in enumerate(images[:3]):  # 最多处理前3页
                image_path = os.path.join(self.temp_dir, f"page_{i+1}.png")
                image.save(image_path, 'PNG')
                image_paths.append(image_path)
            
            print(f"✅ PDF转图片完成: {len(image_paths)} 页")
            return image_paths
            
        except Exception as e:
            print(f"❌ PDF转图片失败: {e}")
            return []
    
    def _analyze_visual_layout(self, image_paths: List[str], latex_content: str) -> LayoutAnalysisResult:
        """使用视觉模型分析布局"""
        print("👁️ 使用Qwen视觉模型分析布局...")
        
        try:
            # 编码图片
            image_data = []
            for image_path in image_paths[:2]:  # 分析前两页
                with open(image_path, 'rb') as f:
                    image_base64 = base64.b64encode(f.read()).decode('utf-8')
                    image_data.append(image_base64)
            
            # 构建视觉分析提示
            visual_prompt = self._build_visual_analysis_prompt()
            
            # 调用Qwen视觉模型
            analysis_result = self._call_qwen_vision_api(image_data, visual_prompt)
            
            if analysis_result:
                return self._parse_visual_analysis_result(analysis_result, latex_content)
            else:
                print("❌ 视觉分析失败，回退到文本分析")
                return self._analyze_text_layout(latex_content)
                
        except Exception as e:
            print(f"❌ 视觉分析错误: {e}")
            return self._analyze_text_layout(latex_content)
    
    def _build_visual_analysis_prompt(self) -> str:
        """构建视觉分析提示"""
        return """
请作为论文布局专家，仔细分析这篇学术论文的视觉布局，从以下维度评估：

1. **整体布局** (1-10分):
   - 页面边距是否合适
   - 文本密度是否适中
   - 视觉平衡性

2. **文本格式** (1-10分):
   - 标题层次是否清晰
   - 段落间距是否合适
   - 字体大小是否协调

3. **图表质量** (1-10分):
   - 图表位置是否合理
   - 图表大小是否适中
   - 图注是否清晰

4. **公式排版** (1-10分):
   - 公式对齐是否正确
   - 公式编号是否规范
   - 公式与文本的间距

5. **专业外观** (1-10分):
   - 整体专业度
   - 会议格式符合性
   - 可读性

请提供：
- 各项评分（1-10）
- 发现的具体问题
- 改进建议
- 整体评分

请用JSON格式回复，包含：score、issues、recommendations、visual_problems、formatting_suggestions字段。
"""
    
    def _call_qwen_vision_api(self, image_data: List[str], prompt: str) -> Optional[str]:
        """调用Qwen视觉API"""
        try:
            from openai import OpenAI
            
            # 使用正确的Qwen API调用方式
            client = OpenAI(
                api_key=os.environ.get("DASHSCOPE_API_KEY"),  # 使用DASHSCOPE_API_KEY
                base_url=self.qwen_config.base_url
            )
            
            # 构建消息内容
            messages = [{
                "role": "user", 
                "content": [
                    {"type": "text", "text": prompt}
                ]
            }]
            
            # 添加图片
            for img_data in image_data:
                messages[0]["content"].append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{img_data}"
                    }
                })
            
            response = client.chat.completions.create(
                model="qwen-vl-max-latest",  # 使用视觉模型
                messages=messages,
                max_tokens=1000,
                temperature=0.1
            )
            
            return response.choices[0].message.content
                
        except Exception as e:
            print(f"❌ Qwen API调用错误: {e}")
            return None
    
    def _parse_visual_analysis_result(self, api_response: str, latex_content: str) -> LayoutAnalysisResult:
        """解析视觉分析结果"""
        try:
            import json
            
            # 尝试提取JSON
            if '{' in api_response and '}' in api_response:
                json_start = api_response.find('{')
                json_end = api_response.rfind('}') + 1
                json_str = api_response[json_start:json_end]
                
                result = json.loads(json_str)
                
                return LayoutAnalysisResult(
                    overall_score=float(result.get('score', 7.0)),
                    issues=result.get('issues', []),
                    recommendations=result.get('recommendations', []),
                    visual_problems=result.get('visual_problems', []),
                    formatting_suggestions=result.get('formatting_suggestions', [])
                )
            else:
                # 解析文本格式
                score = 7.0
                if '评分' in api_response or 'score' in api_response.lower():
                    import re
                    score_match = re.search(r'(\d+(?:\.\d+)?)', api_response)
                    if score_match:
                        score = float(score_match.group(1))
                
                return LayoutAnalysisResult(
                    overall_score=score,
                    issues=[api_response[:100] + "..."],
                    recommendations=["基于视觉分析的通用建议"],
                    visual_problems=["需要进一步分析"],
                    formatting_suggestions=["优化排版格式"]
                )
                
        except Exception as e:
            print(f"❌ 解析视觉分析结果失败: {e}")
            return self._analyze_text_layout(latex_content)
    
    def _analyze_text_layout(self, latex_content: str) -> LayoutAnalysisResult:
        """文本布局分析（备用方案）"""
        print("📝 使用文本分析方法...")
        
        issues = []
        recommendations = []
        score = 7.0  # 基础分数
        
        # 简单的文本分析
        lines = latex_content.split('\n')
        
        # 检查标题结构
        section_count = sum(1 for line in lines if line.strip().startswith('\\section'))
        if section_count < 3:
            issues.append("章节数量过少")
            recommendations.append("增加更多章节结构")
        
        # 检查图表
        figure_count = sum(1 for line in lines if '\\begin{figure}' in line)
        if figure_count == 0:
            issues.append("缺少图表")
            recommendations.append("添加相关图表")
        
        # 检查公式
        equation_count = sum(1 for line in lines if ('\\begin{equation}' in line or '\\[' in line))
        if equation_count == 0:
            issues.append("缺少数学公式")
            recommendations.append("添加必要的数学公式")
        
        return LayoutAnalysisResult(
            overall_score=score,
            issues=issues,
            recommendations=recommendations,
            visual_problems=["需要视觉模型进行详细分析"],
            formatting_suggestions=["建议进行PDF编译后的视觉检查"]
        )
    
    def _generate_optimization_suggestions(self, latex_content: str, 
                                         analysis: LayoutAnalysisResult) -> List[str]:
        """生成优化建议"""
        suggestions = []
        
        # 基于分析结果生成具体建议
        for issue in analysis.issues:
            if "边距" in issue:
                suggestions.append("调整页面边距设置")
            elif "间距" in issue:
                suggestions.append("优化段落和行间距")
            elif "图表" in issue:
                suggestions.append("调整图表位置和大小")
            elif "公式" in issue:
                suggestions.append("优化公式排版和对齐")
        
        return suggestions
    
    def _apply_layout_optimizations(self, latex_content: str, 
                                   suggestions: List[str]) -> str:
        """应用布局优化"""
        optimized_latex = latex_content
        
        # 应用基本的LaTeX优化
        for suggestion in suggestions:
            if "边距" in suggestion:
                # 优化边距
                if "\\usepackage{geometry}" not in optimized_latex:
                    optimized_latex = optimized_latex.replace(
                        "\\documentclass",
                        "\\usepackage[margin=1in]{geometry}\n\\documentclass"
                    )
            
            elif "间距" in suggestion:
                # 优化间距
                if "\\setlength{\\parskip}" not in optimized_latex:
                    optimized_latex = optimized_latex.replace(
                        "\\begin{document}",
                        "\\setlength{\\parskip}{6pt}\n\\begin{document}"
                    )
        
        return optimized_latex
    
    def _get_final_visual_feedback(self, latex_content: str, paper_title: str) -> str:
        """获取最终视觉反馈"""
        try:
            # 如果可以生成PDF，使用视觉推理模型进行最终评估
            pdf_path = self._compile_latex_to_pdf(latex_content, paper_title)
            
            if pdf_path:
                image_paths = self._convert_pdf_to_images(pdf_path)
                if image_paths:
                    return self._get_visual_reasoning_feedback(image_paths)
            
            return "布局优化完成，建议人工检查最终PDF效果。"
            
        except Exception as e:
            return f"视觉反馈生成失败: {e}"
    
    def _get_visual_reasoning_feedback(self, image_paths: List[str]) -> str:
        """使用视觉推理模型获取反馈"""
        try:
            from openai import OpenAI
            
            # 编码第一页图片
            with open(image_paths[0], 'rb') as f:
                image_base64 = base64.b64encode(f.read()).decode('utf-8')
            
            # 使用视觉推理模型
            client = OpenAI(
                api_key=os.environ.get("DASHSCOPE_API_KEY"),
                base_url=self.qwen_config.base_url
            )
            
            response = client.chat.completions.create(
                model="qvq-max-latest",  # 视觉推理模型
                messages=[{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "请评估这篇学术论文的整体视觉效果和专业度，给出最终的布局质量评价。"},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{image_base64}"}}
                    ]
                }],
                max_tokens=300
            )
            
            return response.choices[0].message.content
                
        except Exception as e:
            return f"视觉推理反馈错误: {e}"
    
    def cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
            print("🗑️ 临时文件已清理")
        except Exception as e:
            print(f"⚠️ 清理临时文件失败: {e}")

# 便捷函数
def optimize_paper_layout(latex_content: str, paper_title: str = "Research Paper") -> LayoutOptimizationResult:
    """优化论文布局的便捷函数"""
    optimizer = PaperLayoutOptimizer()
    try:
        result = optimizer.optimize_paper_layout(latex_content, paper_title)
        return result
    finally:
        optimizer.cleanup()

if __name__ == "__main__":
    # 测试用例
    sample_latex = r"""
    \documentclass[10pt,twocolumn,letterpaper]{article}
    \usepackage{aaai}
    \begin{document}
    \title{Sample Paper}
    \author{Author Name}
    \maketitle
    
    \section{Introduction}
    This is a sample paper for layout optimization testing.
    
    \section{Related Work}
    Related work content here.
    
    \section{Conclusion}
    Conclusion content here.
    
    \end{document}
    """
    
    print("🧪 论文布局优化器测试")
    result = optimize_paper_layout(sample_latex, "Test Paper")
    
    print(f"📊 优化结果:")
    print(f"   成功: {result.success}")
    print(f"   原始评分: {result.original_score}")
    print(f"   优化后评分: {result.optimized_score}")
    print(f"   改进项目: {result.improvements}")
