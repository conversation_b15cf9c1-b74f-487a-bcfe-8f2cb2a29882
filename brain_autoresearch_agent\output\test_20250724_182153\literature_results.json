{"papers": [{"title": "Brain Inspired Intelligence Research: Novel Approach 1", "abstract": "This paper presents a novel approach to Brain Inspired Intelligence. Our research shows significant improvements over existing methods.", "authors": ["Researcher 1A", "Researcher 1B"], "year": 2022, "venue": "Nature Machine Intelligence", "url": "https://example.com/paper1", "citation_count": 95, "source": "semantic_scholar", "paper_id": "mock-paper-id-1", "keywords": [], "doi": null}, {"title": "Brain Inspired Intelligence Research: Novel Approach 2", "abstract": "This paper presents a novel approach to Brain Inspired Intelligence. Our research shows significant improvements over existing methods.", "authors": ["Researcher 2A", "Researcher 2B"], "year": 2021, "venue": "Conference 2", "url": "https://example.com/paper2", "citation_count": 90, "source": "semantic_scholar", "paper_id": "mock-paper-id-2", "keywords": [], "doi": null}, {"title": "Brain Inspired Intelligence Research: Novel Approach 3", "abstract": "This paper presents a novel approach to Brain Inspired Intelligence. Our research shows significant improvements over existing methods.", "authors": ["Researcher 3A", "Researcher 3B"], "year": 2023, "venue": "Conference 3", "url": "https://example.com/paper3", "citation_count": 85, "source": "semantic_scholar", "paper_id": "mock-paper-id-3", "keywords": [], "doi": null}, {"title": "Brain Inspired Intelligence Research: Novel Approach 4", "abstract": "This paper presents a novel approach to Brain Inspired Intelligence. Our research shows significant improvements over existing methods.", "authors": ["Researcher 4A", "Researcher 4B"], "year": 2022, "venue": "Conference 4", "url": "https://example.com/paper4", "citation_count": 80, "source": "semantic_scholar", "paper_id": "mock-paper-id-4", "keywords": [], "doi": null}, {"title": "Brain Inspired Intelligence Research: Novel Approach 5", "abstract": "This paper presents a novel approach to Brain Inspired Intelligence. Our research shows significant improvements over existing methods.", "authors": ["Researcher 5A", "Researcher 5B"], "year": 2021, "venue": "Conference 5", "url": "https://example.com/paper5", "citation_count": 75, "source": "semantic_scholar", "paper_id": "mock-paper-id-5", "keywords": [], "doi": null}, {"title": "Brain Inspired Intelligence Research: Novel Approach 6", "abstract": "This paper presents a novel approach to Brain Inspired Intelligence. Our research shows significant improvements over existing methods.", "authors": ["Researcher 6A", "Researcher 6B"], "year": 2023, "venue": "Conference 6", "url": "https://example.com/paper6", "citation_count": 70, "source": "semantic_scholar", "paper_id": "mock-paper-id-6", "keywords": [], "doi": null}, {"title": "Brain Inspired Intelligence Research: Novel Approach 7", "abstract": "This paper presents a novel approach to Brain Inspired Intelligence. Our research shows significant improvements over existing methods.", "authors": ["Researcher 7A", "Researcher 7B"], "year": 2022, "venue": "Conference 7", "url": "https://example.com/paper7", "citation_count": 65, "source": "semantic_scholar", "paper_id": "mock-paper-id-7", "keywords": [], "doi": null}, {"title": "Brain Inspired Intelligence Research: Novel Approach 8", "abstract": "This paper presents a novel approach to Brain Inspired Intelligence. Our research shows significant improvements over existing methods.", "authors": ["Researcher 8A", "Researcher 8B"], "year": 2021, "venue": "Conference 8", "url": "https://example.com/paper8", "citation_count": 60, "source": "semantic_scholar", "paper_id": "mock-paper-id-8", "keywords": [], "doi": null}, {"title": "Brain Inspired Intelligence Research: Novel Approach 9", "abstract": "This paper presents a novel approach to Brain Inspired Intelligence. Our research shows significant improvements over existing methods.", "authors": ["Researcher 9A", "Researcher 9B"], "year": 2023, "venue": "Conference 9", "url": "https://example.com/paper9", "citation_count": 55, "source": "semantic_scholar", "paper_id": "mock-paper-id-9", "keywords": [], "doi": null}, {"title": "Brain Inspired Intelligence Research: Novel Approach 10", "abstract": "This paper presents a novel approach to Brain Inspired Intelligence. Our research shows significant improvements over existing methods.", "authors": ["Researcher 10A", "Researcher 10B"], "year": 2022, "venue": "Conference 10", "url": "https://example.com/paper10", "citation_count": 50, "source": "semantic_scholar", "paper_id": "mock-paper-id-10", "keywords": [], "doi": null}, {"title": "BrainCog: A Spiking Neural Network based Brain-inspired Cognitive\n  Intelligence Engine for Brain-inspired AI and Brain Simulation", "abstract": "Spiking neural networks (SNNs) have attracted extensive attentions in\nBrain-inspired Artificial Intelligence and computational neuroscience. They can\nbe used to simulate biological information processing in the brain at multiple\nscales. More importantly, SNNs serve as an appropriate level of abstraction to\nbring inspirations from brain and cognition to Artificial Intelligence. In this\npaper, we present the Brain-inspired Cognitive Intelligence Engine (BrainCog)\nfor creating brain-inspired AI and brain simulation models. BrainCog\nincorporates different types of spiking neuron models, learning rules, brain\nareas, etc., as essential modules provided by the platform. Based on these\neasy-to-use modules, BrainCog supports various brain-inspired cognitive\nfunctions, including Perception and Learning, Decision Making, Knowledge\nRepresentation and Reasoning, Motor Control, and Social Cognition. These\nbrain-inspired AI models have been effectively validated on various supervised,\nunsupervised, and reinforcement learning tasks, and they can be used to enable\nAI models to be with multiple brain-inspired cognitive functions. For brain\nsimulation, BrainCog realizes the function simulation of decision-making,\nworking memory, the structure simulation of the Neural Circuit, and whole brain\nstructure simulation of Mouse brain, Macaque brain, and Human brain. An AI\nengine named BORN is developed based on BrainCog, and it demonstrates how the\ncomponents of BrainCog can be integrated and used to build AI models and\napplications. To enable the scientific quest to decode the nature of biological\nintelligence and create AI, BrainCog aims to provide essential and easy-to-use\nbuilding blocks, and infrastructural support to develop brain-inspired spiking\nneural network based AI, and to simulate the cognitive brains at multiple\nscales. The online repository of BrainCog can be found at\nhttps://github.com/braincog-x.", "authors": ["<PERSON>", "Dongcheng Zhao", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Chengcheng Du", "Qingqun Kong", "<PERSON><PERSON><PERSON>", "Wei<PERSON> B<PERSON>"], "year": 2022, "venue": "arXiv", "url": "http://arxiv.org/abs/2207.08533v2", "citation_count": null, "source": "arxiv", "paper_id": "2207.08533v2", "keywords": [], "doi": ""}, {"title": "The whole brain architecture approach: Accelerating the development of\n  artificial general intelligence by referring to the brain", "abstract": "The vastness of the design space created by the combination of a large number\nof computational mechanisms, including machine learning, is an obstacle to\ncreating an artificial general intelligence (AGI). Brain-inspired AGI\ndevelopment, in other words, cutting down the design space to look more like a\nbiological brain, which is an existing model of a general intelligence, is a\npromising plan for solving this problem. However, it is difficult for an\nindividual to design a software program that corresponds to the entire brain\nbecause the neuroscientific data required to understand the architecture of the\nbrain are extensive and complicated. The whole-brain architecture approach\ndivides the brain-inspired AGI development process into the task of designing\nthe brain reference architecture (BRA) -- the flow of information and the\ndiagram of corresponding components -- and the task of developing each\ncomponent using the BRA. This is called BRA-driven development. Another\ndifficulty lies in the extraction of the operating principles necessary for\nreproducing the cognitive-behavioral function of the brain from neuroscience\ndata. Therefore, this study proposes the Structure-constrained Interface\nDecomposition (SCID) method, which is a hypothesis-building method for creating\na hypothetical component diagram consistent with neuroscientific findings. The\napplication of this approach has begun for building various regions of the\nbrain. Moving forward, we will examine methods of evaluating the biological\nplausibility of brain-inspired software. This evaluation will also be used to\nprioritize different computational mechanisms, which should be merged,\nassociated with the same regions of the brain.", "authors": ["<PERSON><PERSON><PERSON>"], "year": 2021, "venue": "arXiv", "url": "http://arxiv.org/abs/2103.06123v1", "citation_count": null, "source": "arxiv", "paper_id": "2103.06123v1", "keywords": [], "doi": ""}, {"title": "Metalearning-Informed Competence in Children: Implications for\n  Responsible Brain-Inspired Artificial Intelligence", "abstract": "This paper offers a novel conceptual framework comprising four essential\ncognitive mechanisms that operate concurrently and collaboratively to enable\nmetalearning (knowledge and regulation of learning) strategy implementation in\nyoung children. A roadmap incorporating the core mechanisms and the associated\nstrategies is presented as an explanation of the developing brain's remarkable\ncross-context learning competence. The tetrad of fundamental complementary\nprocesses is chosen to collectively represent the bare-bones metalearning\narchitecture that can be extended to artificial intelligence (AI) systems\nemulating brain-like learning and problem-solving skills. Utilizing the\nmetalearning-enabled young mind as a model for brain-inspired computing, this\nwork further discusses important implications for morally grounded AI.", "authors": ["<PERSON><PERSON><PERSON>"], "year": 2023, "venue": "arXiv", "url": "http://arxiv.org/abs/2401.01001v1", "citation_count": null, "source": "arxiv", "paper_id": "2401.01001v1", "keywords": [], "doi": ""}, {"title": "Brain-Inspired AI to a Symbiosis of Human Intelligence and Artificial Intelligence", "abstract": "<jats:p>Brain-based artificial intelligence has been a popular topic. Applications include military and defense, intelligent manufacturing, business intelligence and management, medical service and healthcare, and others. In order to strengthen their national interests and capacities in the global marketplace, many countries have started national brain-related projects. Numerous difficulties in brain-inspired computing and computation based on spiking-neural-networks, as well as various concepts, principles, and emerging technologies in brain science and brain-inspired artificial intelligence, are discussed in this chapter (SNNs). The advances and trends section covers topics such as brain-inspired computing, neuromorphic computing systems, and multi-scale brain simulation, as well as the brain association graph, brainnetome, connectome, brain imaging, brain-inspired chips and devices, brain-computer interface (BCI) and brain-machine interface (BMI), brain-inspired robotics and applications, quantum robots, and cyborgs (human-machine hybrids).</jats:p>", "authors": ["<PERSON><PERSON><PERSON><PERSON>"], "year": 2023, "venue": "Advances in Computational Intelligence and Robotics", "url": "https://doi.org/10.4018/978-1-6684-6980-4.ch007", "citation_count": null, "source": "crossref", "paper_id": "10.4018/978-1-6684-6980-4.ch007", "keywords": [], "doi": "10.4018/978-1-6684-6980-4.ch007"}, {"title": "Neurogenesis of Intelligence Principles of Brain-Inspired Computing", "abstract": "<jats:p>In machine learning, artificial neural networks (ANNs) are becoming indispensable tools, showing impressive results in various applications such as robotics, game development, picture and speech synthesis, and more. Nevertheless, there are inherent disparities between the operational mechanisms of artificial neural networks and the real brain, specifically concerning learning procedures. This chapter covers the overview of Brain-Inspired Computing, its key principles, importance, applications, and future directions. This work also thoroughly examines the learning patterns inspired by the brain in neural network models. We explore the incorporation of biologically realistic processes, including plasticity in synapses, to enhance the potential of these networks. Furthermore, we thoroughly examine this method's possible benefits and difficulties. This review identifies potential areas of investigation for further studies in this fast-progressing discipline, which may lead us to a deeper comprehension of the fundamental nature of intelligence.</jats:p>", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "year": 2024, "venue": "Advances in Computational Intelligence and Robotics", "url": "https://doi.org/10.4018/979-8-3693-6303-4.ch004", "citation_count": null, "source": "crossref", "paper_id": "10.4018/979-8-3693-6303-4.ch004", "keywords": [], "doi": "10.4018/979-8-3693-6303-4.ch004"}, {"title": "A differentiable brain simulator bridging brain simulation and\n  brain-inspired computing", "abstract": "Brain simulation builds dynamical models to mimic the structure and functions\nof the brain, while brain-inspired computing (BIC) develops intelligent systems\nby learning from the structure and functions of the brain. The two fields are\nintertwined and should share a common programming framework to facilitate each\nother's development. However, none of the existing software in the fields can\nachieve this goal, because traditional brain simulators lack differentiability\nfor training, while existing deep learning (DL) frameworks fail to capture the\nbiophysical realism and complexity of brain dynamics. In this paper, we\nintroduce BrainPy, a differentiable brain simulator developed using JAX and\nXLA, with the aim of bridging the gap between brain simulation and BIC. BrainPy\nexpands upon the functionalities of JAX, a powerful AI framework, by\nintroducing complete capabilities for flexible, efficient, and scalable brain\nsimulation. It offers a range of sparse and event-driven operators for\nefficient and scalable brain simulation, an abstraction for managing the\nintricacies of synaptic computations, a modular and flexible interface for\nconstructing multi-scale brain models, and an object-oriented just-in-time\ncompilation approach to handle the memory-intensive nature of brain dynamics.\nWe showcase the efficiency and scalability of BrainPy on benchmark tasks,\nhighlight its differentiable simulation for biologically plausible spiking\nmodels, and discuss its potential to support research at the intersection of\nbrain simulation and BIC.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Hongyaoxing Gu", "<PERSON><PERSON>ang Li", "<PERSON>"], "year": 2023, "venue": "arXiv", "url": "http://arxiv.org/abs/2311.05106v2", "citation_count": null, "source": "arxiv", "paper_id": "2311.05106v2", "keywords": [], "doi": ""}, {"title": "Brain Inspired Computing", "abstract": "", "authors": ["<PERSON>"], "year": 2023, "venue": "Python for Scientific Computing and Artificial Intelligence", "url": "https://doi.org/10.1201/9781003285816-16", "citation_count": null, "source": "crossref", "paper_id": "10.1201/9781003285816-16", "keywords": [], "doi": "10.1201/9781003285816-16"}, {"title": "Planning with Brain-inspired AI", "abstract": "This article surveys engineering and neuroscientific models of planning as a\ncognitive function, which is regarded as a typical function of fluid\nintelligence in the discussion of general intelligence. It aims to present\nexisting planning models as references for realizing the planning function in\nbrain-inspired AI or artificial general intelligence (AGI). It also proposes\nthemes for the research and development of brain-inspired AI from the viewpoint\nof tasks and architecture.", "authors": ["<PERSON><PERSON>"], "year": 2020, "venue": "arXiv", "url": "http://arxiv.org/abs/2003.12353v1", "citation_count": null, "source": "arxiv", "paper_id": "2003.12353v1", "keywords": [], "doi": ""}, {"title": "Brain-inspired AI Agent: The Way Towards AGI", "abstract": "Artificial General Intelligence (AGI), widely regarded as the fundamental\ngoal of artificial intelligence, represents the realization of cognitive\ncapabilities that enable the handling of general tasks with human-like\nproficiency. Researchers in brain-inspired AI seek inspiration from the\noperational mechanisms of the human brain, aiming to replicate its functional\nrules in intelligent models. Moreover, with the rapid development of\nlarge-scale models in recent years, the concept of agents has garnered\nincreasing attention, with researchers widely recognizing it as a necessary\npathway toward achieving AGI. In this article, we propose the concept of a\nbrain-inspired AI agent and analyze how to extract relatively feasible and\nagent-compatible cortical region functionalities and their associated\nfunctional connectivity networks from the complex mechanisms of the human\nbrain. Implementing these structures within an agent enables it to achieve\nbasic cognitive intelligence akin to human capabilities. Finally, we explore\nthe limitations and challenges for realizing brain-inspired agents and discuss\ntheir future development.", "authors": ["<PERSON>", "<PERSON><PERSON>", "Minzhen Hu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "year": 2024, "venue": "arXiv", "url": "http://arxiv.org/abs/2412.08875v1", "citation_count": null, "source": "arxiv", "paper_id": "2412.08875v1", "keywords": [], "doi": ""}, {"title": "Brain-inspired Computing Based on Deep Learning for Human-computer\n  Interaction: A Review", "abstract": "The continuous development of artificial intelligence has a profound impact\non biomedicine and other fields, providing new research ideas and technical\nmethods. Brain-inspired computing is an important intersection between\nmultimodal technology and biomedical field. Focusing on the application\nscenarios of decoding text and speech from brain signals in human-computer\ninteraction, this paper presents a comprehensive review of the brain-inspired\ncomputing models based on deep learning (DL), tracking its evolution,\napplication value, challenges and potential research trends. We first reviews\nits basic concepts and development history, and divides its evolution into two\nstages: recent machine learning and current deep learning, emphasizing the\nimportance of each stage in the research of brain-inspired computing for\nhuman-computer interaction. In addition, the latest progress of deep learning\nin different tasks of brain-inspired computing for human-computer interaction\nis reviewed from five perspectives, including datasets and different brain\nsignals, and the application of key technologies in the model is elaborated in\ndetail. Despite significant advances in brain-inspired computational models,\nchallenges remain to fully exploit their capabilities, and we provide insights\ninto possible directions for future academic research. For more detailed\ninformation, please visit our GitHub page:\nhttps://github.com/ultracoolHub/brain-inspired-computing.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Linzhuang Sun", "<PERSON><PERSON>"], "year": 2023, "venue": "arXiv", "url": "http://arxiv.org/abs/2312.07213v4", "citation_count": null, "source": "arxiv", "paper_id": "2312.07213v4", "keywords": [], "doi": ""}], "workflows": {"paper_1": {"title": "Brain Inspired Intelligence Research: Novel Approach 1", "datasets": [], "network_architectures": [], "platforms_tools": [], "research_methods": ["novel approach"], "evaluation_metrics": [], "brain_inspiration": ["Brain Inspired Intelligence"], "ai_techniques": []}, "paper_2": {"title": "Brain Inspired Intelligence Research: Novel Approach 2", "datasets": [], "network_architectures": [], "platforms_tools": [], "research_methods": [], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": []}, "paper_3": {"title": "Brain Inspired Intelligence Research: Novel Approach 3", "datasets": [], "network_architectures": [], "platforms_tools": [], "research_methods": [], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": []}, "paper_4": {"title": "Brain Inspired Intelligence Research: Novel Approach 4", "datasets": [], "network_architectures": [], "platforms_tools": [], "research_methods": ["novel approach"], "evaluation_metrics": [], "brain_inspiration": ["Brain Inspired Intelligence"], "ai_techniques": []}, "paper_5": {"title": "Brain Inspired Intelligence Research: Novel Approach 5", "datasets": [], "network_architectures": [], "platforms_tools": [], "research_methods": [], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": []}}, "research_topic": "Brain Inspired Intelligence", "timestamp": "2025-07-24 18:21:53", "total_papers": 20}