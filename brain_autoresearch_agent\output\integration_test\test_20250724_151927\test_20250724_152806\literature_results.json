{"papers": [{"title": "Brain-Inspired Efficient Neural Networks For Image Classification Research: Novel Approach 1", "abstract": "This paper presents a novel brain-inspired approach to Efficient Neural Networks for Image Classification. Our method demonstrates significant improvements over existing approaches through bio-plausible mechanisms and efficient learning algorithms.", "authors": ["Researcher 1A", "Researcher 1B"], "year": 2023, "venue": "Nature Machine Intelligence", "url": "https://example.com/paper_1", "citation_count": 50, "source": "semantic_scholar", "paper_id": "mock_paper_1", "keywords": null, "doi": null}, {"title": "Brain-Inspired Efficient Neural Networks For Image Classification Research: Novel Approach 2", "abstract": "This paper presents a novel brain-inspired approach to Efficient Neural Networks for Image Classification. Our method demonstrates significant improvements over existing approaches through bio-plausible mechanisms and efficient learning algorithms.", "authors": ["Researcher 2A", "Researcher 2B"], "year": 2022, "venue": "Conference 1", "url": "https://example.com/paper_2", "citation_count": 40, "source": "semantic_scholar", "paper_id": "mock_paper_2", "keywords": null, "doi": null}, {"title": "Brain-Inspired Efficient Neural Networks For Image Classification Research: Novel Approach 3", "abstract": "This paper presents a novel brain-inspired approach to Efficient Neural Networks for Image Classification. Our method demonstrates significant improvements over existing approaches through bio-plausible mechanisms and efficient learning algorithms.", "authors": ["Researcher 3A", "Researcher 3B"], "year": 2021, "venue": "Conference 2", "url": "https://example.com/paper_3", "citation_count": 30, "source": "semantic_scholar", "paper_id": "mock_paper_3", "keywords": null, "doi": null}, {"title": "Brain-Inspired Efficient Neural Networks For Image Classification Research: Novel Approach 4", "abstract": "This paper presents a novel brain-inspired approach to Efficient Neural Networks for Image Classification. Our method demonstrates significant improvements over existing approaches through bio-plausible mechanisms and efficient learning algorithms.", "authors": ["Researcher 4A", "Researcher 4B"], "year": 2023, "venue": "Conference 3", "url": "https://example.com/paper_4", "citation_count": 20, "source": "semantic_scholar", "paper_id": "mock_paper_4", "keywords": null, "doi": null}, {"title": "Brain-Inspired Efficient Neural Networks For Image Classification Research: Novel Approach 5", "abstract": "This paper presents a novel brain-inspired approach to Efficient Neural Networks for Image Classification. Our method demonstrates significant improvements over existing approaches through bio-plausible mechanisms and efficient learning algorithms.", "authors": ["Researcher 5A", "Researcher 5B"], "year": 2022, "venue": "Conference 4", "url": "https://example.com/paper_5", "citation_count": 10, "source": "semantic_scholar", "paper_id": "mock_paper_5", "keywords": null, "doi": null}, {"title": "Exploring Highly Efficient Compact Neural Networks For Image Classification", "abstract": "Academic paper published in 2020 IEEE International Conference on Image Processing (ICIP). Full text available via DOI.", "authors": ["<PERSON><PERSON> Xi<PERSON>", "<PERSON>", "Sun-Yuan Kung"], "year": 2020, "venue": "2020 IEEE International Conference on Image Processing (ICIP)", "url": "https://doi.org/10.1109/icip40778.2020.9191334", "citation_count": 6, "source": "crossref", "paper_id": "10.1109/icip40778.2020.9191334", "keywords": null, "doi": "10.1109/icip40778.2020.9191334"}, {"title": "Creating Deep Convolutional Neural Networks for Image Classification", "abstract": "<jats:p>\n            This lesson provides a beginner-friendly introduction to convolutional neural networks (CNNs) for image classification. The tutorial provides a conceptual understanding of how neural networks work by using Google's Teachable Machine to train a model on paintings from the ArtUK database. This lesson also demonstrates how to use Javascript to embed the model in a live website.\n          </jats:p>", "authors": ["<PERSON><PERSON><PERSON>"], "year": 2023, "venue": "Programming Historian", "url": "https://doi.org/10.46430/phen0108", "citation_count": 2, "source": "crossref", "paper_id": "10.46430/phen0108", "keywords": null, "doi": "10.46430/phen0108"}, {"title": "Efficient Convolutional Neural Networks for Multi-Spectral Image Classification", "abstract": "Academic paper published in 2019 International Joint Conference on Neural Networks (IJCNN). Full text available via DOI.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "year": 2019, "venue": "2019 International Joint Conference on Neural Networks (IJCNN)", "url": "https://doi.org/10.1109/ijcnn.2019.8851840", "citation_count": 18, "source": "crossref", "paper_id": "10.1109/ijcnn.2019.8851840", "keywords": null, "doi": "10.1109/ijcnn.2019.8851840"}, {"title": "Provably efficient neural network representation for image\n  classification", "abstract": "The state-of-the-art approaches for image classification are based on neural\nnetworks. Mathematically, the task of classifying images is equivalent to\nfinding the function that maps an image to the label it is associated with. To\nrigorously establish the success of neural network methods, we should first\nprove that the function has an efficient neural network representation, and\nthen design provably efficient training algorithms to find such a\nrepresentation. Here, we achieve the first goal based on a set of assumptions\nabout the patterns in the images. The validity of these assumptions is very\nintuitive in many image classification problems, including but not limited to,\nrecognizing handwritten digits.", "authors": ["<PERSON><PERSON>"], "year": 2017, "venue": "arXiv", "url": "http://arxiv.org/abs/1711.04606v1", "citation_count": 0, "source": "arxiv", "paper_id": "1711.04606v1", "keywords": null, "doi": null}, {"title": "Classifier Ensemble for Efficient Uncertainty Calibration of Deep Neural Networks for Image Classification", "abstract": "Academic paper published in Proceedings of the 20th International Joint Conference on Computer Vision, Imaging and Computer Graphics Theory and Applications. Full text available via DOI.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "year": 2025, "venue": "Proceedings of the 20th International Joint Conference on Computer Vision, Imaging and Computer Graphics Theory and Applications", "url": "https://doi.org/10.5220/0013129000003912", "citation_count": 0, "source": "crossref", "paper_id": "10.5220/0013129000003912", "keywords": null, "doi": "10.5220/0013129000003912"}, {"title": "Hybrid technique for colour image classification and efficient retrieval based on fuzzy logic and neural networks", "abstract": "Academic paper published in The 2012 International Joint Conference on Neural Networks (IJCNN). Full text available via DOI.", "authors": ["<PERSON><PERSON>", "Siddhivina<PERSON><PERSON>"], "year": 2012, "venue": "The 2012 International Joint Conference on Neural Networks (IJCNN)", "url": "https://doi.org/10.1109/ijcnn.2012.6252587", "citation_count": 4, "source": "crossref", "paper_id": "10.1109/ijcnn.2012.6252587", "keywords": null, "doi": "10.1109/ijcnn.2012.6252587"}, {"title": "Deep Convolutional Spiking Neural Networks for Image Classification", "abstract": "<p>Spiking neural networks are biologically plausible counterparts of artificial neural networks. Artificial neural networks are usually trained with stochastic gradient descent (SGD) and spiking neural networks are trained with bioinspired spike timing dependent plasticity (STDP). Spiking networks could potentially help in reducing power usage owing to their binary activations. In this work, we use unsupervised STDP in the feature extraction layers of a neural network with instantaneous neurons to extract meaningful features. The extracted binary feature vectors are then classified using classification layers containing neurons with binary activations. Gradient descent (backpropagation) is used only on the output layer to perform training for classification. Surrogate gradients are proposed to perform backpropagation with binary gradients. The accuracies obtained for MNIST and the balanced EMNIST data set compare favorably with other approaches. The effect of the stochastic gradient descent (SGD) approximations on learning capabilities of our network are also explored. We also studied catastrophic forgetting and its effect on spiking neural networks (SNNs). For the experiments regarding catastrophic forgetting, in the classification sections of the network we use a modified synaptic intelligence that we refer to as cost per synapse metric as a regularizer to immunize the network against catastrophic forgetting in a Single-Incremental-Task scenario (SIT). In catastrophic forgetting experiments, we use MNIST and EMNIST handwritten digits datasets that were divided into five and ten incremental subtasks respectively. We also examine behavior of the spiking neural network and empirically study the effect of various hyperparameters on its learning capabilities using the software tool SPYKEFLOW that we developed. We employ MNIST, EMNIST and NMNIST data sets to produce our results.</p>", "authors": ["<PERSON><PERSON> Vaila"], "year": null, "venue": "Boise State University", "url": "https://doi.org/10.18122/td.1782.boisestate", "citation_count": 5, "source": "crossref", "paper_id": "10.18122/td.1782.boisestate", "keywords": null, "doi": "10.18122/td.1782.boisestate"}, {"title": "Entanglement Entropy of Target Functions for Image Classification and\n  Convolutional Neural Network", "abstract": "The success of deep convolutional neural network (CNN) in computer vision\nespecially image classification problems requests a new information theory for\nfunction of image, instead of image itself. In this article, after establishing\na deep mathematical connection between image classification problem and quantum\nspin model, we propose to use entanglement entropy, a generalization of\nclassical Boltzmann-Shannon entropy, as a powerful tool to characterize the\ninformation needed for representation of general function of image. We prove\nthat there is a sub-volume-law bound for entanglement entropy of target\nfunctions of reasonable image classification problems. Therefore target\nfunctions of image classification only occupy a small subspace of the whole\nHilbert space. As a result, a neural network with polynomial number of\nparameters is efficient for representation of such target functions of image.\nThe concept of entanglement entropy can also be useful to characterize the\nexpressive power of different neural networks. For example, we show that to\nmaintain the same expressive power, number of channels $D$ in a convolutional\nneural network should scale with the number of convolution layers $n_c$ as\n$D\\sim D_0^{\\frac{1}{n_c}}$. Therefore, deeper CNN with large $n_c$ is more\nefficient than shallow ones.", "authors": ["<PERSON><PERSON><PERSON>"], "year": 2017, "venue": "arXiv", "url": "http://arxiv.org/abs/1710.05520v1", "citation_count": 0, "source": "arxiv", "paper_id": "1710.05520v1", "keywords": null, "doi": null}, {"title": "Genetic Programming-Based Evolutionary Deep Learning for Data-Efficient\n  Image Classification", "abstract": "Data-efficient image classification is a challenging task that aims to solve\nimage classification using small training data. Neural network-based deep\nlearning methods are effective for image classification, but they typically\nrequire large-scale training data and have major limitations such as requiring\nexpertise to design network architectures and having poor interpretability.\nEvolutionary deep learning is a recent hot topic that combines evolutionary\ncomputation with deep learning. However, most evolutionary deep learning\nmethods focus on evolving architectures of neural networks, which still suffer\nfrom limitations such as poor interpretability. To address this, this paper\nproposes a new genetic programming-based evolutionary deep learning approach to\ndata-efficient image classification. The new approach can automatically evolve\nvariable-length models using many important operators from both image and\nclassification domains. It can learn different types of image features from\ncolour or gray-scale images, and construct effective and diverse ensembles for\nimage classification. A flexible multi-layer representation enables the new\napproach to automatically construct shallow or deep models/trees for different\ntasks and perform effective transformations on the input data via multiple\ninternal nodes. The new approach is applied to solve five image classification\ntasks with different training set sizes. The results show that it achieves\nbetter performance in most cases than deep learning methods for data-efficient\nimage classification. A deep analysis shows that the new approach has good\nconvergence and evolves models with high interpretability, different\nlengths/sizes/shapes, and good transferability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "year": 2022, "venue": "arXiv", "url": "http://arxiv.org/abs/2209.13233v1", "citation_count": 0, "source": "arxiv", "paper_id": "2209.13233v1", "keywords": null, "doi": null}, {"title": "Hybrid Quantum Neural Network Structures for Image Multi-classification", "abstract": "Image classification is a fundamental computer vision problem, and neural\nnetworks offer efficient solutions. With advancing quantum technology, quantum\nneural networks have gained attention. However, they work only for\nlow-dimensional data and demand dimensionality reduction and quantum encoding.\nTwo recent image classification methods have emerged: one employs PCA\ndimensionality reduction and angle encoding, the other integrates QNNs into\nCNNs to boost performance. Despite numerous algorithms, comparing PCA reduction\nwith angle encoding against the latter remains unclear. This study explores\nthese algorithms' performance in multi-class image classification and proposes\nan optimized hybrid quantum neural network suitable for the current\nenvironment. Investigating PCA-based quantum algorithms unveils a barren\nplateau issue for QNNs as categories increase, unsuitable for multi-class in\nthe hybrid setup. Simultaneously, the combined CNN-QNN model partly overcomes\nQNN's multi-class training challenges but lags in accuracy to superior\ntraditional CNN models. Additionally, this work explores transfer learning in\nthe hybrid quantum neural network model. In conclusion, quantum neural networks\nshow promise but require further research and optimization, facing challenges\nahead.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2023, "venue": "arXiv", "url": "http://arxiv.org/abs/2308.16005v1", "citation_count": 0, "source": "arxiv", "paper_id": "2308.16005v1", "keywords": null, "doi": null}, {"title": "Evolving Deep Neural Networks for Efficient Image Classification", "abstract": "Academic paper published in SSRN Electronic Journal. Full text available via DOI.", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "year": null, "venue": "SSRN Electronic Journal", "url": "https://doi.org/10.2139/ssrn.3576482", "citation_count": 0, "source": "crossref", "paper_id": "10.2139/ssrn.3576482", "keywords": null, "doi": "10.2139/ssrn.3576482"}, {"title": "Classification of optics-free images with deep neural networks", "abstract": "The thinnest possible camera is achieved by removing all optics, leaving only\nthe image sensor. We train deep neural networks to perform multi-class\ndetection and binary classification (with accuracy of 92%) on optics-free\nimages without the need for anthropocentric image reconstructions. Inferencing\nfrom optics-free images has the potential for enhanced privacy and power\nefficiency.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2020, "venue": "arXiv", "url": "http://arxiv.org/abs/2011.05132v1", "citation_count": 0, "source": "arxiv", "paper_id": "2011.05132v1", "keywords": null, "doi": null}, {"title": "Image edge enhancement for effective image classification", "abstract": "Image classification has been a popular task due to its feasibility in\nreal-world applications. Training neural networks by feeding them RGB images\nhas demonstrated success over it. Nevertheless, improving the classification\naccuracy and computational efficiency of this process continues to present\nchallenges that researchers are actively addressing. A widely popular embraced\nmethod to improve the classification performance of neural networks is to\nincorporate data augmentations during the training process. Data augmentations\nare simple transformations that create slightly modified versions of the\ntraining data and can be very effective in training neural networks to mitigate\noverfitting and improve their accuracy performance. In this study, we draw\ninspiration from high-boost image filtering and propose an edge\nenhancement-based method as means to enhance both accuracy and training speed\nof neural networks. Specifically, our approach involves extracting high\nfrequency features, such as edges, from images within the available dataset and\nfusing them with the original images, to generate new, enriched images. Our\ncomprehensive experiments, conducted on two distinct datasets CIFAR10 and\nCALTECH101, and three different network architectures ResNet-18, LeNet-5 and\nCNN-9 demonstrates the effectiveness of our proposed method.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2024, "venue": "arXiv", "url": "http://arxiv.org/abs/2401.07028v1", "citation_count": 0, "source": "arxiv", "paper_id": "2401.07028v1", "keywords": null, "doi": null}, {"title": "Convolutional neural network classification of cancer cytopathology\n  images: taking breast cancer as an example", "abstract": "Breast cancer is a relatively common cancer among gynecological cancers. Its\ndiagnosis often relies on the pathology of cells in the lesion. The\npathological diagnosis of breast cancer not only requires professionals and\ntime, but also sometimes involves subjective judgment. To address the\nchallenges of dependence on pathologists expertise and the time-consuming\nnature of achieving accurate breast pathological image classification, this\npaper introduces an approach utilizing convolutional neural networks (CNNs) for\nthe rapid categorization of pathological images, aiming to enhance the\nefficiency of breast pathological image detection. And the approach enables the\nrapid and automatic classification of pathological images into benign and\nmalignant groups. The methodology involves utilizing a convolutional neural\nnetwork (CNN) model leveraging the Inceptionv3 architecture and transfer\nlearning algorithm for extracting features from pathological images. Utilizing\na neural network with fully connected layers and employing the SoftMax function\nfor image classification. Additionally, the concept of image partitioning is\nintroduced to handle high-resolution images. To achieve the ultimate\nclassification outcome, the classification probabilities of each image block\nare aggregated using three algorithms: summation, product, and maximum.\nExperimental validation was conducted on the BreaKHis public dataset, resulting\nin accuracy rates surpassing 0.92 across all four magnification coefficients\n(40X, 100X, 200X, and 400X). It demonstrates that the proposed method\neffectively enhances the accuracy in classifying pathological images of breast\ncancer.", "authors": ["<PERSON><PERSON><PERSON>", "Yufeng Li", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "year": 2024, "venue": "arXiv", "url": "http://arxiv.org/abs/2404.08279v1", "citation_count": 0, "source": "arxiv", "paper_id": "2404.08279v1", "keywords": null, "doi": null}, {"title": "Using Neural Networks for Image Classification", "abstract": "Academic paper published in San Jose State University Library. Full text available via DOI.", "authors": ["<PERSON>"], "year": null, "venue": "San Jose State University Library", "url": "https://doi.org/10.31979/etd.ssss-za3p", "citation_count": 0, "source": "crossref", "paper_id": "10.31979/etd.ssss-za3p", "keywords": null, "doi": "10.31979/etd.ssss-za3p"}], "workflows": {"paper_1": {"title": "Brain-Inspired Efficient Neural Networks For Image Classification Research: Novel Approach 1", "datasets": [], "network_architectures": ["Efficient Neural Networks"], "platforms_tools": [], "research_methods": ["bio-plausible mechanisms", "efficient learning algorithms"], "evaluation_metrics": [], "brain_inspiration": ["bio-plausible mechanisms"], "ai_techniques": ["efficient learning algorithms"]}, "paper_2": {"title": "Brain-Inspired Efficient Neural Networks For Image Classification Research: Novel Approach 2", "datasets": [], "network_architectures": ["Efficient Neural Networks"], "platforms_tools": [], "research_methods": ["bio-plausible mechanisms", "efficient learning algorithms"], "evaluation_metrics": [], "brain_inspiration": ["bio-plausible mechanisms"], "ai_techniques": ["efficient learning algorithms"]}, "paper_3": {"title": "Brain-Inspired Efficient Neural Networks For Image Classification Research: Novel Approach 3", "datasets": [], "network_architectures": ["Efficient Neural Networks"], "platforms_tools": [], "research_methods": ["bio-plausible mechanisms", "efficient learning algorithms"], "evaluation_metrics": [], "brain_inspiration": ["bio-plausible mechanisms"], "ai_techniques": ["efficient learning algorithms"]}, "paper_4": {"title": "Brain-Inspired Efficient Neural Networks For Image Classification Research: Novel Approach 4", "datasets": [], "network_architectures": ["Efficient Neural Networks"], "platforms_tools": [], "research_methods": ["bio-plausible mechanisms", "efficient learning algorithms"], "evaluation_metrics": [], "brain_inspiration": ["bio-plausible mechanisms"], "ai_techniques": ["efficient learning algorithms"]}, "paper_5": {"title": "Brain-Inspired Efficient Neural Networks For Image Classification Research: Novel Approach 5", "datasets": [], "network_architectures": ["Efficient Neural Networks"], "platforms_tools": [], "research_methods": ["bio-plausible mechanisms", "efficient learning algorithms"], "evaluation_metrics": [], "brain_inspiration": ["bio-plausible mechanisms"], "ai_techniques": ["efficient learning algorithms"]}}, "research_topic": "Efficient Neural Networks for Image Classification", "timestamp": "2025-07-24 15:28:06", "total_papers": 20}