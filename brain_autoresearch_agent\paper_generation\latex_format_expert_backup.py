"""
LaTeX格式优化专家模块
专门负责论文的LaTeX格式优化，确保符合顶级会议标准
"""

import re
import os
import json
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class LaTeXFormatIssue:
    """LaTeX格式问题"""
    issue_type: str  # 问题类型
    line_number: int  # 行号
    description: str  # 问题描述
    severity: str  # 严重程度: critical, warning, suggestion
    fix_suggestion: str  # 修复建议

@dataclass
class LaTeXOptimizationResult:
    """LaTeX优化结果"""
    original_content: str
    optimized_content: str
    issues_found: List[LaTeXFormatIssue]
    issues_fixed: List[LaTeXFormatIssue]
    venue_compliance: Dict[str, bool]  # 会议格式合规性
    quality_score: float  # 格式质量分数

class LaTeXFormatExpert:
    """LaTeX格式优化专家 - 专业级别会议格式优化"""
    
    def __init__(self, hybrid_client=None):
        self.hybrid_client = hybrid_client
        self.venue_templates = self._load_venue_templates()
        self.format_rules = self._load_format_rules()
        self.citation_patterns = self._load_citation_patterns()
        
    def _load_venue_templates(self) -> Dict[str, Dict]:
        """加载会议模板配置 - 扩展版"""
        return {
            'ICML': {
                'documentclass': r'\documentclass[accepted]{icml2024}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'subfigure'],
                'title_format': r'\icmltitle{{{title}}}',
                'author_format': r'\icmlauthor{{{author}}}{{{affiliation}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': r'\icmlkeywords{{{keywords}}}',
                'section_numbering': True,
                'figure_placement': 'htbp',
                'table_style': 'booktabs',
                'algorithm_package': 'algorithm2e',
                'math_packages': ['amsmath', 'amssymb', 'amsthm'],
                'references_style': 'icml2024',
                'page_limit': 8,
                'appendix_allowed': True
            },
            'NeurIPS': {
                'documentclass': r'\documentclass{neurips_2024}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'natbib'],
                'title_format': r'\title{{{title}}}',
                'author_format': r'\author{{{author}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': None,
                'section_numbering': True,
                'figure_placement': 'htbp',
                'table_style': 'booktabs',
                'algorithm_package': 'algorithm',
                'math_packages': ['amsmath', 'amssymb', 'amsthm'],
                'references_style': 'neurips_2024',
                'page_limit': 9,
                'appendix_allowed': True
            },
            'ICLR': {
                'documentclass': r'\documentclass{iclr2024_conference}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx'],
                'title_format': r'\title{{{title}}}',
                'author_format': r'\author{{{author}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': r'\keywords{{{keywords}}}',
                'section_numbering': True,
                'figure_placement': 'htbp',
                'table_style': 'booktabs',
                'algorithm_package': 'algorithm2e',
                'math_packages': ['amsmath', 'amssymb', 'amsthm'],
                'references_style': 'iclr2024',
                'page_limit': 9,
                'appendix_allowed': True
            },
            'AAAI': {
                'documentclass': r'\documentclass[letterpaper]{aaai24}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'times'],
                'title_format': r'\title{{{title}}}',
                'author_format': r'\author{{{author}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': None,
                'section_numbering': True,
                'figure_placement': 'htbp',
                'table_style': 'booktabs',
                'algorithm_package': 'algorithm2e',
                'math_packages': ['amsmath', 'amssymb', 'amsthm'],
                'references_style': 'aaai24',
                'page_limit': 7,
                'appendix_allowed': False
            },
            'IEEE': {
                'documentclass': r'\documentclass[conference]{IEEEtran}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'cite'],
                'title_format': r'\title{{{title}}}',
                'author_format': r'\author{{{author}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': r'\begin{{IEEEkeywords}}{keywords}\end{{IEEEkeywords}}',
                'section_numbering': True,
                'figure_placement': 'htbp',
                'table_style': 'booktabs',
                'algorithm_package': 'algorithmic',
                'math_packages': ['amsmath', 'amssymb', 'amsthm'],
                'references_style': 'IEEEtran',
                'page_limit': 6,
                'appendix_allowed': True
            }
        }
    
    def _load_citation_patterns(self) -> Dict[str, str]:
        """加载引用模式配置"""
        return {
            'ICML': r'\\cite\{[^}]+\}',
            'NeurIPS': r'\\citep?\{[^}]+\}',
            'ICLR': r'\\cite\{[^}]+\}',
            'AAAI': r'\\cite\{[^}]+\}',
            'IEEE': r'\\cite\{[^}]+\}'
        }
        
    def _load_format_rules(self) -> Dict[str, Dict]:
        """加载格式规则配置"""
        return {
                'max_pages': 8,
                'reference_style': 'icml2024'
            },
            'NeurIPS': {
                'documentclass': r'\documentclass{neurips_2024}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype'],
                'title_format': r'\title{{{title}}}',
                'author_format': r'\author{{{author}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': None,
                'section_numbering': True,
                'max_pages': 9,
                'reference_style': 'neurips_2024'
            },
            'ICLR': {
                'documentclass': r'\documentclass{iclr2024_conference}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts', 'nicefrac', 'microtype'],
                'title_format': r'\title{{{title}}}',
                'author_format': r'\author{{{author}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': r'\keywords{{{keywords}}}',
                'section_numbering': True,
                'max_pages': 8,
                'reference_style': 'iclr2024'
            },
            'AAAI': {
                'documentclass': r'\documentclass[letterpaper]{aaai24}',
                'required_packages': ['inputenc', 'fontenc', 'hyperref', 'url', 'booktabs', 'amsfonts'],
                'title_format': r'\title{{{title}}}',
                'author_format': r'\author{{{author}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': r'\keywords{{{keywords}}}',
                'section_numbering': True,
                'max_pages': 7,
                'reference_style': 'aaai24'
            },
            'IEEE': {
                'documentclass': r'\documentclass[conference]{IEEEtran}',
                'required_packages': ['cite', 'amsmath', 'amssymb', 'amsfonts', 'algorithmic', 'graphicx', 'textcomp', 'xcolor'],
                'title_format': r'\title{{{title}}}',
                'author_format': r'\author{{\IEEEauthorblockN{{{author}}}\IEEEauthorblockA{{{affiliation}}}}}',
                'abstract_env': 'abstract',
                'keywords_cmd': r'\begin{{IEEEkeywords}}{keywords}\end{{IEEEkeywords}}',
                'section_numbering': True,
                'max_pages': 6,
                'reference_style': 'IEEEtran'
            }
        }
    
    def _load_format_rules(self) -> Dict[str, Dict]:
        """加载格式规则"""
        return {
            'critical_issues': {
                'missing_documentclass': r'Missing \\documentclass',
                'missing_begin_document': r'Missing \\begin{document}',
                'missing_end_document': r'Missing \\end{document}',
                'unmatched_braces': r'Unmatched braces',
                'undefined_commands': r'Undefined commands',
                'missing_packages': r'Missing required packages'
            },
            'warning_issues': {
                'long_lines': r'Lines longer than 80 characters',
                'missing_labels': r'Missing labels for figures/tables',
                'inconsistent_spacing': r'Inconsistent spacing',
                'deprecated_commands': r'Deprecated LaTeX commands',
                'improper_citations': r'Improper citation format'
            },
            'style_suggestions': {
                'section_formatting': r'Section formatting improvements',
                'figure_positioning': r'Figure positioning optimization',
                'table_formatting': r'Table formatting improvements',
                'equation_formatting': r'Equation formatting suggestions',
                'reference_formatting': r'Reference formatting improvements'
            }
        }
    
    def analyze_latex_format(self, latex_content: str, target_venue: str = 'ICML') -> List[LaTeXFormatIssue]:
        """分析LaTeX格式问题"""
        issues = []
        lines = latex_content.split('\n')
        
        # 检查文档类
        if not re.search(r'\\documentclass', latex_content):
            issues.append(LaTeXFormatIssue(
                issue_type='missing_documentclass',
                line_number=1,
                description='Missing document class declaration',
                severity='critical',
                fix_suggestion=f'Add {self.venue_templates[target_venue]["documentclass"]} at the beginning'
            ))
        
        # 检查必需包
        venue_config = self.venue_templates.get(target_venue, self.venue_templates['ICML'])
        for package in venue_config['required_packages']:
            if not re.search(rf'\\usepackage.*{package}', latex_content):
                issues.append(LaTeXFormatIssue(
                    issue_type='missing_package',
                    line_number=0,
                    description=f'Missing required package: {package}',
                    severity='warning',
                    fix_suggestion=f'Add \\usepackage{{{package}}} in preamble'
                ))
        
        # 检查标题格式
        if not re.search(r'\\title\{.*\}', latex_content):
            issues.append(LaTeXFormatIssue(
                issue_type='missing_title',
                line_number=0,
                description='Missing title declaration',
                severity='critical',
                fix_suggestion='Add \\title{Your Paper Title}'
            ))
        
        # 检查作者格式
        if not re.search(r'\\author\{.*\}', latex_content):
            issues.append(LaTeXFormatIssue(
                issue_type='missing_author',
                line_number=0,
                description='Missing author declaration',
                severity='critical',
                fix_suggestion='Add \\author{Author Name}'
            ))
        
        # 检查摘要
        if not re.search(r'\\begin\{abstract\}.*\\end\{abstract\}', latex_content, re.DOTALL):
            issues.append(LaTeXFormatIssue(
                issue_type='missing_abstract',
                line_number=0,
                description='Missing abstract environment',
                severity='critical',
                fix_suggestion='Add \\begin{abstract}...\\end{abstract}'
            ))
        
        # 检查参考文献
        if not re.search(r'\\bibliography\{.*\}|\\begin\{thebibliography\}', latex_content):
            issues.append(LaTeXFormatIssue(
                issue_type='missing_bibliography',
                line_number=0,
                description='Missing bibliography',
                severity='warning',
                fix_suggestion='Add \\bibliography{references} or \\begin{thebibliography}'
            ))
        
        # 检查行长度
        for i, line in enumerate(lines):
            if len(line) > 80:
                issues.append(LaTeXFormatIssue(
                    issue_type='long_line',
                    line_number=i+1,
                    description=f'Line too long ({len(line)} characters)',
                    severity='suggestion',
                    fix_suggestion='Break long lines for better readability'
                ))
        
        # 检查花括号匹配
        brace_count = 0
        for i, line in enumerate(lines):
            for char in line:
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count < 0:
                        issues.append(LaTeXFormatIssue(
                            issue_type='unmatched_braces',
                            line_number=i+1,
                            description='Unmatched closing brace',
                            severity='critical',
                            fix_suggestion='Check brace matching'
                        ))
                        brace_count = 0
        
        if brace_count != 0:
            issues.append(LaTeXFormatIssue(
                issue_type='unmatched_braces',
                line_number=len(lines),
                description='Unmatched opening braces',
                severity='critical',
                fix_suggestion='Add missing closing braces'
            ))
        
        return issues
    
    def fix_latex_format(self, latex_content: str, issues: List[LaTeXFormatIssue], target_venue: str = 'ICML') -> str:
        """修复LaTeX格式问题"""
        fixed_content = latex_content
        venue_config = self.venue_templates.get(target_venue, self.venue_templates['ICML'])
        
        # 修复文档类
        if not re.search(r'\\documentclass', fixed_content):
            fixed_content = venue_config['documentclass'] + '\n' + fixed_content
        
        # 修复必需包
        preamble_end = fixed_content.find('\\begin{document}')
        if preamble_end != -1:
            preamble = fixed_content[:preamble_end]
            document_body = fixed_content[preamble_end:]
            
            # 添加缺失的包
            packages_to_add = []
            for package in venue_config['required_packages']:
                if not re.search(rf'\\usepackage.*{package}', preamble):
                    packages_to_add.append(f'\\usepackage{{{package}}}')
            
            if packages_to_add:
                package_block = '\n'.join(packages_to_add) + '\n'
                fixed_content = preamble + package_block + document_body
        
        # 修复标题格式
        title_match = re.search(r'\\title\{([^}]*)\}', fixed_content)
        if title_match:
            title = title_match.group(1)
            if target_venue == 'ICML':
                fixed_content = re.sub(r'\\title\{([^}]*)\}', r'\\icmltitle{' + title + '}', fixed_content)
        
        # 修复作者格式
        author_match = re.search(r'\\author\{([^}]*)\}', fixed_content)
        if author_match and target_venue == 'ICML':
            author = author_match.group(1)
            fixed_content = re.sub(r'\\author\{([^}]*)\}', r'\\icmlauthor{' + author + '}{Institution}', fixed_content)
        
        # 修复关键词格式
        if target_venue == 'ICML':
            keywords_match = re.search(r'\\begin\{IEEEkeywords\}(.*?)\\end\{IEEEkeywords\}', fixed_content, re.DOTALL)
            if keywords_match:
                keywords = keywords_match.group(1).strip()
                fixed_content = re.sub(r'\\begin\{IEEEkeywords\}.*?\\end\{IEEEkeywords\}', 
                                     r'\\icmlkeywords{' + keywords + '}', fixed_content, flags=re.DOTALL)
        
        # 美化格式
        fixed_content = self._beautify_latex(fixed_content)
        
        return fixed_content
    
    def _beautify_latex(self, latex_content: str) -> str:
        """美化LaTeX格式"""
        lines = latex_content.split('\n')
        beautified_lines = []
        indent_level = 0
        
        for line in lines:
            stripped = line.strip()
            
            # 跳过空行
            if not stripped:
                beautified_lines.append('')
                continue
            
            # 处理缩进
            if stripped.startswith('\\end{'):
                indent_level = max(0, indent_level - 1)
            
            # 添加缩进
            if indent_level > 0:
                beautified_lines.append('  ' * indent_level + stripped)
            else:
                beautified_lines.append(stripped)
            
            # 更新缩进级别
            if stripped.startswith('\\begin{') and not stripped.endswith('}'):
                indent_level += 1
            elif '\\begin{' in stripped and not stripped.endswith('}'):
                indent_level += 1
        
        return '\n'.join(beautified_lines)
    
    def optimize_latex_format(self, latex_content: str, target_venue: str = 'ICML') -> LaTeXOptimizationResult:
        """优化LaTeX格式"""
        # 分析问题
        issues = self.analyze_latex_format(latex_content, target_venue)
        
        # 修复问题
        optimized_content = self.fix_latex_format(latex_content, issues, target_venue)
        
        # 再次分析确认修复
        remaining_issues = self.analyze_latex_format(optimized_content, target_venue)
        fixed_issues = [issue for issue in issues if issue not in remaining_issues]
        
        # 检查会议格式合规性
        venue_compliance = self._check_venue_compliance(optimized_content, target_venue)
        
        # 计算质量分数
        quality_score = self._calculate_format_quality(optimized_content, remaining_issues)
        
        return LaTeXOptimizationResult(
            original_content=latex_content,
            optimized_content=optimized_content,
            issues_found=issues,
            issues_fixed=fixed_issues,
            venue_compliance=venue_compliance,
            quality_score=quality_score
        )
    
    def _check_venue_compliance(self, latex_content: str, target_venue: str) -> Dict[str, bool]:
        """检查会议格式合规性"""
        venue_config = self.venue_templates.get(target_venue, self.venue_templates['ICML'])
        compliance = {}
        
        # 检查文档类
        compliance['documentclass'] = bool(re.search(rf'\\documentclass.*{target_venue.lower()}', latex_content, re.IGNORECASE))
        
        # 检查必需包
        missing_packages = []
        for package in venue_config['required_packages']:
            if not re.search(rf'\\usepackage.*{package}', latex_content):
                missing_packages.append(package)
        compliance['required_packages'] = len(missing_packages) == 0
        
        # 检查标题格式
        if target_venue == 'ICML':
            compliance['title_format'] = bool(re.search(r'\\icmltitle\{', latex_content))
        else:
            compliance['title_format'] = bool(re.search(r'\\title\{', latex_content))
        
        # 检查作者格式
        compliance['author_format'] = bool(re.search(r'\\author', latex_content))
        
        # 检查摘要
        compliance['abstract'] = bool(re.search(r'\\begin\{abstract\}', latex_content))
        
        # 检查参考文献
        compliance['bibliography'] = bool(re.search(r'\\bibliography|\\begin\{thebibliography\}', latex_content))
        
        return compliance
    
    def _calculate_format_quality(self, latex_content: str, issues: List[LaTeXFormatIssue]) -> float:
        """计算格式质量分数"""
        base_score = 10.0
        
        # 扣分规则
        for issue in issues:
            if issue.severity == 'critical':
                base_score -= 2.0
            elif issue.severity == 'warning':
                base_score -= 1.0
            elif issue.severity == 'suggestion':
                base_score -= 0.5
        
        # 加分项
        if re.search(r'\\begin\{figure\}', latex_content):
            base_score += 0.5
        if re.search(r'\\begin\{table\}', latex_content):
            base_score += 0.5
        if re.search(r'\\begin\{algorithm\}', latex_content):
            base_score += 0.5
        if re.search(r'\\cite\{', latex_content):
            base_score += 0.5
        
        return max(0.0, min(10.0, base_score))
    
    def generate_venue_template(self, title: str, authors: List[str], abstract: str, 
                               keywords: List[str], target_venue: str = 'ICML') -> str:
        """生成会议模板"""
        venue_config = self.venue_templates.get(target_venue, self.venue_templates['ICML'])
        
        template = f"""{venue_config['documentclass']}
"""
        
        # 添加必需包
        for package in venue_config['required_packages']:
            template += f"\\usepackage{{{package}}}\n"
        
        template += "\n"
        
        # 添加标题
        if target_venue == 'ICML':
            template += f"\\icmltitle{{{title}}}\n\n"
        else:
            template += f"\\title{{{title}}}\n\n"
        
        # 添加作者
        if target_venue == 'ICML':
            for author in authors:
                template += f"\\icmlauthor{{{author}}}{{Institution}}\n"
        elif target_venue == 'IEEE':
            author_block = "\\author{\n"
            for author in authors:
                author_block += f"\\IEEEauthorblockN{{{author}}}\n"
                author_block += f"\\IEEEauthorblockA{{Institution}}\n"
            author_block += "}\n"
            template += author_block
        else:
            template += f"\\author{{{', '.join(authors)}}}\n"
        
        template += "\n\\begin{document}\n\n"
        template += "\\maketitle\n\n"
        
        # 添加摘要
        template += f"\\begin{{abstract}}\n{abstract}\n\\end{{abstract}}\n\n"
        
        # 添加关键词
        if venue_config['keywords_cmd']:
            keywords_str = ', '.join(keywords)
            if target_venue == 'ICML':
                template += f"\\icmlkeywords{{{keywords_str}}}\n\n"
            elif target_venue == 'IEEE':
                template += f"\\begin{{IEEEkeywords}}\n{keywords_str}\n\\end{{IEEEkeywords}}\n\n"
            else:
                template += f"\\keywords{{{keywords_str}}}\n\n"
        
        # 添加基本结构
        template += """\\section{Introduction}
Your introduction here.

\\section{Related Work}
Your related work here.

\\section{Methodology}
Your methodology here.

\\section{Experiments}
Your experiments here.

\\section{Results}
Your results here.

\\section{Conclusion}
Your conclusion here.

\\bibliographystyle{""" + venue_config['reference_style'] + """}
\\bibliography{references}

\\end{document}
"""
        
        return template
    
    def validate_latex_compilation(self, latex_content: str) -> Tuple[bool, List[str]]:
        """验证LaTeX编译"""
        # 这里可以集成实际的LaTeX编译检查
        # 目前返回基本的语法检查结果
        errors = []
        
        # 检查基本结构
        if not re.search(r'\\documentclass', latex_content):
            errors.append("Missing \\documentclass")
        
        if not re.search(r'\\begin\{document\}', latex_content):
            errors.append("Missing \\begin{document}")
        
        if not re.search(r'\\end\{document\}', latex_content):
            errors.append("Missing \\end{document}")
        
        # 检查花括号匹配
        brace_count = latex_content.count('{') - latex_content.count('}')
        if brace_count != 0:
            errors.append(f"Unmatched braces: {brace_count}")
        
        # 检查常见错误
        if re.search(r'\\begin\{[^}]*\}(?!.*\\end\{[^}]*\})', latex_content, re.DOTALL):
            errors.append("Unmatched begin/end environments")
        
        return len(errors) == 0, errors
    
    def detect_format_issues(self, latex_content: str, target_venue: str = 'ICML') -> List[LaTeXFormatIssue]:
        """检测LaTeX格式问题 - 别名方法"""
        return self.analyze_latex_format(latex_content, target_venue)
    
    def check_venue_compliance(self, latex_content: str, target_venue: str = 'ICML') -> Dict[str, bool]:
        """检查会议合规性 - 别名方法"""
        return self._check_venue_compliance(latex_content, target_venue)
    
    def fix_format_issues(self, latex_content: str, issues: List[LaTeXFormatIssue], target_venue: str = 'ICML') -> str:
        """修复格式问题 - 别名方法"""
        return self.fix_latex_format(latex_content, issues, target_venue)

def main():
    """测试LaTeX格式优化专家"""
    # 创建测试用的LaTeX内容
    test_latex = """
\\documentclass[conference]{IEEEtran}
\\usepackage{cite}
\\usepackage{amsmath}

\\title{Test Paper Title}
\\author{Test Author}

\\begin{document}
\\maketitle

\\begin{abstract}
This is a test abstract.
\\end{abstract}

\\section{Introduction}
This is the introduction.

\\section{Conclusion}
This is the conclusion.

\\end{document}
"""
    
    # 创建LaTeX格式专家
    expert = LaTeXFormatExpert()
    
    # 分析和优化
    result = expert.optimize_latex_format(test_latex, 'ICML')
    
    print("LaTeX格式优化结果:")
    print(f"发现问题: {len(result.issues_found)}")
    print(f"修复问题: {len(result.issues_fixed)}")
    print(f"格式质量分数: {result.quality_score:.1f}/10")
    print(f"会议格式合规性: {result.venue_compliance}")
    
    print("\n优化后的LaTeX:")
    print(result.optimized_content)

if __name__ == "__main__":
    main()
