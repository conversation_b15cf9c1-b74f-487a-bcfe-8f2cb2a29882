"""
测试论文工作流提取系统

验证工作流提取器和分析器的功能
"""

import os
import sys
import json
import time
from typing import Dict, List, Any

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from paper_generation.workflow_extraction.paper_workflow_extractor import PaperWorkflowExtractor
from paper_generation.workflow_extraction.workflow_analyzer import WorkflowAnalyzer
from paper_generation.workflow_extraction.workflow_data_models import PaperWorkflow


def create_test_paper_data() -> Dict[str, Any]:
    """创建测试论文数据"""
    return {
        'title': 'Brain-Inspired Spiking Neural Networks with Adaptive Plasticity for Multi-Modal Learning',
        'abstract': '''
        This paper introduces a novel brain-inspired spiking neural network (SNN) architecture 
        that incorporates adaptive synaptic plasticity mechanisms for multi-modal learning tasks. 
        Our approach combines spike-timing-dependent plasticity (STDP) with attention mechanisms 
        to achieve competitive performance on image and text classification benchmarks. We evaluate 
        our method on CIFAR-10, MNIST, and GLUE datasets using PyTorch and Brian2 implementations. 
        Experimental results demonstrate that our brain-inspired approach achieves 94.2% accuracy 
        on CIFAR-10 while maintaining biological plausibility and energy efficiency.
        ''',
        'full_text': '''
        1. Introduction
        
        Deep learning has achieved remarkable success in various domains, but current artificial 
        neural networks lack the biological plausibility and energy efficiency of the human brain. 
        Spiking neural networks (SNNs) offer a more biologically realistic approach by using 
        discrete spikes for information processing. Recent advances in neuromorphic computing 
        and brain-inspired algorithms have renewed interest in SNNs for practical applications.
        
        In this work, we propose a novel brain-inspired SNN architecture that incorporates:
        - Adaptive synaptic plasticity based on STDP rules
        - Multi-modal attention mechanisms inspired by cortical processing
        - Hierarchical feature learning similar to visual cortex organization
        
        2. Related Work
        
        Previous work on spiking neural networks has focused on either biological accuracy or 
        practical performance. Our approach bridges this gap by maintaining biological plausibility 
        while achieving competitive results on standard benchmarks.
        
        3. Methodology
        
        3.1 Network Architecture
        Our SNN architecture consists of multiple layers with different neuron types:
        - Input layer: Poisson spike encoders
        - Hidden layers: Leaky integrate-and-fire (LIF) neurons
        - Output layer: Rate-based readout neurons
        
        The network uses convolutional layers for spatial feature extraction and recurrent 
        connections for temporal processing. We implement attention mechanisms through 
        dynamic synaptic weights that adapt based on input relevance.
        
        3.2 Adaptive Plasticity Mechanism
        We implement STDP-based learning rules that modify synaptic weights based on 
        relative spike timing. The plasticity mechanism includes:
        - Long-term potentiation (LTP) for correlated spikes
        - Long-term depression (LTD) for uncorrelated activity
        - Homeostatic regulation to maintain network stability
        
        3.3 Multi-Modal Integration
        Our approach handles multiple input modalities through specialized encoder networks:
        - Vision encoder: Convolutional SNN for image processing
        - Text encoder: Transformer-based spike encoder for language
        - Fusion layer: Cross-modal attention for information integration
        
        4. Experimental Setup
        
        4.1 Datasets
        We evaluate our method on three benchmark datasets:
        - CIFAR-10: 60,000 32x32 color images in 10 classes
        - MNIST: 70,000 handwritten digit images
        - GLUE: General Language Understanding Evaluation benchmark
        
        4.2 Implementation Details
        We implement our SNN using PyTorch for the main framework and Brian2 for 
        detailed neuromorphic simulations. The network is trained using surrogate 
        gradient methods with STDP-based fine-tuning.
        
        Hardware: NVIDIA V100 GPU, Intel Xeon CPU
        Software: Python 3.8, PyTorch 1.10, Brian2 2.4
        Training time: 12 hours for CIFAR-10, 6 hours for MNIST
        
        4.3 Baseline Methods
        We compare against several baseline approaches:
        - Standard CNN (ResNet-18)
        - Traditional SNN (without plasticity)
        - Transformer networks (for text tasks)
        - Multi-modal fusion baselines
        
        4.4 Evaluation Metrics
        Performance is measured using:
        - Classification accuracy
        - Energy consumption (spike count)
        - Training convergence time
        - Biological plausibility scores
        
        5. Results
        
        5.1 Classification Performance
        Our brain-inspired SNN achieves competitive results:
        - CIFAR-10: 94.2% accuracy (vs 95.1% for ResNet-18)
        - MNIST: 99.1% accuracy (vs 99.3% for standard CNN)
        - GLUE average: 81.3% (vs 83.7% for BERT-base)
        
        5.2 Energy Efficiency
        Our SNN demonstrates significant energy savings:
        - 67% reduction in computational operations compared to equivalent CNNs
        - Sparse spike patterns lead to efficient neuromorphic implementation
        
        5.3 Plasticity Analysis
        STDP-based learning shows improved adaptation:
        - Faster convergence on new tasks (transfer learning)
        - Better robustness to input noise
        - Maintained performance over extended training
        
        6. Ablation Studies
        
        We conduct ablation studies to analyze individual components:
        - Removing STDP reduces accuracy by 3.2% on CIFAR-10
        - Attention mechanism contributes 2.1% improvement
        - Multi-modal fusion adds 4.7% for combined tasks
        
        7. Discussion
        
        Our results demonstrate that brain-inspired mechanisms can enhance SNN performance 
        while maintaining biological plausibility. The adaptive plasticity mechanism is 
        particularly effective for continual learning scenarios.
        
        Limitations:
        - Higher computational overhead during training
        - Requires careful hyperparameter tuning
        - Limited to specific network architectures
        
        8. Conclusion
        
        We presented a brain-inspired SNN architecture with adaptive plasticity for 
        multi-modal learning. Our approach achieves competitive performance while 
        maintaining biological realism and energy efficiency. Future work will explore 
        applications to larger-scale datasets and real-world neuromorphic hardware.
        
        The key contributions of this work are:
        1. Novel STDP-based plasticity mechanism for SNNs
        2. Multi-modal attention architecture inspired by cortical processing
        3. Comprehensive evaluation on standard benchmarks
        4. Demonstration of energy efficiency benefits
        5. Analysis of biological plausibility vs performance trade-offs
        '''
    }


def create_test_metadata() -> Dict[str, Any]:
    """创建测试元数据"""
    return {
        'paperId': 'test_brain_snn_2024',
        'authors': ['Dr. Neural Zhang', 'Prof. Spike Wang', 'Dr. Plasticity Li'],
        'venue': 'NeurIPS',
        'year': 2024,
        'doi': '10.1000/test.2024.brain.snn'
    }


def test_workflow_extraction():
    """测试工作流提取功能"""
    print("🧪 测试论文工作流提取功能")
    print("=" * 60)
    
    # 创建测试数据
    paper_data = create_test_paper_data()
    metadata = create_test_metadata()
    
    print(f"📋 测试论文: {paper_data['title']}")
    print(f"📊 论文长度: {len(paper_data['full_text'])} 字符")
    
    # 初始化提取器
    print("\\n🔧 初始化工作流提取器...")
    extractor = PaperWorkflowExtractor()
    
    # 提取工作流
    print("\\n🚀 开始提取工作流...")
    start_time = time.time()
    
    result = extractor.extract_workflow_from_paper(paper_data, metadata)
    
    extraction_time = time.time() - start_time
    print(f"⏱️ 提取耗时: {extraction_time:.2f}秒")
    
    # 检查结果
    if result.success:
        print("\\n✅ 工作流提取成功!")
        workflow = result.workflow
        
        print(f"📊 提取置信度: {result.confidence_score:.2f}")
        print(f"📋 论文ID: {workflow.paper_id}")
        print(f"📄 标题: {workflow.title}")
        print(f"👥 作者: {', '.join(workflow.authors)}")
        print(f"🏛️ 会议: {workflow.venue} {workflow.year}")
        
        print("\\n📊 提取结果统计:")
        print(f"  📚 数据集数量: {len(workflow.datasets)}")
        print(f"  🏗️ 网络架构数量: {len(workflow.network_architectures)}")
        print(f"  🔧 实现细节数量: {len(workflow.implementation_details)}")
        print(f"  🔬 研究方法数量: {len(workflow.research_methods)}")
        print(f"  ⚗️ 实验设置: {'有' if workflow.experiment_setup else '无'}")
        print(f"  📈 可视化规格数量: {len(workflow.visualizations)}")
        print(f"  🧠 脑启发元素数量: {len(workflow.brain_inspired_elements)}")
        print(f"  🎯 关键贡献数量: {len(workflow.contributions)}")
        
        # 详细信息展示
        if workflow.datasets:
            print("\\n📚 提取的数据集:")
            for i, dataset in enumerate(workflow.datasets, 1):
                print(f"  {i}. {dataset.name} ({dataset.type.value})")
                if dataset.description:
                    print(f"     描述: {dataset.description[:100]}...")
        
        if workflow.network_architectures:
            print("\\n🏗️ 提取的网络架构:")
            for i, arch in enumerate(workflow.network_architectures, 1):
                print(f"  {i}. {arch.name} ({arch.type.value})")
                if arch.brain_inspiration:
                    print(f"     脑启发: {arch.brain_inspiration}")
                if arch.innovations:
                    print(f"     创新点: {', '.join(arch.innovations[:3])}...")
        
        if workflow.implementation_details:
            print("\\n🔧 实现细节:")
            for i, impl in enumerate(workflow.implementation_details, 1):
                print(f"  {i}. 框架: {impl.framework.value}")
                if impl.version:
                    print(f"     版本: {impl.version}")
                if impl.hardware_requirements:
                    print(f"     硬件: {', '.join(impl.hardware_requirements)}")
        
        if workflow.brain_inspired_elements:
            print(f"\\n🧠 脑启发元素: {', '.join(workflow.brain_inspired_elements)}")
        
        if workflow.contributions:
            print("\\n🎯 关键贡献:")
            for i, contribution in enumerate(workflow.contributions[:5], 1):
                print(f"  {i}. {contribution}")
        
        # 保存结果
        output_path = "test_workflow_extraction_result.json"
        extractor.save_workflow(workflow, output_path)
        print(f"\\n💾 结果已保存到: {output_path}")
        
        return workflow
        
    else:
        print("\\n❌ 工作流提取失败!")
        print(f"错误信息: {result.errors}")
        return None


def test_workflow_analysis(workflow: PaperWorkflow):
    """测试工作流分析功能"""
    print("\\n" + "=" * 60)
    print("🔍 测试工作流分析功能")
    print("=" * 60)
    
    if not workflow:
        print("❌ 没有有效的工作流数据，跳过分析测试")
        return
    
    # 初始化分析器
    print("🔧 初始化工作流分析器...")
    analyzer = WorkflowAnalyzer()
    
    # 进行分析
    print("🚀 开始分析工作流...")
    start_time = time.time()
    
    analysis = analyzer.analyze_workflow(workflow)
    
    analysis_time = time.time() - start_time
    print(f"⏱️ 分析耗时: {analysis_time:.2f}秒")
    
    # 展示分析结果
    print("\\n📊 工作流分析结果:")
    
    # 工作流摘要
    summary = analysis.get('workflow_summary', {})
    print(f"\\n📋 工作流摘要:")
    print(f"  🔬 研究领域: {summary.get('research_area', 'Unknown')}")
    print(f"  📈 复杂度等级: {summary.get('complexity_level', 'Unknown')}")
    print(f"  🔑 关键技术: {', '.join(summary.get('key_technologies', [])[:5])}")
    print(f"  💻 计算需求: {summary.get('computational_demands', 'Unknown')}")
    
    # 技术分析
    tech_analysis = analysis.get('technical_analysis', {})
    if 'framework_analysis' in tech_analysis:
        framework_info = tech_analysis['framework_analysis']
        print(f"\\n🔧 框架分析:")
        print(f"  使用框架: {', '.join(framework_info.get('frameworks_used', []))}")
        
        for fw, assessment in framework_info.get('compatibility_assessment', {}).items():
            print(f"  {fw} 优势: {', '.join(assessment.get('strengths', [])[:3])}")
    
    # 实验设计分析
    exp_analysis = analysis.get('experimental_design', {})
    print(f"\\n⚗️ 实验设计分析:")
    print(f"  设计质量: {exp_analysis.get('design_quality', 'Unknown')}")
    if exp_analysis.get('strength_indicators'):
        print(f"  优势指标: {', '.join(exp_analysis['strength_indicators'][:3])}")
    if exp_analysis.get('missing_elements'):
        print(f"  缺失元素: {', '.join(exp_analysis['missing_elements'][:3])}")
    
    # 改进建议
    improvements = analysis.get('potential_improvements', [])
    if improvements:
        print(f"\\n💡 潜在改进建议:")
        for i, improvement in enumerate(improvements[:3], 1):
            print(f"  {i}. [{improvement.get('category', 'General')}] {improvement.get('suggestion', '')}")
            print(f"     优先级: {improvement.get('priority', 'Medium')}")
    
    # 复现指南
    replication = analysis.get('replication_guide', {})
    if replication:
        print(f"\\n📖 复现指南摘要:")
        env_steps = replication.get('environment_setup', [])
        if env_steps:
            print(f"  环境设置步骤: {len(env_steps)} 步")
        
        data_steps = replication.get('data_preparation', [])
        if data_steps:
            print(f"  数据准备步骤: {len(data_steps)} 步")
        
        impl_steps = replication.get('implementation_steps', [])
        if impl_steps:
            print(f"  实现步骤: {len(impl_steps)} 步")
    
    # 相关模板
    templates = analysis.get('related_templates', [])
    if templates:
        print(f"\\n🔗 相关工作流模板: {', '.join(templates)}")
    
    # 保存分析结果
    analysis_output_path = "test_workflow_analysis_result.json"
    with open(analysis_output_path, 'w', encoding='utf-8') as f:
        json.dump(analysis, f, indent=2, ensure_ascii=False)
    print(f"\\n💾 分析结果已保存到: {analysis_output_path}")
    
    return analysis


def run_comprehensive_test():
    """运行综合测试"""
    print("🧠 Brain AutoResearch Agent - 论文工作流提取系统综合测试")
    print("=" * 80)
    
    # 测试1: 工作流提取
    workflow = test_workflow_extraction()
    
    # 测试2: 工作流分析
    if workflow:
        analysis = test_workflow_analysis(workflow)
    
    print("\\n" + "=" * 80)
    print("🎯 测试总结")
    print("=" * 80)
    
    if workflow:
        print("✅ 工作流提取测试: 成功")
        print(f"   - 提取置信度: {workflow.extraction_confidence:.2f}")
        print(f"   - 数据集数量: {len(workflow.datasets)}")
        print(f"   - 架构数量: {len(workflow.network_architectures)}")
        print(f"   - 脑启发元素: {len(workflow.brain_inspired_elements)}")
        
        if 'analysis' in locals():
            print("✅ 工作流分析测试: 成功")
            summary = analysis.get('workflow_summary', {})
            print(f"   - 研究领域: {summary.get('research_area', 'Unknown')}")
            print(f"   - 复杂度: {summary.get('complexity_level', 'Unknown')}")
            
        print("\\n🎉 所有测试通过! 论文工作流提取系统运行正常。")
    else:
        print("❌ 工作流提取测试: 失败")
        print("\\n⚠️ 测试未完全通过，请检查系统配置。")


if __name__ == "__main__":
    run_comprehensive_test()
