#!/usr/bin/env python3
"""
Stage 3 诊断测试 - 检查测试框架问题
"""

import sys
import os
import traceback
from unittest.mock import Mock

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def diagnose_stage3_issues():
    """诊断Stage 3测试问题"""
    print("🔍 Stage 3 诊断测试开始...")
    
    issue_count = 0
    total_checks = 6
    
    # 1. 检查导入
    try:
        print("1. 测试导入...")
        from reasoning.enhanced_hypothesis_experiment_designer import EnhancedHypothesisExperimentDesigner
        from core.experiment_code_generator import EnhancedExperimentCodeGenerator, ExperimentSpecification
        from reasoning.enhanced_visualization_advisor import EnhancedVisualizationAdvisor
        from core.unified_api_client import UnifiedAPIClient
        from reasoning.data_models import (
            ResearchProblem, ExperimentPlan, ImplementationPlan,
            VisualizationPlan, CollaborationSession
        )
        print("✅ 所有导入成功")
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        traceback.print_exc()
        issue_count += 1
    
    # 2. 测试模拟客户端创建
    try:
        print("2. 创建模拟客户端...")
        mock_unified_client = Mock()
        mock_unified_client.generate_response = Mock(return_value="Mock response")
        mock_unified_client.generate_response_with_json = Mock(return_value=("Mock response", {"test": "data"}))
        mock_unified_client.available_models = {"text": ["deepseek-chat"], "vision": ["qwen-vl"]}
        print("✅ 模拟客户端创建成功")
    except Exception as e:
        print(f"❌ 模拟客户端创建失败: {e}")
        traceback.print_exc()
        issue_count += 1
    
    # 3. 测试实验设计器初始化
    try:
        print("3. 测试实验设计器初始化...")
        designer = EnhancedHypothesisExperimentDesigner(mock_unified_client)
        print("✅ 实验设计器初始化成功")
    except Exception as e:
        print(f"❌ 实验设计器初始化失败: {e}")
        traceback.print_exc()
        issue_count += 1
    
    # 4. 测试代码生成器初始化
    try:
        print("4. 测试代码生成器初始化...")
        generator = EnhancedExperimentCodeGenerator(mock_unified_client)
        print("✅ 代码生成器初始化成功")
    except Exception as e:
        print(f"❌ 代码生成器初始化失败: {e}")
        traceback.print_exc()
        issue_count += 1
    
    # 5. 测试可视化顾问初始化
    try:
        print("5. 测试可视化顾问初始化...")
        advisor = EnhancedVisualizationAdvisor(mock_unified_client)
        print("✅ 可视化顾问初始化成功")
    except Exception as e:
        print(f"❌ 可视化顾问初始化失败: {e}")
        traceback.print_exc()
        issue_count += 1
    
    # 6. 测试数据模型创建
    try:
        print("6. 测试数据模型创建...")
        research_problem = ResearchProblem(
            question="Test question",
            hypothesis=["Test hypothesis"],
            background={"test": "data"},
            domain="test"
        )
        print("✅ 数据模型创建成功")
    except Exception as e:
        print(f"❌ 数据模型创建失败: {e}")
        traceback.print_exc()
        issue_count += 1
    
    # 总结
    print(f"\n📊 诊断结果: {total_checks - issue_count}/{total_checks} 检查通过")
    
    if issue_count == 0:
        print("🎉 所有诊断检查通过！Stage 3组件正常")
        return True
    else:
        print(f"❌ 发现 {issue_count} 个问题，需要修复")
        return False

if __name__ == "__main__":
    success = diagnose_stage3_issues()
    sys.exit(0 if success else 1)
