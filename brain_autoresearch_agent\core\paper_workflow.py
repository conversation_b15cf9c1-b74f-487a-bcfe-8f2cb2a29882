"""
论文工作流提取器 - 从论文中提取数据集、网络结构、平台工具、研究方法等信息
"""

import re
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

from .unified_api_client import UnifiedAPIClient


@dataclass
class PaperWorkflow:
    """论文工作流数据结构"""
    title: str
    abstract: str
    datasets: List[str]
    network_architectures: List[str]
    platforms_tools: List[str]
    research_methods: List[str]
    evaluation_metrics: List[str]
    brain_inspiration: List[str]
    ai_techniques: List[str]


class PaperWorkflowExtractor:
    """
    论文工作流提取器
    从论文文本中提取结构化的研究工作流信息
    """
    
    def __init__(self, unified_client: Optional[UnifiedAPIClient] = None):
        """
        初始化提取器
        
        Args:
            unified_client: 统一API客户端，如果不提供则使用默认配置
        """
        self.unified_client = unified_client or UnifiedAPIClient()
        
    def extract_workflow(self, paper_text: str, paper_title: str = "") -> PaperWorkflow:
        """
        从论文文本中提取工作流信息
        
        Args:
            paper_text: 论文全文或摘要
            paper_title: 论文标题
            
        Returns:
            提取的工作流信息
        """
        print(f"🔍 正在提取论文工作流: {paper_title[:50]}...")
        
        # 构建提取提示词
        system_message = self._get_extraction_system_message()
        user_prompt = self._build_extraction_prompt(paper_text, paper_title)
        
        try:
            # 调用统一API客户端进行提取
            response = self.unified_client.get_text_response(
                prompt=user_prompt,
                system_message=system_message,
                model_type="chat",
                temperature=0.1,  # 使用较低温度以获得更稳定的结构化输出
                print_debug=False
            )
            
            if not response.success:
                raise Exception(f"API调用失败: {response.error_message}")
                
            print("✅ API响应获取成功")
            
            # 解析API响应
            extracted_data = self.unified_client.extract_json(response.content)
            
            if extracted_data is None:
                print("⚠️ JSON解析失败，使用后备文本解析")
                # 如果JSON解析失败，尝试文本解析
                extracted_data = self._fallback_text_parsing(response.content)
            else:
                print("✅ JSON解析成功")
            
        except Exception as e:
            print(f"❌ API调用失败: {e}")
            print("🔄 使用后备文本解析方法")
            extracted_data = self._fallback_text_parsing(paper_text)
        
        # 构建PaperWorkflow对象
        return self._build_workflow_object(extracted_data, paper_title, paper_text)
    
    def _get_extraction_system_message(self) -> str:
        """Get system message for extraction task"""
        return """You are an expert research assistant specialized in analyzing academic papers in brain-inspired artificial intelligence and machine learning.

Your task is to extract comprehensive structured information from research papers with high precision and completeness. Focus on:

1. **Datasets**: Extract ALL datasets mentioned, including:
   - Training datasets (e.g., ImageNet, CIFAR-10, MNIST)
   - Evaluation/test datasets 
   - Benchmark datasets
   - Custom or synthetic datasets

2. **Network Architectures**: Identify ALL model architectures:
   - Specific neural network types (CNN, RNN, Transformer, SNN)
   - Architecture names (ResNet, VGG, BERT, Vision Transformer)
   - Novel architectures proposed
   - Baseline/comparison models

3. **Platforms/Tools/Frameworks**: Extract ALL technical infrastructure:
   - Software frameworks (PyTorch, TensorFlow, Keras, Brian2)
   - Hardware platforms (GPU, TPU, neuromorphic chips, Loihi)
   - Simulation tools and development environments

4. **Research Methods**: Identify ALL experimental approaches:
   - Training procedures and algorithms
   - Optimization methods (SGD, Adam, STDP)
   - Experimental design principles

5. **Evaluation Metrics**: Extract ALL performance measures:
   - Accuracy metrics (accuracy, precision, recall, F1-score)
   - Efficiency metrics (energy consumption, latency)
   - Task-specific metrics

6. **Brain-Inspired Elements**: Identify ALL biological concepts:
   - Neuroscience principles (spike-timing, plasticity, inhibition)
   - Brain regions and structures (cortex, visual cortex)
   - Neural mechanisms (attention, memory, learning rules)

7. **AI Techniques**: Extract ALL computational methods:
   - Machine learning algorithms
   - Deep learning techniques
   - Novel AI approaches

**Critical Instructions:**
- Be exhaustive and precise in extraction
- Preserve original terminology exactly as written
- Do NOT invent information not present in the text
- Return results in valid JSON format only

Response format:
{
    "datasets": ["exact_dataset_name_1", "exact_dataset_name_2"],
    "network_architectures": ["exact_architecture_1", "exact_architecture_2"],
    "platforms_tools": ["exact_tool_1", "exact_tool_2"],
    "research_methods": ["exact_method_1", "exact_method_2"],
    "evaluation_metrics": ["exact_metric_1", "exact_metric_2"],
    "brain_inspiration": ["exact_concept_1", "exact_concept_2"],
    "ai_techniques": ["exact_technique_1", "exact_technique_2"]
}"""
    
    def _build_extraction_prompt(self, paper_text: str, paper_title: str) -> str:
        """Build extraction prompt for a paper

        Args:
            paper_text: The paper text content
            paper_title: The paper title
            
        Returns:
            Extraction prompt string
        """
        prompt = f"""Please analyze the following research paper and extract the structured information.

Paper Title: {paper_title}

Paper Content:
{paper_text[:4000]}  # Limit text length to avoid token limits

Please extract and categorize the following information:
1. Datasets: Any datasets mentioned for training, evaluation, or comparison
2. Network Architectures: Neural network models, architectures, or structures
3. Platforms/Tools: Software frameworks, libraries, hardware platforms
4. Research Methods: Experimental methodologies, approaches, techniques
5. Evaluation Metrics: Performance measures, evaluation criteria
6. Brain Inspiration: Biological concepts, neuroscience principles, brain mechanisms
7. AI Techniques: Machine learning algorithms, AI methods, computational approaches

Provide the response in the JSON format specified in the system message."""
        
        return prompt
    
    def _fallback_text_parsing(self, response: str) -> Dict[str, List[str]]:
        """
        当JSON解析失败时的后备文本解析方法
        """
        # 简单的关键词匹配方法
        categories = {
            "datasets": ["dataset", "data", "benchmark", "corpus"],
            "network_architectures": ["network", "model", "architecture", "CNN", "RNN", "transformer"],
            "platforms_tools": ["framework", "library", "platform", "tool", "software"],
            "research_methods": ["method", "approach", "technique", "algorithm"],
            "evaluation_metrics": ["accuracy", "precision", "recall", "F1", "metric"],
            "brain_inspiration": ["brain", "neural", "neuron", "synapse", "cortex", "biological"],
            "ai_techniques": ["learning", "training", "optimization", "classification", "regression"]
        }
        
        result = {category: [] for category in categories.keys()}
        
        # 简单的文本匹配（这是一个基础实现，可以进一步优化）
        response_lower = response.lower()
        words = re.findall(r'\b\w+\b', response_lower)
        
        for category, keywords in categories.items():
            for keyword in keywords:
                if keyword in response_lower:
                    # 尝试找到包含关键词的完整术语
                    matches = re.findall(rf'\b\w*{keyword}\w*\b', response_lower)
                    result[category].extend(matches[:3])  # 限制每个类别的结果数量
        
        return result
    
    def _build_workflow_object(self, extracted_data: Dict, paper_title: str, paper_text: str) -> PaperWorkflow:
        """
        构建PaperWorkflow对象
        """
        # 提取摘要（取前500个字符作为简化处理）
        abstract = paper_text[:500] + "..." if len(paper_text) > 500 else paper_text
        
        return PaperWorkflow(
            title=paper_title,
            abstract=abstract,
            datasets=extracted_data.get("datasets", []),
            network_architectures=extracted_data.get("network_architectures", []),
            platforms_tools=extracted_data.get("platforms_tools", []),
            research_methods=extracted_data.get("research_methods", []),
            evaluation_metrics=extracted_data.get("evaluation_metrics", []),
            brain_inspiration=extracted_data.get("brain_inspiration", []),
            ai_techniques=extracted_data.get("ai_techniques", [])
        )
    
    def extract_from_semantic_scholar(self, paper_id: str) -> Optional[PaperWorkflow]:
        """
        从Semantic Scholar API获取论文并提取工作流
        
        Args:
            paper_id: Semantic Scholar的论文ID或搜索查询
            
        Returns:
            提取的工作流信息，如果失败则返回None
        """
        try:
            from .semantic_scholar_tool import SemanticScholarTool
            
            print(f"🔍 从Semantic Scholar搜索: {paper_id}")
            
            # 创建搜索工具
            search_tool = SemanticScholarTool(max_results=1)
            
            # 搜索论文
            papers = search_tool.search_for_papers(paper_id)
            
            if not papers or len(papers) == 0:
                print("❌ 未找到相关论文")
                return None
            
            paper = papers[0]
            paper_title = paper.get("title", "")
            paper_abstract = paper.get("abstract", "")
            
            print(f"✅ 找到论文: {paper_title}")
            
            # 提取工作流
            workflow = self.extract_workflow(paper_abstract, paper_title)
            
            # 添加额外的元数据
            if hasattr(workflow, 'metadata'):
                workflow.metadata = {
                    'source': 'semantic_scholar',
                    'paper_id': paper.get('paperId', ''),
                    'citations': paper.get('citationCount', 0),
                    'year': paper.get('year', ''),
                    'venue': paper.get('venue', '')
                }
            
            return workflow
            
        except ImportError:
            print("❌ Semantic Scholar工具不可用")
            return None
        except Exception as e:
            print(f"❌ Semantic Scholar提取失败: {e}")
            return None
    
    def batch_extract(self, papers: List[Dict[str, str]]) -> List[PaperWorkflow]:
        """
        批量提取多篇论文的工作流信息
        
        Args:
            papers: 论文列表，每个元素包含title和content字段
            
        Returns:
            提取的工作流信息列表
        """
        workflows = []
        for paper in papers:
            try:
                workflow = self.extract_workflow(
                    paper_text=paper.get("content", ""),
                    paper_title=paper.get("title", "")
                )
                workflows.append(workflow)
            except Exception as e:
                print(f"Error extracting workflow from paper '{paper.get('title', 'Unknown')}': {e}")
                continue
        
        return workflows
    
    def extract_research_workflow(self, research_topic: str) -> Optional[Dict[str, Any]]:
        """
        基于研究主题提取研究工作流程
        
        Args:
            research_topic: 研究主题
            
        Returns:
            Dict[str, Any]: 研究工作流程信息
        """
        try:
            workflow_prompt = f"""
            Analyze the research topic "{research_topic}" and extract a research workflow.
            
            Please provide:
            1. Research objectives and goals
            2. Key methodological approaches
            3. Experimental design considerations
            4. Expected challenges and solutions
            5. Evaluation metrics and criteria
            6. Related work areas to investigate
            
            Format as structured information about the research workflow.
            """
            
            response = self.llm_client.get_response(workflow_prompt)
            
            if response:
                workflow_text = response[0] if isinstance(response, tuple) else response
                
                # 构建结构化的工作流程信息
                workflow_data = {
                    'research_topic': research_topic,
                    'workflow_analysis': workflow_text,
                    'extracted_components': {
                        'objectives': self._extract_objectives(workflow_text),
                        'methodology': self._extract_methodology(workflow_text),
                        'evaluation': self._extract_evaluation(workflow_text)
                    },
                    'extraction_timestamp': datetime.now().isoformat()
                }
                
                return workflow_data
            
            return None
            
        except Exception as e:
            print(f"Error extracting research workflow: {e}")
            return None
    
    def _extract_objectives(self, workflow_text: str) -> List[str]:
        """从工作流程文本中提取研究目标"""
        # 简化实现：寻找目标相关的关键词
        objectives = []
        lines = workflow_text.lower().split('\n')
        
        for line in lines:
            if any(keyword in line for keyword in ['objective', 'goal', 'aim', 'purpose']):
                objectives.append(line.strip())
        
        return objectives[:5]  # 返回前5个目标
    
    def _extract_methodology(self, workflow_text: str) -> List[str]:
        """从工作流程文本中提取方法论"""
        methodology = []
        lines = workflow_text.lower().split('\n')
        
        for line in lines:
            if any(keyword in line for keyword in ['method', 'approach', 'technique', 'algorithm']):
                methodology.append(line.strip())
        
        return methodology[:5]  # 返回前5个方法
    
    def _extract_evaluation(self, workflow_text: str) -> List[str]:
        """从工作流程文本中提取评估方法"""
        evaluation = []
        lines = workflow_text.lower().split('\n')
        
        for line in lines:
            if any(keyword in line for keyword in ['evaluation', 'metric', 'measure', 'assessment']):
                evaluation.append(line.strip())
        
        return evaluation[:5]  # 返回前5个评估方法
