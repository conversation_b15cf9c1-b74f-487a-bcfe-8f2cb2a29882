#!/usr/bin/env python3
"""
导入检查测试 - 验证所有Stage 3组件是否可以正确导入
"""

import sys
import traceback

def test_import(module_name, description):
    """测试单个模块导入"""
    try:
        exec(f"import {module_name}")
        print(f"✅ {description}: {module_name}")
        return True
    except Exception as e:
        print(f"❌ {description}: {module_name} - 错误: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔍 开始Stage 3导入测试...")
    
    import_tests = [
        ("core.unified_api_client", "统一API客户端"),
        ("reasoning.data_models", "数据模型"),
        ("reasoning.enhanced_multi_agent_collaborator", "增强多专家协作"),
        ("reasoning.enhanced_hypothesis_experiment_designer", "增强实验设计"),
        ("core.experiment_code_generator", "实验代码生成"),
        ("reasoning.enhanced_visualization_advisor", "增强可视化顾问")
    ]
    
    passed = 0
    total = len(import_tests)
    
    for module, desc in import_tests:
        if test_import(module, desc):
            passed += 1
    
    print(f"\n📊 导入测试结果: {passed}/{total} 成功")
    
    if passed == total:
        print("🎉 所有导入测试通过！Stage 3组件准备就绪")
        return True
    else:
        print("⚠️  存在导入问题，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
