#!/usr/bin/env python3
"""
测试研究日志系统
"""

import os
import sys
import unittest
import tempfile
import json
import shutil

# 导入研究日志系统
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from core.research_logger import ResearchLogger

class TestResearchLogger(unittest.TestCase):
    """测试研究日志系统"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_dir = tempfile.mkdtemp()
        self.research_topic = "Brain-Inspired Neural Networks"
        self.logger = ResearchLogger(self.test_dir, self.research_topic)
    
    def tearDown(self):
        """清理测试环境"""
        shutil.rmtree(self.test_dir)
    
    def test_init(self):
        """测试初始化"""
        # 检查日志文件是否创建
        md_path = self.logger.get_log_path()
        self.assertTrue(os.path.exists(md_path))
        
        # 检查JSON文件是否创建
        json_path = os.path.join(self.test_dir, f"research_log_{self.logger.timestamp}.json")
        self.assertTrue(os.path.exists(json_path))
        
        # 检查日志数据结构
        log_data = self.logger.get_log_data()
        self.assertEqual(log_data['research_topic'], self.research_topic)
        self.assertIn('literature', log_data)
        self.assertIn('reasoning', log_data)
        self.assertIn('experiments', log_data)
        self.assertIn('paper', log_data)
    
    def test_log_literature_search(self):
        """测试记录文献搜索结果"""
        query = "Neural Networks"
        papers = [
            {
                'title': 'Paper 1',
                'authors': [{'name': 'Author 1'}, {'name': 'Author 2'}],
                'year': 2023
            },
            {
                'title': 'Paper 2',
                'authors': [{'name': 'Author 3'}],
                'year': 2022
            }
        ]
        
        self.logger.log_literature_search(query, papers)
        
        # 检查日志数据
        log_data = self.logger.get_log_data()
        self.assertEqual(log_data['literature']['papers'], papers)
        self.assertEqual(log_data['literature']['query'], query)
        
        # 检查MD文件
        md_path = self.logger.get_log_path()
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
            self.assertIn('文献搜索结果', md_content)
            self.assertIn(query, md_content)
            self.assertIn('Paper 1', md_content)
            self.assertIn('Paper 2', md_content)
    
    def test_log_workflow_extraction(self):
        """测试记录工作流提取结果"""
        workflows = {
            'Paper 1': {
                'datasets': ['MNIST', 'CIFAR-10'],
                'methods': ['CNN', 'RNN'],
                'metrics': ['Accuracy', 'F1']
            },
            'Paper 2': {
                'datasets': ['ImageNet'],
                'methods': ['Transformer'],
                'metrics': ['BLEU']
            }
        }
        
        self.logger.log_workflow_extraction(workflows)
        
        # 检查日志数据
        log_data = self.logger.get_log_data()
        self.assertEqual(log_data['literature']['workflows'], workflows)
        
        # 检查MD文件
        md_path = self.logger.get_log_path()
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
            self.assertIn('工作流提取结果', md_content)
            self.assertIn('MNIST', md_content)
            self.assertIn('CNN', md_content)
            self.assertIn('Accuracy', md_content)
    
    def test_log_experiment_design(self):
        """测试记录实验设计"""
        experiment_design = {
            'consensus_experiment_plan': {
                'datasets': ['MNIST', 'CIFAR-10'],
                'methods': ['CNN', 'RNN'],
                'metrics': ['Accuracy', 'F1'],
                'baseline_approaches': ['Traditional ML', 'Statistical Methods']
            },
            'feasibility_assessment': {
                'score': 0.85,
                'is_feasible': True,
                'assessment': '实验设计可行'
            }
        }
        
        self.logger.log_experiment_design(experiment_design)
        
        # 检查日志数据
        log_data = self.logger.get_log_data()
        self.assertEqual(log_data['experiments']['design'], experiment_design)
        
        # 检查MD文件
        md_path = self.logger.get_log_path()
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
            self.assertIn('实验设计', md_content)
            self.assertIn('MNIST', md_content)
            self.assertIn('CNN', md_content)
            self.assertIn('Accuracy', md_content)
            self.assertIn('可行性评估', md_content)
    
    def test_log_paper_generation(self):
        """测试记录论文生成"""
        paper_path = os.path.join(self.test_dir, "paper.tex")
        paper_info = {
            'title': 'Brain-Inspired Neural Networks',
            'abstract': 'This paper presents a novel approach...',
            'sections': [
                {'title': 'Introduction'},
                {'title': 'Related Work'},
                {'title': 'Methodology'}
            ]
        }
        
        self.logger.log_paper_generation(paper_path, paper_info)
        
        # 检查日志数据
        log_data = self.logger.get_log_data()
        self.assertEqual(log_data['paper']['path'], paper_path)
        self.assertEqual(log_data['paper']['info'], paper_info)
        self.assertEqual(log_data['paper']['status'], 'generated')
        
        # 检查MD文件
        md_path = self.logger.get_log_path()
        with open(md_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
            self.assertIn('论文生成', md_content)
            self.assertIn('Brain-Inspired Neural Networks', md_content)
            self.assertIn('This paper presents', md_content)

if __name__ == '__main__':
    unittest.main() 