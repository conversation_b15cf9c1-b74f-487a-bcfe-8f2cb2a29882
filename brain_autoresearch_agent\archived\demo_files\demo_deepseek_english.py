"""
DeepSeek API Complete Paper Generation Demo (English Version)
Optimized for DeepSeek's performance using English prompts
"""

import os
import sys
import asyncio
from datetime import datetime

# Add project path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from paper_generation.unified_paper_workflow import (
    UnifiedPaperGenerationWorkflow, 
    UnifiedWorkflowConfig
)
from paper_generation.enhanced_brain_paper_writer import PaperGenerationConfig


def setup_deepseek_environment():
    """Setup DeepSeek environment with English interface"""
    print("🔧 Setting up DeepSeek environment...")
    
    # Set API key
    api_key = "sk-1b1d72e2e10643029de548b655e1f93e"
    os.environ["DEEPSEEK_API_KEY"] = api_key
    os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
    
    # Disable mock mode for real API usage
    os.environ["MOCK_MODE"] = "false"
    os.environ["ENABLE_MOCK_DATA"] = "false"
    
    print(f"✅ DeepSeek environment configured")
    print(f"🔑 API key configured")
    print(f"🌐 API base URL: https://api.deepseek.com")
    print(f"🚫 Mock mode disabled")
    
    return api_key


async def demo_english_deepseek_generation():
    """Complete paper generation demo optimized for DeepSeek"""
    print("=" * 80)
    print("🧠 DeepSeek API Complete Paper Generation Demo (English)")
    print("=" * 80)
    
    # 1. Setup environment
    setup_deepseek_environment()
    
    # 2. Define research topic and requirements in English
    research_topic = "Adaptive Deep Learning Algorithms Inspired by Neural Plasticity Mechanisms"
    research_requirements = {
        "target_conference": "ICML 2024",
        "paper_length": "8 pages",
        "focus_areas": [
            "synaptic plasticity modeling in artificial networks",
            "biologically-plausible learning algorithms", 
            "energy-efficient neural architectures",
            "continual learning without catastrophic forgetting",
            "experimental validation on benchmark datasets"
        ],
        "innovation_requirements": [
            "develop novel plasticity-inspired learning mechanisms",
            "design computationally efficient adaptive architectures",
            "implement online learning algorithms with biological constraints",
            "provide comprehensive theoretical analysis and empirical validation"
        ],
        "technical_requirements": [
            "support for online learning and adaptation",
            "maintain biological plausibility in computational models",
            "achieve computational efficiency improvements",
            "demonstrate scalability to large-scale problems"
        ],
        "evaluation_criteria": [
            "learning efficiency compared to standard deep learning",
            "energy consumption analysis",
            "performance on continual learning benchmarks",
            "biological realism assessment"
        ]
    }
    
    print(f"📋 Research Topic: {research_topic}")
    print(f"🎯 Target Conference: {research_requirements['target_conference']}")
    print(f"📄 Paper Length: {research_requirements['paper_length']}")
    print(f"🔬 Focus Areas: {len(research_requirements['focus_areas'])} key areas")
    print(f"💡 Innovation Requirements: {len(research_requirements['innovation_requirements'])} items")
    
    # 3. Create DeepSeek-optimized configuration
    print(f"\\n⚙️ Creating DeepSeek-optimized configuration...")
    
    paper_config = PaperGenerationConfig(
        target_venue="ICML",
        paper_type="research",
        max_review_iterations=2,  # Reduced for API efficiency
        quality_threshold=7.5,   # High quality requirement
        enable_auto_revision=True,
        enable_multi_expert_review=True,
        latex_output=True,
        language="english"  # Explicit English specification
    )
    
    config = UnifiedWorkflowConfig(
        use_advanced_writer=True,
        paper_generation_config=paper_config,
        output_formats=["markdown", "latex", "json"],
        output_directory=f"output/deepseek_english_demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        enable_workflow_extraction=True,
        enable_integration_analysis=True
    )
    
    print(f"✅ Configuration created successfully")
    print(f"📊 Quality threshold: {paper_config.quality_threshold}")
    print(f"🔄 Max review iterations: {paper_config.max_review_iterations}")
    print(f"📁 Output directory: {config.output_directory}")
    
    # 4. Create DeepSeek LLM client
    print(f"\\n🤖 Creating DeepSeek LLM client...")
    
    llm_client = LLMClient(
        provider="deepseek",
        model="deepseek-chat",
        temperature=0.7,
        api_key=os.environ["DEEPSEEK_API_KEY"]
    )
    
    # Verify client status
    if llm_client.deepseek_mode:
        print(f"✅ DeepSeek client created successfully")
        print(f"🧠 Model: {llm_client.model}")
        print(f"🌡️ Temperature: {llm_client.temperature}")
    else:
        print(f"❌ DeepSeek client creation failed, will use mock mode")
        response = input("Continue anyway? [y/N]: ")
        if response.lower() not in ['y', 'yes']:
            return
    
    # 5. Test API connection with English prompt
    print(f"\\n🧪 Testing DeepSeek API connection...")
    try:
        test_response = llm_client.generate_response(
            "Briefly explain the core concepts of brain-inspired artificial intelligence in academic terms."
        )
        if test_response and len(test_response.strip()) > 20:
            print(f"✅ API connection test successful")
            print(f"📝 Test response: {test_response[:150]}...")
        else:
            print(f"⚠️ API response anomaly, may affect subsequent generation")
    except Exception as e:
        print(f"❌ API connection test failed: {e}")
        return
    
    # 6. Create and execute workflow
    print(f"\\n🚀 Creating unified paper generation workflow...")
    
    workflow = UnifiedPaperGenerationWorkflow(llm_client, config)
    
    print(f"🔄 Starting paper generation process...")
    print(f"⏱️ Estimated time: 5-10 minutes (depending on API response speed)")
    
    start_time = datetime.now()
    
    try:
        # Execute paper generation
        result = await workflow.generate_complete_paper(
            research_topic=research_topic,
            research_requirements=research_requirements,
            reference_papers=[]
        )
        
        generation_time = (datetime.now() - start_time).total_seconds()
        
        # 7. Display results
        print(f"\\n" + "=" * 60)
        print(f"📊 Paper Generation Results")
        print(f"=" * 60)
        
        if result.success:
            print(f"✅ Paper generation successful!")
            print(f"📁 Output directory: {config.output_directory}")
            print(f"📄 Generated formats: {', '.join(config.output_formats)}")
            print(f"⏱️ Total generation time: {generation_time:.2f} seconds")
            print(f"📊 Paper quality score: {result.paper_quality_score:.2f}/10")
            
            # Display output files
            print(f"\\n📂 Generated files:")
            total_size = 0
            for format_name, file_path in result.output_files.items():
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path) / 1024  # KB
                    total_size += file_size
                    print(f"  📄 {format_name}: {os.path.basename(file_path)} ({file_size:.1f} KB)")
                else:
                    print(f"  ❌ {format_name}: File not generated")
            
            print(f"\\n💾 Total file size: {total_size:.1f} KB")
            
            # Display paper core information
            if hasattr(result, 'paper_content') and result.paper_content:
                paper_content = result.paper_content
                print(f"\\n📋 Paper core information:")
                print(f"  📝 Title length: {len(paper_content.get('title', ''))}")
                print(f"  📄 Abstract length: {len(paper_content.get('abstract', ''))}")
                print(f"  📑 Number of sections: {len([k for k in paper_content.keys() if k not in ['title', 'abstract', 'keywords']])}")
                
                # Display abstract
                if 'abstract' in paper_content and paper_content['abstract']:
                    print(f"\\n📄 Paper Abstract:")
                    print("-" * 40)
                    abstract = paper_content['abstract']
                    print(abstract[:500] + "..." if len(abstract) > 500 else abstract)
                    print("-" * 40)
            
            # Display evaluation information
            if hasattr(result, 'evaluation_results') and result.evaluation_results:
                eval_results = result.evaluation_results
                print(f"\\n📊 Quality evaluation results:")
                for metric, score in eval_results.items():
                    if isinstance(score, (int, float)):
                        print(f"  {metric}: {score:.2f}/10")
                    else:
                        print(f"  {metric}: {score}")
            
            print(f"\\n🎉 DeepSeek API paper generation demo completed!")
            print(f"💡 You can view the generated paper files in {config.output_directory}")
            
        else:
            print(f"❌ Paper generation failed")
            error_msg = getattr(result, 'error_message', 'Unknown error')
            print(f"🔍 Error message: {error_msg}")
            print(f"⏱️ Execution time: {generation_time:.2f} seconds")
            
    except Exception as e:
        print(f"\\n💥 Exception occurred during generation:")
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        
        generation_time = (datetime.now() - start_time).total_seconds()
        print(f"⏱️ Execution time: {generation_time:.2f} seconds")


def main():
    """Main function"""
    print("🚀 Starting DeepSeek API Complete Paper Generation Demo...")
    
    # Check necessary dependencies
    try:
        import openai
        print("✅ OpenAI library available")
    except ImportError:
        print("❌ Missing OpenAI library, please run: pip install openai")
        return
    
    # Run async demo
    asyncio.run(demo_english_deepseek_generation())


if __name__ == "__main__":
    main()
