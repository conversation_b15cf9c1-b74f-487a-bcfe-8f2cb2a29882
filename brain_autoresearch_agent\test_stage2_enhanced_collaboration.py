"""
阶段2: 增强多专家协作推理系统测试
测试新的多Agent协作功能和增强推理工作流
"""

import sys
import os
import traceback
from typing import Dict, Any, List

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from core.unified_api_client import UnifiedAPIClient
from reasoning.enhanced_multi_agent_collaborator import EnhancedMultiAgentCollaborator
from reasoning.enhanced_reasoning_workflow import EnhancedExperimentReasoningWorkflow
from agents.agent_manager import AgentManager


class Stage2TestSuite:
    """阶段2测试套件"""
    
    def __init__(self):
        """初始化测试套件"""
        print("🧪 初始化阶段2增强协作系统测试")
        
        # 初始化核心组件
        self.unified_client = UnifiedAPIClient()
        self.collaborator = EnhancedMultiAgentCollaborator(self.unified_client)
        self.workflow = EnhancedExperimentReasoningWorkflow(self.unified_client)
        self.agent_manager = AgentManager(self.unified_client)
        
        # 测试结果
        self.test_results: Dict[str, bool] = {}
        self.test_details: Dict[str, Dict[str, Any]] = {}
        
        print("✅ 测试套件初始化完成")
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有阶段2测试"""
        print("\n🚀 开始阶段2测试")
        print("=" * 80)
        
        # 测试列表
        tests = [
            ("test_enhanced_collaborator_initialization", "增强协作器初始化测试"),
            ("test_expert_selection_mechanism", "专家选择机制测试"),
            ("test_collaboration_session_creation", "协作会话创建测试"),
            ("test_multi_round_discussion_simulation", "多轮讨论模拟测试"),
            ("test_consensus_scoring_algorithm", "共识评分算法测试"),
            ("test_enhanced_workflow_initialization", "增强工作流初始化测试"),
            ("test_collaborative_evaluation_stage", "协作评估阶段测试"),
            ("test_collaborative_design_stage", "协作设计阶段测试"),
            ("test_workflow_session_management", "工作流会话管理测试"),
            ("test_end_to_end_collaboration", "端到端协作测试")
        ]
        
        for test_method, test_name in tests:
            try:
                print(f"\n🧪 运行测试: {test_name}")
                print(f"   方法: {test_method}")
                
                # 执行测试
                result = getattr(self, test_method)()
                self.test_results[test_method] = result
                
                if result:
                    print(f"   ✅ 通过")
                else:
                    print(f"   ❌ 失败")
                    
            except Exception as e:
                print(f"   ❌ 异常: {e}")
                self.test_results[test_method] = False
                self.test_details[test_method] = {"error": str(e), "traceback": traceback.format_exc()}
        
        # 生成测试报告
        self._generate_test_report()
        
        return self.test_results
    
    def test_enhanced_collaborator_initialization(self) -> bool:
        """测试增强协作器初始化"""
        try:
            # 检查基本组件
            assert hasattr(self.collaborator, 'unified_client'), "缺少统一客户端"
            assert hasattr(self.collaborator, 'agent_manager'), "缺少代理管理器"
            assert hasattr(self.collaborator, 'collaboration_config'), "缺少协作配置"
            assert hasattr(self.collaborator, 'expert_capabilities'), "缺少专家能力映射"
            
            # 检查配置
            config = self.collaborator.collaboration_config
            assert config["max_rounds"] > 0, "最大轮数必须大于0"
            assert config["min_consensus_score"] > 0, "最小共识分数必须大于0"
            assert config["max_participants"] > 0, "最大参与者数量必须大于0"
            
            # 检查专家能力映射
            capabilities = self.collaborator.expert_capabilities
            assert len(capabilities) > 0, "专家能力映射不能为空"
            assert "ai_technology" in capabilities, "必须包含AI技术专家"
            assert "experiment_design" in capabilities, "必须包含实验设计专家"
            
            self.test_details["test_enhanced_collaborator_initialization"] = {
                "config_keys": list(config.keys()),
                "expert_count": len(capabilities),
                "experts": list(capabilities.keys())
            }
            
            return True
            
        except Exception as e:
            self.test_details["test_enhanced_collaborator_initialization"] = {"error": str(e)}
            return False
    
    def test_expert_selection_mechanism(self) -> bool:
        """测试专家选择机制"""
        try:
            # 测试数据
            topic = "深度学习神经网络优化"
            questions = ["如何优化模型训练效率", "如何设计更好的网络架构"]
            
            # 执行专家选择
            selected_experts = self.collaborator._select_optimal_experts(
                topic=topic,
                questions=questions,
                required=["ai_technology"]
            )
            
            # 验证选择结果
            assert len(selected_experts) > 0, "必须选择至少一个专家"
            assert "ai_technology" in selected_experts, "必须包含必需的专家"
            assert len(selected_experts) <= self.collaborator.collaboration_config["max_participants"], "不能超过最大参与者数量"
            
            # 测试不同主题的选择
            brain_topic = "大脑神经可塑性机制"
            brain_questions = ["神经元连接如何变化", "学习如何影响大脑结构"]
            
            brain_experts = self.collaborator._select_optimal_experts(
                topic=brain_topic,
                questions=brain_questions
            )
            
            # 验证神经科学专家被选中
            expected_neuroscience = any("neuroscience" in expert or "brain" in topic.lower() for expert in brain_experts)
            
            self.test_details["test_expert_selection_mechanism"] = {
                "selected_experts_ai": selected_experts,
                "selected_experts_brain": brain_experts,
                "total_available": len(self.agent_manager.agents)
            }
            
            return True
            
        except Exception as e:
            self.test_details["test_expert_selection_mechanism"] = {"error": str(e)}
            return False
    
    def test_collaboration_session_creation(self) -> bool:
        """测试协作会话创建"""
        try:
            research_topic = "基于注意力机制的神经网络改进"
            questions = [
                "注意力机制的计算复杂度问题",
                "如何提高注意力的解释性",
                "多头注意力的优化策略"
            ]
            
            # 创建协作会话
            session = self.collaborator.create_collaboration_session(
                research_topic=research_topic,
                specific_questions=questions,
                required_experts=["ai_technology"]
            )
            
            # 验证会话属性
            assert session.session_id is not None, "会话ID不能为空"
            assert session.research_topic == research_topic, "研究主题不匹配"
            assert len(session.participants) > 0, "参与者列表不能为空"
            assert "ai_technology" in session.participants, "必需专家未包含在参与者中"
            assert session.rounds == [], "初始轮次应为空"
            assert session.quality_score == 0.0, "初始质量分数应为0"
            
            # 验证会话ID格式
            assert session.session_id.startswith("collab_"), "会话ID格式错误"
            
            self.test_details["test_collaboration_session_creation"] = {
                "session_id": session.session_id,
                "participants": session.participants,
                "participant_count": len(session.participants)
            }
            
            return True
            
        except Exception as e:
            self.test_details["test_collaboration_session_creation"] = {"error": str(e)}
            return False
    
    def test_multi_round_discussion_simulation(self) -> bool:
        """测试多轮讨论模拟（不调用真实API）"""
        try:
            # 创建模拟会话
            research_topic = "神经网络压缩技术"
            questions = ["如何在保持性能的同时减少模型大小"]
            
            session = self.collaborator.create_collaboration_session(
                research_topic=research_topic,
                specific_questions=questions
            )
            
            # 模拟讨论结果而不进行真实API调用
            # 这里我们验证讨论框架的逻辑结构
            
            # 验证讨论提示构建
            initial_prompt = self.collaborator._build_initial_discussion_prompt(
                research_topic, questions
            )
            assert len(initial_prompt) > 0, "初始讨论提示不能为空"
            assert research_topic in initial_prompt, "讨论提示应包含研究主题"
            
            # 验证共识计算逻辑（使用模拟响应）
            from agents.base_agent import AgentResponse
            import time
            current_time = time.strftime('%Y-%m-%d %H:%M:%S')
            
            mock_responses = {
                "ai_technology": AgentResponse(
                    agent_type="ai_technology",
                    content="建议使用知识蒸馏技术进行模型压缩，这是当前最有效的方法",
                    confidence=0.85,
                    reasoning="基于大量文献调研和实验验证",
                    metadata={},
                    timestamp=current_time
                ),
                "data_analysis": AgentResponse(
                    agent_type="data_analysis",
                    content="需要关注压缩后的性能评估指标，建议使用多维度的评估框架",
                    confidence=0.78,
                    reasoning="从数据分析角度考虑",
                    metadata={},
                    timestamp=current_time
                )
            }
            
            consensus_score = self.collaborator._calculate_consensus_score(mock_responses)
            assert 0 <= consensus_score <= 1, "共识分数应在0-1之间"
            
            # 验证见解提取
            insights = self.collaborator._extract_key_insights(mock_responses)
            assert isinstance(insights, list), "见解应为列表格式"
            
            self.test_details["test_multi_round_discussion_simulation"] = {
                "prompt_length": len(initial_prompt),
                "consensus_score": consensus_score,
                "insights_count": len(insights),
                "mock_responses_count": len(mock_responses)
            }
            
            return True
            
        except Exception as e:
            self.test_details["test_multi_round_discussion_simulation"] = {"error": str(e)}
            return False
    
    def test_consensus_scoring_algorithm(self) -> bool:
        """测试共识评分算法"""
        try:
            from agents.base_agent import AgentResponse
            import time
            current_time = time.strftime('%Y-%m-%d %H:%M:%S')
            
            # 测试高共识情况
            high_consensus_responses = {
                "expert1": AgentResponse(
                    agent_type="expert1", 
                    content="使用深度学习方法效果最好", 
                    confidence=0.9, 
                    reasoning="分析",
                    metadata={},
                    timestamp=current_time
                ),
                "expert2": AgentResponse(
                    agent_type="expert2", 
                    content="深度学习确实是最佳选择", 
                    confidence=0.85, 
                    reasoning="分析",
                    metadata={},
                    timestamp=current_time
                ),
                "expert3": AgentResponse(
                    agent_type="expert3", 
                    content="推荐深度学习解决方案", 
                    confidence=0.88, 
                    reasoning="分析",
                    metadata={},
                    timestamp=current_time
                )
            }
            
            high_score = self.collaborator._calculate_consensus_score(high_consensus_responses)
            
            # 测试低共识情况
            low_consensus_responses = {
                "expert1": AgentResponse(
                    agent_type="expert1", 
                    content="应该使用传统机器学习", 
                    confidence=0.7, 
                    reasoning="分析",
                    metadata={},
                    timestamp=current_time
                ),
                "expert2": AgentResponse(
                    agent_type="expert2", 
                    content="深度学习更适合", 
                    confidence=0.8, 
                    reasoning="分析",
                    metadata={},
                    timestamp=current_time
                ),
                "expert3": AgentResponse(
                    agent_type="expert3", 
                    content="统计方法可能更好", 
                    confidence=0.6, 
                    reasoning="分析",
                    metadata={},
                    timestamp=current_time
                )
            }
            
            low_score = self.collaborator._calculate_consensus_score(low_consensus_responses)
            
            # 验证评分逻辑
            assert 0 <= high_score <= 1, "高共识分数应在0-1之间"
            assert 0 <= low_score <= 1, "低共识分数应在0-1之间"
            assert high_score >= low_score, "高共识分数应大于等于低共识分数"
            
            # 测试边界情况
            empty_responses = {}
            empty_score = self.collaborator._calculate_consensus_score(empty_responses)
            assert empty_score == 0.0, "空响应的共识分数应为0"
            
            single_response = {
                "expert1": AgentResponse(
                    agent_type="expert1", 
                    content="测试", 
                    confidence=0.5, 
                    reasoning="分析",
                    metadata={},
                    timestamp=current_time
                )
            }
            single_score = self.collaborator._calculate_consensus_score(single_response)
            assert single_score == 0.0, "单个响应的共识分数应为0"
            
            self.test_details["test_consensus_scoring_algorithm"] = {
                "high_consensus_score": high_score,
                "low_consensus_score": low_score,
                "empty_score": empty_score,
                "single_score": single_score
            }
            
            return True
            
        except Exception as e:
            self.test_details["test_consensus_scoring_algorithm"] = {"error": str(e)}
            return False
    
    def test_enhanced_workflow_initialization(self) -> bool:
        """测试增强工作流初始化"""
        try:
            # 检查核心组件
            assert hasattr(self.workflow, 'unified_client'), "缺少统一客户端"
            assert hasattr(self.workflow, 'collaborator'), "缺少协作器"
            assert hasattr(self.workflow, 'agent_manager'), "缺少代理管理器"
            assert hasattr(self.workflow, 'literature_manager'), "缺少文献管理器"
            
            # 检查工作流配置
            config = self.workflow.workflow_config
            assert config["enable_multi_agent_collaboration"] is True, "多专家协作应启用"
            assert config["quality_threshold"] > 0, "质量阈值应大于0"
            assert config["consensus_threshold"] > 0, "共识阈值应大于0"
            
            # 检查会话管理
            assert hasattr(self.workflow, 'current_session'), "缺少当前会话属性"
            assert hasattr(self.workflow, 'session_history'), "缺少会话历史属性"
            
            self.test_details["test_enhanced_workflow_initialization"] = {
                "collaboration_enabled": config["enable_multi_agent_collaboration"],
                "quality_threshold": config["quality_threshold"],
                "consensus_threshold": config["consensus_threshold"]
            }
            
            return True
            
        except Exception as e:
            self.test_details["test_enhanced_workflow_initialization"] = {"error": str(e)}
            return False
    
    def test_collaborative_evaluation_stage(self) -> bool:
        """测试协作评估阶段（不调用API）"""
        try:
            # 准备测试数据
            research_question = "如何提高神经网络的泛化能力"
            hypothesis = ["数据增强可以提高泛化", "正则化技术有助于泛化"]
            background = {"domain": "机器学习", "context": "泛化能力研究"}
            
            # 验证评估方法存在
            assert hasattr(self.workflow, '_run_collaborative_evaluation'), "缺少协作评估方法"
            
            # 验证会话创建
            session = self.workflow._create_new_session(research_question, hypothesis, background)
            assert session is not None, "会话创建失败"
            assert session.session_id is not None, "会话ID不能为空"
            assert session.status == "in_progress", "初始状态应为进行中"
            
            # 验证会话摘要生成
            summary = self.workflow.get_session_summary(session.session_id)
            # 由于会话不在历史中，摘要应为None
            assert summary is None, "新创建的会话不应有摘要"
            
            self.test_details["test_collaborative_evaluation_stage"] = {
                "session_id": session.session_id,
                "session_status": session.status,
                "research_question": session.research_problem.question
            }
            
            return True
            
        except Exception as e:
            self.test_details["test_collaborative_evaluation_stage"] = {"error": str(e)}
            return False
    
    def test_collaborative_design_stage(self) -> bool:
        """测试协作设计阶段"""
        try:
            # 验证设计方法存在
            assert hasattr(self.workflow, '_run_collaborative_experiment_design'), "缺少协作实验设计方法"
            assert hasattr(self.workflow, '_run_collaborative_implementation_planning'), "缺少协作实现规划方法"
            assert hasattr(self.workflow, '_run_collaborative_visualization_planning'), "缺少协作可视化规划方法"
            
            # 验证工作流报告生成
            research_question = "测试问题"
            hypothesis = ["测试假设"]
            background = {"domain": "测试"}
            
            session = self.workflow._create_new_session(research_question, hypothesis, background)
            report = self.workflow.generate_workflow_report(session)
            
            assert len(report) > 0, "报告不能为空"
            assert "增强多专家协作推理工作流报告" in report, "报告标题错误"
            assert session.session_id in report, "报告应包含会话ID"
            
            self.test_details["test_collaborative_design_stage"] = {
                "report_length": len(report),
                "has_session_info": session.session_id in report
            }
            
            return True
            
        except Exception as e:
            self.test_details["test_collaborative_design_stage"] = {"error": str(e)}
            return False
    
    def test_workflow_session_management(self) -> bool:
        """测试工作流会话管理"""
        try:
            # 测试会话列表功能
            recent_sessions = self.workflow.list_recent_sessions(limit=5)
            assert isinstance(recent_sessions, list), "最近会话应为列表"
            
            # 创建测试会话
            research_question = "测试会话管理"
            hypothesis = ["测试假设"]
            background = {"domain": "测试"}
            
            session = self.workflow._create_new_session(research_question, hypothesis, background)
            
            # 测试会话保存功能
            self.workflow._save_session(session)
            
            # 验证保存目录
            import os
            save_dir = self.workflow.workflow_config["save_directory"]
            assert os.path.exists(save_dir), "保存目录应存在"
            
            self.test_details["test_workflow_session_management"] = {
                "save_directory_exists": os.path.exists(save_dir),
                "recent_sessions_count": len(recent_sessions),
                "session_created": session.session_id is not None
            }
            
            return True
            
        except Exception as e:
            self.test_details["test_workflow_session_management"] = {"error": str(e)}
            return False
    
    def test_end_to_end_collaboration(self) -> bool:
        """测试端到端协作系统"""
        try:
            # 验证完整流程方法
            assert hasattr(self.workflow, 'run_enhanced_reasoning_flow'), "缺少增强推理流程方法"
            
            # 验证协作器和工作流的集成
            assert self.workflow.collaborator is not None, "工作流应包含协作器"
            assert self.workflow.agent_manager is not None, "工作流应包含代理管理器"
            assert self.workflow.literature_manager is not None, "工作流应包含文献管理器"
            
            # 验证组件间的一致性
            assert self.workflow.collaborator.agent_manager is not None, "协作器应包含代理管理器"
            
            # 验证便利函数
            from reasoning.enhanced_reasoning_workflow import create_enhanced_reasoning_workflow
            new_workflow = create_enhanced_reasoning_workflow(self.unified_client)
            assert new_workflow is not None, "便利函数应能创建工作流实例"
            
            self.test_details["test_end_to_end_collaboration"] = {
                "workflow_has_collaborator": self.workflow.collaborator is not None,
                "workflow_has_agent_manager": self.workflow.agent_manager is not None,
                "workflow_has_literature_manager": self.workflow.literature_manager is not None,
                "convenience_function_works": new_workflow is not None
            }
            
            return True
            
        except Exception as e:
            self.test_details["test_end_to_end_collaboration"] = {"error": str(e)}
            return False
    
    def _generate_test_report(self) -> None:
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("📊 阶段2测试报告")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n📈 测试统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过数: {passed_tests}")
        print(f"   失败数: {failed_tests}")
        print(f"   成功率: {success_rate:.1f}%")
        
        # 详细结果
        print(f"\n📋 详细结果:")
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            
            if not result and test_name in self.test_details:
                error_info = self.test_details[test_name]
                if "error" in error_info:
                    print(f"      错误: {error_info['error']}")
        
        # 重要发现
        print(f"\n💡 重要发现:")
        if success_rate == 100:
            print("   ✅ 所有测试通过！阶段2增强协作系统功能正常")
        elif success_rate >= 80:
            print("   ⚠️ 大部分测试通过，少数问题需要修复")
        else:
            print("   ❌ 多个测试失败，需要重点检查系统设计")
        
        # 阶段2特色功能验证
        collaboration_tests = [k for k in self.test_results.keys() if 'collaboration' in k or 'consensus' in k]
        collaboration_pass_rate = sum(1 for test in collaboration_tests if self.test_results[test]) / len(collaboration_tests) * 100 if collaboration_tests else 0
        
        print(f"\n🤖 多专家协作功能:")
        print(f"   协作相关测试通过率: {collaboration_pass_rate:.1f}%")
        
        if collaboration_pass_rate == 100:
            print("   ✅ 多专家协作系统完全就绪！")
        else:
            print("   ⚠️ 协作系统需要进一步优化")


def main():
    """主测试函数"""
    print("🚀 启动阶段2: 增强多专家协作推理系统测试")
    print("=" * 60)
    
    try:
        # 创建并运行测试套件
        test_suite = Stage2TestSuite()
        results = test_suite.run_all_tests()
        
        # 计算总体结果
        total_tests = len(results)
        passed_tests = sum(1 for result in results.values() if result)
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n🎯 阶段2测试完成")
        print(f"   成功率: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("   🎉 阶段2完全成功！准备进入阶段3")
        elif success_rate >= 80:
            print("   ✅ 阶段2基本成功，可以继续下一阶段")
        else:
            print("   ⚠️ 阶段2需要修复后再继续")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        print(f"   详细错误: {traceback.format_exc()}")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
