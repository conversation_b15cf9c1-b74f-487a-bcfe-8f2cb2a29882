"""
具体实现方法规划器
根据workflow讨论具体实现方法，给出实验思路
"""

import os
import sys
import json
import time
from typing import Dict, List, Any, Optional, Tuple, TYPE_CHECKING

if TYPE_CHECKING:
    from core.llm_client import LLMClient
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from reasoning.data_models import (
    ExperimentPlan, ImplementationPlan, ImplementationStep
)
from agents.agent_manager import AgentManager
from core.llm_client import LLMClient


class ImplementationPlanner:
    """具体实现方法规划器"""
    
    def __init__(self, llm_client: Optional['LLMClient'] = None):
        """
        初始化规划器
        
        Args:
            llm_client: LLM客户端实例
        """
        # 初始化LLM客户端
        if llm_client is None:
            from core.llm_client import LLMClient
            from config.model_config import create_model_config
            model_config = create_model_config()
            llm_client = LLMClient()
        
        self.llm_client = llm_client
        self.agent_manager = AgentManager(llm_client)
        
        # 脑启发智能常用技术栈
        self.brain_inspired_tech_stack = {
            "deep_learning_frameworks": {
                "pytorch": {
                    "advantages": "灵活性强，研究友好，动态图支持",
                    "use_cases": ["原型开发", "研究实验", "自定义网络"]
                },
                "tensorflow": {
                    "advantages": "生产级稳定，生态丰富，部署便利",
                    "use_cases": ["生产部署", "大规模训练", "模型服务"]
                },
                "jax": {
                    "advantages": "函数式编程，JIT编译，梯度变换",
                    "use_cases": ["高性能计算", "数值研究", "并行训练"]
                }
            },
            "brain_inspired_libraries": {
                "snntorch": {
                    "description": "脉冲神经网络PyTorch库",
                    "applications": ["SNN实现", "时序建模", "神经动力学"]
                },
                "norse": {
                    "description": "深度脉冲神经网络库",
                    "applications": ["大规模SNN", "神经计算", "事件驱动学习"]
                },
                "brian2": {
                    "description": "神经元建模仿真器",
                    "applications": ["神经元仿真", "网络动力学", "生物建模"]
                },
                "nengo": {
                    "description": "神经工程框架",
                    "applications": ["认知建模", "神经形态计算", "控制系统"]
                }
            },
            "datasets": {
                "vision": ["MNIST", "CIFAR-10/100", "ImageNet", "DVS-Gesture", "N-MNIST"],
                "nlp": ["Penn Treebank", "WikiText", "GLUE", "SuperGLUE"],
                "neuromorphic": ["DVS128", "ATIS", "DDD17", "Prophesee"],
                "brain_data": ["Allen Brain Atlas", "Human Connectome Project", "Neurodata"]
            },
            "evaluation_tools": {
                "visualization": ["matplotlib", "seaborn", "plotly", "tensorboard", "wandb"],
                "metrics": ["scikit-learn", "torchmetrics", "tensorflow-addons"],
                "analysis": ["numpy", "scipy", "pandas", "jupyter"]
            }
        }
        
        # 代码模板库
        self.code_templates = {
            "pytorch_basic": {
                "description": "PyTorch基础实验框架",
                "template": """
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import matplotlib.pyplot as plt

class BrainInspiredModel(nn.Module):
    def __init__(self, input_size, hidden_size, output_size):
        super().__init__()
        # 在此定义网络结构
        pass
    
    def forward(self, x):
        # 在此定义前向传播
        pass

def train_model(model, train_loader, criterion, optimizer, epochs):
    model.train()
    for epoch in range(epochs):
        for batch_idx, (data, target) in enumerate(train_loader):
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            loss.backward()
            optimizer.step()
    
def evaluate_model(model, test_loader):
    model.eval()
    with torch.no_grad():
        # 评估逻辑
        pass
                """
            },
            "snn_template": {
                "description": "脉冲神经网络实现模板",
                "template": """
import snntorch as snn
import torch.nn as nn

class SpikingNeuralNetwork(nn.Module):
    def __init__(self, input_size, hidden_size, output_size):
        super().__init__()
        
        # 网络层定义
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.lif1 = snn.Leaky(beta=0.95)
        self.fc2 = nn.Linear(hidden_size, output_size)
        self.lif2 = snn.Leaky(beta=0.95)
    
    def forward(self, x):
        # 脉冲神经网络前向传播
        mem1 = self.lif1.init_leaky()
        mem2 = self.lif2.init_leaky()
        
        spk_rec = []
        for step in range(x.size(0)):
            cur1 = self.fc1(x[step])
            spk1, mem1 = self.lif1(cur1, mem1)
            cur2 = self.fc2(spk1)
            spk2, mem2 = self.lif2(cur2, mem2)
            spk_rec.append(spk2)
        
        return torch.stack(spk_rec)
                """
            }
        }
    
    def plan_implementation(self, experiment_plan: ExperimentPlan, 
                          workflow_context: Dict[str, Any] = None) -> ImplementationPlan:
        """
        根据实验计划制定具体实现方案
        
        Args:
            experiment_plan: 实验计划
            workflow_context: 从论文提取的工作流上下文
            
        Returns:
            详细的实现计划
        """
        print(f"\n⚙️ 开始制定实现方案")
        print(f"🔬 实验类型: {experiment_plan.experiment_type}")
        print(f"📊 评估指标: {len(experiment_plan.metrics)}个")
        
        # 第一步：分析技术需求
        tech_requirements = self._analyze_technical_requirements(experiment_plan, workflow_context)
        
        # 第二步：选择技术栈
        tech_stack = self._select_technology_stack(experiment_plan, tech_requirements)
        
        # 第三步：设计代码架构
        code_structure = self._design_code_architecture(experiment_plan, tech_stack)
        
        # 第四步：制定实现步骤
        implementation_steps = self._create_implementation_steps(experiment_plan, code_structure)
        
        # 第五步：配置实验环境
        environment_config = self._configure_environment(tech_stack)
        
        # 第六步：推荐数据集和预处理
        data_recommendations = self._recommend_datasets_and_preprocessing(experiment_plan)
        
        # 第七步：生成代码模板
        code_templates = self._generate_code_templates(experiment_plan, tech_stack)
        
        # 创建实现计划
        implementation_plan = ImplementationPlan(
            experiment_plan_id=f"exp_{int(time.time())}",
            programming_language="Python",
            frameworks=tech_stack.get("frameworks", []),
            libraries=tech_stack.get("libraries", []),
            steps=implementation_steps,
            code_structure=code_structure,
            environment=environment_config,
            recommended_datasets=data_recommendations.get("datasets", []),
            data_preprocessing=data_recommendations.get("preprocessing", []),
            code_templates=code_templates
        )
        
        print(f"\n✅ 实现方案制定完成!")
        print(f"🐍 编程语言: {implementation_plan.programming_language}")
        print(f"📚 框架数量: {len(implementation_plan.frameworks)}")
        print(f"🔧 实现步骤: {len(implementation_plan.steps)}个")
        
        return implementation_plan
    
    def _analyze_technical_requirements(self, experiment_plan: ExperimentPlan, 
                                      workflow_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """分析技术需求"""
        
        analysis_prompt = f"""
        分析以下实验的技术实现需求：
        
        实验类型: {experiment_plan.experiment_type}
        研究问题: {experiment_plan.research_question}
        实验变量: {[var.name for var in experiment_plan.variables]}
        评估指标: {experiment_plan.metrics}
        实验设计: {json.dumps(experiment_plan.design, ensure_ascii=False)}
        
        工作流上下文: {json.dumps(workflow_context or {}, ensure_ascii=False)}
        
        请分析以下技术需求：
        1. 计算复杂度和性能需求
        2. 数据处理和存储需求
        3. 模型训练和推理需求
        4. 可视化和分析需求
        5. 特殊的脑启发智能技术需求
        
        请以JSON格式输出：
        {{
            "computational_requirements": {{
                "complexity": "<计算复杂度级别>",
                "memory_usage": "<内存需求>",
                "training_time": "<预估训练时间>",
                "hardware_acceleration": "<是否需要GPU等加速>"
            }},
            "data_requirements": {{
                "data_size": "<数据规模>",
                "preprocessing_complexity": "<预处理复杂度>",
                "storage_needs": "<存储需求>",
                "real_time_processing": "<是否需要实时处理>"
            }},
            "model_requirements": {{
                "model_complexity": "<模型复杂度>",
                "custom_layers": ["<自定义层1>", "<自定义层2>"],
                "optimization_challenges": ["<优化挑战1>", "<优化挑战2>"],
                "interpretability_needs": "<可解释性需求>"
            }},
            "brain_inspired_specifics": {{
                "spiking_networks": "<是否需要脉冲网络>",
                "plasticity_mechanisms": "<是否需要可塑性机制>",
                "biological_constraints": ["<生物约束1>", "<生物约束2>"],
                "neuromorphic_features": ["<神经形态特性1>", "<神经形态特性2>"]
            }}
        }}
        """
        
        try:
            response = self.llm_client.get_response(analysis_prompt)
            if response:
                response_text = response[0] if isinstance(response, tuple) else response
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    return json.loads(json_text)
        except Exception as e:
            print(f"  ⚠️ 技术需求分析失败: {e}")
        
        # 返回默认需求
        return {
            "computational_requirements": {
                "complexity": "中等",
                "memory_usage": "2-8GB",
                "training_time": "数小时到数天",
                "hardware_acceleration": "推荐GPU"
            },
            "brain_inspired_specifics": {
                "spiking_networks": "可能需要",
                "plasticity_mechanisms": "是",
                "biological_constraints": ["能耗限制", "时间常数"],
                "neuromorphic_features": ["事件驱动", "异步处理"]
            }
        }
    
    def _select_technology_stack(self, experiment_plan: ExperimentPlan, 
                               tech_requirements: Dict[str, Any]) -> Dict[str, List[str]]:
        """选择合适的技术栈"""
        
        frameworks = []
        libraries = []
        
        # 基于实验类型选择主框架
        if any(keyword in experiment_plan.research_question.lower() 
               for keyword in ["脉冲", "snn", "spiking"]):
            frameworks.append("pytorch")
            libraries.extend(["snntorch", "norse"])
        elif any(keyword in experiment_plan.research_question.lower() 
                for keyword in ["神经元", "仿真", "brian"]):
            frameworks.append("brian2")
            libraries.extend(["brian2", "numpy", "matplotlib"])
        else:
            frameworks.append("pytorch")
            libraries.extend(["torch", "torchvision"])
        
        # 基础科学计算库
        libraries.extend(["numpy", "scipy", "matplotlib", "seaborn"])
        
        # 机器学习工具
        libraries.extend(["scikit-learn", "pandas"])
        
        # 可视化工具
        if "可视化" in str(experiment_plan.metrics):
            libraries.extend(["plotly", "tensorboard"])
        
        # 去重
        frameworks = list(set(frameworks))
        libraries = list(set(libraries))
        
        return {
            "frameworks": frameworks,
            "libraries": libraries
        }
    
    def _design_code_architecture(self, experiment_plan: ExperimentPlan, 
                                tech_stack: Dict[str, List[str]]) -> Dict[str, Any]:
        """设计代码架构"""
        
        architecture = {
            "project_structure": {
                "src/": {
                    "models/": ["brain_inspired_model.py", "baseline_models.py"],
                    "data/": ["dataset_loader.py", "preprocessing.py"],
                    "training/": ["trainer.py", "optimizer.py"],
                    "evaluation/": ["evaluator.py", "metrics.py"],
                    "visualization/": ["plotter.py", "analysis.py"],
                    "utils/": ["helpers.py", "config.py"]
                },
                "experiments/": ["experiment_runner.py", "configs/"],
                "data/": ["raw/", "processed/", "results/"],
                "notebooks/": ["exploration.ipynb", "analysis.ipynb"],
                "tests/": ["test_models.py", "test_utils.py"],
                "docs/": ["README.md", "API.md"]
            },
            "main_modules": {
                "model_definition": "定义实验中的各种模型",
                "data_pipeline": "数据加载和预处理流水线",
                "training_loop": "训练循环和优化策略",
                "evaluation_framework": "评估框架和指标计算",
                "experiment_runner": "实验执行和结果收集"
            },
            "design_patterns": {
                "factory_pattern": "用于创建不同类型的模型",
                "strategy_pattern": "用于不同的训练策略",
                "observer_pattern": "用于训练过程监控",
                "template_pattern": "用于实验流程模板"
            }
        }
        
        return architecture
    
    def _create_implementation_steps(self, experiment_plan: ExperimentPlan, 
                                   code_structure: Dict[str, Any]) -> List[ImplementationStep]:
        """创建实现步骤"""
        
        steps = []
        
        # 步骤1：环境搭建
        steps.append(ImplementationStep(
            step_number=1,
            title="环境搭建和依赖安装",
            description="创建Python环境，安装必要的依赖库",
            code_template="""
# 创建虚拟环境
python -m venv brain_ai_env
source brain_ai_env/bin/activate  # Linux/Mac
# brain_ai_env\\Scripts\\activate  # Windows

# 安装依赖
pip install torch torchvision
pip install numpy matplotlib seaborn
pip install scikit-learn pandas jupyter
            """,
            dependencies=[],
            estimated_time="30分钟",
            difficulty_level="easy"
        ))
        
        # 步骤2：项目结构创建
        steps.append(ImplementationStep(
            step_number=2,
            title="创建项目结构",
            description="根据设计架构创建项目目录和基础文件",
            code_template="""
mkdir -p src/{models,data,training,evaluation,visualization,utils}
mkdir -p experiments/configs
mkdir -p data/{raw,processed,results}
mkdir -p notebooks tests docs
touch README.md requirements.txt
            """,
            dependencies=["步骤1"],
            estimated_time="15分钟",
            difficulty_level="easy"
        ))
        
        # 步骤3：数据准备
        steps.append(ImplementationStep(
            step_number=3,
            title="数据集准备和预处理",
            description="下载数据集，实现数据加载和预处理功能",
            code_template="""
from torch.utils.data import DataLoader
import torchvision.transforms as transforms

def get_data_loaders(batch_size=32):
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize((0.5,), (0.5,))
    ])
    
    # 加载数据集
    train_dataset = torchvision.datasets.MNIST(
        root='./data', train=True, download=True, transform=transform
    )
    test_dataset = torchvision.datasets.MNIST(
        root='./data', train=False, download=True, transform=transform
    )
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, test_loader
            """,
            dependencies=["步骤2"],
            estimated_time="1小时",
            difficulty_level="medium"
        ))
        
        # 步骤4：模型实现
        steps.append(ImplementationStep(
            step_number=4,
            title="脑启发模型实现",
            description="实现核心的脑启发智能模型",
            code_template="# 根据具体研究问题实现模型",
            dependencies=["步骤3"],
            estimated_time="4-8小时",
            difficulty_level="hard"
        ))
        
        # 步骤5：训练框架
        steps.append(ImplementationStep(
            step_number=5,
            title="训练框架实现",
            description="实现模型训练循环和优化策略",
            dependencies=["步骤4"],
            estimated_time="2-4小时",
            difficulty_level="medium"
        ))
        
        # 步骤6：评估系统
        steps.append(ImplementationStep(
            step_number=6,
            title="评估系统实现", 
            description="实现模型评估和指标计算",
            dependencies=["步骤5"],
            estimated_time="2小时",
            difficulty_level="medium"
        ))
        
        # 步骤7：实验执行
        steps.append(ImplementationStep(
            step_number=7,
            title="实验执行和结果收集",
            description="运行完整实验并收集结果",
            dependencies=["步骤6"],
            estimated_time="数小时到数天",
            difficulty_level="medium"
        ))
        
        # 步骤8：结果分析
        steps.append(ImplementationStep(
            step_number=8,
            title="结果分析和可视化",
            description="分析实验结果并生成可视化图表",
            dependencies=["步骤7"],
            estimated_time="2-4小时",
            difficulty_level="medium"
        ))
        
        return steps
    
    def _configure_environment(self, tech_stack: Dict[str, List[str]]) -> Dict[str, Any]:
        """配置实验环境"""
        
        environment = {
            "python_version": "3.8+",
            "package_manager": "pip",
            "virtual_environment": "venv或conda",
            "development_tools": ["jupyter", "vscode", "pycharm"],
            "version_control": "git",
            "requirements": {
                "core": tech_stack.get("libraries", []),
                "development": ["jupyter", "ipython", "pytest"],
                "optional": ["wandb", "tensorboard", "mlflow"]
            },
            "hardware_recommendations": {
                "minimum": "CPU: 4核, RAM: 8GB, 存储: 10GB",
                "recommended": "CPU: 8核, RAM: 16GB, GPU: 6GB显存, 存储: 50GB",
                "optimal": "CPU: 16核, RAM: 32GB, GPU: 12GB显存, 存储: 100GB"
            },
            "cloud_options": [
                "Google Colab (免费GPU)",
                "Kaggle Notebooks (免费GPU)",
                "AWS SageMaker",
                "Azure ML Studio",
                "阿里云机器学习PAI"
            ]
        }
        
        return environment
    
    def _recommend_datasets_and_preprocessing(self, experiment_plan: ExperimentPlan) -> Dict[str, List[str]]:
        """推荐数据集和预处理方法"""
        
        datasets = []
        preprocessing = []
        
        question_lower = experiment_plan.research_question.lower()
        
        # 基于研究问题推荐数据集
        if any(word in question_lower for word in ["视觉", "图像", "识别"]):
            datasets.extend(["MNIST", "CIFAR-10", "CIFAR-100", "ImageNet"])
            preprocessing.extend(["归一化", "数据增强", "resize"])
        
        if any(word in question_lower for word in ["文本", "语言", "nlp"]):
            datasets.extend(["Penn Treebank", "WikiText", "IMDB"])
            preprocessing.extend(["分词", "词向量", "序列填充"])
        
        if any(word in question_lower for word in ["脉冲", "神经形态", "事件"]):
            datasets.extend(["DVS128", "N-MNIST", "DVS-Gesture"])
            preprocessing.extend(["事件编码", "时间窗口", "脉冲转换"])
        
        # 默认推荐
        if not datasets:
            datasets = ["MNIST", "CIFAR-10"]
            preprocessing = ["归一化", "数据增强"]
        
        return {
            "datasets": datasets,
            "preprocessing": preprocessing
        }
    
    def _generate_code_templates(self, experiment_plan: ExperimentPlan, 
                               tech_stack: Dict[str, List[str]]) -> Dict[str, str]:
        """生成代码模板"""
        
        templates = {}
        
        # 基础实验框架模板
        if "pytorch" in tech_stack.get("frameworks", []):
            templates["main_experiment"] = self.code_templates["pytorch_basic"]["template"]
        
        # 脉冲神经网络模板
        if any(lib in tech_stack.get("libraries", []) for lib in ["snntorch", "norse"]):
            templates["snn_model"] = self.code_templates["snn_template"]["template"]
        
        # 数据加载模板
        templates["data_loader"] = """
import torch
from torch.utils.data import DataLoader
import torchvision.datasets as datasets
import torchvision.transforms as transforms

def create_data_loaders(dataset_name='MNIST', batch_size=32, data_dir='./data'):
    if dataset_name == 'MNIST':
        transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.1307,), (0.3081,))
        ])
        train_dataset = datasets.MNIST(data_dir, train=True, download=True, transform=transform)
        test_dataset = datasets.MNIST(data_dir, train=False, transform=transform)
    elif dataset_name == 'CIFAR10':
        transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
        ])
        train_dataset = datasets.CIFAR10(data_dir, train=True, download=True, transform=transform)
        test_dataset = datasets.CIFAR10(data_dir, train=False, transform=transform)
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, test_loader
        """
        
        # 评估模板
        templates["evaluation"] = """
import torch
import numpy as np
from sklearn.metrics import accuracy_score, precision_recall_fscore_support

def evaluate_model(model, test_loader, device='cpu'):
    model.eval()
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for data, target in test_loader:
            data, target = data.to(device), target.to(device)
            output = model(data)
            pred = output.argmax(dim=1)
            
            all_predictions.extend(pred.cpu().numpy())
            all_targets.extend(target.cpu().numpy())
    
    accuracy = accuracy_score(all_targets, all_predictions)
    precision, recall, f1, _ = precision_recall_fscore_support(
        all_targets, all_predictions, average='weighted'
    )
    
    metrics = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1
    }
    
    return metrics
        """
        
        return templates
    
    def generate_implementation_guide(self, implementation_plan: ImplementationPlan) -> str:
        """生成实现指南"""
        
        guide = f"""
# 实验实现指南

## 技术栈概览
- **编程语言**: {implementation_plan.programming_language}
- **主要框架**: {', '.join(implementation_plan.frameworks)}
- **依赖库**: {', '.join(implementation_plan.libraries[:10])}{'...' if len(implementation_plan.libraries) > 10 else ''}

## 项目结构
```
{json.dumps(implementation_plan.code_structure.get('project_structure', {}), indent=2)}
```

## 实现步骤

{chr(10).join(f"### 步骤 {step.step_number}: {step.title}" + chr(10) + f"**描述**: {step.description}" + chr(10) + f"**预估时间**: {step.estimated_time}" + chr(10) + f"**难度**: {step.difficulty_level}" + chr(10) for step in implementation_plan.steps)}

## 环境配置

### 硬件要求
{json.dumps(implementation_plan.environment.get('hardware_recommendations', {}), ensure_ascii=False, indent=2)}

### 软件依赖
```bash
pip install {' '.join(implementation_plan.libraries)}
```

## 推荐数据集
{chr(10).join(f"- {dataset}" for dataset in implementation_plan.recommended_datasets)}

## 数据预处理
{chr(10).join(f"- {preprocess}" for preprocess in implementation_plan.data_preprocessing)}

## 代码模板

### 主实验框架
```python
{implementation_plan.code_templates.get('main_experiment', '# 主实验代码模板')}
```

### 数据加载
```python  
{implementation_plan.code_templates.get('data_loader', '# 数据加载代码模板')}
```

### 模型评估
```python
{implementation_plan.code_templates.get('evaluation', '# 模型评估代码模板')}
```

## 注意事项
1. 确保所有依赖库版本兼容
2. 使用GPU可以显著加速训练过程
3. 定期保存实验结果和模型检查点
4. 记录实验参数以确保可重现性
5. 关注脑启发智能的特有指标和约束

## 扩展建议
1. 集成实验管理工具（如wandb、mlflow）
2. 实现自动超参数调优
3. 添加模型可视化和解释功能
4. 考虑分布式训练以处理大规模数据
        """
        
        return guide.strip()


# 测试函数
def test_implementation_planner():
    """测试具体实现方法规划器"""
    
    print("🧪 测试具体实现方法规划器")
    
    # 创建测试实验计划
    from reasoning.data_models import ExperimentVariable
    
    test_plan = ExperimentPlan(
        research_question="如何设计一种基于脑神经可塑性的自适应神经网络架构？",
        hypothesis=[
            "脑神经可塑性机制可以指导神经网络结构的动态调整",
            "自适应架构能够提高学习效率和泛化能力"
        ],
        experiment_type="controlled_experiment",
        design={"baseline": "传统CNN", "experimental": "自适应架构"},
        methodology={},
        variables=[
            ExperimentVariable("模型架构", "independent", "不同的网络架构", ["CNN", "自适应CNN"], "架构对比"),
            ExperimentVariable("准确率", "dependent", "分类准确率", ["0-1"], "测试集评估")
        ],
        metrics=["accuracy", "training_time", "adaptability"]
    )
    
    # 创建规划器并制定实现方案
    planner = ImplementationPlanner()
    implementation_plan = planner.plan_implementation(test_plan)
    
    # 生成实现指南
    guide = planner.generate_implementation_guide(implementation_plan)
    print(f"\n📋 实现指南:")
    print(guide)
    
    return implementation_plan


if __name__ == "__main__":
    test_implementation_planner()
