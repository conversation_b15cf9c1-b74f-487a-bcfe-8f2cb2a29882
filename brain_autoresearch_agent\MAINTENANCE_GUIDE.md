# Brain AutoResearch Agent - 维护指南

## 📋 维护指南概述
**创建日期**: 2025年7月19日
**适用对象**: 开发人员、维护人员、项目管理者
**目的**: 提供系统维护的标准化流程和最佳实践

## 🏗️ 项目架构概览

### 核心目录结构
```
brain_autoresearch_agent/
├── core/                    # 核心基础设施
│   ├── llm_client.py       # LLM客户端（关键）
│   ├── hybrid_model_client.py # 混合模型客户端
│   ├── *_tool.py          # API工具集
│   └── ...
├── agents/                  # 多专家代理系统
│   ├── base_agent.py       # 基础代理类
│   ├── agent_manager.py    # 代理管理器（关键）
│   └── expert_agents/      # 5个专家代理
├── reasoning/               # 推理系统
│   ├── reasoning_workflow.py # 推理流程协调（关键）
│   └── ...
├── paper_generation/        # 论文生成系统
│   ├── brain_paper_writer.py # 主要写作器（关键）
│   ├── enhanced_citation_manager.py # 引用管理
│   ├── latex_format_expert.py # LaTeX格式专家
│   └── ...
├── workflow/               # 工作流系统
├── tests/                  # 正式测试套件
├── archived/               # 归档文件
└── [配置和文档文件]
```

## 🔧 日常维护任务

### 🟢 每日维护检查
- [ ] **系统健康检查**
  ```bash
  python simple_test.py
  ```
  - 验证基本功能正常
  - 检查API连接状态
  - 确认核心模块加载正常

- [ ] **日志检查**
  - 查看错误日志，关注异常模式
  - 监控API调用成功率
  - 检查内存和CPU使用情况

### 🟡 每周维护任务
- [ ] **完整测试运行**
  ```bash
  python tests/test_stage1_complete_integration.py
  python test_priority_one_integration.py
  ```
  - 运行核心集成测试
  - 验证论文生成pipeline
  - 检查专家代理协作

- [ ] **性能监控**
  - 测量论文生成时间（目标：<60秒）
  - 监控API调用延迟
  - 检查系统资源使用

- [ ] **依赖检查**
  - 更新requirements.txt中的包版本
  - 检查API密钥有效性
  - 验证第三方服务状态

### 🟠 每月维护任务
- [ ] **完整测试套件**
  ```bash
  python test_ultimate_enhanced_stage4.py
  python test_priority_two_complete.py
  ```
  - 运行所有重要测试
  - 验证新功能集成
  - 性能回归测试

- [ ] **代码质量检查**
  - 运行代码质量检测工具
  - 检查技术债务累积
  - 审查代码覆盖率

- [ ] **文档更新**
  - 更新API文档
  - 检查README.md准确性
  - 更新系统使用指南

## 🚨 问题诊断和解决

### 常见问题排查
#### 1. 论文生成失败
**症状**: 论文生成过程中断或输出格式错误
**排查步骤**:
```bash
# 1. 检查LLM客户端状态
python -c "from core.llm_client import LLMClient; client = LLMClient(); print('LLM正常' if client.is_available() else 'LLM故障')"

# 2. 检查LaTeX格式专家
python -c "from paper_generation.latex_format_expert import LaTeXFormatExpert; expert = LaTeXFormatExpert(); print('LaTeX专家正常')"

# 3. 运行简单测试
python simple_test.py
```

**可能原因**:
- API密钥失效或配额不足
- LaTeX模板格式问题
- 专家代理协作失败
- 网络连接问题

**解决方案**:
1. 检查并更新API密钥
2. 重启系统使用模拟模式
3. 检查网络连接
4. 查看具体错误日志

#### 2. 专家代理无响应
**症状**: 多专家协作过程中某个代理无响应
**排查步骤**:
```bash
# 检查代理管理器状态
python -c "from agents.agent_manager import AgentManager; from core.llm_client import LLMClient; manager = AgentManager(LLMClient()); print(f'已注册专家: {list(manager.agents.keys())}')"
```

**解决方案**:
1. 重新初始化代理管理器
2. 检查特定专家的错误日志
3. 使用降级模式运行

#### 3. API调用限制
**症状**: 频繁出现429错误或API调用失败
**排查步骤**:
- 检查API配额使用情况
- 确认API密钥权限
- 查看调用频率

**解决方案**:
1. 实施调用频率限制
2. 使用备用API源
3. 升级API服务计划

### 📊 系统监控指标
#### 关键性能指标 (KPI)
- **论文生成成功率**: >95%
- **平均生成时间**: <60秒
- **API调用成功率**: >98%
- **系统可用性**: >99%

#### 监控脚本
```bash
# 性能监控脚本
python monitoring/system_monitor.py --check-all
```

## 🔄 更新和升级流程

### 代码更新流程
1. **创建备份**
   ```bash
   # 备份当前版本
   cp -r brain_autoresearch_agent brain_autoresearch_agent_backup_$(date +%Y%m%d)
   ```

2. **更新代码**
   ```bash
   git pull origin main
   ```

3. **依赖更新**
   ```bash
   pip install -r requirements.txt --upgrade
   ```

4. **运行测试**
   ```bash
   python tests/test_stage1_complete_integration.py
   ```

5. **验证功能**
   ```bash
   python simple_test.py
   ```

### 配置更新
- **API密钥更新**: 更新.env文件中的密钥
- **模型配置**: 修改config/目录下的配置文件
- **模板更新**: 更新paper_generation/模板文件

## 📁 文件管理

### 归档策略
- **测试输出**: 保留最近30天的测试结果
- **生成论文**: 按日期组织，保留重要版本
- **日志文件**: 定期轮转，保留最近90天

### 清理任务
```bash
# 每月清理脚本
find output/ -name "*.json" -mtime +30 -delete
find reasoning_sessions/ -name "*.json" -mtime +30 -delete
find __pycache__/ -name "*.pyc" -delete
```

### 备份策略
- **代码备份**: Git版本控制
- **配置备份**: 每周备份配置文件
- **数据备份**: 重要生成结果的备份

## 🚀 性能优化建议

### 短期优化
1. **缓存机制**: 缓存频繁的API调用结果
2. **并行处理**: 优化专家代理并行执行
3. **资源管理**: 及时释放不用的模型资源

### 长期优化
1. **数据库集成**: 使用数据库存储历史数据
2. **微服务架构**: 将组件拆分为独立服务
3. **负载均衡**: 支持多实例部署

## 📞 故障联系和支持

### 紧急问题处理
1. **立即行动**:
   - 记录错误信息和复现步骤
   - 检查系统日志
   - 尝试重启服务

2. **升级流程**:
   - 联系技术负责人
   - 创建问题工单
   - 实施临时解决方案

### 技术支持资源
- **文档**: README.md, SYSTEM_USAGE_GUIDE.md
- **测试**: tests/目录下的测试文件
- **示例**: archived/demo_files/目录

## ✅ 维护检查清单

### 日常检查 (5分钟)
- [ ] 运行simple_test.py
- [ ] 检查API密钥状态
- [ ] 查看最近的错误日志

### 周检查 (30分钟)
- [ ] 运行核心集成测试
- [ ] 检查系统性能指标
- [ ] 更新依赖包（如有需要）

### 月检查 (2小时)
- [ ] 完整测试套件
- [ ] 代码质量审查
- [ ] 文档同步更新
- [ ] 系统备份验证

## 🎯 最佳实践

1. **变更管理**: 所有变更都要经过测试验证
2. **日志记录**: 保持详细的操作日志
3. **监控警报**: 设置关键指标的监控警报
4. **文档维护**: 及时更新维护文档
5. **团队沟通**: 定期分享维护经验和问题解决方案

通过遵循这个维护指南，可以确保Brain AutoResearch Agent系统的稳定运行和持续改进。
