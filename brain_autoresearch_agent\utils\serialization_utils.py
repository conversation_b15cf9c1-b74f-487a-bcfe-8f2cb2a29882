"""
JSON序列化工具 - 支持自定义数据类型的序列化
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Union
import sys
import os

# 添加项目根路径到sys.path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)

# 导入数据类型
from agents.base_agent import AgentResponse


class EnhancedJSONEncoder(json.JSONEncoder):
    """增强的JSON编码器，支持更多Python对象类型"""
    
    def default(self, obj: Any) -> Any:
        """
        处理默认JSON编码器不支持的对象类型
        
        Args:
            obj: 要序列化的对象
            
        Returns:
            可序列化的对象表示
        """
        # 处理常见的时间类型
        if isinstance(obj, datetime):
            return obj.isoformat()
            
        # 处理AgentResponse
        if isinstance(obj, AgentResponse):
            return {
                "agent_type": obj.agent_type,
                "content": obj.content,
                "confidence": obj.confidence,
                "reasoning": obj.reasoning,
                "metadata": obj.metadata,
                "timestamp": obj.timestamp,
                "_type": "AgentResponse"  # 添加类型标记
            }
        
        # 处理PaperInfo和PaperWorkflow
        if obj.__class__.__name__ in ["PaperInfo", "PaperWorkflow"]:
            if hasattr(obj, 'to_dict') and callable(getattr(obj, 'to_dict')):
                return obj.to_dict()
            result = {}
            for key, value in obj.__dict__.items():
                if not key.startswith("_"):  # 跳过私有属性
                    result[key] = value
            return result
            
        # 处理对象的__dict__
        try:
            return obj.__dict__
        except AttributeError:
            pass
            
        # 处理其他自定义对象
        try:
            return str(obj)
        except Exception:
            pass
            
        # 默认行为
        return super().default(obj)


def to_json(data: Any) -> str:
    """
    将任意数据转换为JSON字符串
    
    Args:
        data: 要转换的数据
        
    Returns:
        JSON字符串
    """
    return json.dumps(data, cls=EnhancedJSONEncoder, ensure_ascii=False, indent=2)


def from_json(json_str: str) -> Any:
    """
    将JSON字符串转换回Python对象
    
    Args:
        json_str: JSON字符串
        
    Returns:
        Python对象
    """
    def object_hook(d):
        """自定义对象转换钩子"""
        if "_type" in d:
            # 根据类型标记恢复特定对象
            if d["_type"] == "AgentResponse":
                return AgentResponse(
                    agent_type=d.get("agent_type", ""),
                    content=d.get("content", ""),
                    confidence=d.get("confidence", 0.0),
                    reasoning=d.get("reasoning", ""),
                    metadata=d.get("metadata", {}),
                    timestamp=d.get("timestamp", "")
                )
        return d
        
    return json.loads(json_str, object_hook=object_hook)


def save_to_json_file(data: Any, file_path: str) -> bool:
    """
    将数据保存为JSON文件
    
    Args:
        data: 要保存的数据
        file_path: 文件路径
        
    Returns:
        是否保存成功
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
        # 使用增强的JSON编码器
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, cls=EnhancedJSONEncoder, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存JSON文件失败: {e}")
        return False


def load_from_json_file(file_path: str) -> Any:
    """
    从JSON文件加载数据
    
    Args:
        file_path: 文件路径
        
    Returns:
        加载的数据
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载JSON文件失败: {e}")
        return None


def serialize_agent_response(response: AgentResponse) -> Dict[str, Any]:
    """
    序列化AgentResponse对象为字典
    
    Args:
        response: AgentResponse对象
        
    Returns:
        字典表示
    """
    if not isinstance(response, AgentResponse):
        if hasattr(response, 'content') and hasattr(response, 'confidence'):
            # 类似AgentResponse的对象
            return {
                "content": getattr(response, 'content', ''),
                "confidence": getattr(response, 'confidence', 0.0),
                "agent_type": getattr(response, 'agent_type', 'unknown'),
                "metadata": getattr(response, 'metadata', {})
            }
        # 返回字符串
        return {"content": str(response)}
    
    # AgentResponse对象
    return {
        "agent_type": response.agent_type,
        "content": response.content,
        "confidence": response.confidence,
        "reasoning": response.reasoning,
        "metadata": response.metadata,
        "timestamp": response.timestamp
    }
