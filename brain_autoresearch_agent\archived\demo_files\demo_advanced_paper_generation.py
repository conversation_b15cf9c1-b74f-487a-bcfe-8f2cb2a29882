"""
高级论文生成系统快速配置示例
使用新的AdvancedBrainPaperWriter进行高质量论文生成
"""

import os
import sys
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from paper_generation.unified_paper_workflow import (
    UnifiedPaperGenerationWorkflow, 
    UnifiedWorkflowConfig
)
from paper_generation.enhanced_brain_paper_writer import PaperGenerationConfig


def create_advanced_paper_config() -> UnifiedWorkflowConfig:
    """创建高级论文生成配置"""
    
    # 论文生成配置
    paper_config = PaperGenerationConfig(
        target_venue="ICML",  # 或 "NeurIPS", "ICLR", "AAAI"
        paper_type="research",  # 或 "survey", "position"
        max_review_iterations=5,  # 增加评审轮次获得更高质量
        quality_threshold=8.0,  # 提高质量阈值
        enable_auto_revision=True,
        enable_multi_expert_review=True,
        latex_output=True,
        max_pages=8,
        include_experiments=True,
        include_ablation_studies=True
    )
    
    # 统一工作流配置
    workflow_config = UnifiedWorkflowConfig(
        enable_workflow_extraction=True,
        workflow_analysis_depth="comprehensive",
        paper_generation_config=paper_config,
        use_advanced_writer=True,  # 使用高级系统！
        output_formats=['markdown', 'latex', 'json'],
        save_intermediate_results=True,
        output_directory=f"output/advanced_papers_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )
    
    return workflow_config


def create_comparison_config() -> UnifiedWorkflowConfig:
    """创建对比配置（使用旧系统）"""
    
    paper_config = PaperGenerationConfig(
        target_venue="ICML",
        paper_type="research",
        max_review_iterations=3,
        quality_threshold=7.0,
        enable_auto_revision=True,
        enable_multi_expert_review=True,
        latex_output=True
    )
    
    workflow_config = UnifiedWorkflowConfig(
        enable_workflow_extraction=True,
        workflow_analysis_depth="comprehensive",
        paper_generation_config=paper_config,
        use_advanced_writer=False,  # 使用旧系统进行对比
        output_formats=['markdown', 'latex'],
        save_intermediate_results=True,
        output_directory=f"output/enhanced_papers_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    )
    
    return workflow_config


async def demo_advanced_paper_generation():
    """演示高级论文生成功能"""
    
    print("🚀 启动高级论文生成系统演示...")
    
    # 创建LLM客户端
    llm_client = LLMClient()
    
    # 使用高级配置
    config = create_advanced_paper_config()
    
    # 初始化统一工作流
    workflow = UnifiedPaperGenerationWorkflow(llm_client, config)
    
    # 示例研究主题
    research_topic = "基于脑启发神经网络的强化学习算法研究"
    research_requirements = {
        "target_conference": "ICML 2024",
        "research_focus": [
            "神经科学启发的网络架构",
            "强化学习算法优化", 
            "生物可塑性机制",
            "多智能体协作"
        ],
        "experimental_datasets": [
            "Atari游戏环境",
            "MuJoCo连续控制",
            "StarCraft II多智能体环境"
        ],
        "baseline_methods": [
            "Deep Q-Network (DQN)",
            "Proximal Policy Optimization (PPO)",
            "Multi-Agent Deep Deterministic Policy Gradient (MADDPG)"
        ]
    }
    
    print(f"📝 生成主题: {research_topic}")
    print(f"🎯 目标会议: {research_requirements['target_conference']}")
    print(f"🔧 使用系统: {'高级论文撰写器 (Advanced)' if config.use_advanced_writer else '增强论文撰写器 (Enhanced)'}")
    
    try:
        # 执行论文生成
        result = await workflow.generate_complete_paper(
            research_topic=research_topic,
            research_requirements=research_requirements,
            reference_papers=[]  # 可以添加参考论文
        )
        
        if result.success:
            print("✅ 论文生成成功!")
            print(f"📁 输出目录: {config.output_directory}")
            print(f"📄 生成格式: {', '.join(config.output_formats)}")
            print(f"⏱️  总耗时: {result.total_time:.2f}秒")
            
            # 显示生成摘要
            print("\n📋 生成摘要:")
            print(result.generation_summary)
            
            # 显示输出文件
            print("\n📂 输出文件:")
            for format_type, file_path in result.output_files.items():
                print(f"  - {format_type}: {file_path}")
                
        else:
            print("❌ 论文生成失败")
            
    except Exception as e:
        print(f"💥 生成过程中出错: {str(e)}")


async def compare_systems():
    """对比两种系统的生成效果"""
    
    print("🔄 启动系统对比测试...")
    
    # 相同的研究主题
    research_topic = "神经符号融合的可解释AI系统"
    research_requirements = {
        "target_conference": "NeurIPS 2024",
        "research_focus": ["神经符号融合", "可解释性", "知识表示"],
        "experimental_datasets": ["CLEVR", "VQA", "知识图谱"]
    }
    
    llm_client = LLMClient()
    
    # 测试高级系统
    print("\n🚀 测试高级系统...")
    advanced_config = create_advanced_paper_config()
    advanced_workflow = UnifiedPaperGenerationWorkflow(llm_client, advanced_config)
    
    # 测试增强系统  
    print("\n🔧 测试增强系统...")
    enhanced_config = create_comparison_config()
    enhanced_workflow = UnifiedPaperGenerationWorkflow(llm_client, enhanced_config)
    
    print("⚡ 同时运行两个系统进行对比...")
    
    # 这里可以并行或串行运行两个系统
    # 由于演示目的，我们展示配置差异
    
    print("\n📊 系统配置对比:")
    print(f"高级系统 - 最大评审轮次: {advanced_config.paper_generation_config.max_review_iterations}")
    print(f"增强系统 - 最大评审轮次: {enhanced_config.paper_generation_config.max_review_iterations}")
    print(f"高级系统 - 质量阈值: {advanced_config.paper_generation_config.quality_threshold}")
    print(f"增强系统 - 质量阈值: {enhanced_config.paper_generation_config.quality_threshold}")
    

if __name__ == "__main__":
    import asyncio
    
    print("🧠 Brain AutoResearch Agent - 高级论文生成系统")
    print("=" * 60)
    
    # 选择演示模式
    mode = input("选择演示模式 (1: 高级系统演示, 2: 系统对比): ")
    
    if mode == "1":
        asyncio.run(demo_advanced_paper_generation())
    elif mode == "2":
        asyncio.run(compare_systems())
    else:
        print("使用默认模式...")
        asyncio.run(demo_advanced_paper_generation())
