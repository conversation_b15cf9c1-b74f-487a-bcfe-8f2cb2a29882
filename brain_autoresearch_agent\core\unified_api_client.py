"""
统一API客户端 - 专为DeepSeek文本模型和Qwen视觉模型优化
"""

import os
import json
import time
import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from openai import OpenAI
import requests
import asyncio

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class APIResponse:
    """API响应封装"""
    content: str
    provider: str
    model: str
    success: bool
    error_message: Optional[str] = None
    token_usage: Optional[Dict[str, int]] = None

class UnifiedAPIClient:
    """
    统一API客户端
    - DeepSeek: 所有文本生成任务
    - Qwen: 视觉分析和布局优化任务
    """
    
    def __init__(self):
        """初始化客户端"""
        # DeepSeek配置
        self.deepseek_api_key = "***********************************"
        self.deepseek_base_url = "https://api.deepseek.com/v1"
        
        # Qwen配置
        self.qwen_api_key = "sk-f8559ea97bad4d638416d20db63bc643"
        self.qwen_base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
        
        # 初始化客户端
        self._setup_clients()
        
        # 模型配置
        self.text_models = {
            "reasoning": "deepseek-reasoner",
            "chat": "deepseek-chat",
            "default": "deepseek-chat"
        }
        
        self.vision_models = {
            "layout": "qwen-vl-plus",
            "analysis": "qwen-vl-plus", 
            "reasoning": "qvq-max-latest",
            "default": "qwen-vl-plus"
        }
        
    def _setup_clients(self):
        """设置API客户端"""
        try:
            # DeepSeek客户端 - 增加超时设置和重试间隔
            self.deepseek_client = OpenAI(
                api_key=self.deepseek_api_key,
                base_url=self.deepseek_base_url,
                timeout=60.0,  # 60秒超时
                max_retries=3  # 最多重试3次
            )
            logger.info("✅ DeepSeek客户端初始化成功")
            
            # Qwen客户端 - 增加超时设置和重试间隔
            self.qwen_client = OpenAI(
                api_key=self.qwen_api_key,
                base_url=self.qwen_base_url,
                timeout=60.0,  # 60秒超时
                max_retries=3  # 最多重试3次
            )
            
            # 设置Qwen环境变量
            os.environ["DASHSCOPE_API_KEY"] = self.qwen_api_key
            logger.info("✅ Qwen客户端初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 客户端初始化失败: {e}")
            raise
    
    def get_text_response(
        self, 
        prompt: str, 
        system_message: str = None,
        model_type: str = "default",
        temperature: float = 0.7,
        max_tokens: int = 4000,
        print_debug: bool = False
    ) -> APIResponse:
        """
        获取文本响应（使用DeepSeek）
        
        Args:
            prompt: 用户提示
            system_message: 系统消息
            model_type: 模型类型 (default/reasoning/chat)
            temperature: 温度参数
            max_tokens: 最大token数
            print_debug: 是否打印调试信息
            
        Returns:
            API响应对象
        """
        model = self.text_models.get(model_type, self.text_models["default"])
        
        if print_debug:
            logger.info(f"🤖 调用DeepSeek模型: {model}")
            logger.info(f"📝 提示长度: {len(prompt)} 字符")
        
        try:
            messages = []
            if system_message:
                messages.append({"role": "system", "content": system_message})
            messages.append({"role": "user", "content": prompt})
            
            response = self.deepseek_client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            content = response.choices[0].message.content
            token_usage = {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            } if response.usage else None
            
            if print_debug:
                logger.info(f"✅ 响应获取成功，长度: {len(content)} 字符")
                if token_usage:
                    logger.info(f"🔢 Token使用: {token_usage}")
            
            return APIResponse(
                content=content,
                provider="deepseek",
                model=model,
                success=True,
                token_usage=token_usage
            )
            
        except Exception as e:
            logger.error(f"❌ DeepSeek API调用失败: {e}")
            return APIResponse(
                content="",
                provider="deepseek", 
                model=model,
                success=False,
                error_message=str(e)
            )
    
    def get_response(self, prompt: str, **kwargs):
        """
        获取文本响应 (get_text_response的别名)
        
        Args:
            prompt: 提示词
            **kwargs: 其他参数，如system_message, model_type, temperature等
            
        Returns:
            str: 文本响应
        """
        try:
            # 只传递已知的get_text_response参数
            known_params = {
                "system_message": kwargs.get("system_message"),
                "model_type": kwargs.get("model_type", "default"),
                "temperature": kwargs.get("temperature", 0.7),
                "max_tokens": kwargs.get("max_tokens", 4000),
                "print_debug": kwargs.get("print_debug", False)
            }
            
            # 过滤掉None值
            filtered_params = {k: v for k, v in known_params.items() if v is not None}
            
            # 调用get_text_response，并处理APIResponse对象
            response = self.get_text_response(prompt=prompt, **filtered_params)
            
            # 如果是APIResponse对象，返回其content
            if hasattr(response, 'content'):
                return response.content
            
            return response
        except Exception as e:
            print(f"⚠️ UnifiedAPIClient.get_response错误: {e}")
            return f"获取响应失败: {str(e)}"
    
    def get_vision_response(
        self,
        prompt: str,
        image_path: str = None,
        image_url: str = None,
        model_type: str = "default",
        temperature: float = 0.7,
        max_tokens: int = 2000
    ) -> APIResponse:
        """
        获取视觉响应（使用Qwen）
        
        Args:
            prompt: 文本提示
            image_path: 本地图片路径
            image_url: 图片URL
            model_type: 模型类型 (default/layout/analysis/reasoning)
            temperature: 温度参数
            max_tokens: 最大token数
            
        Returns:
            API响应对象
        """
        model = self.vision_models.get(model_type, self.vision_models["default"])
        
        logger.info(f"👁️ 调用Qwen视觉模型: {model}")
        
        try:
            messages = []
            
            if image_path or image_url:
                # 构建视觉消息
                content = [{"type": "text", "text": prompt}]
                
                if image_path and os.path.exists(image_path):
                    # 本地图片需要转为base64
                    import base64
                    with open(image_path, "rb") as f:
                        image_data = base64.b64encode(f.read()).decode()
                    content.append({
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{image_data}"}
                    })
                elif image_url:
                    content.append({
                        "type": "image_url", 
                        "image_url": {"url": image_url}
                    })
                
                messages.append({"role": "user", "content": content})
            else:
                # 纯文本模式
                messages.append({"role": "user", "content": prompt})
            
            response = self.qwen_client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            content = response.choices[0].message.content
            token_usage = {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            } if response.usage else None
            
            logger.info(f"✅ Qwen响应获取成功，长度: {len(content)} 字符")
            
            return APIResponse(
                content=content,
                provider="qwen",
                model=model,
                success=True,
                token_usage=token_usage
            )
            
        except Exception as e:
            logger.error(f"❌ Qwen API调用失败: {e}")
            return APIResponse(
                content="",
                provider="qwen",
                model=model,
                success=False,
                error_message=str(e)
            )
    
    def extract_json(self, text: str, print_debug: bool = False) -> Optional[Dict]:
        """
        从文本中提取JSON数据
        
        Args:
            text: 包含JSON的文本
            print_debug: 是否打印调试信息
            
        Returns:
            解析的JSON数据或None
        """
        if print_debug:
            logger.info("🔍 开始JSON提取")
        
        # 尝试多种JSON提取方法
        extraction_methods = [
            self._extract_json_markers,
            self._extract_json_braces,
            self._extract_json_code_blocks
        ]
        
        for method in extraction_methods:
            try:
                result = method(text)
                if result:
                    if print_debug:
                        logger.info("✅ JSON提取成功")
                    return result
            except Exception as e:
                if print_debug:
                    logger.debug(f"JSON提取方法失败: {e}")
                continue
        
        if print_debug:
            logger.warning("⚠️ 所有JSON提取方法都失败")
        
        return None
    
    def _extract_json_markers(self, text: str) -> Optional[Dict]:
        """从标记间提取JSON"""
        import re
        pattern = r'```json\s*(\{.*?\})\s*```'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return json.loads(match.group(1))
        return None
    
    def _extract_json_braces(self, text: str) -> Optional[Dict]:
        """从大括号间提取JSON"""
        import re
        pattern = r'\{.*\}'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return json.loads(match.group(0))
        return None
    
    def _extract_json_code_blocks(self, text: str) -> Optional[Dict]:
        """从代码块提取JSON"""
        import re
        pattern = r'```\s*(\{.*?\})\s*```'
        match = re.search(pattern, text, re.DOTALL)
        if match:
            return json.loads(match.group(1))
        return None
    
    def extract_json_from_text(self, text: str) -> Optional[Dict]:
        """
        从文本中提取JSON数据（别名方法）
        
        Args:
            text: 包含JSON的文本
            
        Returns:
            解析的JSON数据或None
        """
        return self.extract_json(text)
    
    def test_connection(self, test_vision: bool = False) -> Dict[str, bool]:
        """
        测试API连接
        
        Args:
            test_vision: 是否测试视觉模型
            
        Returns:
            测试结果字典
        """
        results = {}
        
        # 测试DeepSeek文本模型
        logger.info("🧪 测试DeepSeek连接...")
        text_response = self.get_text_response(
            prompt="请说'测试成功'", 
            print_debug=True
        )
        results["deepseek_text"] = text_response.success
        
        if test_vision:
            # 测试Qwen视觉模型
            logger.info("🧪 测试Qwen视觉连接...")
            vision_response = self.get_vision_response(
                prompt="描述一下学术论文的常见布局特点"
            )
            results["qwen_vision"] = vision_response.success
        
        return results
    
    def get_optimal_model_for_task(self, task_type: str) -> Tuple[str, str]:
        """
        为任务获取最优模型
        
        Args:
            task_type: 任务类型
            
        Returns:
            (provider, model) 元组
        """
        task_mapping = {
            # 文本任务 - 使用DeepSeek
            "paper_writing": ("deepseek", "deepseek-chat"),
            "reasoning": ("deepseek", "deepseek-reasoner"),
            "code_generation": ("deepseek", "deepseek-chat"),
            "literature_analysis": ("deepseek", "deepseek-chat"),
            "experiment_design": ("deepseek", "deepseek-reasoner"),
            
            # 视觉任务 - 使用Qwen
            "layout_analysis": ("qwen", "qwen-vl-plus"),
            "visual_review": ("qwen", "qwen-vl-plus"),
            "layout_optimization": ("qwen", "qvq-max-latest"),
            "paper_formatting": ("qwen", "qwen-vl-plus"),
        }
        
        return task_mapping.get(task_type, ("deepseek", "deepseek-chat"))

    async def generate_async(self, prompt, temperature=0.2, max_tokens=2048, model=None):
        """
        异步生成文本响应
        
        Args:
            prompt: 提示词
            temperature: 温度参数
            max_tokens: 最大生成token数
            model: 指定模型，如不指定则使用默认模型
            
        Returns:
            生成的文本响应
        """
        # 在异步环境中，我们仍然使用同步方法，但包装在一个协程中
        loop = asyncio.get_event_loop()
        try:
            return await loop.run_in_executor(
                None, 
                lambda: self.generate_text(prompt, temperature, max_tokens, model)
            )
        except Exception as e:
            print(f"⚠️ 异步生成失败: {e}")
            return f"模拟异步响应: {prompt[:30]}..."

# 全局客户端实例
_global_client = None

def get_unified_client() -> UnifiedAPIClient:
    """获取全局统一客户端实例"""
    global _global_client
    if _global_client is None:
        _global_client = UnifiedAPIClient()
    return _global_client

if __name__ == "__main__":
    # 测试客户端
    print("🧪 统一API客户端测试")
    print("=" * 50)
    
    client = UnifiedAPIClient()
    
    # 测试连接
    results = client.test_connection(test_vision=False)
    
    print(f"\n📊 测试结果:")
    for service, success in results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {service}: {status}")
    
    # 测试任务模型推荐
    print(f"\n🎯 任务模型推荐:")
    tasks = [
        "paper_writing",
        "reasoning",
        "layout_analysis",
        "experiment_design"
    ]
    
    for task in tasks:
        provider, model = client.get_optimal_model_for_task(task)
        print(f"  {task}: {provider} - {model}")
