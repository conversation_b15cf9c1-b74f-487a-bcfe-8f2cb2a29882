"""
会议模板适配系统 - 第二优先级
支持多个顶级会议的专业格式模板
"""

import os
import json
from typing import Dict, List, Optional
from dataclasses import dataclass
from pathlib import Path

@dataclass
class ConferenceTemplate:
    """会议模板"""
    name: str
    full_name: str
    year: int
    documentclass: str
    required_packages: List[str]
    page_limit: int
    format_requirements: Dict[str, str]
    citation_style: str
    template_content: str

class ConferenceTemplateAdapter:
    """会议模板适配器 - 专业级别多会议支持"""
    
    def __init__(self):
        self.templates = self._load_conference_templates()
        self.template_dir = Path("paper_generation/latex_templates")
        self.template_dir.mkdir(exist_ok=True)
        
    def _load_conference_templates(self) -> Dict[str, ConferenceTemplate]:
        """加载会议模板配置"""
        templates = {}
        
        # ICML 2024
        templates['ICML'] = ConferenceTemplate(
            name='ICML',
            full_name='International Conference on Machine Learning',
            year=2024,
            documentclass='\\documentclass[accepted]{icml2024}',
            required_packages=[
                'inputenc', 'fontenc', 'hyperref', 'url', 'booktabs',
                'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'subfigure'
            ],
            page_limit=8,
            format_requirements={
                'title': '\\icmltitle{{{title}}}',
                'author': '\\icmlauthor{{{author}}}{{{affiliation}}}',
                'abstract': '\\begin{{abstract}}{abstract}\\end{{abstract}}',
                'keywords': '\\icmlkeywords{{{keywords}}}',
                'sections': 'numbered',
                'figures': 'htbp placement',
                'tables': 'booktabs style'
            },
            citation_style='icml',
            template_content=self._generate_icml_template()
        )
        
        # NeurIPS 2024
        templates['NeurIPS'] = ConferenceTemplate(
            name='NeurIPS',
            full_name='Conference on Neural Information Processing Systems',
            year=2024,
            documentclass='\\documentclass{neurips_2024}',
            required_packages=[
                'inputenc', 'fontenc', 'hyperref', 'url', 'booktabs',
                'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'natbib'
            ],
            page_limit=9,
            format_requirements={
                'title': '\\title{{{title}}}',
                'author': '\\author{{{author}}}',
                'abstract': '\\begin{{abstract}}{abstract}\\end{{abstract}}',
                'keywords': '',  # NeurIPS doesn't require keywords
                'sections': 'numbered',
                'figures': 'htbp placement',
                'tables': 'booktabs style'
            },
            citation_style='neurips',
            template_content=self._generate_neurips_template()
        )
        
        # ICLR 2024
        templates['ICLR'] = ConferenceTemplate(
            name='ICLR',
            full_name='International Conference on Learning Representations',
            year=2024,
            documentclass='\\documentclass{iclr2024_conference}',
            required_packages=[
                'inputenc', 'fontenc', 'hyperref', 'url', 'booktabs',
                'amsfonts', 'nicefrac', 'microtype', 'graphicx'
            ],
            page_limit=9,
            format_requirements={
                'title': '\\title{{{title}}}',
                'author': '\\author{{{author}}}',
                'abstract': '\\begin{{abstract}}{abstract}\\end{{abstract}}',
                'keywords': '\\keywords{{{keywords}}}',
                'sections': 'numbered',
                'figures': 'htbp placement',
                'tables': 'booktabs style'
            },
            citation_style='iclr',
            template_content=self._generate_iclr_template()
        )
        
        # AAAI 2024
        templates['AAAI'] = ConferenceTemplate(
            name='AAAI',
            full_name='Conference on Artificial Intelligence',
            year=2024,
            documentclass='\\documentclass[letterpaper]{aaai24}',
            required_packages=[
                'inputenc', 'fontenc', 'hyperref', 'url', 'booktabs',
                'amsfonts', 'nicefrac', 'microtype', 'graphicx', 'times'
            ],
            page_limit=7,
            format_requirements={
                'title': '\\title{{{title}}}',
                'author': '\\author{{{author}}}',
                'abstract': '\\begin{{abstract}}{abstract}\\end{{abstract}}',
                'keywords': '',  # AAAI doesn't require keywords
                'sections': 'numbered',
                'figures': 'htbp placement',
                'tables': 'booktabs style'
            },
            citation_style='aaai',
            template_content=self._generate_aaai_template()
        )
        
        # ACL 2024
        templates['ACL'] = ConferenceTemplate(
            name='ACL',
            full_name='Association for Computational Linguistics',
            year=2024,
            documentclass='\\documentclass[11pt,a4paper]{article}',
            required_packages=[
                'acl', 'inputenc', 'fontenc', 'hyperref', 'url', 
                'booktabs', 'amsfonts', 'microtype', 'graphicx'
            ],
            page_limit=8,
            format_requirements={
                'title': '\\title{{{title}}}',
                'author': '\\author{{{author}}}',
                'abstract': '\\begin{{abstract}}{abstract}\\end{{abstract}}',
                'keywords': '',
                'sections': 'numbered',
                'figures': 'htbp placement',
                'tables': 'booktabs style'
            },
            citation_style='acl',
            template_content=self._generate_acl_template()
        )
        
        return templates
    
    def _generate_icml_template(self) -> str:
        """生成ICML模板"""
        return '''% ICML 2024 Template
\\documentclass[accepted]{icml2024}

% Required packages
\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}
\\usepackage{hyperref}
\\usepackage{url}
\\usepackage{booktabs}
\\usepackage{amsfonts}
\\usepackage{nicefrac}
\\usepackage{microtype}
\\usepackage{graphicx}
\\usepackage{subfigure}

% Title and Author
\\icmltitle{{{title}}}
\\icmlauthor{{{author}}}{{{affiliation}}}

\\begin{document}

\\icmltitleabstract

\\begin{abstract}
{abstract}
\\end{abstract}

\\icmlkeywords{{{keywords}}}

% Main content starts here
{content}

% References
\\bibliography{references}
\\bibliographystyle{icml2024}

\\end{document}
'''
    
    def _generate_neurips_template(self) -> str:
        """生成NeurIPS模板"""
        return '''% NeurIPS 2024 Template
\\documentclass{neurips_2024}

% Required packages
\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}
\\usepackage{hyperref}
\\usepackage{url}
\\usepackage{booktabs}
\\usepackage{amsfonts}
\\usepackage{nicefrac}
\\usepackage{microtype}
\\usepackage{graphicx}
\\usepackage{natbib}

% Title and Author
\\title{{{title}}}
\\author{{{author}}}

\\begin{document}

\\maketitle

\\begin{abstract}
{abstract}
\\end{abstract}

% Main content starts here
{content}

% References
\\bibliography{references}
\\bibliographystyle{neurips_2024}

\\end{document}
'''
    
    def _generate_iclr_template(self) -> str:
        """生成ICLR模板"""
        return '''% ICLR 2024 Template
\\documentclass{iclr2024_conference}

% Required packages
\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}
\\usepackage{hyperref}
\\usepackage{url}
\\usepackage{booktabs}
\\usepackage{amsfonts}
\\usepackage{nicefrac}
\\usepackage{microtype}
\\usepackage{graphicx}

% Title and Author
\\title{{{title}}}
\\author{{{author}}}

\\begin{document}

\\maketitle

\\begin{abstract}
{abstract}
\\end{abstract}

\\keywords{{{keywords}}}

% Main content starts here
{content}

% References
\\bibliography{references}
\\bibliographystyle{iclr2024}

\\end{document}
'''
    
    def _generate_aaai_template(self) -> str:
        """生成AAAI模板"""
        return '''% AAAI 2024 Template
\\documentclass[letterpaper]{aaai24}

% Required packages
\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}
\\usepackage{hyperref}
\\usepackage{url}
\\usepackage{booktabs}
\\usepackage{amsfonts}
\\usepackage{nicefrac}
\\usepackage{microtype}
\\usepackage{graphicx}
\\usepackage{times}

% Title and Author
\\title{{{title}}}
\\author{{{author}}}

\\begin{document}

\\maketitle

\\begin{abstract}
{abstract}
\\end{abstract}

% Main content starts here
{content}

% References
\\bibliography{references}
\\bibliographystyle{aaai24}

\\end{document}
'''
    
    def _generate_acl_template(self) -> str:
        """生成ACL模板"""
        return '''% ACL 2024 Template
\\documentclass[11pt,a4paper]{article}
\\usepackage{acl}

% Required packages
\\usepackage[utf8]{inputenc}
\\usepackage[T1]{fontenc}
\\usepackage{hyperref}
\\usepackage{url}
\\usepackage{booktabs}
\\usepackage{amsfonts}
\\usepackage{microtype}
\\usepackage{graphicx}

% Title and Author
\\title{{{title}}}
\\author{{{author}}}

\\begin{document}

\\maketitle

\\begin{abstract}
{abstract}
\\end{abstract}

% Main content starts here
{content}

% References
\\bibliography{references}
\\bibliographystyle{acl_natbib}

\\end{document}
'''
    
    def get_available_conferences(self) -> List[str]:
        """获取可用的会议列表"""
        return list(self.templates.keys())
    
    def get_conference_info(self, conference: str) -> Optional[ConferenceTemplate]:
        """获取会议信息"""
        return self.templates.get(conference)
    
    def generate_paper_from_template(self, 
                                   conference: str,
                                   title: str,
                                   author: str,
                                   affiliation: str,
                                   abstract: str,
                                   content: str,
                                   keywords: str = "") -> str:
        """基于模板生成论文"""
        
        template = self.templates.get(conference)
        if not template:
            raise ValueError(f"不支持的会议: {conference}")
        
        print(f"🏛️ 使用 {template.full_name} ({conference}) 模板生成论文")
        print(f"   📄 页面限制: {template.page_limit} 页")
        print(f"   📦 必需包: {len(template.required_packages)} 个")
        
        # 替换模板变量
        latex_content = template.template_content.format(
            title=title,
            author=author,
            affiliation=affiliation,
            abstract=abstract,
            content=content,
            keywords=keywords
        )
        
        # 保存模板文件
        template_file = self.template_dir / f"{conference.lower()}_template.tex"
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(latex_content)
        
        print(f"   ✅ 模板文件已生成: {template_file}")
        
        return latex_content
    
    def validate_conference_compliance(self, content: str, conference: str) -> Dict[str, bool]:
        """验证会议格式合规性"""
        template = self.templates.get(conference)
        if not template:
            return {'error': False}
        
        compliance = {}
        
        # 检查文档类
        compliance['documentclass'] = template.documentclass.replace('\\\\', '\\') in content
        
        # 检查必需包
        package_count = 0
        for pkg in template.required_packages:
            if f'usepackage{{{pkg}}}' in content or f'usepackage[' in content and pkg in content:
                package_count += 1
        compliance['required_packages'] = package_count >= len(template.required_packages) * 0.8
        
        # 检查页面限制（简单估算）
        estimated_pages = len(content) / 3000  # 粗略估算
        compliance['page_limit'] = estimated_pages <= template.page_limit
        
        # 检查标题格式
        title_format = template.format_requirements['title']
        if 'icmltitle' in title_format:
            compliance['title_format'] = 'icmltitle' in content
        else:
            compliance['title_format'] = 'title{' in content
        
        # 检查摘要
        compliance['abstract'] = 'begin{abstract}' in content
        
        return compliance
    
    def get_format_suggestions(self, conference: str) -> List[str]:
        """获取格式建议"""
        template = self.templates.get(conference)
        if not template:
            return []
        
        suggestions = [
            f"📄 页面限制: {template.page_limit} 页",
            f"📦 必需包: {', '.join(template.required_packages[:5])} 等 {len(template.required_packages)} 个",
            f"📝 引用格式: {template.citation_style}",
        ]
        
        if template.format_requirements.get('keywords'):
            suggestions.append("🔑 需要关键词")
        
        return suggestions
    
    def export_template_files(self) -> Dict[str, str]:
        """导出所有模板文件"""
        exported_files = {}
        
        for conf_name, template in self.templates.items():
            filename = f"{conf_name.lower()}_template.tex"
            filepath = self.template_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(template.template_content)
            
            exported_files[conf_name] = str(filepath)
        
        print(f"✅ 已导出 {len(exported_files)} 个会议模板文件")
        return exported_files

def main():
    """测试会议模板适配器"""
    adapter = ConferenceTemplateAdapter()
    
    print("🏛️ 可用会议模板:")
    for conf in adapter.get_available_conferences():
        info = adapter.get_conference_info(conf)
        print(f"   • {conf}: {info.full_name} (页限: {info.page_limit})")
    
    # 测试生成论文
    test_paper = adapter.generate_paper_from_template(
        conference='ICML',
        title='Brain-Inspired AI: A Novel Approach',
        author='Test Author',
        affiliation='Test University',
        abstract='This paper presents a novel approach...',
        content='\\section{Introduction}\\nThis is the introduction...',
        keywords='artificial intelligence, machine learning'
    )
    
    print(f"\\n✅ 测试论文生成成功，长度: {len(test_paper)} 字符")
    
    # 导出模板文件
    adapter.export_template_files()

if __name__ == "__main__":
    main()
