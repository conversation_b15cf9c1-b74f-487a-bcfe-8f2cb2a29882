"""
增强版LaTeX生成器
- 支持多种会议模板
- 智能格式化
- 自动引用处理
"""

import os
import re
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import shutil

class ImprovedLaTeXGenerator:
    """
    增强版LaTeX生成器，支持多会议模板和格式优化
    """
    
    def __init__(self):
        """初始化LaTeX生成器"""
        # 定义模板目录
        self.base_dir = os.path.dirname(os.path.abspath(__file__))
        self.templates_dir = os.path.join(self.base_dir, "latex_templates")
        
        # 支持的会议/期刊模板
        self.supported_venues = {
            "ICML": "icml",
            "NeurIPS": "neurips",
            "ICLR": "iclr",
            "AAAI": "aaai",
            "JMLR": "jmlr",
            "general": "general"
        }
        
        # 确保模板目录存在
        os.makedirs(self.templates_dir, exist_ok=True)
        
    def generate_paper_latex(
        self,
        paper_content: Dict[str, str],
        metadata: Dict[str, Any],
        output_dir: str,
        target_venue: str = "ICML"
    ) -> Tu<PERSON>[str, str]:
        """
        生成完整的LaTeX论文
        
        Args:
            paper_content: 包含论文各部分内容的字典
            metadata: 论文元数据
            output_dir: 输出目录
            target_venue: 目标会议/期刊
            
        Returns:
            生成的LaTeX内容和文件路径
        """
        print(f"📄 开始生成LaTeX论文 (目标会议: {target_venue})...")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取模板路径
        template_key = target_venue.upper() if target_venue.upper() in self.supported_venues else "general"
        template_dir = self.supported_venues.get(template_key, "general")
        
        # 获取模板文件
        template_path = os.path.join(self.templates_dir, template_dir, "template.tex")
        
        # 如果模板不存在，使用通用模板
        if not os.path.exists(template_path):
            print(f"⚠️ 模板 {template_path} 不存在，使用通用模板")
            template_path = self._create_general_template()
        
        # 读取模板内容
        with open(template_path, "r", encoding="utf-8") as f:
            template_content = f.read()
        
        # 填充模板
        latex_content = self._fill_template(template_content, paper_content, metadata)
        
        # 格式优化
        latex_content = self._optimize_latex_format(latex_content)
        
        # 保存LaTeX文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        latex_filename = f"generated_latex_{timestamp}.tex"
        latex_filepath = os.path.join(output_dir, latex_filename)
        
        with open(latex_filepath, "w", encoding="utf-8") as f:
            f.write(latex_content)
            
        # 复制必要的模板文件到输出目录
        self._copy_template_files(template_dir, output_dir)
        
        print(f"✅ LaTeX生成完成: {latex_filepath}")
        return latex_content, latex_filepath
    
    def ggenerate_latex_paper(self, paper_content, target_venue="ICML"):
        """
        This is a typo in the method name - should be generate_latex_paper
        Keeping this for backward compatibility
        """
        return self.generate_latex_paper(paper_content, target_venue)
        
    def generate_latex_paper(self, paper_content, target_venue="ICML", venue=None):
        """
        Generate LaTeX paper from paper content
        """
        try:
            # Use venue parameter if provided (for compatibility)
            if venue is not None:
                target_venue = venue
            # 类型检查与修正
            if not isinstance(paper_content, dict):
                raise ValueError("paper_content must be a dict")
            metadata = paper_content.get('metadata', {})
            sections = paper_content.get('sections', None)
            # sections为None时，尝试从paper_content本身提取
            if sections is None:
                # 兼容直接平铺的结构
                section_keys = [
                    'abstract', 'introduction', 'related_work', 'methodology',
                    'experiments', 'results', 'discussion', 'conclusion', 'references'
                ]
                sections = {k: paper_content.get(k, '') for k in section_keys}
            # sections为str时，转为dict
            if isinstance(sections, str):
                import json
                try:
                    sections = json.loads(sections)
                except Exception:
                    sections = {'content': sections}
            # sections为dict但内容为dict时，取content字段
            for k, v in list(sections.items()):
                if isinstance(v, dict):
                    sections[k] = str(v.get('content', str(v)))
                elif v is not None and not isinstance(v, str):
                    sections[k] = str(v)
            # Get appropriate template
            template = self._get_template_for_venue(target_venue)
            # Generate LaTeX content
            latex_content = self._generate_latex_content(template, metadata, sections)
            return latex_content
        except Exception as e:
            print(f"⚠️ Improved LaTeX generation failed: {e}")
            # Fall back to basic template
            return self._generate_basic_latex(paper_content)
    
    def _fill_template(
        self, 
        template: str, 
        paper_content: Dict[str, str],
        metadata: Dict[str, Any]
    ) -> str:
        """填充LaTeX模板"""
        result = template
        
        # 替换标题和作者
        result = result.replace("%%TITLE%%", metadata.get("title", "未提供标题"))
        
        # 处理作者信息
        authors = metadata.get("authors", ["未提供作者"])
        if isinstance(authors, list):
            authors_str = " \\and ".join(authors)
        else:
            authors_str = str(authors)
        result = result.replace("%%AUTHORS%%", authors_str)
        
        # 替换摘要
        abstract = paper_content.get("abstract", "未提供摘要")
        result = result.replace("%%ABSTRACT%%", abstract)
        
        # 替换关键词
        keywords = metadata.get("keywords", ["神经网络", "深度学习", "人工智能"])
        if isinstance(keywords, list):
            keywords_str = ", ".join(keywords)
        else:
            keywords_str = str(keywords)
        result = result.replace("%%KEYWORDS%%", keywords_str)
        
        # 替换各部分内容
        sections = [
            ("introduction", "Introduction"),
            ("related_work", "Related Work"),
            ("methodology", "Methodology"),
            ("experiments", "Experiments"),
            ("results", "Results"),
            ("discussion", "Discussion"),
            ("conclusion", "Conclusion")
        ]
        
        for section_key, section_title in sections:
            content = paper_content.get(section_key, "")
            if content:
                section_latex = f"\\section{{{section_title}}}\n{content}\n\n"
                result = result.replace(f"%%{section_key.upper()}%%", section_latex)
            else:
                result = result.replace(f"%%{section_key.upper()}%%", "")
        
        # 处理参考文献
        references = paper_content.get("references", "")
        if references:
            if "\\begin{thebibliography}" not in references:
                references = self._format_references(references)
            result = result.replace("%%REFERENCES%%", references)
        else:
            result = result.replace("%%REFERENCES%%", "")
        
        return result
    
    def _format_references(self, references: str) -> str:
        """格式化参考文献"""
        if not references:
            return ""
            
        # 检查是否已经是LaTeX格式的参考文献
        if "\\bibitem" in references:
            return references
            
        # 处理可能的参考文献条目列表
        ref_items = []
        
        # 尝试拆分为条目
        if "\n" in references:
            ref_list = references.split("\n")
            ref_list = [r for r in ref_list if r.strip()]
        else:
            ref_list = [references]
        
        for i, ref in enumerate(ref_list):
            ref = ref.strip()
            if ref:
                if not ref.startswith("[") and not ref.startswith("\\bibitem"):
                    ref = f"\\bibitem{{ref{i+1}}} {ref}"
                ref_items.append(ref)
        
        if ref_items:
            formatted_refs = "\\begin{thebibliography}{99}\n"
            formatted_refs += "\n".join(ref_items)
            formatted_refs += "\n\\end{thebibliography}"
            return formatted_refs
        
        return references
    
    def _optimize_latex_format(self, content: str) -> str:
        """优化LaTeX格式"""
        # 修复数学公式
        content = self._fix_math_environments(content)
        
        # 修复引用
        content = self._fix_citations(content)
        
        # 修复图表
        content = self._fix_figures_tables(content)
        
        # 修复可能的Unicode问题
        content = self._sanitize_latex_content(content)
        
        return content
    
    def _fix_math_environments(self, content: str) -> str:
        """修复数学环境"""
        # 处理行内公式
        content = re.sub(r'(\$[^$]+\$)', lambda m: m.group(1).replace('_', '\\_'), content)
        
        # 确保数学环境中下标和上标格式正确
        content = re.sub(r'\\begin\{equation\}(.*?)\\end\{equation\}', 
                         lambda m: m.group(0).replace('_', '\\_').replace('\\\\\\', '\\'), 
                         content, flags=re.DOTALL)
        
        return content
    
    def _fix_citations(self, content: str) -> str:
        """修复引用格式"""
        # 确保引用使用正确的命令
        content = re.sub(r'\[([^\]]+)\]', lambda m: self._process_citation(m.group(1)), content)
        return content
    
    def _process_citation(self, citation_text: str) -> str:
        """处理可能的引用文本"""
        # 如果看起来像引用编号，转为\cite
        if re.match(r'^\d+(,\s*\d+)*$', citation_text):
            citations = [c.strip() for c in citation_text.split(',')]
            return f"\\cite{{{','.join(citations)}}}"
        return f"[{citation_text}]"
    
    def _fix_figures_tables(self, content: str) -> str:
        """修复图表环境"""
        # 处理图表环境
        for env in ['figure', 'table']:
            pattern = f"\\\\begin{{{env}}}(.*?)\\\\end{{{env}}}"
            content = re.sub(pattern, 
                            lambda m: self._fix_figure_table_env(m.group(0), m.group(1), env),
                            content, flags=re.DOTALL)
        return content
    
    def _fix_figure_table_env(self, full_match: str, inner_content: str, env_type: str) -> str:
        """修复单个图表环境"""
        # 如果没有caption或label，添加它们
        if "\\caption" not in inner_content:
            label = f"{env_type}1" if "\\label" not in inner_content else ""
            caption_line = f"\\caption{{{env_type.capitalize()} caption}} "
            if label:
                caption_line += f"\\label{{{label}}}"
            
            # 在末尾添加caption
            fixed = full_match.replace(f"\\end{{{env_type}}}", f"{caption_line}\n\\end{{{env_type}}}")
            return fixed
        return full_match
    
    def _create_general_template(self) -> str:
        """创建通用LaTeX模板"""
        general_dir = os.path.join(self.templates_dir, "general")
        os.makedirs(general_dir, exist_ok=True)
        
        template_path = os.path.join(general_dir, "template.tex")
        
        # 如果模板已存在，直接返回路径
        if os.path.exists(template_path):
            return template_path
        
        # 创建通用模板
        template_content = """\\documentclass[11pt,a4paper]{article}

\\usepackage{amsmath,amssymb,amsfonts}
\\usepackage{graphicx}
\\usepackage{hyperref}
\\usepackage{xcolor}

\\title{%%TITLE%%}
\\author{%%AUTHORS%%}
\\date{\\today}

\\begin{document}

\\maketitle

\\begin{abstract}
%%ABSTRACT%%
\\end{abstract}

\\textbf{Keywords:} %%KEYWORDS%%

%%INTRODUCTION%%

%%RELATED_WORK%%

%%METHODOLOGY%%

%%EXPERIMENTS%%

%%RESULTS%%

%%DISCUSSION%%

%%CONCLUSION%%

%%REFERENCES%%

\\end{document}
"""
        
        # 写入文件
        with open(template_path, "w", encoding="utf-8") as f:
            f.write(template_content)
        
        return template_path
    
    def _copy_template_files(self, template_dir: str, output_dir: str) -> None:
        """复制模板相关文件到输出目录"""
        template_path = os.path.join(self.templates_dir, template_dir)
        
        if not os.path.exists(template_path):
            return
        
        # 复制.sty、.bst等支持文件
        for file in os.listdir(template_path):
            if file.endswith(('.sty', '.bst', '.cls')) and file != "template.tex":
                src = os.path.join(template_path, file)
                dst = os.path.join(output_dir, file)
                shutil.copy(src, dst)
    
    def _sanitize_latex_content(self, content: str) -> str:
        """清理LaTeX内容，处理Unicode和特殊字符问题"""
        # 替换可能导致问题的Unicode转义序列
        content = content.replace('\\u', '\\textbackslash u')
        
        # 替换其他常见问题字符
        replacements = {
            '…': '\\ldots',
            '−': '-',
            '–': '--',
            '—': '---',
            ''': "'",
            ''': "'",
            '"': "``",
            '"': "''",
            '≈': '$\\approx$',
            '≤': '$\\leq$',
            '≥': '$\\geq$',
            '×': '$\\times$',
            '÷': '$\\div$',
            '→': '$\\rightarrow$',
            '↓': '$\\downarrow$',
            '⇒': '$\\Rightarrow$'
        }
        
        for char, replacement in replacements.items():
            content = content.replace(char, replacement)
        
        return content

    def _get_template_for_venue(self, target_venue: str) -> str:
        """
        Get the appropriate LaTeX template for the target venue
        
        Args:
            target_venue: Target conference/journal
            
        Returns:
            Template content as string
        """
        # Get template path
        template_key = target_venue.upper() if target_venue.upper() in self.supported_venues else "general"
        template_dir = self.supported_venues.get(template_key, "general")
        template_path = os.path.join(self.templates_dir, template_dir, "template.tex")
        
        # If template doesn't exist, use general template
        if not os.path.exists(template_path):
            print(f"⚠️ Template {template_path} does not exist, using general template")
            template_path = self._create_general_template()
        
        # Read template content
        try:
            with open(template_path, "r", encoding="utf-8") as f:
                template_content = f.read()
            return template_content
        except Exception as e:
            print(f"⚠️ Error reading template: {e}")
            # Fall back to basic template
            return self._create_general_template()
    
    def _generate_latex_content(self, template: str, metadata: Dict[str, Any], sections: Dict[str, str]) -> str:
        """
        Generate LaTeX content from template, metadata and sections
        
        Args:
            template: LaTeX template content
            metadata: Paper metadata
            sections: Paper sections content
            
        Returns:
            Generated LaTeX content
        """
        # Prepare content for template filling
        paper_content = {
            "abstract": sections.get("abstract", "Abstract not provided."),
            "introduction": sections.get("introduction", ""),
            "related_work": sections.get("related_work", ""),
            "methodology": sections.get("methodology", ""),
            "experiments": sections.get("experiments", ""),
            "results": sections.get("results", ""),
            "discussion": sections.get("discussion", ""),
            "conclusion": sections.get("conclusion", ""),
            "references": sections.get("references", "")
        }
        
        # Fill template
        latex_content = self._fill_template(template, paper_content, metadata)
        
        # Optimize format
        latex_content = self._optimize_latex_format(latex_content)
        
        return latex_content
    
    def _generate_basic_latex(self, paper_content: Dict[str, Any]) -> str:
        """
        Generate basic LaTeX content when template filling fails
        
        Args:
            paper_content: Paper content dictionary
            
        Returns:
            Basic LaTeX content
        """
        metadata = paper_content.get('metadata', {})
        sections = paper_content.get('sections', {})
        
        title = metadata.get('title', 'Untitled Paper')
        
        # Get authors
        authors = metadata.get('authors', ['Anonymous'])
        if isinstance(authors, list):
            authors_str = " \\and ".join(authors)
        else:
            authors_str = str(authors)
        
        # Get abstract
        abstract = sections.get('abstract', 'Abstract not provided.')
        
        # Basic LaTeX template
        basic_template = f"""\\documentclass[11pt,a4paper]{{article}}
\\usepackage[utf8]{{inputenc}}
\\usepackage[T1]{{fontenc}}
\\usepackage{{amsmath,amssymb,amsfonts}}
\\usepackage{{graphicx}}
\\usepackage{{booktabs}}
\\usepackage{{hyperref}}

\\title{{{title}}}
\\author{{{authors_str}}}
\\date{{\\today}}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
{abstract}
\\end{{abstract}}

"""
        
        # Add sections
        section_order = [
            ("introduction", "Introduction"),
            ("related_work", "Related Work"),
            ("methodology", "Methodology"),
            ("experiments", "Experiments"),
            ("results", "Results"),
            ("discussion", "Discussion"),
            ("conclusion", "Conclusion")
        ]
        
        for section_key, section_title in section_order:
            content = sections.get(section_key, "")
            if content:
                basic_template += f"\\section{{{section_title}}}\n{content}\n\n"
        
        # Add references if available
        references = sections.get("references", "")
        if references:
            if "\\begin{thebibliography}" not in references:
                references = self._format_references(references)
            basic_template += f"\n{references}\n"
        
        basic_template += "\\end{document}"
        
        return basic_template
