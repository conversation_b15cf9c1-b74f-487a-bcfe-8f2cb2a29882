"""
直接测试论文生成
使用真实的DeepSeek API而不是模拟模式
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置环境变量确保使用真实API
os.environ["DEEPSEEK_API_KEY"] = "***********************************"
os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
os.environ["MOCK_MODE"] = "false"
os.environ["ENABLE_MOCK_DATA"] = "false"

from paper_generation.brain_paper_writer import BrainPaperWriter
from core.llm_client import LLMClient

def test_direct_paper_generation():
    """
    直接测试论文生成
    使用真实的DeepSeek API生成一篇简短的论文
    """
    print("\n" + "=" * 60)
    print("🚀 直接测试论文生成 (使用真实DeepSeek API)")
    print("=" * 60)
    
    # 创建LLM客户端并确认使用DeepSeek模式
    llm_client = LLMClient(
        model="deepseek-chat", 
        temperature=0.7,
        api_key=os.environ["DEEPSEEK_API_KEY"],
        provider="deepseek"  # 显式指定provider
    )
    
    if not llm_client.deepseek_mode:
        print("❌ LLMClient未使用DeepSeek模式，测试终止")
        print(f"🔍 模式: deepseek_mode={llm_client.deepseek_mode}, ai_scientist_mode={llm_client.ai_scientist_mode}")
        return False
        
    print(f"✅ LLM客户端初始化成功")
    print(f"🔌 使用DeepSeek模式: {llm_client.deepseek_mode}")
    
    try:
        # 创建论文写作器，直接传入LLM客户端
        paper_writer = BrainPaperWriter(
            model="deepseek-chat",
            temperature=0.7,
            llm_client=llm_client  # 直接传递已初始化的客户端
        )
        
        # 确认论文写作器LLM客户端使用DeepSeek模式
        if not paper_writer.llm_client.deepseek_mode:
            print("❌ 论文写作器的LLM客户端未使用DeepSeek模式，测试终止")
            return False
            
        print(f"✅ 论文写作器初始化成功")
        print(f"📝 开始生成简短论文...")
        
        # 设置简短的研究主题
        research_topic = "Efficient Neural Architecture for Low-Power Devices"
        target_venue = "ICML"
        
        # 生成单个段落而不是完整论文，以加快测试速度
        section = paper_writer.generate_single_section(
            "abstract", 
            {"research_topic": research_topic}
        )
        
        print(f"\n✅ 段落生成成功!")
        print(f"📄 内容长度: {len(section)} 字符")
        print(f"📄 内容预览: {section[:150]}...")
        
        # 创建输出目录
        output_dir = "output/test_direct_paper"
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = os.path.join(output_dir, f"generated_section_{timestamp}.json")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                "section": "abstract",
                "topic": research_topic,
                "content": section,
                "timestamp": timestamp,
                "used_deepseek": paper_writer.llm_client.deepseek_mode
            }, f, ensure_ascii=False, indent=2)
        
        print(f"💾 结果已保存至: {output_file}")
        return True
        
    except Exception as e:
        print(f"\n❌ 论文生成失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_direct_paper_generation() 