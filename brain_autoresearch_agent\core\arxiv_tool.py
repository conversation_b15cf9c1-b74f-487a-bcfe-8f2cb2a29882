"""
ArXiv API Tool

用于搜索arXiv的论文数据
"""

import re
import time
import requests
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
import xml.etree.ElementTree as ET

class ArxivTool:
    """用于与ArXiv API交互的工具类"""
    
    def __init__(self):
        """初始化ArXiv工具"""
        self.base_url = "http://export.arxiv.org/api/query"
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def search_papers(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        根据关键词搜索arXiv论文
        
        Args:
            query: 搜索查询
            max_results: 最大返回结果数量
            
        Returns:
            论文信息列表
        """
        params = {
            "search_query": f"all:{query}",
            "start": 0,
            "max_results": max_results
        }
        
        try:
            response = requests.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            # 解析XML响应
            root = ET.fromstring(response.content)
            
            # 命名空间
            namespaces = {
                "atom": "http://www.w3.org/2005/Atom",
                "arxiv": "http://arxiv.org/schemas/atom"
            }
            
            entries = root.findall("./atom:entry", namespaces)
            papers = []
            
            for entry in entries:
                # 基本字段
                title = self._get_text(entry, "./atom:title", namespaces)
                abstract = self._get_text(entry, "./atom:summary", namespaces)
                published = self._get_text(entry, "./atom:published", namespaces)
                
                # 获取arXiv ID
                id_url = self._get_text(entry, "./atom:id", namespaces)
                arxiv_id = id_url.split("/")[-1] if id_url else ""
                
                # 获取DOI (可能不存在)
                doi = ""
                journal_ref = self._get_text(entry, "./arxiv:journal_ref", namespaces)
                
                # 获取作者
                authors = []
                for author_element in entry.findall("./atom:author", namespaces):
                    name = self._get_text(author_element, "./atom:name", namespaces)
                    if name:
                        authors.append({"name": name})
                
                # 获取分类
                categories = []
                for category in entry.findall("./atom:category", namespaces):
                    term = category.get("term")
                    if term:
                        categories.append(term)
                
                # 构建论文信息
                paper = {
                    "title": title,
                    "abstract": abstract,
                    "authors": authors,
                    "year": self._extract_year(published),
                    "venue": "arXiv",
                    "url": id_url,
                    "paperId": arxiv_id,
                    "doi": doi,
                    "publicationDate": published,
                    "categories": categories,
                    "source": "arxiv"
                }
                
                papers.append(paper)
            
            return papers
            
        except Exception as e:
            self.logger.error(f"ArXiv API request failed: {e}")
            return []
    
    def _get_text(self, element, xpath: str, namespaces: Dict[str, str]) -> str:
        """从元素中提取文本"""
        sub_element = element.find(xpath, namespaces)
        return sub_element.text.strip() if sub_element is not None and sub_element.text else ""
    
    def _extract_year(self, date_str: str) -> Optional[int]:
        """从日期字符串提取年份"""
        if not date_str:
            return None
        
        # 尝试ISO格式
        try:
            dt = datetime.fromisoformat(date_str.replace("Z", "+00:00"))
            return dt.year
        except ValueError:
            pass
        
        # 尝试正则表达式
        match = re.search(r'(\d{4})', date_str)
        if match:
            return int(match.group(1))
        
        return None
    
    def mock_search_papers(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        模拟论文搜索（用于测试和开发）
        
        Args:
            query: 搜索查询
            max_results: 最大返回结果数量
            
        Returns:
            模拟的论文信息列表
        """
        mock_papers = []
        for i in range(1, max_results + 1):
            paper = {
                "title": f"ArXiv: {query} Neural Networks Research: Novel Approach {i}",
                "abstract": f"This paper presents a novel approach to {query} using neural networks. "
                          f"Our research shows significant improvements over existing methods.",
                "authors": [
                    {"name": f"ArXiv Author {i}A"},
                    {"name": f"ArXiv Author {i}B"}
                ],
                "year": 2023 - (i % 3),
                "venue": "arXiv",
                "url": f"https://arxiv.org/abs/{2000+i}.{1000+i}",
                "paperId": f"{2000+i}.{1000+i}",
                "publicationDate": f"2023-0{i}-01",
                "categories": ["cs.AI", "cs.LG"],
                "source": "arxiv"
            }
            mock_papers.append(paper)
        
        return mock_papers