"""
快速验证高级论文生成系统
"""

import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_imports():
    """测试所有必要的导入"""
    try:
        print("🔧 测试导入...")
        
        # 测试核心组件
        from core.llm_client import LLMClient
        print("✅ LLMClient 导入成功")
        
        # 测试论文生成组件
        from paper_generation.enhanced_brain_paper_writer import (
            EnhancedBrainPaperWriter, PaperGenerationConfig
        )
        print("✅ EnhancedBrainPaperWriter 导入成功")
        
        # 测试高级论文生成组件
        from paper_generation.advanced_brain_paper_writer import AdvancedBrainPaperWriter
        print("✅ AdvancedBrainPaperWriter 导入成功")
        
        # 测试评审系统
        from paper_generation.review_system.multi_expert_review_system import MultiExpertReviewSystem
        print("✅ MultiExpertReviewSystem 导入成功")
        
        # 测试修订引擎
        from paper_generation.review_system.auto_revision_engine import AutoRevisionEngine
        print("✅ AutoRevisionEngine 导入成功")
        
        # 测试统一工作流
        from paper_generation.unified_paper_workflow import (
            UnifiedPaperGenerationWorkflow, UnifiedWorkflowConfig
        )
        print("✅ UnifiedPaperGenerationWorkflow 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False


def test_configuration():
    """测试配置创建"""
    try:
        print("\n⚙️  测试配置...")
        
        from paper_generation.enhanced_brain_paper_writer import PaperGenerationConfig
        from paper_generation.unified_paper_workflow import UnifiedWorkflowConfig
        
        # 创建论文生成配置
        paper_config = PaperGenerationConfig(
            target_venue="ICML",
            paper_type="research",
            max_review_iterations=3,
            quality_threshold=7.5,
            enable_auto_revision=True,
            enable_multi_expert_review=True,
            latex_output=True
        )
        print("✅ PaperGenerationConfig 创建成功")
        
        # 创建统一工作流配置 - 高级模式
        advanced_config = UnifiedWorkflowConfig(
            enable_workflow_extraction=True,
            workflow_analysis_depth="comprehensive",
            paper_generation_config=paper_config,
            use_advanced_writer=True,  # 关键：使用高级系统
            output_formats=['markdown', 'latex'],
            save_intermediate_results=True,
            output_directory="output/test_advanced"
        )
        print("✅ 高级模式 UnifiedWorkflowConfig 创建成功")
        
        # 创建统一工作流配置 - 对比模式
        enhanced_config = UnifiedWorkflowConfig(
            enable_workflow_extraction=True,
            workflow_analysis_depth="comprehensive", 
            paper_generation_config=paper_config,
            use_advanced_writer=False,  # 使用增强系统
            output_formats=['markdown', 'latex'],
            save_intermediate_results=True,
            output_directory="output/test_enhanced"
        )
        print("✅ 增强模式 UnifiedWorkflowConfig 创建成功")
        
        return advanced_config, enhanced_config
        
    except Exception as e:
        print(f"❌ 配置创建失败: {str(e)}")
        return None, None


def test_system_initialization():
    """测试系统初始化"""
    try:
        print("\n🚀 测试系统初始化...")
        
        from core.llm_client import LLMClient
        from paper_generation.unified_paper_workflow import UnifiedPaperGenerationWorkflow
        
        advanced_config, enhanced_config = test_configuration()
        if not advanced_config or not enhanced_config:
            return False
            
        # 创建LLM客户端
        llm_client = LLMClient()
        print("✅ LLMClient 初始化成功")
        
        # 测试高级系统初始化
        print("🔧 初始化高级系统...")
        advanced_workflow = UnifiedPaperGenerationWorkflow(llm_client, advanced_config)
        print(f"✅ 高级系统初始化成功 - 使用 {type(advanced_workflow.paper_writer).__name__}")
        
        # 测试增强系统初始化
        print("🔧 初始化增强系统...")
        enhanced_workflow = UnifiedPaperGenerationWorkflow(llm_client, enhanced_config)
        print(f"✅ 增强系统初始化成功 - 使用 {type(enhanced_workflow.paper_writer).__name__}")
        
        # 验证正确的系统选择
        assert "Advanced" in type(advanced_workflow.paper_writer).__name__, "高级系统应该使用AdvancedBrainPaperWriter"
        assert "Enhanced" in type(enhanced_workflow.paper_writer).__name__, "增强系统应该使用EnhancedBrainPaperWriter"
        
        print("✅ 系统选择验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统初始化失败: {str(e)}")
        return False


def test_system_capabilities():
    """测试系统能力"""
    try:
        print("\n🧪 测试系统能力...")
        
        from paper_generation.advanced_brain_paper_writer import AdvancedBrainPaperWriter
        from paper_generation.enhanced_brain_paper_writer import EnhancedBrainPaperWriter, PaperGenerationConfig
        from core.llm_client import LLMClient
        
        llm_client = LLMClient()
        config = PaperGenerationConfig()
        
        # 测试高级系统功能
        advanced_writer = AdvancedBrainPaperWriter(llm_client, config)
        
        # 检查高级系统的关键方法
        advanced_methods = [
            '_generate_abstract_detailed_with_review',
            '_generate_introduction_detailed_with_novelty',
            '_generate_methodology_detailed_with_validation',
            'generate_paper_with_comprehensive_quality_control',
            'load_and_integrate_experiment_data'
        ]
        
        for method in advanced_methods:
            if hasattr(advanced_writer, method):
                print(f"✅ 高级系统包含方法: {method}")
            else:
                print(f"⚠️  高级系统缺少方法: {method}")
        
        # 测试增强系统功能
        enhanced_writer = EnhancedBrainPaperWriter(llm_client, config)
        
        # 检查增强系统的关键方法
        enhanced_methods = [
            'generate_paper_with_quality_control',
            '_setup_multi_expert_review',
            '_setup_auto_revision_engine'
        ]
        
        for method in enhanced_methods:
            if hasattr(enhanced_writer, method):
                print(f"✅ 增强系统包含方法: {method}")
            else:
                print(f"⚠️  增强系统缺少方法: {method}")
                
        return True
        
    except Exception as e:
        print(f"❌ 系统能力测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🧠 Brain AutoResearch Agent - 高级系统验证")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("配置测试", lambda: test_configuration() is not None),
        ("初始化测试", test_system_initialization),
        ("能力测试", test_system_capabilities)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 运行 {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {str(e)}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！高级系统已正确集成。")
        print("\n🚀 下一步:")
        print("1. 运行 demo_advanced_paper_generation.py 进行完整演示")
        print("2. 查看 ADVANCED_SYSTEM_TECHNICAL_COMPARISON.md 了解技术详情") 
        print("3. 使用 use_advanced_writer=True 配置启用高级系统")
    else:
        print("⚠️  部分测试失败，请检查系统配置。")


if __name__ == "__main__":
    main()
