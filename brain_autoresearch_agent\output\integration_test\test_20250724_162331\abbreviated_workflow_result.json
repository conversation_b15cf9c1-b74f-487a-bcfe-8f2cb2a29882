{"papers": [{"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 1", "abstract": "This paper presents a novel approach to Efficient Neural Networks for Image Classification. Our research shows significant improvements over existing methods.", "authors": ["Researcher 1A", "Researcher 1B"], "year": 2022, "venue": "Nature Machine Intelligence", "url": "https://example.com/paper1", "citation_count": 95, "source": "semantic_scholar", "paper_id": "mock-paper-id-1", "keywords": null, "doi": null}, {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 2", "abstract": "This paper presents a novel approach to Efficient Neural Networks for Image Classification. Our research shows significant improvements over existing methods.", "authors": ["Researcher 2A", "Researcher 2B"], "year": 2021, "venue": "Conference 2", "url": "https://example.com/paper2", "citation_count": 90, "source": "semantic_scholar", "paper_id": "mock-paper-id-2", "keywords": null, "doi": null}, {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 3", "abstract": "This paper presents a novel approach to Efficient Neural Networks for Image Classification. Our research shows significant improvements over existing methods.", "authors": ["Researcher 3A", "Researcher 3B"], "year": 2023, "venue": "Conference 3", "url": "https://example.com/paper3", "citation_count": 85, "source": "semantic_scholar", "paper_id": "mock-paper-id-3", "keywords": null, "doi": null}, {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 4", "abstract": "This paper presents a novel approach to Efficient Neural Networks for Image Classification. Our research shows significant improvements over existing methods.", "authors": ["Researcher 4A", "Researcher 4B"], "year": 2022, "venue": "Conference 4", "url": "https://example.com/paper4", "citation_count": 80, "source": "semantic_scholar", "paper_id": "mock-paper-id-4", "keywords": null, "doi": null}, {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 5", "abstract": "This paper presents a novel approach to Efficient Neural Networks for Image Classification. Our research shows significant improvements over existing methods.", "authors": ["Researcher 5A", "Researcher 5B"], "year": 2021, "venue": "Conference 5", "url": "https://example.com/paper5", "citation_count": 75, "source": "semantic_scholar", "paper_id": "mock-paper-id-5", "keywords": null, "doi": null}, {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 6", "abstract": "This paper presents a novel approach to Efficient Neural Networks for Image Classification. Our research shows significant improvements over existing methods.", "authors": ["Researcher 6A", "Researcher 6B"], "year": 2023, "venue": "Conference 6", "url": "https://example.com/paper6", "citation_count": 70, "source": "semantic_scholar", "paper_id": "mock-paper-id-6", "keywords": null, "doi": null}, {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 7", "abstract": "This paper presents a novel approach to Efficient Neural Networks for Image Classification. Our research shows significant improvements over existing methods.", "authors": ["Researcher 7A", "Researcher 7B"], "year": 2022, "venue": "Conference 7", "url": "https://example.com/paper7", "citation_count": 65, "source": "semantic_scholar", "paper_id": "mock-paper-id-7", "keywords": null, "doi": null}, {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 8", "abstract": "This paper presents a novel approach to Efficient Neural Networks for Image Classification. Our research shows significant improvements over existing methods.", "authors": ["Researcher 8A", "Researcher 8B"], "year": 2021, "venue": "Conference 8", "url": "https://example.com/paper8", "citation_count": 60, "source": "semantic_scholar", "paper_id": "mock-paper-id-8", "keywords": null, "doi": null}, {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 9", "abstract": "This paper presents a novel approach to Efficient Neural Networks for Image Classification. Our research shows significant improvements over existing methods.", "authors": ["Researcher 9A", "Researcher 9B"], "year": 2023, "venue": "Conference 9", "url": "https://example.com/paper9", "citation_count": 55, "source": "semantic_scholar", "paper_id": "mock-paper-id-9", "keywords": null, "doi": null}, {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 10", "abstract": "This paper presents a novel approach to Efficient Neural Networks for Image Classification. Our research shows significant improvements over existing methods.", "authors": ["Researcher 10A", "Researcher 10B"], "year": 2022, "venue": "Conference 10", "url": "https://example.com/paper10", "citation_count": 50, "source": "semantic_scholar", "paper_id": "mock-paper-id-10", "keywords": null, "doi": null}, {"title": "Creating Deep Convolutional Neural Networks for Image Classification", "abstract": "<jats:p>\n            This lesson provides a beginner-friendly introduction to convolutional neural networks (CNNs) for image classification. The tutorial provides a conceptual understanding of how neural networks work by using Google's Teachable Machine to train a model on paintings from the ArtUK database. This lesson also demonstrates how to use Javascript to embed the model in a live website.\n          </jats:p>", "authors": ["<PERSON><PERSON><PERSON>"], "year": 2023, "venue": "Programming Historian", "url": "https://doi.org/10.46430/phen0108", "citation_count": null, "source": "crossref", "paper_id": "10.46430/phen0108", "keywords": null, "doi": "10.46430/phen0108"}, {"title": "Provably efficient neural network representation for image\n  classification", "abstract": "The state-of-the-art approaches for image classification are based on neural\nnetworks. Mathematically, the task of classifying images is equivalent to\nfinding the function that maps an image to the label it is associated with. To\nrigorously establish the success of neural network methods, we should first\nprove that the function has an efficient neural network representation, and\nthen design provably efficient training algorithms to find such a\nrepresentation. Here, we achieve the first goal based on a set of assumptions\nabout the patterns in the images. The validity of these assumptions is very\nintuitive in many image classification problems, including but not limited to,\nrecognizing handwritten digits.", "authors": ["<PERSON><PERSON>"], "year": 2017, "venue": "arXiv", "url": "http://arxiv.org/abs/1711.04606v1", "citation_count": null, "source": "arxiv", "paper_id": "1711.04606v1", "keywords": null, "doi": ""}, {"title": "Exploring Highly Efficient Compact Neural Networks For Image Classification", "abstract": "", "authors": ["<PERSON><PERSON> Xi<PERSON>", "<PERSON>", "Sun-Yuan Kung"], "year": 2020, "venue": "2020 IEEE International Conference on Image Processing (ICIP)", "url": "https://doi.org/10.1109/icip40778.2020.9191334", "citation_count": null, "source": "crossref", "paper_id": "10.1109/icip40778.2020.9191334", "keywords": null, "doi": "10.1109/icip40778.2020.9191334"}, {"title": "Classifier Ensemble for Efficient Uncertainty Calibration of Deep Neural Networks for Image Classification", "abstract": "", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>"], "year": 2025, "venue": "Proceedings of the 20th International Joint Conference on Computer Vision, Imaging and Computer Graphics Theory and Applications", "url": "https://doi.org/10.5220/0013129000003912", "citation_count": null, "source": "crossref", "paper_id": "10.5220/0013129000003912", "keywords": null, "doi": "10.5220/0013129000003912"}, {"title": "Efficient Convolutional Neural Networks for Multi-Spectral Image Classification", "abstract": "", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "year": 2019, "venue": "2019 International Joint Conference on Neural Networks (IJCNN)", "url": "https://doi.org/10.1109/ijcnn.2019.8851840", "citation_count": null, "source": "crossref", "paper_id": "10.1109/ijcnn.2019.8851840", "keywords": null, "doi": "10.1109/ijcnn.2019.8851840"}, {"title": "Evolving Deep Neural Networks for Efficient Image Classification", "abstract": "", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "year": 2019, "venue": "SSRN Electronic Journal", "url": "https://doi.org/10.2139/ssrn.3576482", "citation_count": null, "source": "crossref", "paper_id": "10.2139/ssrn.3576482", "keywords": null, "doi": "10.2139/ssrn.3576482"}, {"title": "Entanglement Entropy of Target Functions for Image Classification and\n  Convolutional Neural Network", "abstract": "The success of deep convolutional neural network (CNN) in computer vision\nespecially image classification problems requests a new information theory for\nfunction of image, instead of image itself. In this article, after establishing\na deep mathematical connection between image classification problem and quantum\nspin model, we propose to use entanglement entropy, a generalization of\nclassical Boltzmann-Shannon entropy, as a powerful tool to characterize the\ninformation needed for representation of general function of image. We prove\nthat there is a sub-volume-law bound for entanglement entropy of target\nfunctions of reasonable image classification problems. Therefore target\nfunctions of image classification only occupy a small subspace of the whole\nHilbert space. As a result, a neural network with polynomial number of\nparameters is efficient for representation of such target functions of image.\nThe concept of entanglement entropy can also be useful to characterize the\nexpressive power of different neural networks. For example, we show that to\nmaintain the same expressive power, number of channels $D$ in a convolutional\nneural network should scale with the number of convolution layers $n_c$ as\n$D\\sim D_0^{\\frac{1}{n_c}}$. Therefore, deeper CNN with large $n_c$ is more\nefficient than shallow ones.", "authors": ["<PERSON><PERSON><PERSON>"], "year": 2017, "venue": "arXiv", "url": "http://arxiv.org/abs/1710.05520v1", "citation_count": null, "source": "arxiv", "paper_id": "1710.05520v1", "keywords": null, "doi": ""}, {"title": "Genetic Programming-Based Evolutionary Deep Learning for Data-Efficient\n  Image Classification", "abstract": "Data-efficient image classification is a challenging task that aims to solve\nimage classification using small training data. Neural network-based deep\nlearning methods are effective for image classification, but they typically\nrequire large-scale training data and have major limitations such as requiring\nexpertise to design network architectures and having poor interpretability.\nEvolutionary deep learning is a recent hot topic that combines evolutionary\ncomputation with deep learning. However, most evolutionary deep learning\nmethods focus on evolving architectures of neural networks, which still suffer\nfrom limitations such as poor interpretability. To address this, this paper\nproposes a new genetic programming-based evolutionary deep learning approach to\ndata-efficient image classification. The new approach can automatically evolve\nvariable-length models using many important operators from both image and\nclassification domains. It can learn different types of image features from\ncolour or gray-scale images, and construct effective and diverse ensembles for\nimage classification. A flexible multi-layer representation enables the new\napproach to automatically construct shallow or deep models/trees for different\ntasks and perform effective transformations on the input data via multiple\ninternal nodes. The new approach is applied to solve five image classification\ntasks with different training set sizes. The results show that it achieves\nbetter performance in most cases than deep learning methods for data-efficient\nimage classification. A deep analysis shows that the new approach has good\nconvergence and evolves models with high interpretability, different\nlengths/sizes/shapes, and good transferability.", "authors": ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "year": 2022, "venue": "arXiv", "url": "http://arxiv.org/abs/2209.13233v1", "citation_count": null, "source": "arxiv", "paper_id": "2209.13233v1", "keywords": null, "doi": ""}, {"title": "Hybrid Quantum Neural Network Structures for Image Multi-classification", "abstract": "Image classification is a fundamental computer vision problem, and neural\nnetworks offer efficient solutions. With advancing quantum technology, quantum\nneural networks have gained attention. However, they work only for\nlow-dimensional data and demand dimensionality reduction and quantum encoding.\nTwo recent image classification methods have emerged: one employs PCA\ndimensionality reduction and angle encoding, the other integrates QNNs into\nCNNs to boost performance. Despite numerous algorithms, comparing PCA reduction\nwith angle encoding against the latter remains unclear. This study explores\nthese algorithms' performance in multi-class image classification and proposes\nan optimized hybrid quantum neural network suitable for the current\nenvironment. Investigating PCA-based quantum algorithms unveils a barren\nplateau issue for QNNs as categories increase, unsuitable for multi-class in\nthe hybrid setup. Simultaneously, the combined CNN-QNN model partly overcomes\nQNN's multi-class training challenges but lags in accuracy to superior\ntraditional CNN models. Additionally, this work explores transfer learning in\nthe hybrid quantum neural network model. In conclusion, quantum neural networks\nshow promise but require further research and optimization, facing challenges\nahead.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2023, "venue": "arXiv", "url": "http://arxiv.org/abs/2308.16005v1", "citation_count": null, "source": "arxiv", "paper_id": "2308.16005v1", "keywords": null, "doi": ""}, {"title": "Hybrid technique for colour image classification and efficient retrieval based on fuzzy logic and neural networks", "abstract": "", "authors": ["<PERSON><PERSON>", "Siddhivina<PERSON><PERSON>"], "year": 2012, "venue": "The 2012 International Joint Conference on Neural Networks (IJCNN)", "url": "https://doi.org/10.1109/ijcnn.2012.6252587", "citation_count": null, "source": "crossref", "paper_id": "10.1109/ijcnn.2012.6252587", "keywords": null, "doi": "10.1109/ijcnn.2012.6252587"}], "workflows": {"paper_1": {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 1", "datasets": [], "network_architectures": ["Efficient Neural Networks"], "platforms_tools": [], "research_methods": ["Novel Approach"], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": ["Image Classification"]}, "paper_2": {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 2", "datasets": [], "network_architectures": ["Efficient Neural Networks"], "platforms_tools": [], "research_methods": ["Novel Approach 2"], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": ["Image Classification"]}, "paper_3": {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 3", "datasets": [], "network_architectures": ["Efficient Neural Networks"], "platforms_tools": [], "research_methods": ["Novel Approach 3"], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": ["Image Classification"]}, "paper_4": {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 4", "datasets": [], "network_architectures": ["Efficient Neural Networks"], "platforms_tools": [], "research_methods": ["Novel Approach 4"], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": ["Image Classification"]}, "paper_5": {"title": "Efficient Neural Networks for Image Classification Research: Novel Approach 5", "datasets": [], "network_architectures": ["Efficient Neural Networks"], "platforms_tools": [], "research_methods": ["Novel Approach 5"], "evaluation_metrics": [], "brain_inspiration": [], "ai_techniques": ["Image Classification"]}}, "research_topic": "Efficient Neural Networks for Image Classification", "timestamp": "2025-07-24 16:38:45", "total_papers": 20}