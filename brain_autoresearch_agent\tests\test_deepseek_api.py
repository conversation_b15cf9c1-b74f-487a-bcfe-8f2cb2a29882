"""
DeepSeek API连接测试
确保能够正确连接到DeepSeek API并使用真实API生成内容
"""

import os
import sys
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.llm_client import LLMClient
from paper_generation.brain_paper_writer import BrainPaperWriter
from paper_generation.paper_quality_optimizer import PaperQualityOptimizer
from core.unified_api_client import get_unified_client

def setup_deepseek_environment():
    """设置DeepSeek环境，确保使用真实API"""
    print("\n🔧 配置DeepSeek环境...")
    
    # 设置DeepSeek API密钥
    api_key = "***********************************"
    os.environ["DEEPSEEK_API_KEY"] = api_key
    os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
    
    # 禁用模拟模式，强制使用真实API
    os.environ["MOCK_MODE"] = "false"
    os.environ["ENABLE_MOCK_DATA"] = "false"
    
    print(f"✅ 环境变量设置完成:")
    print(f"  - DEEPSEEK_API_KEY: {api_key[:8]}...{api_key[-4:]}")
    print(f"  - DEEPSEEK_BASE_URL: {os.environ.get('DEEPSEEK_BASE_URL')}")
    print(f"  - MOCK_MODE: {os.environ.get('MOCK_MODE')}")
    
    return api_key

def test_deepseek_connection():
    """测试DeepSeek API的基本连接"""
    print("\n🔄 测试DeepSeek API基本连接...")
    
    try:
        # 创建直接使用DeepSeek API的LLM客户端
        client = LLMClient(
            provider="deepseek",
            model="deepseek-chat",
            temperature=0.7,
            api_key=os.environ.get("DEEPSEEK_API_KEY")
        )
        
        # 验证客户端是否使用DeepSeek模式
        if not getattr(client, 'deepseek_mode', False):
            print("❌ 客户端没有使用DeepSeek模式")
            return False
            
        # 发送一个简单的测试查询
        response = client.get_text_response(
            "Please respond with exactly 'DeepSeek API test successful' to verify connectivity."
        )
        
        print(f"✅ API连接成功!")
        print(f"📄 响应内容: {response}")
        
        # 检查响应内容是否包含预期文本
        return response and "test successful" in response.lower()
        
    except Exception as e:
        print(f"❌ API连接失败: {e}")
        return False

def test_paper_generation_with_deepseek():
    """测试使用DeepSeek生成论文内容"""
    print("\n📝 测试使用DeepSeek生成论文内容...")
    
    try:
        # 创建使用DeepSeek的论文生成器
        from paper_generation.brain_paper_writer import BrainPaperWriter
        
        # 初始化时不需要指定model_name参数
        writer = BrainPaperWriter()
        
        # 测试生成一个简单的摘要段落
        section_content = writer.generate_single_section(
            "abstract", 
            {"research_topic": "Brain-Inspired Deep Learning Architectures"}
        )
        
        # 验证内容生成
        success = section_content and len(section_content) > 0 and "处理失败" not in section_content
        if success:
            print("✅ 段落生成成功!")
            print(f"⏱️ 生成时间: {0.00:.2f} 秒")
            print(f"📄 内容长度: {len(section_content)} 字符")
            print(f"📄 内容预览: {section_content[:100]}...")
        else:
            print(f"❌ 段落生成失败: {section_content}")
            
        return success
        
    except Exception as e:
        print(f"❌ 论文生成测试失败: {e}")
        return False

def test_deepseek_with_unified_client():
    """测试统一API客户端对DeepSeek的支持"""
    print("\n🔌 测试统一API客户端对DeepSeek的支持...")
    
    try:
        from core.unified_api_client import get_unified_client
        
        # 获取统一API客户端
        client = get_unified_client()
        
        # 测试使用统一客户端发送DeepSeek请求
        response = client.get_text_response(
            prompt="Provide a one-sentence definition of brain-inspired computing.",
            model_type="chat",  # 使用聊天模型
            temperature=0.7
        )
        
        # 检查响应是否成功
        if hasattr(response, 'success') and response.success:
            print("✅ 统一客户端响应成功:")
            print(f"📄 响应内容: {response.content[:100]}...")
            return True
        else:
            print(f"❌ 统一客户端响应失败: {response}")
            return False
            
    except Exception as e:
        print(f"❌ 统一客户端测试失败: {e}")
        return False

def main():
    """运行所有DeepSeek API测试"""
    print("=" * 60)
    print("🚀 DeepSeek API 连接和功能测试")
    print("=" * 60)
    
    # 设置环境
    setup_deepseek_environment()
    
    # 运行测试
    tests = [
        ("基本API连接", test_deepseek_connection),
        ("论文生成", test_paper_generation_with_deepseek),
        ("统一API客户端", test_deepseek_with_unified_client)
    ]
    
    results = {}
    for name, test_func in tests:
        print(f"\n{'-' * 40}")
        print(f"🧪 运行测试: {name}")
        try:
            start_time = time.time()
            success = test_func()
            end_time = time.time()
            
            results[name] = {
                "success": success,
                "time": end_time - start_time
            }
            
            if success:
                print(f"✅ 测试通过: {name} ({end_time - start_time:.2f}秒)")
            else:
                print(f"❌ 测试失败: {name} ({end_time - start_time:.2f}秒)")
                
        except Exception as e:
            print(f"❌ 测试出错: {name} - {e}")
            results[name] = {"success": False, "error": str(e)}
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果摘要")
    
    success_count = sum(1 for r in results.values() if r.get("success", False))
    print(f"✅ 通过: {success_count}/{len(tests)}")
    
    for name, result in results.items():
        status = "✅" if result.get("success", False) else "❌"
        print(f"{status} {name}")
    
    print("=" * 60)
    
    if success_count == len(tests):
        print("🎉 所有测试通过! DeepSeek API工作正常。")
    else:
        print("⚠️ 部分测试失败。请检查错误信息和API配置。")

if __name__ == "__main__":
    main() 