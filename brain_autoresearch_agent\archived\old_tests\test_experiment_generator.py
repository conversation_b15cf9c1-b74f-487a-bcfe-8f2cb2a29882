"""
测试实验代码生成器
验证基于AI Scientist方法论的实验代码自动生成功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.experiment_code_generator import ExperimentCodeGenerator, ExperimentSpecification
from core.llm_client import HybridModelClient
import json

def test_experiment_code_generator():
    """测试实验代码生成器"""
    print("🧪 测试实验代码生成器")
    print("=" * 60)
    
    try:
        # 初始化混合模型客户端
        print("🔧 初始化混合模型客户端...")
        model_client = HybridModelClient()
        
        # 创建实验代码生成器
        generator = ExperimentCodeGenerator(model_client)
        print("✅ 实验代码生成器创建成功")
        
        # 测试研究想法
        research_ideas = [
            "A novel attention-based neural network that combines self-attention with residual connections for improved image classification",
            "Brain-inspired spiking neural networks for energy-efficient pattern recognition",
            "Multi-modal fusion architecture integrating visual and textual features for enhanced understanding"
        ]
        
        for i, research_idea in enumerate(research_ideas, 1):
            print(f"\n🔬 测试想法 {i}: {research_idea[:50]}...")
            
            try:
                # 生成实验规格
                print("📋 生成实验规格...")
                spec = generator.generate_experiment_specification(research_idea, "ICML")
                print(f"✅ 实验规格生成成功:")
                print(f"   📝 实验名称: {spec.name}")
                print(f"   🎯 实验标题: {spec.title}")
                print(f"   💡 研究假设: {spec.hypothesis[:100]}...")
                print(f"   🛠️ 使用框架: {spec.framework}")
                print(f"   📊 实验类型: {spec.experiment_type}")
                print(f"   💾 数据集: {spec.dataset}")
                print(f"   📈 评估指标: {', '.join(spec.metrics)}")
                
                # 生成实验代码
                output_dir = f"./test_experiments/experiment_{i}"
                print(f"🔧 生成实验代码到 {output_dir}...")
                files_created = generator.generate_complete_experiment(spec, output_dir)
                
                print("✅ 实验代码生成完成！")
                print("📁 生成的文件:")
                for file_name, file_path in files_created.items():
                    file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    print(f"   📄 {file_name}: {file_size:,} 字节")
                
                # 验证生成的文件内容
                main_script_path = files_created.get('main_experiment.py')
                if main_script_path and os.path.exists(main_script_path):
                    with open(main_script_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        print(f"   🔍 主脚本内容验证: {len(content):,} 字符")
                        
                        # 检查关键组件
                        components = ['import torch', 'class', 'def train_model', 'def evaluate', 'def main']
                        for component in components:
                            if component in content:
                                print(f"   ✅ 包含 {component}")
                            else:
                                print(f"   ⚠️ 缺少 {component}")
                
                print(f"🎉 想法 {i} 测试完成")
                
            except Exception as e:
                print(f"❌ 想法 {i} 测试失败: {str(e)}")
                continue
        
        print("\n" + "=" * 60)
        print("📊 实验代码生成器测试总结")
        print("✅ 基于AI Scientist方法论的实验代码自动生成")
        print("✅ 支持分类和回归任务")
        print("✅ 完整的PyTorch实验流程")
        print("✅ 自动基准方法对比")
        print("✅ 专业级代码结构")
        
    except Exception as e:
        print(f"❌ 实验代码生成器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_manual_specification():
    """测试手动创建实验规格"""
    print("\n🔧 测试手动实验规格创建...")
    
    try:
        # 创建手动实验规格
        spec = ExperimentSpecification(
            name="attention_classification",
            title="Attention-Enhanced Neural Network for Classification",
            hypothesis="Adding attention mechanisms to neural networks will improve classification performance",
            framework="pytorch",
            experiment_type="classification",
            dataset="iris",
            metrics=["accuracy", "precision", "recall", "f1"],
            baseline_methods=["logistic_regression", "mlp_baseline"],
            proposed_method="Attention-Enhanced MLP",
            code_requirements=["PyTorch implementation", "Attention mechanism", "Cross-validation"]
        )
        
        print("✅ 手动实验规格创建成功")
        
        # 初始化生成器
        model_client = HybridModelClient()
        generator = ExperimentCodeGenerator(model_client)
        
        # 生成代码
        output_dir = "./test_experiments/manual_experiment"
        files_created = generator.generate_complete_experiment(spec, output_dir)
        
        print("✅ 手动实验代码生成成功")
        print("📁 生成的文件:")
        for file_name, file_path in files_created.items():
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"   📄 {file_name}: {file_size:,} 字节")
        
    except Exception as e:
        print(f"❌ 手动实验规格测试失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始测试实验代码生成系统")
    print("基于AI Scientist方法论的自动实验代码生成")
    print("=" * 60)
    
    # 测试自动生成
    test_experiment_code_generator()
    
    # 测试手动创建
    test_manual_specification()
    
    print("\n🎯 实验代码生成系统测试完成！")
