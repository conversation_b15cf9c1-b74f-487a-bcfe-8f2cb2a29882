"""
混合文献搜索工具
集成Semantic Scholar和arXiv API
"""

from typing import Dict, List, Any, Optional
from .semantic_scholar_tool import SemanticScholarTool
from .arxiv_tool import ArxivTool
from .crossref_tool import CrossrefTool


class HybridLiteratureTool:
    """
    混合文献搜索工具，自动选择最佳数据源
    现在支持 Semantic Scholar、arXiv 和 Crossref API
    """
    
    def __init__(self, max_results: int = 10, email: str = None):
        self.max_results = max_results
        self.per_source = max_results // 3  # 三个数据源平分
        
        # 初始化工具类 - 注意这里不再传递max_results参数
        self.semantic_scholar = SemanticScholarTool()
        self.arxiv = ArxivTool()
        self.crossref = CrossrefTool()
    
    def search_papers(self, query: str, max_results: int = 10) -> List[Dict[str, Any]]:
        """
        使用多个数据源搜索论文，优雅降级处理API限制
        现在支持三个数据源：Semantic Scholar、arXiv 和 Crossref
        """
        all_papers = []
        ss_success = False
        arxiv_success = False
        crossref_success = False
        
        per_source = max_results // 3
        
        # 尝试Semantic Scholar
        try:
            ss_papers = self.semantic_scholar.search_papers(query, per_source)
            all_papers.extend(ss_papers)
            ss_success = True
            print(f"✅ Semantic Scholar: {len(ss_papers)} 篇论文")
        except Exception as e:
            error_msg = str(e)
            if "429" in error_msg or "rate limit" in error_msg.lower():
                print(f"❌ Semantic Scholar API请求失败: 429")
            else:
                print(f"⚠️ Semantic Scholar失败: {e}")
            # 尝试mock
            try:
                ss_papers = self.semantic_scholar.mock_search_papers(query, per_source)
                all_papers.extend(ss_papers)
                print(f"✅ Semantic Scholar (模拟数据): {len(ss_papers)} 篇论文")
                ss_success = True
            except Exception as mock_e:
                print(f"⚠️ 生成模拟数据也失败了: {mock_e}")
        
        # 尝试Crossref
        try:
            crossref_papers = self.crossref.search_papers(query, per_source)
            all_papers.extend(crossref_papers)
            crossref_success = True
            print(f"✅ Crossref: {len(crossref_papers)} 篇论文")
        except Exception as e:
            print(f"⚠️ Crossref失败: {e}")
            # 尝试mock
            try:
                crossref_papers = self.crossref.mock_search_papers(query, per_source)
                all_papers.extend(crossref_papers)
                print(f"✅ Crossref (模拟数据): {len(crossref_papers)} 篇论文")
                crossref_success = True
            except Exception as mock_e:
                print(f"⚠️ 生成模拟数据也失败了: {mock_e}")
        
        # 尝试arXiv
        remaining_quota = max_results - len(all_papers)
        arxiv_max = max(per_source, remaining_quota)
        try:
            arxiv_papers = self.arxiv.search_papers(query, arxiv_max)
            all_papers.extend(arxiv_papers)
            arxiv_success = True
            print(f"✅ arXiv: {len(arxiv_papers)} 篇论文")
        except Exception as e:
            print(f"⚠️ arXiv失败: {e}")
            # 尝试mock
            try:
                arxiv_papers = self.arxiv.mock_search_papers(query, arxiv_max)
                all_papers.extend(arxiv_papers)
                print(f"✅ arXiv (模拟数据): {len(arxiv_papers)} 篇论文")
                arxiv_success = True
            except Exception as mock_e:
                print(f"⚠️ 生成模拟数据也失败了: {mock_e}")
        
        # 只有所有API和所有mock都失败时才用placeholder
        if not ss_success and not arxiv_success and not crossref_success and len(all_papers) == 0:
            print("⚠️ 所有API和模拟都失败，使用基础查询结果")
            return self._create_placeholder_papers(query, max_results)
        return all_papers
    
    def _create_placeholder_papers(self, query: str, count: int) -> List[Dict[str, Any]]:
        """当API都失败时，创建基于查询的占位符论文"""
        placeholders = []
        
        # 基于查询生成相关的论文标题模板
        base_titles = [
            f"A Survey on {query}",
            f"Deep Learning Approaches for {query}",
            f"Novel Methods in {query}",
            f"Recent Advances in {query}",
            f"{query}: State of the Art Review",
            f"Efficient {query} Algorithms",
            f"{query} for Real-world Applications",
            f"Comparative Study of {query} Methods"
        ]
        
        for i in range(min(count, len(base_titles))):
            placeholder = {
                'title': base_titles[i],
                'abstract': f"This paper presents research on {query} with focus on novel methodologies and applications.",
                'authors': [{'name': 'Research Team'}],
                'year': 2023,
                'citationCount': 10 + i,
                'url': f"https://example.org/placeholder_{i}",
                'venue': 'Academic Conference',
                'source': 'placeholder'
            }
            placeholders.append(placeholder)
        
        return placeholders
    
    def _deduplicate_papers(self, papers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """简单去重"""
        seen_titles = set()
        unique_papers = []
        
        for paper in papers:
            title = paper.get('title', '').lower().strip()
            if title and title not in seen_titles:
                seen_titles.add(title)
                unique_papers.append(paper)
        
        return unique_papers