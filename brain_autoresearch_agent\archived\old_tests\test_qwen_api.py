"""
Qwen API Test Script
Test the availability and functionality of Qwen API for visual model integration
"""

import os
from openai import OpenAI


def test_qwen_api():
    """Test Qwen API connectivity and functionality"""
    print("🧪 Testing Qwen API connectivity...")
    
    try:
        # Configure Qwen API
        api_key = "sk-f8559ea97bad4d638416d20db63bc643"
        os.environ["DASHSCOPE_API_KEY"] = api_key
        
        client = OpenAI(
            api_key=api_key,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        
        print(f"🔑 API Key configured: {api_key[:8]}...{api_key[-4:]}")
        
        # Test 1: Basic text completion
        print(f"\\n📝 Test 1: Basic text completion")
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {'role': 'system', 'content': 'You are a helpful research assistant.'},
                {'role': 'user', 'content': 'Explain neural plasticity in one sentence.'}
            ]
        )
        
        response = completion.choices[0].message.content
        print(f"✅ Basic completion: OK")
        print(f"📄 Response: {response[:100]}...")
        
        # Test 2: Check available models
        print(f"\\n🔍 Test 2: Model availability check")
        try:
            # Test qwen-max (if available)
            completion_max = client.chat.completions.create(
                model="qwen-max",
                messages=[
                    {'role': 'system', 'content': 'You are a helpful assistant.'},
                    {'role': 'user', 'content': 'Hello'}
                ]
            )
            print(f"✅ qwen-max: Available")
        except Exception as e:
            print(f"❌ qwen-max: Not available - {e}")
        
        # Test 3: Check for vision capabilities
        print(f"\\n👁️ Test 3: Vision model check")
        try:
            # Try to find vision model
            vision_models = ["qwen-vl-plus", "qwen-vl-max", "qvq-max-latest"]
            vision_available = None
            
            for model in vision_models:
                try:
                    # Simple test without image
                    completion_vision = client.chat.completions.create(
                        model=model,
                        messages=[
                            {'role': 'user', 'content': 'Describe the importance of visual layout in academic papers.'}
                        ]
                    )
                    print(f"✅ {model}: Available")
                    vision_available = model
                    break
                except Exception as e:
                    print(f"❌ {model}: Not available - {str(e)[:50]}...")
            
            if vision_available:
                print(f"🎯 Best vision model: {vision_available}")
            else:
                print(f"⚠️ No vision models available")
                
        except Exception as e:
            print(f"❌ Vision model test failed: {e}")
        
        # Test 4: Academic writing capability
        print(f"\\n📚 Test 4: Academic writing capability")
        academic_prompt = """
        Write a brief methodology section for a paper about neural plasticity-inspired deep learning.
        Include: 1) Approach overview, 2) Key algorithms, 3) Evaluation metrics.
        Keep it under 150 words and maintain academic tone.
        """
        
        completion_academic = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {'role': 'system', 'content': 'You are an expert academic writer specializing in AI and neuroscience.'},
                {'role': 'user', 'content': academic_prompt}
            ]
        )
        
        academic_response = completion_academic.choices[0].message.content
        print(f"✅ Academic writing: OK")
        print(f"📊 Response length: {len(academic_response)} characters")
        
        if len(academic_response) > 100:
            print(f"\\n📄 Academic writing sample:")
            print("-" * 40)
            print(academic_response[:300] + "..." if len(academic_response) > 300 else academic_response)
            print("-" * 40)
        
        print(f"\\n🎉 Qwen API test completed successfully!")
        print(f"✅ API connectivity: Working")
        print(f"✅ Text generation: Functional")
        print(f"✅ Academic writing: Capable")
        
        return True
        
    except Exception as e:
        print(f"\\n❌ Qwen API test failed: {e}")
        print(f"🔍 Error details:")
        import traceback
        traceback.print_exc()
        return False


def test_model_comparison():
    """Compare Qwen vs DeepSeek for different tasks"""
    print(f"\\n🔬 Model Comparison Test")
    print("=" * 50)
    
    # Test prompt
    test_prompt = "Explain the concept of synaptic plasticity and its applications in artificial neural networks."
    
    # Test DeepSeek
    print(f"🧠 Testing DeepSeek...")
    try:
        from openai import OpenAI as DeepSeekClient
        
        deepseek_client = DeepSeekClient(
            api_key="***********************************",
            base_url="https://api.deepseek.com"
        )
        
        deepseek_response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=[
                {'role': 'system', 'content': 'You are a helpful research assistant.'},
                {'role': 'user', 'content': test_prompt}
            ]
        )
        
        deepseek_text = deepseek_response.choices[0].message.content
        print(f"✅ DeepSeek response: {len(deepseek_text)} chars")
        
    except Exception as e:
        print(f"❌ DeepSeek test failed: {e}")
        deepseek_text = "Error"
    
    # Test Qwen
    print(f"🌟 Testing Qwen...")
    try:
        qwen_client = OpenAI(
            api_key="sk-f8559ea97bad4d638416d20db63bc643",
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
        )
        
        qwen_response = qwen_client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {'role': 'system', 'content': 'You are a helpful research assistant.'},
                {'role': 'user', 'content': test_prompt}
            ]
        )
        
        qwen_text = qwen_response.choices[0].message.content
        print(f"✅ Qwen response: {len(qwen_text)} chars")
        
    except Exception as e:
        print(f"❌ Qwen test failed: {e}")
        qwen_text = "Error"
    
    # Compare results
    if deepseek_text != "Error" and qwen_text != "Error":
        print(f"\\n📊 Comparison Results:")
        print(f"🧠 DeepSeek length: {len(deepseek_text)} chars")
        print(f"🌟 Qwen length: {len(qwen_text)} chars")
        
        # Simple quality indicators
        deepseek_academic = sum(1 for word in ['research', 'study', 'analysis', 'methodology'] if word.lower() in deepseek_text.lower())
        qwen_academic = sum(1 for word in ['research', 'study', 'analysis', 'methodology'] if word.lower() in qwen_text.lower())
        
        print(f"📚 Academic terms - DeepSeek: {deepseek_academic}, Qwen: {qwen_academic}")
        
        if qwen_academic >= deepseek_academic:
            print(f"🏆 Qwen shows comparable/better academic writing")
        else:
            print(f"🏆 DeepSeek shows better academic writing")


if __name__ == "__main__":
    success = test_qwen_api()
    
    if success:
        test_model_comparison()
        print(f"\\n💡 Next steps:")
        print(f"  1. Integrate Qwen for visual layout optimization")
        print(f"  2. Use DeepSeek for core text generation")
        print(f"  3. Implement hybrid model approach")
    else:
        print(f"\\n⚠️ Qwen API not available, will use DeepSeek only")
