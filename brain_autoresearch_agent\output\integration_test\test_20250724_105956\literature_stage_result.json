{"papers": [{"title": "Brain-Inspired Neural Networks For Efficient Learning Research: Novel Approach 1", "abstract": "This paper presents a novel brain-inspired approach to Brain-Inspired Neural Networks for Efficient Learning. Our method demonstrates significant improvements over existing approaches through bio-plausible mechanisms and efficient learning algorithms.", "authors": ["Researcher 1A", "Researcher 1B"], "year": 2023, "venue": "Nature Machine Intelligence", "url": "https://example.com/paper_1", "citation_count": 50, "source": "semantic_scholar", "paper_id": "mock_paper_1", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks For Efficient Learning Research: Novel Approach 2", "abstract": "This paper presents a novel brain-inspired approach to Brain-Inspired Neural Networks for Efficient Learning. Our method demonstrates significant improvements over existing approaches through bio-plausible mechanisms and efficient learning algorithms.", "authors": ["Researcher 2A", "Researcher 2B"], "year": 2022, "venue": "Conference 1", "url": "https://example.com/paper_2", "citation_count": 40, "source": "semantic_scholar", "paper_id": "mock_paper_2", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks For Efficient Learning Research: Novel Approach 3", "abstract": "This paper presents a novel brain-inspired approach to Brain-Inspired Neural Networks for Efficient Learning. Our method demonstrates significant improvements over existing approaches through bio-plausible mechanisms and efficient learning algorithms.", "authors": ["Researcher 3A", "Researcher 3B"], "year": 2021, "venue": "Conference 2", "url": "https://example.com/paper_3", "citation_count": 30, "source": "semantic_scholar", "paper_id": "mock_paper_3", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks For Efficient Learning Research: Novel Approach 4", "abstract": "This paper presents a novel brain-inspired approach to Brain-Inspired Neural Networks for Efficient Learning. Our method demonstrates significant improvements over existing approaches through bio-plausible mechanisms and efficient learning algorithms.", "authors": ["Researcher 4A", "Researcher 4B"], "year": 2023, "venue": "Conference 3", "url": "https://example.com/paper_4", "citation_count": 20, "source": "semantic_scholar", "paper_id": "mock_paper_4", "keywords": null, "doi": null}, {"title": "Brain-Inspired Neural Networks For Efficient Learning Research: Novel Approach 5", "abstract": "This paper presents a novel brain-inspired approach to Brain-Inspired Neural Networks for Efficient Learning. Our method demonstrates significant improvements over existing approaches through bio-plausible mechanisms and efficient learning algorithms.", "authors": ["Researcher 5A", "Researcher 5B"], "year": 2022, "venue": "Conference 4", "url": "https://example.com/paper_5", "citation_count": 10, "source": "semantic_scholar", "paper_id": "mock_paper_5", "keywords": null, "doi": null}, {"title": "Learning Rate Optimization for Deep Neural Networks Using Lipschitz\n  Bandits", "abstract": "Learning rate is a crucial parameter in training of neural networks. A\nproperly tuned learning rate leads to faster training and higher test accuracy.\nIn this paper, we propose a Lipschitz bandit-driven approach for tuning the\nlearning rate of neural networks. The proposed approach is compared with the\npopular HyperOpt technique used extensively for hyperparameter optimization and\nthe recently developed bandit-based algorithm BLiE. The results for multiple\nneural network architectures indicate that our method finds a better learning\nrate using a) fewer evaluations and b) lesser number of epochs per evaluation,\nwhen compared to both HyperOpt and BLiE. Thus, the proposed approach enables\nmore efficient training of neural networks, leading to lower training time and\nlesser computational cost.", "authors": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "year": 2024, "venue": "arXiv", "url": "http://arxiv.org/abs/2409.09783v1", "citation_count": 0, "source": "arxiv", "paper_id": "2409.09783v1", "keywords": null, "doi": null}, {"title": "A brain-inspired network architecture for cost-efficient object recognition in shallow hierarchical neural networks", "abstract": "Academic paper published in Neural Networks. Full text available via DOI.", "authors": ["Youngjin Park", "<PERSON><PERSON><PERSON> Baek", "Se-<PERSON><PERSON>"], "year": 2021, "venue": "Neural Networks", "url": "https://doi.org/10.1016/j.neunet.2020.11.013", "citation_count": 12, "source": "crossref", "paper_id": "10.1016/j.neunet.2020.11.013", "keywords": null, "doi": "10.1016/j.neunet.2020.11.013"}, {"title": "Brain-Inspired Memristive Neural Networks for Unsupervised Learning", "abstract": "Academic paper published in Handbook of Memristor Networks. Full text available via DOI.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2019, "venue": "Handbook of Memristor Networks", "url": "https://doi.org/10.1007/978-3-319-76375-0_17", "citation_count": 2, "source": "crossref", "paper_id": "10.1007/978-3-319-76375-0_17", "keywords": null, "doi": "10.1007/978-3-319-76375-0_17"}, {"title": "Brain-inspired Multimodal Learning Based on Neural Networks", "abstract": "<jats:p> Modern computational models have leveraged biological advances in human brain research. This study addresses the problem of multimodal learning with the help of brain-inspired models. Specifically, a unified multimodal learning architecture is proposed based on deep neural networks, which are inspired by the biology of the visual cortex of the human brain. This unified framework is validated by two practical multimodal learning tasks: image captioning, involving visual and natural language signals, and visual-haptic fusion, involving haptic and visual signals. Extensive experiments are conducted under the framework, and competitive results are achieved. </jats:p>", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "year": 2018, "venue": "Brain Science Advances", "url": "https://doi.org/10.26599/bsa.2018.9050004", "citation_count": 9, "source": "crossref", "paper_id": "10.26599/bsa.2018.9050004", "keywords": null, "doi": "10.26599/bsa.2018.9050004"}, {"title": "Effective and Efficient Computation with Multiple-timescale Spiking\n  Recurrent Neural Networks", "abstract": "The emergence of brain-inspired neuromorphic computing as a paradigm for edge\nAI is motivating the search for high-performance and efficient spiking neural\nnetworks to run on this hardware. However, compared to classical neural\nnetworks in deep learning, current spiking neural networks lack competitive\nperformance in compelling areas. Here, for sequential and streaming tasks, we\ndemonstrate how a novel type of adaptive spiking recurrent neural network\n(SRNN) is able to achieve state-of-the-art performance compared to other\nspiking neural networks and almost reach or exceed the performance of classical\nrecurrent neural networks (RNNs) while exhibiting sparse activity. From this,\nwe calculate a $>$100x energy improvement for our SRNNs over classical RNNs on\nthe harder tasks. To achieve this, we model standard and adaptive\nmultiple-timescale spiking neurons as self-recurrent neural units, and leverage\nsurrogate gradients and auto-differentiation in the PyTorch Deep Learning\nframework to efficiently implement backpropagation-through-time, including\nlearning of the important spiking neuron parameters to adapt our spiking\nneurons to the tasks.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "year": 2020, "venue": "arXiv", "url": "http://arxiv.org/abs/2005.11633v2", "citation_count": 0, "source": "arxiv", "paper_id": "2005.11633v2", "keywords": null, "doi": null}, {"title": "Brain-Inspired Spiking Neural Networks", "abstract": "<jats:p>Brain is a very efficient computing system. It performs very complex tasks while occupying about 2 liters of volume and consuming very little energy. The computation tasks are performed by special cells in the brain called neurons. They compute using electrical pulses and exchange information between them through chemicals called neurotransmitters. With this as inspiration, there are several compute models which exist today trying to exploit the inherent efficiencies demonstrated by nature. The compute models representing spiking neural networks (SNNs) are biologically plausible, hence are used to study and understand the workings of brain and nervous system. More importantly, they are used to solve a wide variety of problems in the field of artificial intelligence (AI). They are uniquely suited to model temporal and spatio-temporal data paradigms. This chapter explores the fundamental concepts of SNNs, few of the popular neuron models, how the information is represented, learning methodologies, and state of the art platforms for implementing and evaluating SNNs along with a discussion on their applications and broader role in the field of AI and data networks.</jats:p>", "authors": ["<PERSON><PERSON><PERSON>"], "year": 2021, "venue": "Biomimetics", "url": "https://doi.org/10.5772/intechopen.93435", "citation_count": 4, "source": "crossref", "paper_id": "10.5772/intechopen.93435", "keywords": null, "doi": "10.5772/intechopen.93435"}, {"title": "Energy-efficient Spiking Neural Network Equalization for IM/DD Systems\n  with Optimized Neural Encoding", "abstract": "We propose an energy-efficient equalizer for IM/DD systems based on spiking\nneural networks. We optimize a neural spike encoding that boosts the\nequalizer's performance while decreasing energy consumption.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>"], "year": 2023, "venue": "arXiv", "url": "http://arxiv.org/abs/2312.12909v1", "citation_count": 0, "source": "arxiv", "paper_id": "2312.12909v1", "keywords": null, "doi": null}, {"title": "Exploring the Imposition of Synaptic Precision Restrictions For\n  Evolutionary Synthesis of Deep Neural Networks", "abstract": "A key contributing factor to incredible success of deep neural networks has\nbeen the significant rise on massively parallel computing devices allowing\nresearchers to greatly increase the size and depth of deep neural networks,\nleading to significant improvements in modeling accuracy. Although deeper,\nlarger, or complex deep neural networks have shown considerable promise, the\ncomputational complexity of such networks is a major barrier to utilization in\nresource-starved scenarios. We explore the synaptogenesis of deep neural\nnetworks in the formation of efficient deep neural network architectures within\nan evolutionary deep intelligence framework, where a probabilistic generative\nmodeling strategy is introduced to stochastically synthesize increasingly\nefficient yet effective offspring deep neural networks over generations,\nmimicking evolutionary processes such as heredity, random mutation, and natural\nselection in a probabilistic manner. In this study, we primarily explore the\nimposition of synaptic precision restrictions and its impact on the\nevolutionary synthesis of deep neural networks to synthesize more efficient\nnetwork architectures tailored for resource-starved scenarios. Experimental\nresults show significant improvements in synaptic efficiency (~10X decrease for\nGoogLeNet-based DetectNet) and inference speed (>5X increase for\nGoogLeNet-based DetectNet) while preserving modeling accuracy.", "authors": ["<PERSON>", "<PERSON>", "<PERSON>"], "year": 2017, "venue": "arXiv", "url": "http://arxiv.org/abs/1707.00095v1", "citation_count": 0, "source": "arxiv", "paper_id": "1707.00095v1", "keywords": null, "doi": null}, {"title": "Convex Formulation of Overparameterized Deep Neural Networks", "abstract": "Analysis of over-parameterized neural networks has drawn significant\nattention in recentyears. It was shown that such systems behave like convex\nsystems under various restrictedsettings, such as for two-level neural\nnetworks, and when learning is only restricted locally inthe so-called neural\ntangent kernel space around specialized initializations. However, there areno\ntheoretical techniques that can analyze fully trained deep neural networks\nencountered inpractice. This paper solves this fundamental problem by\ninvestigating such overparameterizeddeep neural networks when fully trained. We\ngeneralize a new technique called neural feature repopulation, originally\nintroduced in (<PERSON> et al., 2019a) for two-level neural networks, to analyze\ndeep neural networks. It is shown that under suitable representations,\noverparameterized deep neural networks are inherently convex, and when\noptimized, the system can learn effective features suitable for the underlying\nlearning task under mild conditions. This new analysis is consistent with\nempirical observations that deep neural networks are capable of learning\nefficient feature representations. Therefore, the highly unexpected result of\nthis paper can satisfactorily explain the practical success of deep neural\nnetworks. Empirical studies confirm that predictions of our theory are\nconsistent with results observed in practice.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "year": 2019, "venue": "arXiv", "url": "http://arxiv.org/abs/1911.07626v1", "citation_count": 0, "source": "arxiv", "paper_id": "1911.07626v1", "keywords": null, "doi": null}, {"title": "Assessing Intelligence in Artificial Neural Networks", "abstract": "The purpose of this work was to develop of metrics to assess network\narchitectures that balance neural network size and task performance. To this\nend, the concept of neural efficiency is introduced to measure neural layer\nutilization, and a second metric called artificial intelligence quotient (aIQ)\nwas created to balance neural network performance and neural network\nefficiency. To study aIQ and neural efficiency, two simple neural networks were\ntrained on MNIST: a fully connected network (LeNet-300-100) and a convolutional\nneural network (LeNet-5). The LeNet-5 network with the highest aIQ was 2.32%\nless accurate but contained 30,912 times fewer parameters than the highest\naccuracy network. Both batch normalization and dropout layers were found to\nincrease neural efficiency. Finally, high aIQ networks are shown to be\nmemorization and overtraining resistant, capable of learning proper digit\nclassification with an accuracy of 92.51% even when 75% of the class labels are\nrandomized. These results demonstrate the utility of aIQ and neural efficiency\nas metrics for balancing network performance and size.", "authors": ["<PERSON>", "<PERSON>"], "year": 2020, "venue": "arXiv", "url": "http://arxiv.org/abs/2006.02909v1", "citation_count": 0, "source": "arxiv", "paper_id": "2006.02909v1", "keywords": null, "doi": null}, {"title": "Graph Structure of Neural Networks", "abstract": "Neural networks are often represented as graphs of connections between\nneurons. However, despite their wide use, there is currently little\nunderstanding of the relationship between the graph structure of the neural\nnetwork and its predictive performance. Here we systematically investigate how\ndoes the graph structure of neural networks affect their predictive\nperformance. To this end, we develop a novel graph-based representation of\nneural networks called relational graph, where layers of neural network\ncomputation correspond to rounds of message exchange along the graph structure.\nUsing this representation we show that: (1) a \"sweet spot\" of relational graphs\nleads to neural networks with significantly improved predictive performance;\n(2) neural network's performance is approximately a smooth function of the\nclustering coefficient and average path length of its relational graph; (3) our\nfindings are consistent across many different tasks and datasets; (4) the sweet\nspot can be identified efficiently; (5) top-performing neural networks have\ngraph structure surprisingly similar to those of real biological neural\nnetworks. Our work opens new directions for the design of neural architectures\nand the understanding on neural networks in general.", "authors": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"], "year": 2020, "venue": "arXiv", "url": "http://arxiv.org/abs/2007.06559v2", "citation_count": 0, "source": "arxiv", "paper_id": "2007.06559v2", "keywords": null, "doi": null}, {"title": "Brain-inspired self-organizing model for incremental learning", "abstract": "Academic paper published in The 2013 International Joint Conference on Neural Networks (IJCNN). Full text available via DOI.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "year": 2013, "venue": "The 2013 International Joint Conference on Neural Networks (IJCNN)", "url": "https://doi.org/10.1109/ijcnn.2013.6706851", "citation_count": 1, "source": "crossref", "paper_id": "10.1109/ijcnn.2013.6706851", "keywords": null, "doi": "10.1109/ijcnn.2013.6706851"}, {"title": "A Comprehensive Review of Spiking Neural Networks: Interpretation,\n  Optimization, Efficiency, and Best Practices", "abstract": "Biological neural networks continue to inspire breakthroughs in neural\nnetwork performance. And yet, one key area of neural computation that has been\nunder-appreciated and under-investigated is biologically plausible,\nenergy-efficient spiking neural networks, whose potential is especially\nattractive for low-power, mobile, or otherwise hardware-constrained settings.\nWe present a literature review of recent developments in the interpretation,\noptimization, efficiency, and accuracy of spiking neural networks. Key\ncontributions include identification, discussion, and comparison of\ncutting-edge methods in spiking neural network optimization, energy-efficiency,\nand evaluation, starting from first principles so as to be accessible to new\npractitioners.", "authors": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "year": 2023, "venue": "arXiv", "url": "http://arxiv.org/abs/2303.10780v2", "citation_count": 0, "source": "arxiv", "paper_id": "2303.10780v2", "keywords": null, "doi": null}, {"title": "Quantum-Inspired Algorithms for Accelerating Machine Learning", "abstract": "Academic paper published in The Brain &amp; Neural Networks. Full text available via DOI.", "authors": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "year": 2022, "venue": "The Brain &amp; Neural Networks", "url": "https://doi.org/10.3902/jnns.29.186", "citation_count": 0, "source": "crossref", "paper_id": "10.3902/jnns.29.186", "keywords": null, "doi": "10.3902/jnns.29.186"}, {"title": "Multi-Grade Deep Learning", "abstract": "The current deep learning model is of a single-grade, that is, it learns a\ndeep neural network by solving a single nonconvex optimization problem. When\nthe layer number of the neural network is large, it is computationally\nchallenging to carry out such a task efficiently. Inspired by the human\neducation process which arranges learning in grades, we propose a multi-grade\nlearning model: We successively solve a number of optimization problems of\nsmall sizes, which are organized in grades, to learn a shallow neural network\nfor each grade. Specifically, the current grade is to learn the leftover from\nthe previous grade. In each of the grades, we learn a shallow neural network\nstacked on the top of the neural network, learned in the previous grades, which\nremains unchanged in training of the current and future grades. By dividing the\ntask of learning a deep neural network into learning several shallow neural\nnetworks, one can alleviate the severity of the nonconvexity of the original\noptimization problem of a large size. When all grades of the learning are\ncompleted, the final neural network learned is a stair-shape neural network,\nwhich is the superposition of networks learned from all grades. Such a model\nenables us to learn a deep neural network much more effectively and\nefficiently. Moreover, multi-grade learning naturally leads to adaptive\nlearning. We prove that in the context of function approximation if the neural\nnetwork generated by a new grade is nontrivial, the optimal error of the grade\nis strictly reduced from the optimal error of the previous grade. Furthermore,\nwe provide several proof-of-concept numerical examples which demonstrate that\nthe proposed multi-grade model outperforms significantly the traditional\nsingle-grade model and is much more robust than the traditional model.", "authors": ["<PERSON><PERSON><PERSON>"], "year": 2023, "venue": "arXiv", "url": "http://arxiv.org/abs/2302.00150v1", "citation_count": 0, "source": "arxiv", "paper_id": "2302.00150v1", "keywords": null, "doi": null}], "workflows": {"paper_1": {"title": "Brain-Inspired Neural Networks For Efficient Learning Research: Novel Approach 1", "datasets": [], "network_architectures": ["Brain-Inspired Neural Networks"], "platforms_tools": [], "research_methods": ["bio-plausible mechanisms", "efficient learning algorithms"], "evaluation_metrics": [], "brain_inspiration": ["bio-plausible mechanisms"], "ai_techniques": ["efficient learning algorithms"]}, "paper_2": {"title": "Brain-Inspired Neural Networks For Efficient Learning Research: Novel Approach 2", "datasets": [], "network_architectures": ["Brain-Inspired Neural Networks"], "platforms_tools": [], "research_methods": ["bio-plausible mechanisms", "efficient learning algorithms"], "evaluation_metrics": [], "brain_inspiration": ["bio-plausible mechanisms"], "ai_techniques": ["efficient learning algorithms"]}, "paper_3": {"title": "Brain-Inspired Neural Networks For Efficient Learning Research: Novel Approach 3", "datasets": [], "network_architectures": ["Brain-Inspired Neural Networks"], "platforms_tools": [], "research_methods": ["bio-plausible mechanisms", "efficient learning algorithms"], "evaluation_metrics": [], "brain_inspiration": ["bio-plausible mechanisms"], "ai_techniques": ["efficient learning algorithms"]}, "paper_4": {"title": "Brain-Inspired Neural Networks For Efficient Learning Research: Novel Approach 4", "datasets": [], "network_architectures": ["Brain-Inspired Neural Networks"], "platforms_tools": [], "research_methods": ["bio-plausible mechanisms", "efficient learning algorithms"], "evaluation_metrics": [], "brain_inspiration": ["bio-plausible mechanisms"], "ai_techniques": ["efficient learning algorithms"]}, "paper_5": {"title": "Brain-Inspired Neural Networks For Efficient Learning Research: Novel Approach 5", "datasets": [], "network_architectures": ["Brain-Inspired Neural Networks"], "platforms_tools": [], "research_methods": ["bio-plausible mechanisms", "efficient learning algorithms"], "evaluation_metrics": [], "brain_inspiration": ["bio-plausible mechanisms"], "ai_techniques": ["efficient learning algorithms"]}}, "research_topic": "Brain-Inspired Neural Networks for Efficient Learning", "timestamp": "2025-07-24 11:01:57", "total_papers": 20}