"""
Enhanced English Prompts Test
Test the new AI Scientist v2 style prompts with real API integration
"""

import os
import sys
import json
import time
from datetime import datetime

# Add project path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from reasoning.data_models import ResearchProblem, ReasoningSession
from reasoning.research_question_evaluator import ResearchQuestionEvaluator
from reasoning.enhanced_prompts import get_enhanced_prompts
from core.llm_client import LLMClient
from agents.agent_manager import <PERSON><PERSON><PERSON><PERSON>

def test_enhanced_prompts():
    """Test the enhanced English prompts with real API"""
    print("🚀 Enhanced English Prompts Test - AI Scientist v2 Style")
    print("=" * 80)
    
    # Configure real API
    api_key = "sk-1b1d72e2e10643029de548b655e1f93e"
    if not api_key:
        print("❌ ERROR: DeepSeek API key not found!")
        return
    
    print(f"✅ Using DeepSeek API with key: {api_key[:10]}...")
    
    # Initialize components with real API - pass parameters to constructor
    llm_client = LLMClient(
        model="deepseek-chat",
        temperature=0.7,
        api_key=api_key
    )
    
    agent_manager = AgentManager(llm_client=llm_client)
    evaluator = ResearchQuestionEvaluator(llm_client=llm_client)
    evaluator.agent_manager = agent_manager
    
    # Initialize experts with actual expert objects
    print("\n🔧 Initializing expert agents...")
    
    # Import expert classes
    from agents.expert_agents.ai_technology_expert import AITechnologyExpert
    from agents.expert_agents.neuroscience_expert import NeuroscienceExpert 
    from agents.expert_agents.data_analysis_expert import DataAnalysisExpert
    from agents.expert_agents.experiment_design_expert import ExperimentDesignExpert
    from agents.expert_agents.paper_writing_expert import PaperWritingExpert
    
    experts_config = {
        "ai_technology": AITechnologyExpert(llm_client),
        "neuroscience": NeuroscienceExpert(llm_client), 
        "data_analysis": DataAnalysisExpert(llm_client),
        "experiment_design": ExperimentDesignExpert(llm_client),
        "paper_writing": PaperWritingExpert(llm_client)
    }
    
    for expert_name, expert_instance in experts_config.items():
        try:
            agent_manager.register_agent(expert_name, expert_instance)
            print(f"  ✅ {expert_name}: {expert_instance.agent_type}")
        except Exception as e:
            print(f"  ❌ Failed to register {expert_name}: {e}")
    
    # Test research problem
    research_problem = ResearchProblem(
        question="How can spike-timing dependent plasticity (STDP) be integrated with transformer attention mechanisms to create more biologically plausible and energy-efficient language models?",
        hypothesis="Combining STDP with transformer attention can reduce computational requirements while maintaining performance by implementing selective attention based on temporal spike patterns",
        background={
            "domain": "Brain-inspired artificial intelligence",
            "context": "Current transformer models are computationally expensive and lack biological plausibility",
            "motivation": "Need for more efficient and brain-like AI architectures"
        }
    )
    
    print(f"\n📋 Research Problem:")
    print(f"   Question: {research_problem.question}")
    print(f"   Hypothesis: {research_problem.hypothesis}")
    print(f"   Domain: {research_problem.background['domain']}")
    
    # Test enhanced evaluation
    print(f"\n🔍 Testing Enhanced Research Question Evaluation...")
    start_time = time.time()
    
    try:
        enhanced_problem = evaluator.evaluate_research_question(research_problem, num_rounds=2)
        evaluation_time = time.time() - start_time
        
        print(f"\n📊 Evaluation Results (completed in {evaluation_time:.1f}s):")
        print(f"   Overall Score: {enhanced_problem.value_score:.2f}/10")
        print(f"   Innovation: {enhanced_problem.innovation_score:.2f}/10")
        print(f"   Feasibility: {enhanced_problem.feasibility_score:.2f}/10") 
        print(f"   Impact: {enhanced_problem.impact_score:.2f}/10")
        
        # Display expert opinions
        if hasattr(enhanced_problem, 'expert_opinions') and enhanced_problem.expert_opinions:
            print(f"\n👥 Expert Opinions ({len(enhanced_problem.expert_opinions)} experts):")
            for i, opinion in enumerate(enhanced_problem.expert_opinions[:3]):  # Show first 3
                if isinstance(opinion, dict):
                    expert_name = opinion.get('expert', f'Expert {i+1}')
                    overall = opinion.get('overall_assessment', 'No assessment')
                    print(f"   {expert_name}: {overall[:100]}...")
        
        # Test enhanced prompts display
        print(f"\n📝 Enhanced Prompt Features:")
        enhanced_prompts = get_enhanced_prompts()
        
        for prompt_type, prompt_content in enhanced_prompts.items():
            lines = len(prompt_content.split('\n'))
            chars = len(prompt_content)
            print(f"   {prompt_type}: {lines} lines, {chars} characters")
            
            # Check for AI Scientist v2 features
            features = []
            if "scoring guide" in prompt_content.lower(): features.append("Scoring Guidelines")
            if "json format" in prompt_content.lower(): features.append("Structured Output")
            if "rigorous" in prompt_content.lower(): features.append("Rigorous Standards")
            if "top-tier" in prompt_content.lower(): features.append("Publication Quality")
            if "detailed" in prompt_content.lower(): features.append("Detailed Instructions")
            
            print(f"     Features: {', '.join(features)}")
        
        # Save results
        session_data = {
            "timestamp": datetime.now().isoformat(),
            "test_type": "enhanced_english_prompts",
            "api_type": "real_deepseek_api",
            "evaluation_time": evaluation_time,
            "research_problem": {
                "question": research_problem.question,
                "hypothesis": research_problem.hypothesis,
                "background": research_problem.background
            },
            "results": {
                "overall_score": enhanced_problem.value_score,
                "innovation_score": enhanced_problem.innovation_score,
                "feasibility_score": enhanced_problem.feasibility_score,
                "impact_score": enhanced_problem.impact_score,
                "expert_opinions_count": len(enhanced_problem.expert_opinions) if hasattr(enhanced_problem, 'expert_opinions') else 0
            },
            "prompt_analysis": {
                "total_prompts": len(enhanced_prompts),
                "prompt_types": list(enhanced_prompts.keys()),
                "average_prompt_length": sum(len(p) for p in enhanced_prompts.values()) // len(enhanced_prompts)
            }
        }
        
        # Create output directory
        output_dir = "reasoning_sessions"
        os.makedirs(output_dir, exist_ok=True)
        
        # Save session
        session_id = f"enhanced_prompts_{int(time.time())}"
        session_file = os.path.join(output_dir, f"{session_id}.json")
        
        with open(session_file, 'w', encoding='utf-8') as f:
            json.dump(session_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Results saved to: {session_file}")
        
        # Quality assessment
        print(f"\n🎯 Quality Assessment:")
        if enhanced_problem.value_score >= 8.0:
            print("   ✅ Excellent: High-quality research evaluation achieved")
        elif enhanced_problem.value_score >= 6.0:
            print("   ⚠️  Good: Solid research evaluation with room for improvement")
        else:
            print("   ❌ Needs Work: Research evaluation quality below expectations")
        
        if evaluation_time > 20:
            print("   ✅ Real API: Realistic evaluation time indicates genuine API usage")
        else:
            print("   ⚠️  Quick Response: May indicate mock responses")
        
        print(f"\n🏆 Enhanced Prompts Test Completed Successfully!")
        print(f"   Total time: {evaluation_time:.1f}s")
        print(f"   Quality score: {enhanced_problem.value_score:.2f}/10")
        print(f"   Prompts tested: {len(enhanced_prompts)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_prompts()
    exit(0 if success else 1)
