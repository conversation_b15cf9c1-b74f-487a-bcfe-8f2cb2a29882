@echo off
REM Brain-Inspired Intelligence 论文生成系统 - Windows启动脚本

echo 🧠 Brain-Inspired Intelligence 论文生成系统启动中...
echo ==================================================

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请确保已安装Python 3.8+
    pause
    exit /b 1
)

REM 检查项目目录
if not exist "paper_cli.py" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

REM 创建必要的目录
echo 📁 创建输出目录...
if not exist "output" mkdir output
if not exist "logs" mkdir logs
if not exist "data" mkdir data

REM 检查依赖
echo 📦 检查依赖库...
python -c "import json, datetime, logging, threading" >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 缺少必要的Python库
    echo 请运行: pip install -r requirements.txt
    pause
    exit /b 1
)

REM 运行系统检查
echo 🔍 运行系统检查...
python tests\test_complete_system.py >nul 2>&1
if errorlevel 1 (
    echo ⚠️ 系统检查有警告，但可以继续运行
) else (
    echo ✅ 系统检查通过
)

REM 启动主程序
echo 🚀 启动论文生成系统...
echo ==================================================
python paper_cli.py %*
