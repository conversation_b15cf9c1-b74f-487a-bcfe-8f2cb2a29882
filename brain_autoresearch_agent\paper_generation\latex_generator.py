"""
LaTeX论文生成器 - 将Markdown内容转换为标准学术论文LaTeX格式

这个模块负责：
1. 提供LaTeX模板支持（ICML, ICLR, NeurIPS等）
2. 格式化论文内容为LaTeX格式
3. 生成可编译的.tex文件
4. 处理参考文献和引用
"""

import os
import re
import json
from typing import Dict, Any, List, Optional
from datetime import datetime


class LaTeXGenerator:
    """LaTeX论文生成器"""
    
    def __init__(self):
        self.templates = {
            'ICML': self._get_icml_template(),
            'ICLR': self._get_iclr_template(),
            'NeurIPS': self._get_neurips_template(),
            'generic': self._get_generic_template()
        }
    
    def generate_latex_paper(self, paper_content: Dict[str, Any], 
                           venue: str = 'ICML') -> str:
        """
        生成完整的LaTeX论文
        
        Args:
            paper_content: 包含论文各部分内容的字典
            venue: 目标会议/期刊
            
        Returns:
            完整的LaTeX文档字符串
        """
        template = self.templates.get(venue, self.templates['generic'])
        
        # 提取并清理内容
        title = self._clean_content(paper_content.get('title', 'Untitled'))
        abstract = self._clean_content(paper_content.get('abstract', ''))
        introduction = self._clean_content(paper_content.get('introduction', ''))
        related_work = self._clean_content(paper_content.get('related_work', ''))
        methodology = self._clean_content(paper_content.get('methodology', ''))
        experiments = self._clean_content(paper_content.get('experiments', ''))
        conclusion = self._clean_content(paper_content.get('conclusion', ''))
        references = self._generate_bibliography(paper_content.get('references', []))
        
        # 替换模板中的占位符
        latex_content = template.format(
            title=title,
            abstract=abstract,
            introduction=introduction,
            related_work=related_work,
            methodology=methodology,
            experiments=experiments,
            conclusion=conclusion,
            references=references
        )
        
        return latex_content
    
    def _clean_content(self, content: str) -> str:
        """
        清理内容，移除调试信息和格式化为LaTeX
        
        Args:
            content: 原始内容
            
        Returns:
            清理后的LaTeX格式内容
        """
        if not content:
            return ""
        
        # 转换为字符串（以防是其他类型）
        content = str(content)
        
        # 移除Python元组格式 ('content', history)
        content = re.sub(r"\('([^']*)',.*?\)", r"\1", content)
        content = re.sub(r"\(\"([^\"]*)\",.*?\)", r"\1", content)
        
        # 移除明显的调试信息
        debug_patterns = [
            r"AgentResponse\([^)]*\)",
            r"\{'[^']*':[^}]*\}",
            r"Error generating [^:]*:",
            r"Generation failed",
            r"Mock response for",
            r"Confidence: [0-9.]+",
            r"Reasoning: .*?(?=\n|$)"
        ]
        
        for pattern in debug_patterns:
            content = re.sub(pattern, "", content, flags=re.IGNORECASE | re.MULTILINE)
        
        # 转换Markdown格式到LaTeX
        content = self._markdown_to_latex(content)
        
        # 清理多余的空行
        content = re.sub(r"\n\s*\n\s*\n", "\n\n", content)
        content = content.strip()
        
        return content
    
    def _markdown_to_latex(self, text: str) -> str:
        """
        将Markdown格式转换为LaTeX格式
        
        Args:
            text: Markdown文本
            
        Returns:
            LaTeX格式文本
        """
        # 标题转换
        text = re.sub(r"^#{1}\s+(.*?)$", r"\\section{\1}", text, flags=re.MULTILINE)
        text = re.sub(r"^#{2}\s+(.*?)$", r"\\subsection{\1}", text, flags=re.MULTILINE)
        text = re.sub(r"^#{3}\s+(.*?)$", r"\\subsubsection{\1}", text, flags=re.MULTILINE)
        
        # 粗体和斜体
        text = re.sub(r"\*\*([^*]+?)\*\*", r"\\textbf{\1}", text)
        text = re.sub(r"\*([^*]+?)\*", r"\\textit{\1}", text)
        
        # 代码块
        text = re.sub(r"`([^`]+?)`", r"\\texttt{\1}", text)
        
        # 列表项（简单处理）
        text = re.sub(r"^[-*]\s+(.*?)$", r"\\item \1", text, flags=re.MULTILINE)
        
        # 引用格式
        text = re.sub(r"\[([^\]]+?)\]\(([^)]+?)\)", r"\\cite{\1}", text)
        
        return text
    
    def _generate_bibliography(self, references: List[Dict[str, Any]]) -> str:
        """
        生成参考文献部分
        
        Args:
            references: 参考文献列表
            
        Returns:
            LaTeX格式的参考文献
        """
        if not references:
            return "\\\\bibliography{references}\\n\\\\bibliographystyle{icml2025}"
        
        bib_entries = []
        for i, ref in enumerate(references):
            if isinstance(ref, dict):
                title = ref.get('title', f'Reference {i+1}')
                authors = ref.get('authors', 'Unknown Author')
                year = ref.get('year', '2024')
                venue = ref.get('venue', 'Unknown Venue')
                
                # 简化的bibitem格式
                bib_entry = f"\\\\bibitem{{ref{i+1}}} {authors}. {title}. {venue}, {year}."
                bib_entries.append(bib_entry)
        
        if bib_entries:
            return "\\\\begin{thebibliography}{99}\\n" + "\\n".join(bib_entries) + "\\n\\\\end{thebibliography}"
        else:
            return "\\\\bibliography{references}\\n\\\\bibliographystyle{icml2025}"
    
    def _get_icml_template(self) -> str:
        """ICML会议LaTeX模板"""
        return """\\documentclass{{icml2025}}

\\usepackage{{times}}
\\usepackage{{helvet}}
\\usepackage{{courier}}
\\usepackage[hyphens]{{url}}
\\usepackage[colorlinks=true,urlcolor=blue,citecolor=blue,linkcolor=blue]{{hyperref}}
\\usepackage{{graphicx}}
\\usepackage{{natbib}}
\\usepackage{{booktabs}}
\\usepackage{{amsfonts}}
\\usepackage{{nicefrac}}
\\usepackage{{microtype}}
\\usepackage{{xcolor}}

\\title{{{title}}}

\\author{{Anonymous Author}}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
{abstract}
\\end{{abstract}}

\\section{{Introduction}}
{introduction}

\\section{{Related Work}}
{related_work}

\\section{{Methodology}}
{methodology}

\\section{{Experiments}}
{experiments}

\\section{{Conclusion}}
{conclusion}

{references}

\\end{{document}}"""
    
    def _get_iclr_template(self) -> str:
        """ICLR会议LaTeX模板"""
        return """\\documentclass{{article}}

\\usepackage{{iclr2025}}
\\usepackage{{times}}
\\usepackage{{helvet}}
\\usepackage{{courier}}
\\usepackage[hyphens]{{url}}
\\usepackage[colorlinks=true,urlcolor=blue,citecolor=blue,linkcolor=blue]{{hyperref}}
\\usepackage{{graphicx}}
\\usepackage{{natbib}}
\\usepackage{{booktabs}}
\\usepackage{{amsfonts}}
\\usepackage{{nicefrac}}
\\usepackage{{microtype}}

\\title{{{title}}}

\\author{{Anonymous submission}}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
{abstract}
\\end{{abstract}}

\\section{{Introduction}}
{introduction}

\\section{{Related Work}}
{related_work}

\\section{{Methodology}}
{methodology}

\\section{{Experiments}}
{experiments}

\\section{{Conclusion}}
{conclusion}

{references}

\\end{{document}}"""
    
    def _get_neurips_template(self) -> str:
        """NeurIPS会议LaTeX模板"""
        return """\\documentclass{{article}}

\\usepackage[preprint]{{neurips_2024}}
\\usepackage[utf8]{{inputenc}}
\\usepackage[T1]{{fontenc}}
\\usepackage{{hyperref}}
\\usepackage{{url}}
\\usepackage{{booktabs}}
\\usepackage{{amsfonts}}
\\usepackage{{nicefrac}}
\\usepackage{{microtype}}
\\usepackage{{xcolor}}

\\title{{{title}}}

\\author{{Anonymous}}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
{abstract}
\\end{{abstract}}

\\section{{Introduction}}
{introduction}

\\section{{Related Work}}
{related_work}

\\section{{Method}}
{methodology}

\\section{{Experiments}}
{experiments}

\\section{{Conclusion}}
{conclusion}

{references}

\\end{{document}}"""
    
    def _get_generic_template(self) -> str:
        """通用LaTeX模板"""
        return """\\documentclass[11pt]{{article}}

\\usepackage{{geometry}}
\\usepackage{{times}}
\\usepackage{{helvet}}
\\usepackage{{courier}}
\\usepackage[hyphens]{{url}}
\\usepackage[colorlinks=true,urlcolor=blue,citecolor=blue,linkcolor=blue]{{hyperref}}
\\usepackage{{graphicx}}
\\usepackage{{natbib}}
\\usepackage{{booktabs}}
\\usepackage{{amsfonts}}
\\usepackage{{amsmath}}
\\usepackage{{nicefrac}}
\\usepackage{{microtype}}

\\geometry{{margin=1in}}

\\title{{{title}}}
\\author{{Anonymous Author}}
\\date{{\\today}}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
{abstract}
\\end{{abstract}}

\\section{{Introduction}}
{introduction}

\\section{{Related Work}}
{related_work}

\\section{{Methodology}}
{methodology}

\\section{{Experiments}}
{experiments}

\\section{{Conclusion}}
{conclusion}

{references}

\\end{{document}}"""
    
    def save_latex_file(self, latex_content: str, filename: str, 
                       output_dir: str = "./output") -> str:
        """
        保存LaTeX文件到指定目录
        
        Args:
            latex_content: LaTeX内容
            filename: 文件名（不包含扩展名）
            output_dir: 输出目录
            
        Returns:
            保存的文件路径
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # 确保文件名以.tex结尾
        if not filename.endswith('.tex'):
            filename += '.tex'
        
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(latex_content)
        
        return filepath
    
    def generate_and_save_paper(self, paper_content: Dict[str, Any], 
                               filename: str, venue: str = 'ICML',
                               output_dir: str = "./output") -> str:
        """
        生成并保存LaTeX论文
        
        Args:
            paper_content: 论文内容
            filename: 文件名
            venue: 目标会议
            output_dir: 输出目录
            
        Returns:
            保存的文件路径
        """
        latex_content = self.generate_latex_paper(paper_content, venue)
        return self.save_latex_file(latex_content, filename, output_dir)


def demo_latex_generation():
    """演示LaTeX生成功能"""
    # 示例论文内容
    sample_paper = {
        'title': 'Brain-Inspired Attention Mechanisms for Deep Learning',
        'abstract': 'This paper presents a novel brain-inspired attention mechanism that mimics the selective attention processes observed in the human visual cortex.',
        'introduction': 'In recent years, attention mechanisms have become a cornerstone of modern deep learning architectures. However, current approaches often lack the biological plausibility and efficiency observed in natural cognitive systems.',
        'related_work': 'Previous work in attention mechanisms includes the seminal work by Bahdanau et al. on neural machine translation and the Transformer architecture by Vaswani et al.',
        'methodology': 'Our approach draws inspiration from the hierarchical processing in the visual cortex, implementing a multi-scale attention mechanism that processes information at different spatial and temporal resolutions.',
        'experiments': 'We evaluate our method on several benchmark datasets including ImageNet, CIFAR-10, and a custom brain imaging dataset.',
        'conclusion': 'Our brain-inspired attention mechanism demonstrates superior performance while maintaining biological plausibility.',
        'references': [
            {'title': 'Attention Is All You Need', 'authors': 'Vaswani et al.', 'year': '2017', 'venue': 'NeurIPS'},
            {'title': 'Neural Machine Translation by Jointly Learning to Align and Translate', 'authors': 'Bahdanau et al.', 'year': '2015', 'venue': 'ICLR'}
        ]
    }
    
    # 生成LaTeX
    generator = LaTeXGenerator()
    latex_content = generator.generate_latex_paper(sample_paper, 'ICML')
    
    # 保存文件
    filepath = generator.save_latex_file(latex_content, 'demo_paper', './output')
    print(f"LaTeX paper saved to: {filepath}")
    
    return filepath


if __name__ == "__main__":
    demo_latex_generation()
