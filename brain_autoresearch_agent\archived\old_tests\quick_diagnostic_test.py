"""
快速诊断测试 - 验证核心组件功能
针对集成测试中发现的问题进行快速诊断
"""

import sys
import os
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试所有关键模块是否可以正常导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试核心模块
        from core.llm_client import LLMClient
        from core.hybrid_model_client import HybridModelClient
        print("   ✅ 核心客户端模块导入成功")
        
        # 测试Agent模块
        from agents.agent_manager import AgentManager
        from agents.base_agent import BaseAgent
        print("   ✅ Agent系统模块导入成功")
        
        # 测试论文生成模块
        from paper_generation.latex_format_expert import LaTeXFormatExpert
        from paper_generation.enhanced_citation_manager import EnhancedCitationManager
        from paper_generation.multi_expert_review_system import MultiExpertReviewSystem
        from paper_generation.paper_quality_optimizer import PaperQualityOptimizer
        print("   ✅ 论文生成模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 模块导入失败: {e}")
        return False

def test_latex_expert():
    """测试LaTeX专家的基本功能"""
    print("\n🔧 测试LaTeX格式专家...")
    
    try:
        from paper_generation.latex_format_expert import LaTeXFormatExpert
        from core.hybrid_model_client import HybridModelClient
        
        # 创建客户端
        client = HybridModelClient()
        
        # 创建LaTeX专家
        latex_expert = LaTeXFormatExpert(client)
        
        # 测试简单的格式检查
        test_content = """
        \\documentclass{article}
        \\begin{document}
        \\title{Test Paper}
        \\author{Author}
        \\maketitle
        \\section{Introduction}
        This is a test.
        \\end{document}
        """
        
        # 测试格式问题检测
        issues = latex_expert.detect_format_issues(test_content, "ICML")
        print(f"   📊 检测到格式问题: {len(issues)} 个")
        
        # 测试会议合规性检查
        compliance = latex_expert.check_venue_compliance(test_content, "ICML")
        print(f"   📋 会议合规性: {compliance}")
        
        print("   ✅ LaTeX专家基本功能正常")
        return True
        
    except Exception as e:
        print(f"   ❌ LaTeX专家测试失败: {e}")
        return False

def test_citation_manager():
    """测试引用管理器的基本功能"""
    print("\n📚 测试引用管理器...")
    
    try:
        from paper_generation.enhanced_citation_manager import EnhancedCitationManager
        from core.hybrid_model_client import HybridModelClient
        
        # 创建客户端和管理器
        client = HybridModelClient()
        citation_manager = EnhancedCitationManager(client)
        
        # 检查enhance_citations方法是否存在
        if hasattr(citation_manager, 'enhance_citations'):
            print("   ✅ enhance_citations方法存在")
        else:
            print("   ❌ enhance_citations方法不存在")
            return False
            
        # 测试基本功能
        test_content = "This is a test paper about neural networks."
        print(f"   📝 测试内容长度: {len(test_content)} 字符")
        
        print("   ✅ 引用管理器基本功能正常")
        return True
        
    except Exception as e:
        print(f"   ❌ 引用管理器测试失败: {e}")
        return False

def test_hybrid_client():
    """测试混合模型客户端的基本功能"""
    print("\n🤖 测试混合模型客户端...")
    
    try:
        from core.hybrid_model_client import HybridModelClient
        
        # 创建客户端
        client = HybridModelClient()
        
        # 检查generate_async方法是否存在
        if hasattr(client, 'generate_async'):
            print("   ✅ generate_async方法存在")
        else:
            print("   ❌ generate_async方法不存在")
            return False
            
        # 测试同步生成
        result = client.generate_text("Hello, this is a test.", task_type="text_generation")
        print(f"   📝 同步生成结果: {result[:50]}...")
        
        print("   ✅ 混合模型客户端基本功能正常")
        return True
        
    except Exception as e:
        print(f"   ❌ 混合模型客户端测试失败: {e}")
        return False

def test_agent_json_parsing():
    """测试Agent系统的JSON解析问题"""
    print("\n🤖 测试Agent系统JSON解析...")
    
    try:
        from agents.base_agent import BaseAgent
        from agents.expert_agents.ai_technology_expert import AITechnologyExpert
        from core.llm_client import LLMClient
        
        # 创建客户端和专家
        client = LLMClient()
        expert = AITechnologyExpert(client)
        
        # 测试简单的分析任务
        task_content = "分析深度学习在计算机视觉中的应用"
        
        # 模拟分析过程
        print("   📊 模拟专家分析过程...")
        print(f"   📝 任务内容: {task_content}")
        print("   🎯 专家类型: AI技术专家")
        
        # 检查专家的关键方法
        if hasattr(expert, 'analyze'):
            print("   ✅ analyze方法存在")
        else:
            print("   ❌ analyze方法不存在")
            return False
            
        print("   ✅ Agent系统基本结构正常")
        return True
        
    except Exception as e:
        print(f"   ❌ Agent系统测试失败: {e}")
        return False

async def test_async_functionality():
    """测试异步功能"""
    print("\n⚡ 测试异步功能...")
    
    try:
        from core.hybrid_model_client import HybridModelClient
        
        client = HybridModelClient()
        
        # 测试异步生成
        if hasattr(client, 'generate_async'):
            result = await client.generate_async("Test async generation", task_type="text_generation")
            print(f"   📝 异步生成结果: {result[:50]}...")
            print("   ✅ 异步功能正常")
            return True
        else:
            print("   ❌ 异步方法不存在")
            return False
            
    except Exception as e:
        print(f"   ❌ 异步功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 启动快速诊断测试")
    print("=" * 50)
    
    # 测试结果统计
    test_results = []
    
    # 1. 测试模块导入
    test_results.append(("模块导入", test_imports()))
    
    # 2. 测试LaTeX专家
    test_results.append(("LaTeX专家", test_latex_expert()))
    
    # 3. 测试引用管理器
    test_results.append(("引用管理器", test_citation_manager()))
    
    # 4. 测试混合模型客户端
    test_results.append(("混合模型客户端", test_hybrid_client()))
    
    # 5. 测试Agent系统
    test_results.append(("Agent系统", test_agent_json_parsing()))
    
    # 6. 测试异步功能
    async_result = asyncio.run(test_async_functionality())
    test_results.append(("异步功能", async_result))
    
    # 生成测试报告
    print("\n" + "=" * 50)
    print("📊 快速诊断测试报告")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 测试统计:")
    print(f"   总测试数: {total}")
    print(f"   通过数: {passed}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！系统基本功能正常。")
    else:
        print(f"\n⚠️ {total-passed} 个测试失败，需要修复。")
        
        # 提供修复建议
        print("\n🔧 修复建议:")
        for test_name, result in test_results:
            if not result:
                if "LaTeX专家" in test_name:
                    print("   - 检查LaTeX专家的异步方法调用")
                elif "引用管理器" in test_name:
                    print("   - 确认enhance_citations方法是否正确实现")
                elif "混合模型客户端" in test_name:
                    print("   - 检查generate_async方法是否正确添加")
                elif "Agent系统" in test_name:
                    print("   - 检查Agent响应的JSON格式和解析逻辑")
                elif "异步功能" in test_name:
                    print("   - 检查异步方法的实现和调用方式")

if __name__ == "__main__":
    main()
