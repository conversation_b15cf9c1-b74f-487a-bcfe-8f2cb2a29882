# 测试程序说明

## 🧪 核心测试程序

### 系统验证测试
- `test_reasoning_engine_fast.py` - **主要系统验证测试** (推荐使用)
- `test_all_experts_comprehensive.py` - 专家系统完整测试
- `test_deepseek_complete.py` - DeepSeek API集成测试

### 模块专项测试  
- `test_stage1_enhanced.py` - 阶段1功能测试
- `test_multi_expert_collaboration.py` - 多专家协作测试
- `test_consensus_decision_only.py` - 共识决策单独测试
- `test_knowledge_fusion_only.py` - 知识融合单独测试

### 运行建议
1. **快速验证**: `python test_reasoning_engine_fast.py`
2. **完整测试**: `python test_all_experts_comprehensive.py`
3. **API测试**: `python test_deepseek_complete.py`

### 环境要求
- 激活conda环境: `conda activate pytorch`
- 切换到项目目录: `cd d:\AutoResearchAgent\brain_autoresearch_agent`
