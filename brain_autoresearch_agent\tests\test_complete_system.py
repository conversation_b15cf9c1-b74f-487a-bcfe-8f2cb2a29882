"""
完整系统端到端测试
测试从研究题目到最终论文的整个流程
"""

import os
import sys
import json
import unittest
import asyncio
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from paper_generation.ai_scientist_v2_integrated_writer import AIScientistV2IntegratedWriter
from core.enhanced_literature_manager import EnhancedLiteratureManager
from core.unified_api_client import get_unified_client
from reasoning.enhanced_hypothesis_experiment_designer import EnhancedHypothesisExperimentDesigner
from reasoning.enhanced_visualization_advisor import EnhancedVisualizationAdvisor
from core.experiment_code_generator import ExperimentCodeGenerator
from reasoning.enhanced_multi_agent_collaborator import EnhancedMultiAgentCollaborator
from agents.agent_manager import AgentManager

class TestCompleteSystem(unittest.TestCase):
    """测试完整系统流程"""
    
    @classmethod
    def setUpClass(cls):
        """初始化测试环境"""
        cls.output_dir = "output/system_integration_test"
        os.makedirs(cls.output_dir, exist_ok=True)
        
        cls.api_client = get_unified_client()
        cls.agent_manager = AgentManager(cls.api_client)
        
        # 测试参数
        cls.research_topic = "Neural-Plasticity-Inspired Deep Learning Architecture"
        cls.target_venue = "ICML"
        
        print(f"\n🚀 开始完整系统集成测试")
        print(f"📋 研究主题: {cls.research_topic}")
        print(f"🏆 目标会议: {cls.target_venue}")
    
    def test_01_connection(self):
        """测试API连接"""
        print("\n📡 测试API连接...")
        
        results = self.api_client.test_connection(test_vision=False)
        
        for service, success in results.items():
            status = "✅ 成功" if success else "❌ 失败"
            print(f"  {service}: {status}")
            self.assertTrue(success, f"API连接失败: {service}")
    
    def test_02_literature_search(self):
        """测试文献检索"""
        print("\n📚 测试文献检索...")
        
        try:
            literature_manager = EnhancedLiteratureManager(self.api_client)
            
            # 执行文献搜索
            search_results = literature_manager.search_literature(
                self.research_topic,
                max_results=10,
                search_depth="standard"
            )
            
            # 验证结果
            self.assertIsNotNone(search_results)
            self.assertIn('papers', search_results)
            self.assertTrue(len(search_results['papers']) > 0)
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.output_dir, f"literature_results_{timestamp}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(search_results, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 文献检索成功，找到 {len(search_results['papers'])} 篇论文")
            print(f"💾 结果保存至: {output_file}")
            
            # 保存结果供后续测试使用
            self.__class__.literature_results = search_results
            
        except Exception as e:
            self.fail(f"文献检索失败: {e}")
    
    def test_03_workflow_extraction(self):
        """测试工作流提取"""
        print("\n📊 测试工作流提取...")
        
        if not hasattr(self.__class__, 'literature_results'):
            self.skipTest("没有文献检索结果可用")
        
        try:
            from core.paper_workflow import PaperWorkflow
            
            papers = self.__class__.literature_results.get('papers', [])
            test_papers = papers[:3]  # 使用前3篇进行测试
            
            workflow_extractor = PaperWorkflow(self.api_client)
            
            workflows = []
            for i, paper in enumerate(test_papers):
                print(f"  提取论文 {i+1}/{len(test_papers)} 的工作流...")
                workflow = workflow_extractor.extract_workflow(paper)
                workflows.append(workflow)
                print(f"  ✅ 提取成功")
            
            # 验证结果
            self.assertTrue(len(workflows) > 0)
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.output_dir, f"workflows_{timestamp}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(workflows, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 工作流提取完成，处理了 {len(workflows)} 篇论文")
            print(f"💾 结果保存至: {output_file}")
            
            # 保存结果供后续测试使用
            self.__class__.workflows = workflows
            
        except Exception as e:
            self.fail(f"工作流提取失败: {e}")
    
    def test_04_hypothesis_design(self):
        """测试假设和实验设计"""
        print("\n🧪 测试假设和实验设计...")
        
        try:
            hypothesis_designer = EnhancedHypothesisExperimentDesigner(
                api_client=self.api_client,
                agent_manager=self.agent_manager
            )
            
            # 创建假设
            hypothesis_result = hypothesis_designer.generate_research_hypothesis(
                self.research_topic
            )
            
            # 验证结果
            self.assertIsNotNone(hypothesis_result)
            self.assertIn('hypothesis', hypothesis_result)
            
            # 设计实验
            experiment_plan = hypothesis_designer.design_experiment(
                hypothesis_result['hypothesis'],
                self.research_topic
            )
            
            # 验证结果
            self.assertIsNotNone(experiment_plan)
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.output_dir, f"experiment_plan_{timestamp}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump({
                    'hypothesis': hypothesis_result,
                    'experiment_plan': experiment_plan
                }, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 假设和实验设计完成")
            print(f"💾 结果保存至: {output_file}")
            
            # 保存结果供后续测试使用
            self.__class__.hypothesis = hypothesis_result
            self.__class__.experiment_plan = experiment_plan
            
        except Exception as e:
            self.fail(f"假设和实验设计失败: {e}")
    
    def test_05_experiment_code(self):
        """测试实验代码生成"""
        print("\n💻 测试实验代码生成...")
        
        if not hasattr(self.__class__, 'experiment_plan'):
            self.skipTest("没有实验计划可用")
        
        try:
            code_generator = ExperimentCodeGenerator(self.api_client)
            
            # 生成代码
            code_result = code_generator.generate_experiment_code(
                self.__class__.experiment_plan,
                framework="pytorch",
                code_style="academic"
            )
            
            # 验证结果
            self.assertIsNotNone(code_result)
            self.assertIn('code', code_result)
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.output_dir, f"experiment_code_{timestamp}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(code_result, f, ensure_ascii=False, indent=2, default=str)
            
            # 保存代码文件
            code_file = os.path.join(self.output_dir, f"experiment_{timestamp}.py")
            with open(code_file, 'w', encoding='utf-8') as f:
                f.write(code_result.get('code', ''))
            
            print(f"✅ 实验代码生成完成")
            print(f"💾 结果保存至: {output_file}")
            print(f"💾 代码保存至: {code_file}")
            
            # 保存结果供后续测试使用
            self.__class__.code_result = code_result
            
        except Exception as e:
            self.fail(f"实验代码生成失败: {e}")
    
    def test_06_visualization_planning(self):
        """测试可视化方案设计"""
        print("\n📊 测试可视化方案设计...")
        
        if not hasattr(self.__class__, 'experiment_plan'):
            self.skipTest("没有实验计划可用")
        
        try:
            visualization_advisor = EnhancedVisualizationAdvisor(
                api_client=self.api_client,
                agent_manager=self.agent_manager
            )
            
            # 设计可视化方案
            visualization_plan = visualization_advisor.generate_visualization_plan(
                research_topic=self.research_topic,
                experiment_plan=self.__class__.experiment_plan
            )
            
            # 验证结果
            self.assertIsNotNone(visualization_plan)
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.output_dir, f"visualization_plan_{timestamp}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(visualization_plan, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 可视化方案设计完成")
            print(f"💾 结果保存至: {output_file}")
            
            # 保存结果供后续测试使用
            self.__class__.visualization_plan = visualization_plan
            
        except Exception as e:
            self.fail(f"可视化方案设计失败: {e}")
    
    def test_07_paper_generation(self):
        """测试论文生成"""
        print("\n📝 测试论文生成...")
        
        try:
            paper_writer = AIScientistV2IntegratedWriter()
            
            # 生成论文
            paper_result = paper_writer.generate_paper(
                research_topic=self.research_topic,
                target_venue=self.target_venue,
                use_ai_scientist=False,  # 在测试中使用Brain Writer
                paper_type="research",
                optimize_paper=True
            )
            
            # 验证结果
            self.assertIsNotNone(paper_result)
            self.assertIn('title', paper_result)
            self.assertIn('latex_content', paper_result)
            
            # 集成实验代码（如果有）
            if hasattr(self.__class__, 'code_result') and hasattr(self.__class__, 'visualization_plan'):
                enhanced_content = paper_writer.integrate_experiment_code(
                    paper_result['paper_content'],
                    {
                        'code_implementation': {'description': self.__class__.code_result.get('code_description', '')},
                        'experimental_results': {'description': 'The experimental results demonstrate the effectiveness of our approach.'},
                        'visualization': self.__class__.visualization_plan
                    }
                )
                
                # 重新生成LaTeX
                latex_content = paper_writer.latex_generator.generate_latex_paper(
                    enhanced_content, venue=self.target_venue
                )
                
                # 保存增强后的LaTeX
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                enhanced_latex_file = os.path.join(self.output_dir, f"enhanced_paper_{timestamp}.tex")
                with open(enhanced_latex_file, 'w', encoding='utf-8') as f:
                    f.write(latex_content)
                
                print(f"✅ 论文增强完成")
                print(f"💾 增强后的论文保存至: {enhanced_latex_file}")
            
            print(f"✅ 论文生成完成")
            print(f"📄 标题: {paper_result.get('title', 'Untitled')}")
            print(f"📊 质量评分: {paper_result.get('quality_score', 0):.2f}/10")
            
            # 保存结果供后续测试使用
            self.__class__.paper_result = paper_result
            
        except Exception as e:
            self.fail(f"论文生成失败: {e}")
    
    def test_08_end_to_end_integration(self):
        """测试端到端集成"""
        print("\n🔄 测试端到端集成...")
        
        try:
            # 创建结果摘要
            summary = {
                'research_topic': self.research_topic,
                'target_venue': self.target_venue,
                'timestamp': datetime.now().isoformat(),
                'components_tested': [
                    'API Connection',
                    'Literature Search',
                    'Workflow Extraction',
                    'Hypothesis Design',
                    'Experiment Code Generation',
                    'Visualization Planning',
                    'Paper Generation'
                ],
                'results': {}
            }
            
            # 添加各组件结果
            if hasattr(self.__class__, 'literature_results'):
                summary['results']['literature_search'] = {
                    'papers_found': len(self.__class__.literature_results.get('papers', [])),
                    'success': True
                }
            
            if hasattr(self.__class__, 'workflows'):
                summary['results']['workflow_extraction'] = {
                    'workflows_extracted': len(self.__class__.workflows),
                    'success': True
                }
            
            if hasattr(self.__class__, 'hypothesis'):
                summary['results']['hypothesis_design'] = {
                    'hypothesis': self.__class__.hypothesis.get('hypothesis', ''),
                    'success': True
                }
            
            if hasattr(self.__class__, 'code_result'):
                summary['results']['code_generation'] = {
                    'success': True
                }
            
            if hasattr(self.__class__, 'visualization_plan'):
                summary['results']['visualization_planning'] = {
                    'success': True
                }
            
            if hasattr(self.__class__, 'paper_result'):
                summary['results']['paper_generation'] = {
                    'title': self.__class__.paper_result.get('title', ''),
                    'quality_score': self.__class__.paper_result.get('quality_score', 0),
                    'success': True
                }
            
            # 计算总体成功率
            components_total = len(summary['components_tested'])
            components_success = sum(1 for comp in summary['results'].values() if comp.get('success', False))
            success_rate = components_success / components_total * 100 if components_total > 0 else 0
            
            summary['overall_success_rate'] = success_rate
            
            # 保存摘要
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.output_dir, f"integration_test_{timestamp}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(summary, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 端到端集成测试完成")
            print(f"📊 总体成功率: {success_rate:.1f}%")
            print(f"💾 测试摘要保存至: {output_file}")
            
        except Exception as e:
            self.fail(f"端到端集成测试失败: {e}")

if __name__ == '__main__':
    unittest.main()
