"""
完整论文撰写系统测试脚本

测试完整的论文生成工作流，包括：
1. 工作流提取
2. 多专家评审
3. 自动修订
4. 统一输出
"""

import os
import sys
import time
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from paper_generation.unified_paper_workflow import (
    UnifiedPaperGenerationWorkflow, UnifiedWorkflowConfig, PaperGenerationConfig
)


def test_complete_paper_generation():
    """测试完整的论文生成系统"""
    
    print("🚀 启动完整论文撰写系统测试")
    print("=" * 60)
    
    # 创建配置
    paper_config = PaperGenerationConfig(
        target_venue="ICML",
        paper_type="research",
        max_review_iterations=2,  # 限制迭代次数以节省时间
        quality_threshold=6.5,
        enable_auto_revision=True,
        enable_multi_expert_review=True,
        latex_output=False  # 暂时关闭LaTeX输出以简化测试
    )
    
    unified_config = UnifiedWorkflowConfig(
        enable_workflow_extraction=True,
        workflow_analysis_depth="standard",
        paper_generation_config=paper_config,
        output_formats=['markdown', 'json'],
        save_intermediate_results=True,
        output_directory="paper_output"
    )
    
    # 初始化统一工作流
    print("🔧 初始化统一论文生成工作流...")
    workflow = UnifiedPaperGenerationWorkflow(config=unified_config)
    
    # 定义测试研究主题
    research_topics = [
        "Brain-Inspired Continual Learning for Dynamic Environments",
        "Adaptive Neural Plasticity Mechanisms for Efficient Learning",
        "Neuromodulated Attention Networks for Visual Recognition"
    ]
    
    # 选择一个研究主题进行测试
    selected_topic = research_topics[0]
    print(f"📋 选择研究主题: {selected_topic}")
    
    # 提供研究上下文
    research_context = {
        "domain": "machine learning",
        "subfield": "continual learning",
        "motivation": "Address catastrophic forgetting in neural networks",
        "target_applications": ["computer vision", "robotics", "autonomous systems"],
        "constraints": ["computational efficiency", "memory limitations"]
    }
    
    # 执行完整的论文生成流程
    print("\\n🚀 开始论文生成流程...")
    print("-" * 40)
    
    start_time = time.time()
    
    try:
        result = workflow.generate_complete_research_paper(
            research_topic=selected_topic,
            research_context=research_context
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 分析和显示结果
        print("\\n" + "=" * 60)
        print("📊 论文生成结果分析")
        print("=" * 60)
        
        print(f"✅ 生成状态: {'成功' if result.success else '失败'}")
        print(f"⏱️ 总用时: {total_time:.1f}秒")
        print(f"🎯 最终评分: {result.paper_generation.quality_metrics.overall_score:.2f}/10")
        
        # 详细质量指标
        metrics = result.paper_generation.quality_metrics
        print(f"\\n📈 质量指标详情:")
        print(f"  • 新颖性: {metrics.novelty_score:.2f}/10")
        print(f"  • 技术质量: {metrics.technical_quality_score:.2f}/10") 
        print(f"  • 清晰度: {metrics.clarity_score:.2f}/10")
        print(f"  • 重要性: {metrics.significance_score:.2f}/10")
        print(f"  • 可重现性: {metrics.reproducibility_score:.2f}/10")
        print(f"  • 专家共识: {metrics.expert_consensus:.2f}")
        
        # 工作流分析结果
        workflow_result = result.workflow_extraction
        if workflow_result.success:
            print(f"\\n🔍 工作流分析结果:")
            print(f"  • 提取工作流: {len(workflow_result.extracted_workflows)} 个")
            print(f"  • 研究空白: {len(workflow_result.research_gaps)} 个")
            print(f"  • 创新机会: {len(workflow_result.innovation_opportunities)} 个")
            print(f"  • 方法论洞察: {len(workflow_result.methodology_insights)} 个")
        
        # 评审和修订历史
        print(f"\\n🔄 评审和修订历史:")
        print(f"  • 评审轮次: {len(result.paper_generation.review_history)}")
        print(f"  • 修订轮次: {len(result.paper_generation.revision_history)}")
        
        if result.paper_generation.review_history:
            final_review = result.paper_generation.review_history[-1]
            print(f"  • 最终建议: {final_review.final_recommendation}")
            print(f"  • 专家评审数: {len(final_review.expert_reviews)}")
        
        # 输出文件
        print(f"\\n📁 输出文件:")
        for format_type, file_path in result.output_files.items():
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"  • {format_type.upper()}: {file_path} ({file_size:.1f} KB)")
            else:
                print(f"  • {format_type.upper()}: 生成失败")
        
        # 集成洞察
        insights = result.integration_insights
        print(f"\\n🔗 集成分析:")
        print(f"  • 工作流-论文一致性: {insights.get('workflow_paper_alignment', 0):.2f}")
        print(f"  • 创新实现度: {insights.get('innovation_realization', 0):.2f}")
        print(f"  • 质量改进潜力: {insights.get('quality_improvement_potential', 0):.2f}")
        
        recommendations = insights.get('recommendations', [])
        if recommendations:
            print(f"\\n💡 改进建议:")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        
        # 论文内容概览
        paper_content = result.paper_generation.paper_content
        if paper_content:
            print(f"\\n📄 论文内容概览:")
            print(f"  • 标题: {paper_content.get('title', 'N/A')}")
            sections = paper_content.get('sections', {})
            print(f"  • 章节数: {len(sections)}")
            
            total_length = sum(len(str(content)) for content in sections.values())
            print(f"  • 总字数: {total_length:,} 字符")
            
            if 'abstract' in sections:
                abstract = sections['abstract']
                print(f"  • 摘要长度: {len(abstract)} 字符")
                print(f"  • 摘要预览: {abstract[:200]}...")
        
        # 总结评估
        print(f"\\n🎯 总结评估:")
        if result.success:
            if metrics.overall_score >= 7.5:
                print("🟢 优秀论文 - 可直接提交顶级会议")
            elif metrics.overall_score >= 6.5:
                print("🟡 良好论文 - 小幅修订后可提交")
            elif metrics.overall_score >= 5.5:
                print("🟠 需要改进 - 大幅修订后可能接受")
            else:
                print("🔴 质量不足 - 需要重大改进")
        else:
            print("❌ 生成失败 - 请检查配置和错误信息")
        
        # 保存测试报告
        save_test_report(result, total_time, selected_topic)
        
        return result
        
    except Exception as e:
        print(f"\\n❌ 测试过程中发生错误: {e}")
        print(f"⏱️ 错误发生时间: {time.time() - start_time:.1f}秒")
        import traceback
        print(f"🔍 错误详情: {traceback.format_exc()}")
        return None


def save_test_report(result, total_time, research_topic):
    """保存测试报告"""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = f"paper_output/test_report_{timestamp}.md"
    
    try:
        os.makedirs("paper_output", exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"# 论文生成系统测试报告\\n\\n")
            f.write(f"**测试时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n")
            f.write(f"**研究主题**: {research_topic}\\n")
            f.write(f"**总用时**: {total_time:.1f}秒\\n")
            f.write(f"**生成状态**: {'✅ 成功' if result.success else '❌ 失败'}\\n\\n")
            
            if result.success:
                metrics = result.paper_generation.quality_metrics
                f.write(f"## 质量评估\\n\\n")
                f.write(f"- **总体评分**: {metrics.overall_score:.2f}/10\\n")
                f.write(f"- **新颖性**: {metrics.novelty_score:.2f}/10\\n")
                f.write(f"- **技术质量**: {metrics.technical_quality_score:.2f}/10\\n")
                f.write(f"- **清晰度**: {metrics.clarity_score:.2f}/10\\n")
                f.write(f"- **重要性**: {metrics.significance_score:.2f}/10\\n")
                f.write(f"- **可重现性**: {metrics.reproducibility_score:.2f}/10\\n")
                f.write(f"- **专家共识**: {metrics.expert_consensus:.2f}\\n\\n")
                
                f.write(result.generation_summary)
            
            f.write(f"\\n\\n---\\n*报告由完整论文撰写系统自动生成*")
        
        print(f"\\n📄 测试报告已保存: {report_path}")
        
    except Exception as e:
        print(f"\\n⚠️ 测试报告保存失败: {e}")


def quick_functionality_test():
    """快速功能测试"""
    
    print("🧪 执行快速功能测试...")
    
    try:
        # 测试核心模块导入
        print("  📦 测试模块导入...")
        from paper_generation.review_system.multi_expert_review_system import MultiExpertReviewSystem
        from paper_generation.review_system.auto_revision_engine import AutoRevisionEngine
        from paper_generation.enhanced_brain_paper_writer import EnhancedBrainPaperWriter
        print("    ✅ 所有核心模块导入成功")
        
        # 测试基础配置
        print("  ⚙️ 测试配置系统...")
        config = PaperGenerationConfig(
            target_venue="ICML",
            enable_multi_expert_review=False,  # 快速测试关闭
            enable_auto_revision=False
        )
        writer = EnhancedBrainPaperWriter(config=config)
        print("    ✅ 配置系统正常")
        
        print("✅ 快速功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 快速功能测试失败: {e}")
        return False


if __name__ == "__main__":
    print("🎯 完整论文撰写系统测试开始")
    print("=" * 60)
    
    # 首先执行快速功能测试
    if quick_functionality_test():
        print("\\n" + "=" * 60)
        # 执行完整测试
        result = test_complete_paper_generation()
        
        if result and result.success:
            print("\\n🎉 完整测试成功完成！")
        else:
            print("\\n⚠️ 测试完成但存在问题，请查看详细信息")
    else:
        print("\\n❌ 快速功能测试失败，跳过完整测试")
    
    print("\\n" + "=" * 60)
    print("📋 测试总结完成")
