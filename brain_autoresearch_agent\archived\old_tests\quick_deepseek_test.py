"""
Quick DeepSeek API Validation Script
Fast test to verify DeepSeek API functionality with English prompts
"""

import os
import sys
from datetime import datetime

# Add project path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient


def quick_deepseek_test():
    """Quick DeepSeek API functionality test"""
    print("🚀 Quick DeepSeek API Validation")
    print("=" * 50)
    
    # Setup API
    api_key = "sk-1b1d72e2e10643029de548b655e1f93e"
    os.environ["DEEPSEEK_API_KEY"] = api_key
    os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
    
    print(f"🔑 API configured")
    
    # Test 1: Basic connection
    print(f"\\n📝 Test 1: Basic connection with deepseek-chat")
    try:
        client = LLMClient(provider="deepseek", model="deepseek-chat", temperature=0.7)
        response = client.generate_response(
            "Explain neural plasticity in one sentence."
        )
        print(f"✅ Chat model: OK")
        print(f"📄 Response: {response[:100]}...")
    except Exception as e:
        print(f"❌ Chat model failed: {e}")
        return False
    
    # Test 2: Reasoning model
    print(f"\\n🧠 Test 2: Reasoning model test")
    try:
        reasoner = LLMClient(provider="deepseek", model="deepseek-reasoner", temperature=0.7)
        response = reasoner.generate_response(
            "Analyze the potential of brain-inspired AI in three key points."
        )
        print(f"✅ Reasoning model: OK")
        print(f"📄 Response length: {len(response)} characters")
    except Exception as e:
        print(f"❌ Reasoning model failed: {e}")
        return False
    
    # Test 3: Complex prompt
    print(f"\\n🔬 Test 3: Complex technical prompt")
    try:
        complex_prompt = """
        Design a brief research proposal for: 'Synaptic Plasticity-Inspired Learning in Deep Networks'
        Include: 1) Objective, 2) Method, 3) Expected outcome
        Keep response under 200 words.
        """
        response = client.generate_response(complex_prompt)
        print(f"✅ Complex prompt: OK")
        print(f"📊 Response length: {len(response)} characters")
        
        if len(response) > 100:
            print(f"\\n📋 Sample response:")
            print("-" * 30)
            print(response[:300] + "..." if len(response) > 300 else response)
            print("-" * 30)
            
    except Exception as e:
        print(f"❌ Complex prompt failed: {e}")
        return False
    
    print(f"\\n🎉 All tests passed! DeepSeek API is ready for paper generation.")
    return True


if __name__ == "__main__":
    success = quick_deepseek_test()
    if success:
        print(f"\\n💡 Next steps:")
        print(f"  1. Run: python test_deepseek_api.py (for comprehensive test)")
        print(f"  2. Run: python demo_deepseek_english.py (for full paper generation)")
    else:
        print(f"\\n⚠️ Please check your API key and network connection")
