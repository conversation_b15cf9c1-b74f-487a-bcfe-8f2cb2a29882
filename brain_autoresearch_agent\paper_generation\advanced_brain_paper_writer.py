"""
增强版论文撰写器 - 结合旧系统和AI Scientist v2优势

整合了详细的章节生成、实验驱动内容、多轮引用收集等功能
"""

import os
import sys
import json
import time
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
import logging

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from agents.agent_manager import AgentManager
from core.paper_workflow import PaperWorkflowExtractor
from paper_generation.review_system.multi_expert_review_system import (
    MultiExpertReviewSystem, PaperReviewResult
)
from paper_generation.review_system.auto_revision_engine import (
    AutoRevisionEngine, PaperRevisionSession
)
from paper_generation.latex_generator import LaTeXGenerator

# 导入旧系统的功能模块
from reasoning.multi_agent_reasoning import MultiAgentReasoning
from reasoning.knowledge_fusion import KnowledgeFusion
from core.hybrid_literature_tool import HybridLiteratureTool


from paper_generation.enhanced_brain_paper_writer import PaperGenerationConfig


@dataclass
class AdvancedPaperConfig:
    """高级论文生成配置"""
    target_venue: str = "ICML"
    paper_type: str = "research"
    max_review_iterations: int = 3
    quality_threshold: float = 7.0
    enable_auto_revision: bool = True
    enable_multi_expert_review: bool = True
    latex_output: bool = True
    
    # 新增：详细控制选项
    enable_detailed_section_generation: bool = True
    enable_async_processing: bool = True
    enable_knowledge_fusion: bool = True
    max_parallel_agents: int = 5
    enable_experiment_integration: bool = True
    enable_iterative_citations: bool = True
    num_citation_rounds: int = 10
    enable_latex_compilation: bool = False
    enable_async_processing: bool = True
    enable_citation_rounds: int = 10
    enable_experiment_integration: bool = True
    page_limit: int = 8
    n_writeup_reflections: int = 3


class AdvancedBrainPaperWriter:
    """
    高级论文撰写系统 - 整合所有优势功能
    
    结合了：
    1. 旧系统的详细章节生成
    2. 新系统的多专家评审
    3. AI Scientist v2的实验驱动方法
    """
    
    def __init__(self, llm_client: Optional[LLMClient] = None, 
                 config = None):
        """初始化高级论文撰写系统"""
        
        if llm_client is None:
            self.llm_client = LLMClient()
        else:
            self.llm_client = llm_client
        
        # 兼容不同的配置类型
        if config is None:
            self.config = AdvancedPaperConfig()
        elif isinstance(config, PaperGenerationConfig):
            # 从PaperGenerationConfig创建AdvancedPaperConfig
            self.config = AdvancedPaperConfig(
                target_venue=config.target_venue,
                paper_type=config.paper_type,
                max_review_iterations=config.max_review_iterations,
                quality_threshold=config.quality_threshold,
                enable_auto_revision=config.enable_auto_revision,
                enable_multi_expert_review=config.enable_multi_expert_review,
                latex_output=config.latex_output,
                enable_detailed_section_generation=True,
                enable_async_processing=getattr(config, 'enable_async_processing', True),
                enable_knowledge_fusion=True,
                max_parallel_agents=5,
                enable_experiment_integration=getattr(config, 'include_experiments', True),
                enable_iterative_citations=getattr(config, 'enable_citation_validation', True),
                num_citation_rounds=getattr(config, 'num_citation_rounds', 10),
                enable_latex_compilation=getattr(config, 'enable_latex_compilation', False)
            )
        else:
            self.config = config
            
        self.agent_manager = AgentManager(self.llm_client)
        self.workflow_extractor = PaperWorkflowExtractor(self.llm_client)
        
        # 初始化旧系统的组件
        if self.config.enable_async_processing:
            self.multi_agent_reasoning = MultiAgentReasoning(
                agent_manager=self.agent_manager, 
                llm_client=self.llm_client
            )
            self.knowledge_fusion = KnowledgeFusion()  # 不需要传递参数
        
        self.literature_tool = HybridLiteratureTool()
        
        # 初始化评审和修订系统
        if self.config.enable_multi_expert_review:
            self.review_system = MultiExpertReviewSystem(self.llm_client)
        
        if self.config.enable_auto_revision:
            self.revision_engine = AutoRevisionEngine(self.llm_client)
        
        # LaTeX生成器
        if self.config.latex_output:
            self.latex_generator = LaTeXGenerator()
        
        self.logger = self._setup_logger()
        
        # 详细的章节生成映射 (从旧系统继承)
        self.section_generators = {
            'abstract': self._generate_abstract_detailed,
            'introduction': self._generate_introduction_detailed,
            'related_work': self._generate_related_work_detailed,
            'methodology': self._generate_methodology_detailed,
            'experiments': self._generate_experiments_detailed,
            'results': self._generate_results_detailed,
            'discussion': self._generate_discussion_detailed,
            'conclusion': self._generate_conclusion_detailed
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('AdvancedBrainPaperWriter')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def generate_comprehensive_paper(self, research_topic: str,
                                         experiment_data: Optional[Dict[str, Any]] = None,
                                         research_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        生成综合性论文 - 整合所有功能
        
        Args:
            research_topic: 研究主题
            experiment_data: 实验数据 (AI Scientist v2风格)
            research_context: 研究上下文
        """
        print(f"\\n🚀 启动高级论文生成系统")
        print(f"📋 研究主题: {research_topic}")
        print(f"🧪 实验数据: {'✅' if experiment_data else '❌'}")
        print(f"📊 目标质量: {self.config.quality_threshold}/10")
        
        generation_start_time = datetime.now()
        
        try:
            # 第1阶段: 增强研究分析 (并行处理)
            print(f"\\n📊 第1阶段: 增强研究分析")
            analysis_tasks = []
            
            if self.config.enable_async_processing:
                # 并行执行多个分析任务
                analysis_tasks = [
                    self._conduct_literature_analysis(research_topic),
                    self._conduct_expert_analysis(research_topic, research_context),
                    self._extract_workflow_insights(research_topic)
                ]
                
                literature_analysis, expert_analysis, workflow_insights = await asyncio.gather(*analysis_tasks)
            else:
                # 顺序执行
                literature_analysis = await self._conduct_literature_analysis(research_topic)
                expert_analysis = await self._conduct_expert_analysis(research_topic, research_context)
                workflow_insights = await self._extract_workflow_insights(research_topic)
            
            # 融合所有分析结果
            comprehensive_analysis = await self._fuse_analysis_results({
                'literature': literature_analysis,
                'expert': expert_analysis,
                'workflow': workflow_insights,
                'experiment': experiment_data
            })
            
            # 第2阶段: 多轮引用收集 (AI Scientist v2风格)
            print(f"\\n📚 第2阶段: 智能引用收集")
            citations = await self._collect_citations_iteratively(
                research_topic, comprehensive_analysis, self.config.enable_citation_rounds
            )
            
            # 第3阶段: 详细论文内容生成
            print(f"\\n📝 第3阶段: 详细论文内容生成")
            paper_content = await self._generate_detailed_paper_content(
                research_topic, comprehensive_analysis, citations, experiment_data
            )
            
            # 第4阶段: 多轮质量控制和反思
            print(f"\\n🔍 第4阶段: 多轮质量控制")
            final_paper = await self._iterative_quality_control(
                paper_content, comprehensive_analysis
            )
            
            # 第5阶段: LaTeX生成和编译验证
            print(f"\\n📄 第5阶段: LaTeX生成和验证")
            latex_output = await self._generate_and_validate_latex(final_paper)
            
            generation_time = (datetime.now() - generation_start_time).total_seconds()
            
            print(f"\\n✅ 高级论文生成完成")
            print(f"⏱️ 总用时: {generation_time:.1f}秒")
            
            return {
                'paper_content': final_paper,
                'comprehensive_analysis': comprehensive_analysis,
                'citations': citations,
                'latex_output': latex_output,
                'generation_metadata': {
                    'generation_time': generation_time,
                    'config': self.config.__dict__,
                    'timestamp': generation_start_time.isoformat()
                },
                'success': True
            }
            
        except Exception as e:
            self.logger.error(f"Advanced paper generation failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'generation_time': (datetime.now() - generation_start_time).total_seconds()
            }
    
    async def _conduct_literature_analysis(self, research_topic: str) -> Dict[str, Any]:
        """执行文献分析 (增强版)"""
        
        # 使用旧系统的详细搜索策略
        search_queries = await self._generate_comprehensive_search_queries(research_topic)
        
        literature_data = {}
        for query in search_queries:
            try:
                papers = self.literature_tool.search_papers(query, max_results=15)
                literature_data[query] = papers
                print(f"  📚 找到 {len(papers)} 篇论文: {query}")
            except Exception as e:
                print(f"  ⚠️ 搜索失败 '{query}': {e}")
                literature_data[query] = []
        
        # 深度分析文献
        literature_analysis = await self._deep_literature_analysis(literature_data)
        
        return {
            'search_queries': search_queries,
            'papers_found': literature_data,
            'analysis': literature_analysis,
            'total_papers': sum(len(papers) for papers in literature_data.values())
        }
    
    async def _conduct_expert_analysis(self, research_topic: str, 
                                     research_context: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """执行专家分析 (多代理协作)"""
        
        expert_analyses = {}
        
        # 获取各类专家的详细分析
        expert_types = ['ai_technology', 'neuroscience', 'data_analysis', 
                       'experiment_design', 'paper_writing']
        
        for expert_type in expert_types:
            expert = self.agent_manager.get_agent(expert_type)
            if expert:
                analysis = expert.analyze({
                    "input_text": research_topic,
                    "context": research_context,
                    "analysis_type": "comprehensive_research_analysis"
                })
                
                if analysis and hasattr(analysis, 'content'):
                    expert_analyses[expert_type] = analysis.content
        
        # 使用知识融合进行整合
        if self.config.enable_async_processing and expert_analyses:
            fused_analysis = self.knowledge_fusion.fuse_knowledge(
                list(expert_analyses.values())
            )
            expert_analyses['fused_insights'] = fused_analysis
        
        return expert_analyses
    
    async def _extract_workflow_insights(self, research_topic: str) -> Dict[str, Any]:
        """提取工作流洞察"""
        
        workflow_analysis = self.workflow_extractor.extract_research_workflow(research_topic)
        
        return {
            'extracted_workflow': workflow_analysis,
            'workflow_type': 'comprehensive' if workflow_analysis else 'basic'
        }
    
    async def _fuse_analysis_results(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """融合分析结果"""
        
        if self.config.enable_async_processing:
            # 使用知识融合系统
            all_insights = []
            
            for key, data in analysis_data.items():
                if data and isinstance(data, dict):
                    # 提取关键信息
                    insights = self._extract_key_insights_from_analysis(data)
                    all_insights.extend(insights)
            
            fused_insights = self.knowledge_fusion.fuse_knowledge(all_insights)
            
            return {
                'original_analyses': analysis_data,
                'fused_insights': fused_insights,
                'fusion_quality': 'high' if fused_insights else 'basic'
            }
        else:
            return analysis_data
    
    def _extract_key_insights_from_analysis(self, data: Dict[str, Any]) -> List[str]:
        """从分析数据中提取关键洞察"""
        
        insights = []
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, str) and len(value) > 50:
                    insights.append(f"{key}: {value[:200]}")
                elif isinstance(value, list):
                    insights.extend([str(item)[:200] for item in value[:3]])
        
        return insights
    
    async def _collect_citations_iteratively(self, research_topic: str,
                                           analysis: Dict[str, Any],
                                           num_rounds: int) -> List[Dict[str, Any]]:
        """迭代收集引用 (AI Scientist v2风格)"""
        
        citations = []
        
        for round_num in range(num_rounds):
            print(f"    📖 引用收集轮次 {round_num + 1}/{num_rounds}")
            
            # 基于已有分析确定搜索需求
            search_need = self._determine_citation_need(analysis, citations)
            
            if not search_need:
                print(f"      ✅ 引用收集完成")
                break
            
            # 搜索相关论文
            new_citations = await self._search_citations_for_need(search_need)
            
            if new_citations:
                citations.extend(new_citations)
                print(f"      📚 新增 {len(new_citations)} 个引用")
            else:
                print(f"      ⭕ 未找到新引用")
        
        return citations
    
    def _determine_citation_need(self, analysis: Dict[str, Any], 
                                existing_citations: List[Dict[str, Any]]) -> Optional[str]:
        """确定引用需求"""
        
        # 简化的需求确定逻辑
        citation_categories = [
            'foundational_work',
            'recent_advances', 
            'methodological_papers',
            'evaluation_benchmarks',
            'related_applications'
        ]
        
        # 检查每个类别的引用覆盖情况
        for category in citation_categories:
            category_citations = [c for c in existing_citations 
                                if c.get('category') == category]
            
            if len(category_citations) < 3:  # 每个类别至少3个引用
                return category
        
        return None
    
    async def _search_citations_for_need(self, search_need: str) -> List[Dict[str, Any]]:
        """为特定需求搜索引用"""
        
        search_queries = {
            'foundational_work': ['neural networks foundations', 'machine learning theory'],
            'recent_advances': ['recent advances', 'state of the art', '2023 2024'],
            'methodological_papers': ['methodology', 'algorithm', 'approach'],
            'evaluation_benchmarks': ['benchmark', 'evaluation', 'dataset'],
            'related_applications': ['applications', 'case study', 'real world']
        }
        
        queries = search_queries.get(search_need, [search_need])
        citations = []
        
        for query in queries:
            try:
                papers = self.literature_tool.search_papers(query, max_results=5)
                for paper in papers[:2]:  # 限制每个查询的结果数
                    citations.append({
                        'paper': paper,
                        'category': search_need,
                        'relevance_score': 0.8  # 简化的相关性评分
                    })
            except Exception as e:
                print(f"        ⚠️ 引用搜索失败: {e}")
        
        return citations
    
    async def _generate_detailed_paper_content(self, research_topic: str,
                                             analysis: Dict[str, Any],
                                             citations: List[Dict[str, Any]],
                                             experiment_data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """生成详细论文内容 (使用专门的章节生成器)"""
        
        sections = {}
        context = {
            'research_topic': research_topic,
            'analysis': analysis,
            'citations': citations,
            'experiment_data': experiment_data,
            'target_venue': self.config.target_venue
        }
        
        # 按顺序生成各个章节
        section_order = ['abstract', 'introduction', 'related_work', 'methodology',
                        'experiments', 'results', 'discussion', 'conclusion']
        
        for section_name in section_order:
            print(f"      📝 生成 {section_name}")
            
            if self.config.enable_detailed_section_generation and section_name in self.section_generators:
                # 使用专门的生成器
                section_content = await self.section_generators[section_name](context, sections)
            else:
                # 使用通用生成器
                section_content = await self._generate_section_generic(section_name, context, sections)
            
            sections[section_name] = section_content
            context['previous_sections'] = sections  # 更新上下文
        
        # 生成标题
        title = await self._generate_title_advanced(context)
        
        # 生成参考文献
        references = self._format_citations_as_references(citations)
        
        return {
            'title': title,
            'sections': sections,
            'references': references,
            'metadata': {
                'total_sections': len(sections),
                'total_citations': len(citations),
                'generation_type': 'detailed'
            }
        }
    
    async def _generate_abstract_detailed(self, context: Dict[str, Any], 
                                        previous_sections: Dict[str, str]) -> str:
        """详细的摘要生成 (继承自旧系统)"""
        
        prompt = f"""
        Write a comprehensive abstract for a {self.config.paper_type} paper on "{context['research_topic']}" 
        for {context['target_venue']}.

        Research Analysis:
        {json.dumps(context['analysis'], indent=2)[:1500]}...

        Key Requirements:
        1. Clear problem statement and motivation
        2. Brief methodology description  
        3. Key results and contributions
        4. Significance and impact
        5. 150-250 words
        6. Single paragraph format

        Generate a high-quality abstract that captures the essence of this research.
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            return response[0] if isinstance(response, tuple) else response
        except Exception as e:
            return f"[Abstract generation failed: {e}]"
    
    async def _generate_introduction_detailed(self, context: Dict[str, Any],
                                            previous_sections: Dict[str, str]) -> str:
        """详细的引言生成"""
        
        # 这里可以实现更复杂的引言生成逻辑
        # 包括背景介绍、问题陈述、贡献说明等
        
        prompt = f"""
        Write a comprehensive introduction for a {self.config.paper_type} paper on "{context['research_topic']}" 
        for {context['target_venue']}.

        Context and Analysis:
        {json.dumps(context['analysis'], indent=2)[:2000]}...

        Previous Abstract:
        {previous_sections.get('abstract', 'N/A')}

        Structure the introduction with:
        1. Background and context (2-3 paragraphs)
        2. Problem statement and motivation (1-2 paragraphs)
        3. Contributions and novelty (1 paragraph)
        4. Paper organization (1 paragraph)

        Ensure smooth flow and proper academic tone.
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            return response[0] if isinstance(response, tuple) else response
        except Exception as e:
            return f"[Introduction generation failed: {e}]"
    
    # 其他详细的章节生成方法...
    async def _generate_related_work_detailed(self, context: Dict[str, Any],
                                           previous_sections: Dict[str, str]) -> str:
        """详细的相关工作生成"""
        return "[Related Work section - detailed implementation needed]"
    
    async def _generate_methodology_detailed(self, context: Dict[str, Any],
                                          previous_sections: Dict[str, str]) -> str:
        """详细的方法论生成"""
        return "[Methodology section - detailed implementation needed]"
    
    async def _generate_experiments_detailed(self, context: Dict[str, Any],
                                          previous_sections: Dict[str, str]) -> str:
        """详细的实验生成"""
        return "[Experiments section - detailed implementation needed]"
    
    async def _generate_results_detailed(self, context: Dict[str, Any],
                                       previous_sections: Dict[str, str]) -> str:
        """详细的结果生成"""
        return "[Results section - detailed implementation needed]"
    
    async def _generate_discussion_detailed(self, context: Dict[str, Any],
                                         previous_sections: Dict[str, str]) -> str:
        """详细的讨论生成"""
        return "[Discussion section - detailed implementation needed]"
    
    async def _generate_conclusion_detailed(self, context: Dict[str, Any],
                                         previous_sections: Dict[str, str]) -> str:
        """详细的结论生成"""
        return "[Conclusion section - detailed implementation needed]"
    
    async def _generate_section_generic(self, section_name: str, 
                                      context: Dict[str, Any],
                                      previous_sections: Dict[str, str]) -> str:
        """通用章节生成"""
        
        prompt = f"""
        Write the {section_name} section for a {self.config.paper_type} paper on "{context['research_topic']}" 
        for {context['target_venue']}.

        Context: {json.dumps(context, default=str)[:1000]}...
        
        Generate comprehensive, academic-quality content for this section.
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            return response[0] if isinstance(response, tuple) else response
        except Exception as e:
            return f"[{section_name} generation failed: {e}]"
    
    async def _generate_title_advanced(self, context: Dict[str, Any]) -> str:
        """生成高级标题"""
        
        prompt = f"""
        Generate an engaging and informative title for a {self.config.paper_type} paper 
        on "{context['research_topic']}" for {context['target_venue']}.

        Research Context:
        {json.dumps(context['analysis'], indent=2)[:1000]}...

        Requirements:
        1. Concise and clear (under 15 words)
        2. Academically appropriate for {context['target_venue']}
        3. Highlights the main contribution
        4. Engaging and informative

        Provide only the title, no additional text.
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            title = response[0] if isinstance(response, tuple) else response
            return title.strip().strip('"').strip("'")
        except Exception as e:
            return f"Advanced Approach to {context['research_topic']}"
    
    def _format_citations_as_references(self, citations: List[Dict[str, Any]]) -> str:
        """格式化引用为参考文献"""
        
        references = []
        for i, citation in enumerate(citations, 1):
            paper = citation.get('paper', {})
            if isinstance(paper, dict):
                title = paper.get('title', 'Unknown Title')
                authors = paper.get('authors', [])
                year = paper.get('year', 'Unknown')
                
                author_names = []
                for author in authors:
                    if isinstance(author, dict):
                        author_names.append(author.get('name', 'Unknown'))
                    else:
                        author_names.append(str(author))
                
                reference = f"[{i}] {', '.join(author_names[:3])}. {title}. {year}."
                references.append(reference)
        
        return "\\n".join(references)
    
    async def _iterative_quality_control(self, paper_content: Dict[str, Any],
                                       analysis: Dict[str, Any]) -> Dict[str, Any]:
        """迭代质量控制"""
        
        current_paper = paper_content.copy()
        
        # 执行多轮反思和改进 (AI Scientist v2风格)
        for reflection_round in range(self.config.n_writeup_reflections):
            print(f"    🔍 质量反思轮次 {reflection_round + 1}/{self.config.n_writeup_reflections}")
            
            # 质量评估
            if self.config.enable_multi_expert_review:
                review_result = self.review_system.conduct_multi_expert_review(
                    current_paper, self.config.target_venue
                )
                
                print(f"      📊 当前评分: {review_result.overall_score:.2f}/10")
                
                # 如果达到质量阈值，提前结束
                if review_result.overall_score >= self.config.quality_threshold:
                    print(f"      ✅ 达到质量阈值")
                    break
                
                # 执行自动修订
                if self.config.enable_auto_revision and review_result.revision_plans:
                    revision_session = self.revision_engine.execute_paper_revision(
                        current_paper, review_result
                    )
                    
                    if revision_session.improvement_metrics.get('overall_improvement', 0) > 0.1:
                        current_paper = revision_session.final_paper
                        print(f"      🔧 修订完成")
        
        return current_paper
    
    async def _generate_and_validate_latex(self, paper_content: Dict[str, Any]) -> Optional[str]:
        """生成和验证LaTeX"""
        
        if not self.config.latex_output:
            return None
        
        try:
            latex_output = self.latex_generator.generate_paper_latex(paper_content)
            
            # 这里可以添加LaTeX编译验证逻辑 (类似AI Scientist v2)
            # validation_result = self._validate_latex_compilation(latex_output)
            
            return latex_output
        except Exception as e:
            self.logger.warning(f"LaTeX generation failed: {e}")
            return None
    
    async def _generate_comprehensive_search_queries(self, research_topic: str) -> List[str]:
        """生成综合搜索查询"""
        
        # 结合旧系统的搜索策略
        base_queries = [
            research_topic,
            f"{research_topic} neural networks",
            f"{research_topic} machine learning",
            f"{research_topic} artificial intelligence",
            f"brain inspired {research_topic}",
            f"{research_topic} deep learning"
        ]
        
        # 添加更多专门化查询
        specialized_queries = await self._generate_specialized_queries(research_topic)
        
        return base_queries + specialized_queries
    
    async def _generate_specialized_queries(self, research_topic: str) -> List[str]:
        """生成专门化查询"""
        
        prompt = f"""
        Generate 5 specialized search queries for academic papers related to "{research_topic}".
        
        Focus on:
        1. Technical methodologies
        2. Application domains  
        3. Evaluation benchmarks
        4. Recent advances
        5. Theoretical foundations
        
        Return as a simple list of queries, one per line.
        """
        
        try:
            response = self.llm_client.get_response(prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            
            queries = [line.strip() for line in response_text.split('\\n') 
                      if line.strip() and not line.startswith('#')]
            
            return queries[:5]  # 限制数量
        except Exception as e:
            return []
    
    async def _deep_literature_analysis(self, literature_data: Dict[str, List]) -> Dict[str, Any]:
        """深度文献分析"""
        
        all_papers = []
        for papers in literature_data.values():
            all_papers.extend(papers)
        
        if not all_papers:
            return {'status': 'no_papers_found'}
        
        # 分析论文特征
        analysis = {
            'total_papers': len(all_papers),
            'recent_papers': len([p for p in all_papers 
                                if isinstance(p, dict) and p.get('year', 0) >= 2020]),
            'highly_cited': len([p for p in all_papers 
                               if isinstance(p, dict) and p.get('citationCount', 0) > 100]),
            'key_venues': self._extract_key_venues(all_papers),
            'main_themes': self._extract_main_themes(all_papers)
        }
        
        return analysis
    
    def _extract_key_venues(self, papers: List[Dict]) -> List[str]:
        """提取关键发表场所"""
        venues = {}
        for paper in papers:
            if isinstance(paper, dict) and 'venue' in paper:
                venue = paper['venue']
                venues[venue] = venues.get(venue, 0) + 1
        
        # 返回出现频率最高的场所
        sorted_venues = sorted(venues.items(), key=lambda x: x[1], reverse=True)
        return [venue for venue, count in sorted_venues[:5]]
    
    def _extract_main_themes(self, papers: List[Dict]) -> List[str]:
        """提取主要主题"""
        # 简化的主题提取
        themes = ['deep learning', 'neural networks', 'machine learning', 
                 'artificial intelligence', 'computer vision', 'natural language processing']
        
        theme_counts = {}
        for paper in papers:
            if isinstance(paper, dict) and 'title' in paper:
                title_lower = paper['title'].lower()
                for theme in themes:
                    if theme in title_lower:
                        theme_counts[theme] = theme_counts.get(theme, 0) + 1
        
        sorted_themes = sorted(theme_counts.items(), key=lambda x: x[1], reverse=True)
        return [theme for theme, count in sorted_themes[:3]]
    
    # ============ 详细章节生成方法 (测试中缺失的) ============
    
    def _generate_abstract_detailed_with_review(self, context: Dict[str, Any], 
                                               review_criteria: Optional[Dict] = None) -> str:
        """生成带评审的详细摘要"""
        print("      🔍 生成详细摘要 (带评审)")
        
        abstract_prompt = f"""
        Generate a comprehensive abstract for a {self.config.paper_type} paper on:
        "{context.get('research_topic', 'Brain-Inspired Intelligence Research')}"
        
        Target venue: {self.config.target_venue}
        
        Context: {json.dumps(context, indent=2)[:1500]}...
        
        Requirements:
        1. Clear problem statement and motivation
        2. Novel contributions and methodology
        3. Key experimental results and insights
        4. Significance and impact
        5. 150-250 words, academically precise
        
        Generate a high-quality abstract that would impress {self.config.target_venue} reviewers.
        """
        
        try:
            # 获取AI技术专家生成
            ai_expert = self.agent_manager.get_agent('ai_technology')
            if ai_expert:
                response = ai_expert.analyze({
                    "input_text": abstract_prompt,
                    "analysis_type": "abstract_generation"
                })
                if response and hasattr(response, 'content'):
                    abstract = response.content.strip()
                    
                    # 如果启用评审，进行质量检查
                    if self.config.enable_multi_expert_review and hasattr(self, 'review_system'):
                        review_result = self.review_system.review_section(
                            abstract, "abstract", self.config.target_venue
                        )
                        if review_result and review_result.overall_score >= 7.0:
                            return abstract
                    else:
                        return abstract
            
            # 备用LLM生成
            response = self.llm_client.get_response(abstract_prompt)
            return response[0] if isinstance(response, tuple) else response
            
        except Exception as e:
            self.logger.warning(f"Detailed abstract generation failed: {e}")
            return f"[Abstract generation failed: {e}]"
    
    def _generate_introduction_detailed_with_novelty(self, context: Dict[str, Any],
                                                    innovation_focus: Optional[List[str]] = None) -> str:
        """生成带创新性检查的详细引言"""
        print("      🔍 生成详细引言 (带创新性检查)")
        
        intro_prompt = f"""
        Write a comprehensive introduction for a {self.config.paper_type} paper on:
        "{context.get('research_topic', 'Brain-Inspired Intelligence Research')}"
        
        Target venue: {self.config.target_venue}
        Innovation focus: {innovation_focus or ['brain-inspired architectures', 'adaptive learning', 'neural mechanisms']}
        
        Context: {json.dumps(context, indent=2)[:1500]}...
        
        Structure:
        1. Broad context and motivation (2-3 paragraphs)
        2. Problem statement and challenges (1-2 paragraphs)
        3. Research gaps and limitations of existing work (1-2 paragraphs)
        4. Our novel contributions and approach (2-3 paragraphs)
        5. Paper organization (1 paragraph)
        
        Requirements:
        - Highlight novelty and significance
        - Clear positioning against related work
        - Compelling motivation for brain-inspired approaches
        - 800-1200 words, academically rigorous
        
        Generate a compelling introduction that establishes clear novelty.
        """
        
        try:
            # 多专家协作生成
            experts = ['ai_technology', 'neuroscience', 'paper_writing']
            expert_inputs = []
            
            for expert_type in experts:
                expert = self.agent_manager.get_agent(expert_type)
                if expert:
                    response = expert.analyze({
                        "input_text": intro_prompt,
                        "analysis_type": "introduction_generation",
                        "focus": expert_type
                    })
                    if response and hasattr(response, 'content'):
                        expert_inputs.append(response.content)
            
            # 知识融合
            if expert_inputs and hasattr(self, 'knowledge_fusion'):
                fused_content = self.knowledge_fusion.fuse_expert_knowledge({
                    'topic': 'introduction_generation',
                    'expert_inputs': expert_inputs,
                    'target': 'comprehensive_introduction'
                })
                return fused_content.get('fused_content', expert_inputs[0] if expert_inputs else '')
            
            # 备用单一生成
            response = self.llm_client.get_response(intro_prompt)
            return response[0] if isinstance(response, tuple) else response
            
        except Exception as e:
            self.logger.warning(f"Detailed introduction generation failed: {e}")
            return f"[Introduction generation failed: {e}]"
    
    def _generate_methodology_detailed_with_validation(self, context: Dict[str, Any],
                                                      experimental_design: Optional[Dict] = None) -> str:
        """生成带验证的详细方法论"""
        print("      🔍 生成详细方法论 (带验证)")
        
        methodology_prompt = f"""
        Write a comprehensive methodology section for:
        "{context.get('research_topic', 'Brain-Inspired Intelligence Research')}"
        
        Target venue: {self.config.target_venue}
        Experimental design: {experimental_design or 'To be specified'}
        
        Context: {json.dumps(context, indent=2)[:1500]}...
        
        Structure:
        1. Overall approach and framework (2-3 paragraphs)
        2. Brain-inspired architectural components (3-4 paragraphs)
        3. Learning algorithms and mechanisms (2-3 paragraphs)
        4. Implementation details and optimization (1-2 paragraphs)
        5. Theoretical analysis and justification (1-2 paragraphs)
        
        Requirements:
        - Technical depth appropriate for {self.config.target_venue}
        - Clear algorithmic descriptions
        - Theoretical grounding
        - Implementation feasibility
        - 1000-1500 words, technically precise
        
        Generate a rigorous methodology that can be reproduced.
        """
        
        try:
            # 获取实验设计专家
            exp_expert = self.agent_manager.get_agent('experiment_design')
            if exp_expert:
                response = exp_expert.analyze({
                    "input_text": methodology_prompt,
                    "analysis_type": "methodology_generation"
                })
                if response and hasattr(response, 'content'):
                    methodology = response.content.strip()
                    
                    # 验证方法论的完整性
                    validation_result = self._validate_methodology(methodology)
                    if validation_result['is_complete']:
                        return methodology
                    else:
                        # 补充缺失部分
                        return self._enhance_methodology(methodology, validation_result['missing_elements'])
            
            # 备用生成
            response = self.llm_client.get_response(methodology_prompt)
            return response[0] if isinstance(response, tuple) else response
            
        except Exception as e:
            self.logger.warning(f"Detailed methodology generation failed: {e}")
            return f"[Methodology generation failed: {e}]"
    
    def generate_paper_with_quality_control(self, research_topic: str, 
                                           research_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        生成带质量控制的完整论文 (兼容接口)
        
        Args:
            research_topic: 研究主题
            research_context: 研究上下文信息
            
        Returns:
            Dict[str, Any]: 论文生成结果
        """
        print(f"\\n🔗 兼容接口调用：生成带质量控制的论文")
        
        # 委托给更高级的方法
        return self.generate_paper_with_comprehensive_quality_control(
            research_topic=research_topic,
            experimental_data=research_context.get('experimental_data') if research_context else None,
            quality_requirements={
                'min_expert_score': self.config.quality_threshold,
                'max_revision_rounds': self.config.max_review_iterations,
                'enable_data_integration': self.config.enable_experiment_integration
            }
        )
    
    def generate_paper_with_comprehensive_quality_control(self, research_topic: str,
                                                         experimental_data: Optional[Dict] = None,
                                                         quality_requirements: Optional[Dict] = None) -> Dict[str, Any]:
        """生成带综合质量控制的论文 (主要入口方法)"""
        print(f"\\n🎯 启动综合质量控制论文生成")
        print(f"📋 主题: {research_topic}")
        print(f"🧪 实验数据: {'✅' if experimental_data else '❌'}")
        
        quality_req = quality_requirements or {
            'min_expert_score': self.config.quality_threshold,
            'max_revision_rounds': self.config.max_review_iterations,
            'enable_data_integration': self.config.enable_experiment_integration
        }
        
        generation_start_time = datetime.now()
        
        try:
            # 第1阶段: 加载和集成实验数据
            if experimental_data and quality_req.get('enable_data_integration', True):
                print(f"\\n📊 第1阶段: 实验数据集成")
                integrated_data = self.load_and_integrate_experiment_data(experimental_data)
            else:
                integrated_data = {}
            
            # 第2阶段: 初始论文生成
            print(f"\\n📝 第2阶段: 初始论文生成")
            initial_paper = self._generate_initial_paper_advanced(
                research_topic, integrated_data
            )
            
            # 第3阶段: 质量控制循环
            print(f"\\n🔍 第3阶段: 综合质量控制")
            final_paper, quality_metrics = self._comprehensive_quality_control_loop(
                initial_paper, quality_req
            )
            
            # 第4阶段: 生成最终输出
            generation_time = (datetime.now() - generation_start_time).total_seconds()
            
            # 创建 PaperGenerationResult 对象 (从 enhanced_brain_paper_writer 导入)
            from paper_generation.enhanced_brain_paper_writer import PaperGenerationResult, PaperQualityMetrics
            
            # 构建质量指标对象
            quality_metrics_obj = PaperQualityMetrics(
                overall_score=quality_metrics.get('overall_score', 6.0),
                novelty_score=quality_metrics.get('novelty_score', 6.0),
                technical_quality_score=quality_metrics.get('technical_quality_score', 6.0),
                clarity_score=quality_metrics.get('clarity_score', 6.0),
                significance_score=quality_metrics.get('significance_score', 6.0),
                reproducibility_score=quality_metrics.get('reproducibility_score', 6.0),
                expert_consensus=quality_metrics.get('expert_consensus', 0.7),
                improvement_history=quality_metrics.get('improvement_history', [6.0])
            )
            
            # 构建生成元数据
            generation_metadata = {
                'generation_time': generation_time,
                'quality_requirements': quality_req,
                'config': self.config.__dict__,
                'timestamp': generation_start_time.isoformat()
            }
            
            # 返回标准的 PaperGenerationResult 对象
            result = PaperGenerationResult(
                paper_content=final_paper,
                quality_metrics=quality_metrics_obj,
                review_history=[],  # 评审历史暂时为空
                revision_history=[],  # 修订历史暂时为空
                latex_output=None,  # LaTeX输出暂时为空
                generation_metadata=generation_metadata,
                success=True,
                warnings=[]
            )
            
            print(f"\\n✅ 综合质量控制完成")
            print(f"📊 最终评分: {quality_metrics.get('overall_score', 0):.2f}/10")
            print(f"⏱️ 总用时: {generation_time:.1f}秒")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Comprehensive quality control failed: {e}")
            
            # 返回失败的 PaperGenerationResult 对象
            from paper_generation.enhanced_brain_paper_writer import PaperGenerationResult, PaperQualityMetrics
            
            failed_quality_metrics = PaperQualityMetrics(
                overall_score=0.0,
                novelty_score=0.0,
                technical_quality_score=0.0,
                clarity_score=0.0,
                significance_score=0.0,
                reproducibility_score=0.0,
                expert_consensus=0.0,
                improvement_history=[]
            )
            
            return PaperGenerationResult(
                paper_content={},
                quality_metrics=failed_quality_metrics,
                review_history=[],
                revision_history=[],
                latex_output=None,
                generation_metadata={'error': str(e), 'generation_time': 0},
                success=False,
                warnings=[f"Generation error: {e}"]
            )
    
    def load_and_integrate_experiment_data(self, experiment_data: Dict[str, Any]) -> Dict[str, Any]:
        """加载和集成实验数据 (AI Scientist v2风格)"""
        print("      🔬 集成实验数据")
        
        try:
            integrated_data = {
                'experiment_summaries': {},
                'performance_metrics': {},
                'datasets_used': [],
                'baseline_comparisons': {}
            }
            
            # 处理不同类型的实验数据
            if 'baseline_results' in experiment_data:
                integrated_data['experiment_summaries']['baseline'] = experiment_data['baseline_results']
            
            if 'proposed_results' in experiment_data:
                integrated_data['experiment_summaries']['proposed'] = experiment_data['proposed_results']
            
            if 'ablation_results' in experiment_data:
                integrated_data['experiment_summaries']['ablation'] = experiment_data['ablation_results']
            
            # 计算性能提升
            if 'baseline_results' in experiment_data and 'proposed_results' in experiment_data:
                baseline = experiment_data['baseline_results']
                proposed = experiment_data['proposed_results']
                
                improvements = {}
                for metric in baseline.keys():
                    if metric in proposed and isinstance(baseline[metric], (int, float)):
                        improvement = ((proposed[metric] - baseline[metric]) / baseline[metric]) * 100
                        improvements[metric] = improvement
                
                integrated_data['performance_metrics']['improvements'] = improvements
            
            # 处理数据集信息
            if 'datasets' in experiment_data:
                integrated_data['datasets_used'] = experiment_data['datasets']
            
            print(f"        ✅ 实验数据集成完成 - {len(integrated_data['experiment_summaries'])} 个实验")
            return integrated_data
            
        except Exception as e:
            self.logger.warning(f"Experiment data integration failed: {e}")
            return {}
    
    # ============ 辅助方法 ============
    
    def _validate_methodology(self, methodology: str) -> Dict[str, Any]:
        """验证方法论的完整性"""
        required_elements = [
            'algorithm', 'architecture', 'training', 'evaluation', 'implementation'
        ]
        
        missing_elements = []
        for element in required_elements:
            if element.lower() not in methodology.lower():
                missing_elements.append(element)
        
        return {
            'is_complete': len(missing_elements) == 0,
            'missing_elements': missing_elements,
            'completeness_score': (len(required_elements) - len(missing_elements)) / len(required_elements)
        }
    
    def _enhance_methodology(self, methodology: str, missing_elements: List[str]) -> str:
        """增强方法论，补充缺失元素"""
        enhancement_prompt = f"""
        Enhance the following methodology section by adding details about: {', '.join(missing_elements)}
        
        Current methodology:
        {methodology}
        
        Please add comprehensive details about the missing elements while maintaining coherence.
        """
        
        try:
            response = self.llm_client.get_response(enhancement_prompt)
            return response[0] if isinstance(response, tuple) else response
        except Exception as e:
            self.logger.warning(f"Methodology enhancement failed: {e}")
            return methodology
    
    def _generate_initial_paper_advanced(self, research_topic: str, 
                                        integrated_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成高级初始论文 (集成实验数据)"""
        print("      📝 生成高级初始论文")
        
        # 构建包含实验数据的上下文
        context = {
            'research_topic': research_topic,
            'integrated_experiments': integrated_data,
            'target_venue': self.config.target_venue,
            'paper_type': self.config.paper_type
        }
        
        # 生成各个章节
        sections = {}
        
        # 使用详细生成方法
        sections['abstract'] = self._generate_abstract_detailed_with_review(context)
        sections['introduction'] = self._generate_introduction_detailed_with_novelty(context)
        sections['methodology'] = self._generate_methodology_detailed_with_validation(context)
        
        # 基于实验数据生成实验章节
        if integrated_data:
            sections['experiments'] = self._generate_experiments_from_data(context, integrated_data)
            sections['results'] = self._generate_results_from_data(context, integrated_data)
        
        # 生成其他标准章节
        sections['related_work'] = self._generate_section_standard('related_work', context)
        sections['discussion'] = self._generate_section_standard('discussion', context)
        sections['conclusion'] = self._generate_section_standard('conclusion', context)
        
        # 生成标题和参考文献
        title = self._generate_paper_title_advanced(research_topic, context)
        sections['references'] = self._generate_references_advanced(context)
        
        return {
            'title': title,
            'abstract': sections['abstract'],
            'sections': sections,
            'metadata': {
                'topic': research_topic,
                'venue': self.config.target_venue,
                'type': self.config.paper_type,
                'has_experimental_data': bool(integrated_data),
                'generated_at': datetime.now().isoformat()
            }
        }
    
    def _comprehensive_quality_control_loop(self, paper: Dict[str, Any], 
                                           quality_req: Dict[str, Any]) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """综合质量控制循环"""
        print("      🔍 执行综合质量控制")
        
        current_paper = paper.copy()
        iteration = 0
        max_iterations = quality_req.get('max_revision_rounds', 3)
        min_score = quality_req.get('min_expert_score', 7.0)
        
        quality_history = []
        
        while iteration < max_iterations:
            print(f"        🔄 质量控制轮次 {iteration + 1}/{max_iterations}")
            
            # 多专家评审
            if self.config.enable_multi_expert_review and hasattr(self, 'review_system'):
                review_result = self.review_system.conduct_multi_expert_review(
                    current_paper, self.config.target_venue
                )
                current_score = review_result.overall_score
                quality_history.append(current_score)
                
                print(f"          📊 当前评分: {current_score:.2f}/10")
                
                if current_score >= min_score:
                    print(f"          ✅ 达到质量阈值")
                    break
                
                # 自动修订
                if self.config.enable_auto_revision and hasattr(self, 'revision_engine'):
                    revision_result = self.revision_engine.execute_paper_revision(
                        current_paper, review_result
                    )
                    if revision_result and hasattr(revision_result, 'final_paper'):
                        current_paper = revision_result.final_paper
                        print(f"          🔧 修订完成")
            
            iteration += 1
        
        final_quality_metrics = {
            'overall_score': quality_history[-1] if quality_history else 6.0,
            'improvement_history': quality_history,
            'iterations_completed': iteration,
            'quality_threshold_met': quality_history[-1] >= min_score if quality_history else False
        }
        
        return current_paper, final_quality_metrics
    
    def _generate_experiments_from_data(self, context: Dict[str, Any], 
                                       experiment_data: Dict[str, Any]) -> str:
        """基于实验数据生成实验章节"""
        experiment_prompt = f"""
        Generate a comprehensive experiments section based on the following data:
        
        Research Topic: {context['research_topic']}
        Experimental Data: {json.dumps(experiment_data, indent=2)}
        
        Include:
        1. Experimental setup and configuration
        2. Datasets and evaluation metrics
        3. Implementation details
        4. Baseline comparisons
        5. Hyperparameter settings
        
        Make it suitable for {context['target_venue']}.
        """
        
        try:
            response = self.llm_client.get_response(experiment_prompt)
            return response[0] if isinstance(response, tuple) else response
        except Exception as e:
            return f"[Experiments section generation failed: {e}]"
    
    def _generate_results_from_data(self, context: Dict[str, Any], 
                                   experiment_data: Dict[str, Any]) -> str:
        """基于实验数据生成结果章节"""
        results_prompt = f"""
        Generate a comprehensive results section based on:
        
        Research Topic: {context['research_topic']}
        Experimental Data: {json.dumps(experiment_data, indent=2)}
        
        Include:
        1. Quantitative results and analysis
        2. Performance comparisons
        3. Statistical significance tests
        4. Ablation study results
        5. Error analysis and limitations
        
        Present results clearly for {context['target_venue']}.
        """
        
        try:
            response = self.llm_client.get_response(results_prompt)
            return response[0] if isinstance(response, tuple) else response
        except Exception as e:
            return f"[Results section generation failed: {e}]"
    
    def _generate_section_standard(self, section_name: str, context: Dict[str, Any]) -> str:
        """生成标准章节"""
        try:
            expert = self.agent_manager.get_agent('paper_writing')
            if expert:
                response = expert.analyze({
                    "input_text": f"Generate {section_name} section for: {context['research_topic']}",
                    "analysis_type": "section_generation"
                })
                if response and hasattr(response, 'content'):
                    return response.content.strip()
            
            # 备用生成
            prompt = f"Write a {section_name} section for a paper on {context['research_topic']} for {context['target_venue']}."
            response = self.llm_client.get_response(prompt)
            return response[0] if isinstance(response, tuple) else response
            
        except Exception as e:
            return f"[{section_name} generation failed: {e}]"
    
    def _generate_paper_title_advanced(self, research_topic: str, context: Dict[str, Any]) -> str:
        """生成高级论文标题"""
        title_prompt = f"""
        Generate an impactful title for a {context['paper_type']} paper on "{research_topic}" 
        for {context['target_venue']}.
        
        Requirements:
        - Concise (under 15 words)
        - Highlights brain-inspired approach
        - Academically compelling
        - Suitable for top-tier venue
        
        Just return the title, no additional text.
        """
        
        try:
            response = self.llm_client.get_response(title_prompt)
            title = response[0] if isinstance(response, tuple) else response
            return title.strip().strip('"').strip("'")
        except Exception as e:
            return f"Brain-Inspired Intelligence: {research_topic}"
    
    def _generate_references_advanced(self, context: Dict[str, Any]) -> str:
        """生成高级参考文献"""
        refs_prompt = f"""
        Generate 20-25 high-quality references for a paper on "{context['research_topic']}" 
        targeting {context['target_venue']}.
        
        Include:
        - Foundational brain-inspired intelligence papers
        - Recent advances (2020-2024)
        - Top venues (NeurIPS, ICML, ICLR, Nature, Science)
        - Proper academic formatting
        
        Focus on quality and relevance.
        """
        
        try:
            response = self.llm_client.get_response(refs_prompt)
            return response[0] if isinstance(response, tuple) else response
        except Exception as e:
            return "[References to be generated]"


# 测试函数
async def test_advanced_paper_writer():
    """测试高级论文撰写系统"""
    print("🧪 测试高级论文撰写系统")
    
    config = AdvancedPaperConfig(
        target_venue="ICML",
        paper_type="research",
        max_review_iterations=2,
        quality_threshold=6.5,
        enable_detailed_section_generation=True,
        enable_async_processing=True,
        enable_citation_rounds=5,
        latex_output=False  # 简化测试
    )
    
    writer = AdvancedBrainPaperWriter(config=config)
    
    research_topic = "Brain-Inspired Adaptive Learning Networks for Visual Recognition"
    
    # 模拟实验数据
    experiment_data = {
        'baseline_results': {'accuracy': 0.85, 'f1_score': 0.82},
        'proposed_results': {'accuracy': 0.92, 'f1_score': 0.89},
        'datasets': ['CIFAR-10', 'ImageNet'],
        'metrics': ['accuracy', 'f1_score', 'inference_time']
    }
    
    result = await writer.generate_comprehensive_paper(
        research_topic=research_topic,
        experiment_data=experiment_data
    )
    
    print(f"\\n📊 高级生成结果:")
    print(f"成功: {result['success']}")
    if result['success']:
        paper = result['paper_content']
        print(f"标题: {paper['title']}")
        print(f"章节数: {len(paper['sections'])}")
        print(f"引用数: {len(result['citations'])}")
        print(f"生成时间: {result['generation_metadata']['generation_time']:.1f}秒")
    
    return result


if __name__ == "__main__":
    # 运行异步测试
    import asyncio
    asyncio.run(test_advanced_paper_writer())
