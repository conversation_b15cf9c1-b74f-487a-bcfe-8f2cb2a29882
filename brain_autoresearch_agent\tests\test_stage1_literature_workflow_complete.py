"""
阶段1测试：文献工作流基础设施完整测试
测试统一API客户端、增强文献管理系统和工作流提取器
"""

import sys
import os
import json
import time
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.unified_api_client import UnifiedAPIClient
from core.enhanced_literature_manager import EnhancedLiteratureManager
from core.paper_workflow import PaperWorkflowExtractor

class Stage1Tester:
    """阶段1综合测试器"""
    
    def __init__(self):
        self.results = {}
        self.start_time = time.time()
        
    def print_header(self, title: str):
        """打印测试标题"""
        print(f"\n{'='*60}")
        print(f"🧪 {title}")
        print(f"{'='*60}")
    
    def print_test_result(self, test_name: str, success: bool, details: str = ""):
        """打印测试结果"""
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if details:
            print(f"   详情: {details}")
        
        self.results[test_name] = {
            "success": success,
            "details": details
        }
    
    def test_unified_api_client(self) -> bool:
        """测试统一API客户端"""
        self.print_header("测试 1: 统一API客户端")
        
        try:
            # 初始化客户端
            client = UnifiedAPIClient()
            print("✅ 客户端初始化成功")
            
            # 测试DeepSeek文本生成
            print("\n🤖 测试DeepSeek文本生成...")
            response = client.get_text_response(
                prompt="请用一句话解释什么是脑启发人工智能",
                system_message="你是一个专业的AI研究助手",
                print_debug=True
            )
            
            if response.success and len(response.content) > 10:
                self.print_test_result(
                    "DeepSeek文本生成", 
                    True, 
                    f"响应长度: {len(response.content)} 字符"
                )
            else:
                self.print_test_result(
                    "DeepSeek文本生成", 
                    False, 
                    response.error_message or "响应内容太短"
                )
                return False
            
            # 测试JSON提取功能
            print("\n🔍 测试JSON提取功能...")
            json_test_text = '''
            这里是一些文本内容，包含JSON数据：
            ```json
            {
                "test": "success",
                "data": ["item1", "item2"],
                "number": 42
            }
            ```
            还有更多文本。
            '''
            
            extracted_json = client.extract_json(json_test_text, print_debug=True)
            if extracted_json and extracted_json.get("test") == "success":
                self.print_test_result(
                    "JSON提取功能",
                    True,
                    f"提取数据: {extracted_json}"
                )
            else:
                self.print_test_result(
                    "JSON提取功能",
                    False,
                    "JSON提取失败或数据不匹配"
                )
                return False
            
            # 测试模型选择
            print("\n🎯 测试任务模型推荐...")
            test_tasks = ["paper_writing", "reasoning", "layout_analysis"]
            for task in test_tasks:
                provider, model = client.get_optimal_model_for_task(task)
                print(f"  {task}: {provider} - {model}")
            
            self.print_test_result("任务模型推荐", True, "所有任务都有推荐模型")
            
            return True
            
        except Exception as e:
            self.print_test_result(
                "统一API客户端整体测试",
                False,
                f"异常: {e}"
            )
            return False
    
    def test_workflow_extractor(self) -> bool:
        """测试工作流提取器"""
        self.print_header("测试 2: 论文工作流提取器")
        
        try:
            # 初始化提取器
            extractor = PaperWorkflowExtractor()
            print("✅ 工作流提取器初始化成功")
            
            # 测试样本论文
            sample_paper = """
            Title: Spike-Timing-Dependent Plasticity in Deep Neural Networks for Brain-Inspired Learning
            
            Abstract: This paper presents a novel approach to integrate spike-timing-dependent plasticity (STDP) 
            mechanisms into deep neural networks. We propose a biologically plausible learning algorithm that 
            combines traditional backpropagation with STDP rules. Our experiments on MNIST and CIFAR-10 datasets 
            demonstrate improved energy efficiency and learning performance. The implementation uses PyTorch 
            framework with custom CUDA kernels for spiking neuron simulations. We evaluate our approach using 
            accuracy, energy consumption, and spike rate metrics, comparing against baseline CNN and RNN 
            architectures. The results show significant improvements in neuromorphic computing applications.
            """
            
            print("📄 测试论文工作流提取...")
            print(f"论文标题: Spike-Timing-Dependent Plasticity in Deep Neural Networks...")
            
            # 提取工作流
            workflow = extractor.extract_workflow(
                paper_text=sample_paper,
                paper_title="Spike-Timing-Dependent Plasticity in Deep Neural Networks for Brain-Inspired Learning"
            )
            
            # 验证提取结果
            success_criteria = [
                (len(workflow.datasets) > 0, "数据集提取"),
                (len(workflow.network_architectures) > 0, "网络架构提取"),
                (len(workflow.platforms_tools) > 0, "平台工具提取"),
                (len(workflow.research_methods) > 0, "研究方法提取"),
                (len(workflow.evaluation_metrics) > 0, "评估指标提取"),
                (len(workflow.brain_inspiration) > 0, "脑启发元素提取"),
                (len(workflow.ai_techniques) > 0, "AI技术提取")
            ]
            
            print("\n🔍 提取结果验证:")
            all_success = True
            for condition, description in success_criteria:
                self.print_test_result(description, condition)
                if not condition:
                    all_success = False
            
            # 打印详细提取结果
            print(f"\n📊 详细提取结果:")
            print(f"  数据集: {workflow.datasets}")
            print(f"  网络架构: {workflow.network_architectures}")
            print(f"  平台工具: {workflow.platforms_tools}")
            print(f"  研究方法: {workflow.research_methods}")
            print(f"  评估指标: {workflow.evaluation_metrics}")
            print(f"  脑启发元素: {workflow.brain_inspiration}")
            print(f"  AI技术: {workflow.ai_techniques}")
            
            return all_success
            
        except Exception as e:
            self.print_test_result(
                "工作流提取器整体测试",
                False,
                f"异常: {e}"
            )
            return False
    
    def test_literature_manager(self) -> bool:
        """测试增强文献管理系统"""
        self.print_header("测试 3: 增强文献管理系统")
        
        try:
            # 初始化文献管理器
            manager = EnhancedLiteratureManager()
            print("✅ 增强文献管理器初始化成功")
            
            # 测试小规模文献搜索（避免API限制）
            test_query = "neuromorphic computing efficiency"
            print(f"\n🔍 测试文献搜索: {test_query}")
            
            # 只使用ArXiv源以减少API调用
            search_results = manager.search_literature(
                query=test_query,
                max_papers=10,
                sources=['arxiv'],  # 只用ArXiv避免API限制
                extract_workflows=False,  # 暂不提取工作流
                brain_inspired_focus=True
            )
            
            # 验证搜索结果
            if search_results.total_papers > 0:
                self.print_test_result(
                    "文献搜索功能",
                    True,
                    f"找到 {search_results.total_papers} 篇论文，耗时 {search_results.search_time:.2f} 秒"
                )
                
                # 显示搜索结果样例
                print(f"\n📚 搜索结果样例 (前3篇):")
                for i, paper in enumerate(search_results.papers[:3], 1):
                    print(f"  {i}. {paper.title[:50]}...")
                    
                    # 安全处理authors字段
                    if hasattr(paper, 'authors') and paper.authors:
                        if isinstance(paper.authors, list):
                            # 处理字符串列表
                            if paper.authors and isinstance(paper.authors[0], str):
                                authors_str = ', '.join(paper.authors[:2])
                            # 处理字典列表
                            elif paper.authors and isinstance(paper.authors[0], dict):
                                author_names = []
                                for author in paper.authors[:2]:
                                    if isinstance(author, dict) and 'name' in author:
                                        author_names.append(author['name'])
                                    elif isinstance(author, dict) and 'authorId' in author:
                                        # Semantic Scholar格式
                                        author_names.append(author.get('name', 'Unknown'))
                                authors_str = ', '.join(author_names) if author_names else 'Unknown'
                            else:
                                authors_str = str(paper.authors[0]) if paper.authors else 'Unknown'
                        else:
                            authors_str = str(paper.authors)
                    else:
                        authors_str = 'Unknown'
                    
                    print(f"     作者: {authors_str}...")
                    print(f"     年份: {getattr(paper, 'year', 'Unknown')} | 来源: {getattr(paper, 'source', 'Unknown')}")
                    print()
                
                # 测试结果保存
                save_path = manager.save_search_results(search_results)
                if os.path.exists(save_path):
                    self.print_test_result(
                        "搜索结果保存",
                        True,
                        f"已保存至: {save_path}"
                    )
                    
                    # 清理测试文件
                    try:
                        os.remove(save_path)
                    except:
                        pass
                else:
                    self.print_test_result("搜索结果保存", False, "文件未创建")
                    return False
                
            else:
                self.print_test_result(
                    "文献搜索功能",
                    False,
                    "未找到任何论文"
                )
                return False
            
            return True
            
        except Exception as e:
            self.print_test_result(
                "增强文献管理系统整体测试",
                False,
                f"异常: {e}"
            )
            return False
    
    def test_workflow_extraction_integration(self) -> bool:
        """测试工作流提取集成（小规模）"""
        self.print_header("测试 4: 工作流提取集成")
        
        try:
            print("🔄 测试文献搜索+工作流提取集成...")
            
            manager = EnhancedLiteratureManager()
            
            # 进行小规模搜索并提取工作流
            results = manager.search_literature(
                query="spiking neural networks",
                max_papers=3,  # 很小的数量以节省API调用
                sources=['arxiv'],
                extract_workflows=True,  # 启用工作流提取
                brain_inspired_focus=True
            )
            
            # 验证集成结果
            if results.workflow_extractions and len(results.workflow_extractions) > 0:
                self.print_test_result(
                    "集成工作流提取",
                    True,
                    f"成功提取 {len(results.workflow_extractions)} 篇论文的工作流"
                )
                
                # 显示一个工作流样例
                sample_workflow = results.workflow_extractions[0]
                print(f"\n🔬 工作流提取样例:")
                print(f"  论文: {sample_workflow.title[:50]}...")
                print(f"  数据集: {sample_workflow.datasets}")
                print(f"  架构: {sample_workflow.network_architectures}")
                print(f"  脑启发: {sample_workflow.brain_inspiration}")
                
                return True
            else:
                self.print_test_result(
                    "集成工作流提取",
                    False,
                    "没有成功提取任何工作流"
                )
                return False
                
        except Exception as e:
            self.print_test_result(
                "工作流提取集成测试",
                False,
                f"异常: {e}"
            )
            return False
    
    def generate_report(self):
        """生成测试报告"""
        self.print_header("阶段1测试报告")
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results.values() if r["success"])
        failed_tests = total_tests - passed_tests
        
        test_duration = time.time() - self.start_time
        
        print(f"📊 测试统计:")
        print(f"  总测试数: {total_tests}")
        print(f"  通过: {passed_tests} ✅")
        print(f"  失败: {failed_tests} ❌")
        print(f"  成功率: {(passed_tests/total_tests)*100:.1f}%")
        print(f"  测试时长: {test_duration:.2f} 秒")
        
        print(f"\n📋 详细结果:")
        for test_name, result in self.results.items():
            status = "✅" if result["success"] else "❌"
            print(f"  {status} {test_name}")
            if result["details"]:
                print(f"      {result['details']}")
        
        # 保存报告
        report_data = {
            "stage": "Stage 1 - Literature Workflow Infrastructure",
            "timestamp": datetime.now().isoformat(),
            "duration_seconds": test_duration,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "success_rate": (passed_tests/total_tests)*100,
            "detailed_results": self.results
        }
        
        report_file = f"stage1_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存至: {report_file}")
        
        # 返回整体成功状态
        return failed_tests == 0
    
    def run_all_tests(self) -> bool:
        """运行所有阶段1测试"""
        print("🚀 开始阶段1综合测试")
        print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 按顺序运行所有测试
        test_functions = [
            self.test_unified_api_client,
            self.test_workflow_extractor,
            self.test_literature_manager,
            self.test_workflow_extraction_integration
        ]
        
        overall_success = True
        for test_func in test_functions:
            try:
                result = test_func()
                if not result:
                    overall_success = False
                    print(f"⚠️ 测试 {test_func.__name__} 未完全通过")
            except Exception as e:
                print(f"❌ 测试 {test_func.__name__} 发生异常: {e}")
                overall_success = False
        
        # 生成最终报告
        self.generate_report()
        
        if overall_success:
            print(f"\n🎉 阶段1测试完全通过！")
            print(f"✅ 文献工作流基础设施已准备就绪")
        else:
            print(f"\n⚠️ 阶段1测试有部分失败")
            print(f"🔧 请查看详细报告并修复问题")
        
        return overall_success


def main():
    """主测试函数"""
    tester = Stage1Tester()
    success = tester.run_all_tests()
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
