"""
简化的推理工作流测试
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def test_simple_import():
    """测试简单导入"""
    try:
        print("🔍 测试模块导入...")
        
        # 测试数据模型导入
        from reasoning.data_models import ResearchProblem, ExperimentPlan
        print("  ✅ 数据模型导入成功")
        
        # 测试LLM客户端导入
        from core.llm_client import LLMClient
        from config.model_config import create_model_config
        print("  ✅ LLM组件导入成功")
        
        # 测试工作流导入
        from reasoning.reasoning_workflow import ExperimentReasoningWorkflow
        print("  ✅ 工作流模块导入成功")
        
        # 测试初始化
        print("\n🚀 测试工作流初始化...")
        model_config = create_model_config()
        llm_client = LLMClient()
        workflow = ExperimentReasoningWorkflow(llm_client)
        print("  ✅ 工作流初始化成功")
        
        print("\n🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple_import()
