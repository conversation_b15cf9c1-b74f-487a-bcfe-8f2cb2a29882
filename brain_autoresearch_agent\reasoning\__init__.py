"""
推理流程框架模块
Reasoning Flow Framework Module

本模块实现多专家代理的深度推理和知识融合功能
This module implements deep reasoning and knowledge fusion for multi-expert agents
"""

# 将导入改为可选的，避免循环导入问题
__all__ = [
    'MultiAgentReasoning',
    'KnowledgeFusion', 
    'ConsensusDecision'
]

def get_multi_agent_reasoning():
    """获取MultiAgentReasoning类"""
    from .multi_agent_reasoning import MultiAgentReasoning
    return MultiAgentReasoning

def get_knowledge_fusion():
    """获取KnowledgeFusion类"""
    from .knowledge_fusion import KnowledgeFusion
    return KnowledgeFusion

def get_consensus_decision():
    """获取ConsensusDecision类"""
    from .consensus_decision import ConsensusDecision
    return ConsensusDecision
