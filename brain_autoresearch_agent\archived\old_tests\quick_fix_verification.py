"""
快速验证修复后的综合测试系统
验证API调用方式修复是否成功
"""

import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def quick_test():
    """快速测试修复后的系统"""
    print("🔧 快速验证修复...")
    
    try:
        # 测试LLM客户端
        print("1. 测试LLM客户端...")
        from core.llm_client import LLMClient
        
        llm_client = LLMClient()
        response = llm_client.generate_response("Hello, test connection.")
        print(f"   ✅ LLM响应: {response[:100]}...")
        
        # 测试多代理推理引擎
        print("2. 测试多代理推理引擎...")
        from reasoning.multi_agent_reasoning import MultiAgentReasoning
        
        reasoning_engine = MultiAgentReasoning(llm_client=llm_client)
        session_id = reasoning_engine.start_reasoning_session(
            research_topic="测试研究主题",
            initial_hypothesis="测试假设"
        )
        print(f"   ✅ 推理会话创建成功: {session_id}")
        
        # 测试研究问题评估器
        print("3. 测试研究问题评估器...")
        from reasoning.research_question_evaluator import ResearchQuestionEvaluator
        
        evaluator = ResearchQuestionEvaluator(llm_client)
        evaluation = evaluator.evaluate_research_question_from_string("测试研究问题")
        print(f"   ✅ 评估完成: {evaluation.get('overall_score', 'N/A')}")
        
        # 测试文献搜索
        print("4. 测试文献搜索...")
        from core.hybrid_literature_tool import HybridLiteratureTool
        
        literature_tool = HybridLiteratureTool()
        results = literature_tool.search_papers("deep learning", max_results=2)
        print(f"   ✅ 文献搜索成功: {len(results)} 篇论文")
        
        print("\n🎉 所有修复验证通过！系统调用方式正确。")
        return True
        
    except Exception as e:
        print(f"\n❌ 验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_test()
    if success:
        print("\n▶️  可以运行完整测试了:")
        print("   python comprehensive_test_runner.py")
    else:
        print("\n⚠️  需要进一步修复")
