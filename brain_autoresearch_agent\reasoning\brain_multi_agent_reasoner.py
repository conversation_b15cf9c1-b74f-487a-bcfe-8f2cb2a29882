"""
脑启发智能多专家推理系统
实现基于用户描述的完整推理流程：多Agent讨论、假设生成、实验设计、实现建议
"""

import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

from core.unified_api_client import UnifiedAPIClient
from core.enhanced_literature_manager import EnhancedLiteratureManager, LiteratureSearchResult, PaperInfo
from agents.agent_manager import AgentManager
from reasoning.research_question_evaluator import ResearchQuestionEvaluator
from reasoning.hypothesis_experiment_designer import HypothesisExperimentDesigner  
from reasoning.implementation_planner import ImplementationPlanner
from reasoning.visualization_advisor import VisualizationAdvisor

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ResearchIdea:
    """研究想法数据结构"""
    title: str
    description: str
    research_question: str
    domain: str = "brain-inspired AI"
    keywords: List[str] = None

@dataclass
class MultiAgentDiscussion:
    """多Agent讨论结果"""
    research_value_score: float
    feasibility_score: float
    innovation_score: float 
    impact_score: float
    consensus_reached: bool
    discussion_rounds: int
    expert_opinions: Dict[str, str]
    final_recommendation: str

@dataclass
class ExperimentPlan:
    """实验方案"""
    hypothesis: str
    experimental_design: str
    methodology: str
    evaluation_metrics: List[str]
    baseline_methods: List[str]
    expected_outcomes: str
    feasibility_analysis: str

@dataclass
class ImplementationStrategy:
    """实现策略"""
    technical_stack: List[str]
    implementation_steps: List[str]
    code_structure: str
    dependencies: List[str]
    estimated_timeline: str
    potential_challenges: List[str]

@dataclass
class VisualizationPlan:
    """可视化方案"""
    chart_types: List[str]
    visualization_tools: List[str]
    presentation_suggestions: List[str]
    figure_descriptions: List[str]

@dataclass
class ComprehensiveResearchPlan:
    """综合研究计划"""
    research_idea: ResearchIdea
    literature_review: LiteratureSearchResult
    multi_agent_discussion: MultiAgentDiscussion
    experiment_plan: ExperimentPlan
    implementation_strategy: ImplementationStrategy
    visualization_plan: VisualizationPlan
    workflow_knowledge: Dict[str, Any]
    confidence_score: float
    recommendations: List[str]

class BrainInspiredMultiAgentReasoner:
    """
    脑启发智能多专家推理器
    根据用户描述的流程实现完整的研究推理系统
    """
    
    def __init__(self, unified_client: Optional[UnifiedAPIClient] = None):
        """初始化推理系统"""
        self.unified_client = unified_client or UnifiedAPIClient()
        
        # 初始化各个组件
        self.literature_manager = EnhancedLiteratureManager(self.unified_client)
        self.agent_manager = AgentManager(self.unified_client)
        
        # 推理组件（稍后会创建/更新这些）
        self.research_evaluator = ResearchQuestionEvaluator(self.unified_client)
        self.experiment_designer = HypothesisExperimentDesigner(self.unified_client)
        self.implementation_planner = ImplementationPlanner(self.unified_client)
        self.visualization_advisor = VisualizationAdvisor(self.unified_client)
        
        # 推理历史
        self.reasoning_sessions: List[ComprehensiveResearchPlan] = []
    
    def generate_research_idea(self, topic: str, context: str = "") -> ResearchIdea:
        """
        生成研究想法
        
        Args:
            topic: 研究主题
            context: 背景信息
            
        Returns:
            研究想法对象
        """
        logger.info(f"💡 生成研究想法: {topic}")
        
        system_message = """你是一个创新的脑启发智能研究专家。你的任务是基于给定主题生成具体的、可行的研究想法。

生成的研究想法应该：
1. 结合脑科学和人工智能的交叉领域
2. 具有创新性和实用价值
3. 技术上可实现
4. 有明确的研究问题

请以JSON格式返回结果：
{
    "title": "研究标题",
    "description": "详细描述（200-300字）",
    "research_question": "核心研究问题",
    "keywords": ["关键词1", "关键词2", "关键词3"]
}"""
        
        prompt = f"""请基于以下主题生成一个脑启发智能研究想法：

主题: {topic}
背景: {context}

要求：
1. 生成一个创新的研究想法
2. 明确定义研究问题
3. 确保技术可行性
4. 体现脑启发和AI的结合

请提供详细的研究构思。"""
        
        try:
            response = self.unified_client.get_text_response(
                prompt=prompt,
                system_message=system_message,
                model_type="reasoning",
                temperature=0.8
            )
            
            if response.success:
                data = self.unified_client.extract_json(response.content)
                if data:
                    return ResearchIdea(
                        title=data.get("title", f"脑启发{topic}研究"),
                        description=data.get("description", ""),
                        research_question=data.get("research_question", ""),
                        keywords=data.get("keywords", [])
                    )
            
            # 如果解析失败，创建基础版本
            return ResearchIdea(
                title=f"脑启发{topic}研究",
                description=f"探索{topic}在脑启发人工智能中的应用",
                research_question=f"如何在人工智能中有效实现{topic}相关的脑启发机制？",
                keywords=[topic, "brain-inspired", "artificial intelligence"]
            )
            
        except Exception as e:
            logger.error(f"研究想法生成失败: {e}")
            # 返回默认想法
            return ResearchIdea(
                title=f"脑启发{topic}研究",
                description=f"探索{topic}在脑启发人工智能中的应用",
                research_question=f"如何在人工智能中有效实现{topic}相关的脑启发机制？",
                keywords=[topic, "brain-inspired", "artificial intelligence"]
            )
    
    def conduct_literature_research(self, research_idea: ResearchIdea) -> LiteratureSearchResult:
        """
        进行文献调研
        
        Args:
            research_idea: 研究想法
            
        Returns:
            文献搜索结果
        """
        logger.info(f"📚 进行文献调研: {research_idea.title}")
        
        # 构建搜索查询
        query_keywords = [research_idea.title] + (research_idea.keywords or [])
        search_query = " ".join(query_keywords[:3])  # 限制查询长度
        
        # 执行文献搜索
        results = self.literature_manager.search_literature(
            query=search_query,
            max_papers=30,
            sources=['semantic_scholar', 'arxiv'],  # 使用主要源
            extract_workflows=True,  # 提取工作流信息
            brain_inspired_focus=True
        )
        
        logger.info(f"✅ 文献调研完成: 找到 {results.total_papers} 篇相关论文")
        return results
    
    def multi_agent_discussion(
        self, 
        research_idea: ResearchIdea, 
        literature_results: LiteratureSearchResult,
        discussion_rounds: int = 2
    ) -> MultiAgentDiscussion:
        """
        多Agent讨论研究价值
        
        Args:
            research_idea: 研究想法
            literature_results: 文献调研结果
            discussion_rounds: 讨论轮数
            
        Returns:
            多Agent讨论结果
        """
        logger.info(f"🤖 启动多Agent讨论: {discussion_rounds} 轮")
        
        # 准备讨论背景
        discussion_context = {
            "research_idea": research_idea,
            "literature_summary": self._summarize_literature(literature_results),
            "expert_focus_areas": {
                "ai_technology": "技术可行性、算法创新性、实现难度",
                "neuroscience": "生物合理性、脑启发程度、神经科学基础",
                "data_analysis": "数据需求、评估方法、统计分析",
                "experiment_design": "实验设计、控制变量、验证方案"
            }
        }
        
        expert_opinions = {}
        scores = {"value": [], "feasibility": [], "innovation": [], "impact": []}
        
        # 获取专家意见
        experts = ["ai_technology", "neuroscience", "data_analysis", "experiment_design"]
        
        for round_num in range(discussion_rounds):
            logger.info(f"📢 第 {round_num + 1} 轮讨论")
            
            for expert_type in experts:
                opinion = self._get_expert_opinion(
                    expert_type, 
                    discussion_context,
                    round_num + 1,
                    expert_opinions  # 传入之前的意见作为参考
                )
                
                expert_opinions[f"{expert_type}_round_{round_num + 1}"] = opinion
                
                # 提取评分
                opinion_scores = self._extract_scores_from_opinion(opinion)
                for key, value in opinion_scores.items():
                    if key in scores:
                        scores[key].append(value)
        
        # 计算平均分数
        avg_scores = {
            key: sum(values) / len(values) if values else 5.0 
            for key, values in scores.items()
        }
        
        # 生成最终推荐
        final_recommendation = self._generate_final_recommendation(
            expert_opinions, 
            avg_scores, 
            research_idea
        )
        
        # 判断是否达成共识（分数差异小于2分）
        score_values = list(avg_scores.values())
        consensus = max(score_values) - min(score_values) < 2.0
        
        logger.info(f"✅ 多Agent讨论完成: 价值分数 {avg_scores['value']:.1f}/10")
        
        return MultiAgentDiscussion(
            research_value_score=avg_scores["value"],
            feasibility_score=avg_scores["feasibility"],
            innovation_score=avg_scores["innovation"],
            impact_score=avg_scores["impact"],
            consensus_reached=consensus,
            discussion_rounds=discussion_rounds,
            expert_opinions=expert_opinions,
            final_recommendation=final_recommendation
        )
    
    def design_experiment(
        self, 
        research_idea: ResearchIdea,
        discussion_result: MultiAgentDiscussion,
        workflow_knowledge: Dict[str, Any]
    ) -> ExperimentPlan:
        """
        设计实验方案
        
        Args:
            research_idea: 研究想法
            discussion_result: 多Agent讨论结果
            workflow_knowledge: 从文献中提取的工作流知识
            
        Returns:
            实验计划
        """
        logger.info(f"🧪 设计实验方案: {research_idea.title}")
        
        # 生成假设
        hypothesis = self._generate_hypothesis(research_idea, discussion_result)
        
        # 设计实验方法
        experimental_design = self._design_experimental_method(
            research_idea, 
            hypothesis, 
            workflow_knowledge
        )
        
        # 确定评估方法
        evaluation_metrics = self._determine_evaluation_metrics(
            research_idea, 
            workflow_knowledge
        )
        
        # 选择基线方法
        baseline_methods = self._select_baseline_methods(workflow_knowledge)
        
        # 预测结果
        expected_outcomes = self._predict_outcomes(hypothesis, experimental_design)
        
        # 可行性分析
        feasibility_analysis = self._analyze_feasibility(
            experimental_design, 
            discussion_result.feasibility_score
        )
        
        logger.info(f"✅ 实验方案设计完成")
        
        return ExperimentPlan(
            hypothesis=hypothesis,
            experimental_design=experimental_design,
            methodology="controlled_comparison_experiment",
            evaluation_metrics=evaluation_metrics,
            baseline_methods=baseline_methods,
            expected_outcomes=expected_outcomes,
            feasibility_analysis=feasibility_analysis
        )
    
    def plan_implementation(
        self, 
        experiment_plan: ExperimentPlan,
        workflow_knowledge: Dict[str, Any]
    ) -> ImplementationStrategy:
        """
        规划具体实现方法
        
        Args:
            experiment_plan: 实验计划
            workflow_knowledge: 工作流知识
            
        Returns:
            实现策略
        """
        logger.info(f"💻 规划实现策略")
        
        # 确定技术栈
        technical_stack = self._determine_technical_stack(workflow_knowledge)
        
        # 规划实现步骤
        implementation_steps = self._plan_implementation_steps(experiment_plan)
        
        # 设计代码结构
        code_structure = self._design_code_structure(experiment_plan)
        
        # 确定依赖
        dependencies = self._determine_dependencies(technical_stack)
        
        # 估计时间线
        timeline = self._estimate_timeline(implementation_steps)
        
        # 识别挑战
        challenges = self._identify_challenges(experiment_plan)
        
        logger.info(f"✅ 实现策略规划完成")
        
        return ImplementationStrategy(
            technical_stack=technical_stack,
            implementation_steps=implementation_steps,
            code_structure=code_structure,
            dependencies=dependencies,
            estimated_timeline=timeline,
            potential_challenges=challenges
        )
    
    def create_visualization_plan(
        self, 
        experiment_plan: ExperimentPlan,
        implementation_strategy: ImplementationStrategy
    ) -> VisualizationPlan:
        """
        创建可视化方案
        
        Args:
            experiment_plan: 实验计划
            implementation_strategy: 实现策略
            
        Returns:
            可视化计划
        """
        logger.info(f"📊 创建可视化方案")
        
        # 确定图表类型
        chart_types = self._determine_chart_types(experiment_plan)
        
        # 推荐可视化工具
        viz_tools = self._recommend_visualization_tools(implementation_strategy)
        
        # 生成展示建议
        presentation_suggestions = self._generate_presentation_suggestions()
        
        # 创建图表描述
        figure_descriptions = self._create_figure_descriptions(experiment_plan)
        
        logger.info(f"✅ 可视化方案创建完成")
        
        return VisualizationPlan(
            chart_types=chart_types,
            visualization_tools=viz_tools,
            presentation_suggestions=presentation_suggestions,
            figure_descriptions=figure_descriptions
        )
    
    def comprehensive_reasoning(
        self, 
        topic: str, 
        context: str = "",
        save_results: bool = True
    ) -> ComprehensiveResearchPlan:
        """
        执行完整的推理流程
        
        Args:
            topic: 研究主题
            context: 背景信息
            save_results: 是否保存结果
            
        Returns:
            综合研究计划
        """
        start_time = time.time()
        logger.info(f"🚀 开始综合推理: {topic}")
        
        try:
            # 1. 生成研究想法
            logger.info("🔄 步骤1: 生成研究想法")
            research_idea = self.generate_research_idea(topic, context)
            
            # 2. 文献调研
            logger.info("🔄 步骤2: 进行文献调研")
            literature_results = self.conduct_literature_research(research_idea)
            
            # 3. 多Agent讨论
            logger.info("🔄 步骤3: 多Agent讨论")
            discussion_result = self.multi_agent_discussion(
                research_idea, 
                literature_results
            )
            
            # 4. 提取工作流知识
            logger.info("🔄 步骤4: 整合工作流知识")
            workflow_knowledge = self._integrate_workflow_knowledge(literature_results)
            
            # 5. 设计实验
            logger.info("🔄 步骤5: 设计实验方案")
            experiment_plan = self.design_experiment(
                research_idea, 
                discussion_result, 
                workflow_knowledge
            )
            
            # 6. 规划实现
            logger.info("🔄 步骤6: 规划实现策略")
            implementation_strategy = self.plan_implementation(
                experiment_plan, 
                workflow_knowledge
            )
            
            # 7. 创建可视化方案
            logger.info("🔄 步骤7: 创建可视化方案")
            visualization_plan = self.create_visualization_plan(
                experiment_plan, 
                implementation_strategy
            )
            
            # 8. 计算综合置信度
            confidence_score = self._calculate_confidence_score(
                discussion_result, 
                len(literature_results.papers),
                experiment_plan
            )
            
            # 9. 生成推荐
            recommendations = self._generate_recommendations(
                discussion_result,
                experiment_plan,
                implementation_strategy
            )
            
            # 创建综合计划
            comprehensive_plan = ComprehensiveResearchPlan(
                research_idea=research_idea,
                literature_review=literature_results,
                multi_agent_discussion=discussion_result,
                experiment_plan=experiment_plan,
                implementation_strategy=implementation_strategy,
                visualization_plan=visualization_plan,
                workflow_knowledge=workflow_knowledge,
                confidence_score=confidence_score,
                recommendations=recommendations
            )
            
            # 保存结果
            if save_results:
                self._save_reasoning_session(comprehensive_plan)
            
            reasoning_time = time.time() - start_time
            logger.info(f"✅ 综合推理完成! 耗时: {reasoning_time:.2f} 秒")
            logger.info(f"🎯 综合置信度: {confidence_score:.2f}/10")
            
            return comprehensive_plan
            
        except Exception as e:
            logger.error(f"❌ 综合推理失败: {e}")
            raise
    
    # === 辅助方法 ===
    
    def _summarize_literature(self, literature_results: LiteratureSearchResult) -> str:
        """总结文献信息"""
        if not literature_results.papers:
            return "未找到相关文献"
        
        summary = f"找到 {literature_results.total_papers} 篇相关论文，"
        summary += f"搜索耗时 {literature_results.search_time:.1f} 秒。"
        
        # 提取年份分布
        years = [p.year for p in literature_results.papers if p.year]
        if years:
            summary += f" 论文年份分布: {min(years)}-{max(years)}。"
        
        return summary
    
    def _get_expert_opinion(
        self, 
        expert_type: str, 
        context: Dict, 
        round_num: int,
        previous_opinions: Dict
    ) -> str:
        """获取专家意见"""
        # 这里应该调用实际的专家代理
        # 简化版本：直接使用LLM模拟专家
        
        expert_prompts = {
            "ai_technology": "作为AI技术专家，评估技术可行性、算法创新性和实现难度",
            "neuroscience": "作为神经科学专家，评估生物合理性、脑启发程度和神经科学基础",
            "data_analysis": "作为数据分析专家，评估数据需求、评估方法和统计分析方案",
            "experiment_design": "作为实验设计专家，评估实验设计、控制变量和验证方案"
        }
        
        prompt = f"""你是一位{expert_prompts[expert_type]}。

请评估以下研究想法：
标题: {context['research_idea'].title}
描述: {context['research_idea'].description}
研究问题: {context['research_idea'].research_question}

文献背景: {context['literature_summary']}

请从你的专业角度提供详细意见，并给出1-10分的评分：
- 研究价值分数
- 可行性分数  
- 创新性分数
- 影响力分数

格式：[你的专业分析] 评分：价值X分，可行性Y分，创新性Z分，影响力W分"""
        
        try:
            response = self.unified_client.get_text_response(
                prompt=prompt,
                model_type="chat",
                temperature=0.7
            )
            
            if response.success:
                return response.content
            else:
                return f"专家{expert_type}意见获取失败"
                
        except Exception as e:
            return f"专家{expert_type}分析异常: {e}"
    
    def _extract_scores_from_opinion(self, opinion: str) -> Dict[str, float]:
        """从专家意见中提取分数"""
        import re
        
        scores = {"value": 7.0, "feasibility": 7.0, "innovation": 7.0, "impact": 7.0}
        
        # 查找分数模式
        patterns = [
            r"价值(\d+)分",
            r"可行性(\d+)分", 
            r"创新性(\d+)分",
            r"影响力(\d+)分"
        ]
        
        keys = ["value", "feasibility", "innovation", "impact"]
        
        for pattern, key in zip(patterns, keys):
            match = re.search(pattern, opinion)
            if match:
                try:
                    scores[key] = float(match.group(1))
                except:
                    pass
        
        return scores
    
    def _generate_final_recommendation(
        self, 
        opinions: Dict, 
        scores: Dict, 
        research_idea: ResearchIdea
    ) -> str:
        """生成最终推荐"""
        avg_score = sum(scores.values()) / len(scores)
        
        if avg_score >= 8.0:
            return f"强烈推荐进行此研究。综合评分{avg_score:.1f}/10，具有很高的研究价值和可行性。"
        elif avg_score >= 6.5:
            return f"推荐进行此研究。综合评分{avg_score:.1f}/10，是一个有价值的研究方向。"
        else:
            return f"需要进一步优化研究方案。综合评分{avg_score:.1f}/10，建议重新考虑研究角度。"
    
    def _generate_hypothesis(self, research_idea: ResearchIdea, discussion: MultiAgentDiscussion) -> str:
        """生成研究假设"""
        return f"基于{research_idea.research_question}，我们假设通过改进的脑启发机制可以显著提升{research_idea.title}的性能表现。"
    
    def _design_experimental_method(self, research_idea: ResearchIdea, hypothesis: str, workflow: Dict) -> str:
        """设计实验方法"""
        return f"采用对比实验设计，在标准数据集上比较所提出方法与现有基线方法的性能。"
    
    def _determine_evaluation_metrics(self, research_idea: ResearchIdea, workflow: Dict) -> List[str]:
        """确定评估指标"""
        return ["准确率", "计算效率", "能耗", "收敛速度"]
    
    def _select_baseline_methods(self, workflow: Dict) -> List[str]:
        """选择基线方法"""
        return ["传统深度学习方法", "现有脑启发方法"]
    
    def _predict_outcomes(self, hypothesis: str, design: str) -> str:
        """预测实验结果"""
        return "预期新方法在准确率上提升5-10%，在能耗上降低15-25%。"
    
    def _analyze_feasibility(self, design: str, feasibility_score: float) -> str:
        """分析可行性"""
        if feasibility_score >= 7.0:
            return "实验设计可行性高，可以在预期时间内完成。"
        else:
            return "实验设计需要进一步简化，降低实施难度。"
    
    def _determine_technical_stack(self, workflow: Dict) -> List[str]:
        """确定技术栈"""
        return ["PyTorch", "Python", "CUDA", "Jupyter"]
    
    def _plan_implementation_steps(self, experiment: ExperimentPlan) -> List[str]:
        """规划实现步骤"""
        return [
            "环境搭建和依赖安装",
            "数据预处理和加载",
            "模型架构设计和实现",
            "训练流程开发",
            "评估系统构建",
            "实验执行和结果分析"
        ]
    
    def _design_code_structure(self, experiment: ExperimentPlan) -> str:
        """设计代码结构"""
        return """
project/
├── data/              # 数据处理
├── models/            # 模型定义  
├── experiments/       # 实验脚本
├── utils/             # 工具函数
├── config/            # 配置文件
└── results/           # 结果存储
        """
    
    def _determine_dependencies(self, technical_stack: List[str]) -> List[str]:
        """确定依赖"""
        return ["torch>=1.9.0", "numpy", "matplotlib", "scipy", "sklearn"]
    
    def _estimate_timeline(self, steps: List[str]) -> str:
        """估计时间线"""
        return f"预计{len(steps)}个步骤，总计需要4-6周时间完成。"
    
    def _identify_challenges(self, experiment: ExperimentPlan) -> List[str]:
        """识别潜在挑战"""
        return [
            "脑启发机制的精确建模",
            "计算资源需求较高",
            "超参数调优复杂"
        ]
    
    def _determine_chart_types(self, experiment: ExperimentPlan) -> List[str]:
        """确定图表类型"""
        return ["性能对比柱状图", "训练曲线图", "能耗对比图", "参数敏感性分析图"]
    
    def _recommend_visualization_tools(self, implementation: ImplementationStrategy) -> List[str]:
        """推荐可视化工具"""
        return ["Matplotlib", "Seaborn", "Plotly", "TensorBoard"]
    
    def _generate_presentation_suggestions(self) -> List[str]:
        """生成展示建议"""
        return [
            "使用一致的颜色主题",
            "确保图表清晰易读",
            "添加详细的图例和标签",
            "包含统计显著性检验结果"
        ]
    
    def _create_figure_descriptions(self, experiment: ExperimentPlan) -> List[str]:
        """创建图表描述"""
        return [
            "图1: 方法整体架构图",
            "图2: 实验结果性能对比",
            "图3: 训练过程动态变化",
            "图4: 参数敏感性分析"
        ]
    
    def _integrate_workflow_knowledge(self, literature: LiteratureSearchResult) -> Dict[str, Any]:
        """整合工作流知识"""
        if not literature.workflow_extractions:
            return {"datasets": [], "architectures": [], "tools": [], "methods": []}
        
        # 汇总所有工作流信息
        all_datasets = []
        all_architectures = []
        all_tools = []
        all_methods = []
        
        for wf in literature.workflow_extractions:
            all_datasets.extend(wf.datasets)
            all_architectures.extend(wf.network_architectures)
            all_tools.extend(wf.platforms_tools)
            all_methods.extend(wf.research_methods)
        
        return {
            "datasets": list(set(all_datasets)),
            "architectures": list(set(all_architectures)),
            "tools": list(set(all_tools)),
            "methods": list(set(all_methods))
        }
    
    def _calculate_confidence_score(
        self, 
        discussion: MultiAgentDiscussion, 
        literature_count: int,
        experiment: ExperimentPlan
    ) -> float:
        """计算综合置信度分数"""
        # 基础分数来自专家讨论
        base_score = (
            discussion.research_value_score + 
            discussion.feasibility_score + 
            discussion.innovation_score + 
            discussion.impact_score
        ) / 4.0
        
        # 文献数量调整
        literature_bonus = min(literature_count / 20.0, 1.0)
        
        # 共识调整
        consensus_bonus = 0.5 if discussion.consensus_reached else 0.0
        
        final_score = base_score + literature_bonus + consensus_bonus
        return min(final_score, 10.0)
    
    def _generate_recommendations(
        self, 
        discussion: MultiAgentDiscussion,
        experiment: ExperimentPlan,
        implementation: ImplementationStrategy
    ) -> List[str]:
        """生成推荐建议"""
        recommendations = []
        
        if discussion.research_value_score >= 8.0:
            recommendations.append("研究价值很高，建议优先进行")
        
        if discussion.feasibility_score < 6.0:
            recommendations.append("可行性较低，建议简化实验设计")
        
        if discussion.innovation_score >= 8.0:
            recommendations.append("创新性突出，适合投稿顶级会议")
        
        recommendations.append("建议进行小规模预实验验证方案")
        recommendations.append("考虑与相关领域专家合作")
        
        return recommendations
    
    def _save_reasoning_session(self, plan: ComprehensiveResearchPlan):
        """保存推理会话"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"reasoning_session_{timestamp}.json"
        
        # 准备可序列化的数据
        session_data = {
            "timestamp": datetime.now().isoformat(),
            "research_idea": {
                "title": plan.research_idea.title,
                "description": plan.research_idea.description,
                "research_question": plan.research_idea.research_question,
                "keywords": plan.research_idea.keywords
            },
            "literature_summary": {
                "total_papers": plan.literature_review.total_papers,
                "search_time": plan.literature_review.search_time,
                "sources_used": plan.literature_review.sources_used
            },
            "discussion_result": {
                "research_value_score": plan.multi_agent_discussion.research_value_score,
                "feasibility_score": plan.multi_agent_discussion.feasibility_score,
                "innovation_score": plan.multi_agent_discussion.innovation_score,
                "impact_score": plan.multi_agent_discussion.impact_score,
                "consensus_reached": plan.multi_agent_discussion.consensus_reached,
                "final_recommendation": plan.multi_agent_discussion.final_recommendation
            },
            "experiment_plan": {
                "hypothesis": plan.experiment_plan.hypothesis,
                "experimental_design": plan.experiment_plan.experimental_design,
                "evaluation_metrics": plan.experiment_plan.evaluation_metrics,
                "baseline_methods": plan.experiment_plan.baseline_methods
            },
            "confidence_score": plan.confidence_score,
            "recommendations": plan.recommendations
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 推理会话已保存: {filename}")
            
        except Exception as e:
            logger.error(f"保存推理会话失败: {e}")


if __name__ == "__main__":
    print("🧪 脑启发智能多专家推理系统测试")
    print("=" * 60)
    
    # 创建推理器
    reasoner = BrainInspiredMultiAgentReasoner()
    
    # 测试完整推理流程
    test_topic = "spike-timing dependent plasticity in attention mechanisms"
    test_context = "研究如何将STDP机制集成到注意力机制中，提升神经网络的生物合理性和效率"
    
    print(f"🔬 测试主题: {test_topic}")
    print(f"📋 背景: {test_context}")
    
    try:
        comprehensive_plan = reasoner.comprehensive_reasoning(
            topic=test_topic,
            context=test_context,
            save_results=True
        )
        
        print(f"\n✅ 综合推理完成!")
        print(f"🎯 研究标题: {comprehensive_plan.research_idea.title}")
        print(f"📊 置信度: {comprehensive_plan.confidence_score:.2f}/10")
        print(f"💡 推荐数量: {len(comprehensive_plan.recommendations)}")
        
        print(f"\n📋 核心推荐:")
        for i, rec in enumerate(comprehensive_plan.recommendations[:3], 1):
            print(f"  {i}. {rec}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
