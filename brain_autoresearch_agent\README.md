# 🧠 Brain-Inspired AutoResearch Agent

A complete brain-inspired intelligent automatic research agent system that automates the entire process from literature review to paper generation.

## 📋 Overview

This system integrates advanced AI techniques to create an end-to-end research automation platform with brain-inspired intelligent components. It encompasses:

1. **Literature Research**: Automated literature search and workflow extraction
2. **Multi-Expert Reasoning**: Collaborative agent-based research discussion
3. **Experiment Design**: Automated hypothesis generation and experiment code creation
4. **Paper Generation**: High-quality academic paper generation with LaTeX output

## 🚀 Installation

### Prerequisites

- Python 3.8+
- DeepSeek API key (for text generation)
- Qwen API key (optional, for visual elements)

### Steps

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/brain_autoresearch_agent.git
   cd brain_autoresearch_agent
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up environment variables:
   ```bash
   # For DeepSeek API
   export DEEPSEEK_API_KEY="your-deepseek-api-key"
   export DEEPSEEK_BASE_URL="https://api.deepseek.com"
   
   # For Qwen API (optional)
   export DASHSCOPE_API_KEY="your-qwen-api-key"
   ```

## 🔧 Configuration

The system uses a YAML configuration file located at `config/brain_research_config.yaml`. Main configuration sections:

- **API Configuration**: Model settings and endpoints
- **Literature Search**: Search parameters and sources
- **Multi-Agent System**: Expert agent parameters
- **Experiment Design**: Framework preferences
- **Paper Generation**: Output formats and style

## 📚 Usage

### Command Line Interface

The system provides a comprehensive CLI with several commands:

#### Complete Workflow

Runs the entire research process from literature search to paper generation:

```bash
python paper_cli.py workflow --topic "Brain-Inspired Meta-Learning" --output-dir "output/my_research"
```

#### Paper Generation Only

Generates a research paper on a specified topic:

```bash
python paper_cli.py paper --topic "Neuromorphic Computing for Edge Devices" --venue "ICML" --optimize
```

#### Version Management

Manage paper versions:

```bash
# List all versions
python paper_cli.py versions --list --output-dir "output/my_research"

# Export a specific version
python paper_cli.py versions --export --version-id "v001" --output-dir "output/my_research"

# Compare two versions
python paper_cli.py versions --compare --version-id "v001" --other-version "v002" --output-dir "output/my_research" --show-diff
```

### Integration with Other Tools

The system can be integrated with your research workflow:

```python
from workflow.complete_research_workflow import CompleteResearchWorkflow

# Initialize workflow
workflow = CompleteResearchWorkflow(output_dir="output/research")

# Run complete workflow
result = workflow.run_complete_workflow("Efficient Brain-Inspired Learning Algorithms")

# Access paper content
paper_content = result["paper_results"]
latex_path = paper_content["final_paper_path"]
```

## 🧪 Testing

The system includes comprehensive tests to verify functionality:

### Running Tests

```bash
# Run all tests
python -m unittest discover tests

# Run a specific test
python -m unittest tests.test_system_integration

# Run a specific test method
python -m unittest tests.test_system_integration.TestSystemIntegration.test_01_api_connection
```

### Testing Different Components

1. **API Connection Test**:
   ```bash
   python tests/test_deepseek_api.py
   ```

2. **Paper Generation Test**:
   ```bash
   python tests/test_direct_paper_generation.py
   ```

3. **End-to-End System Test**:
   ```bash
   python tests/test_system_integration.py
   ```

## 📁 Directory Structure

```
brain_autoresearch_agent/
├── agents/                   # Expert agent system
│   ├── base_agent.py
│   ├── agent_manager.py
│   └── expert_agents/        # Domain-specific experts
├── config/                   # Configuration files
├── core/                     # Core components
│   ├── unified_api_client.py # Unified API client
│   ├── paper_workflow.py     # Literature workflow
│   └── ...
├── paper_generation/         # Paper generation components
│   ├── brain_paper_writer.py
│   ├── latex_generator.py
│   ├── paper_quality_optimizer.py
│   └── review_system/        # Paper review system
├── reasoning/                # Research reasoning components
│   ├── enhanced_multi_agent_collaborator.py
│   ├── enhanced_reasoning_workflow.py
│   └── ...
├── tests/                    # Test suite
├── workflow/                 # End-to-end workflow
│   └── complete_research_workflow.py
└── paper_cli.py              # Command line interface
```

## 🌟 Features

- **Multi-Expert Collaboration**: 5 specialized expert agents working together
- **Comprehensive Literature Analysis**: Multiple source integration with workflow extraction
- **Brain-Inspired Architecture**: Neuro-inspired reasoning and knowledge synthesis
- **High-Quality Output**: Paper writing with automated review and quality optimization
- **Version Management**: Track and compare different paper versions
- **Conference Template Support**: LaTeX output for different academic venues
- **Unified API Integration**: Seamless use of DeepSeek and Qwen APIs

## 🔬 Technical Details

- The system uses English prompts for all LLM interactions to ensure optimal results
- Asynchronous processing for improved performance
- Modular architecture for easy extension
- Comprehensive error handling and fallback mechanisms

## 📘 Example Research Topics

- Brain-Inspired Neural Network Architectures
- Neuromorphic Computing for Edge AI
- Meta-Learning in Spiking Neural Networks
- Efficient Training Methods for Brain-Inspired AI
- Biologically Plausible Deep Learning

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📧 Contact

For questions and support, please open an issue in the GitHub repository.
