class LaTeXGenerator:
    """LaTeX生成器"""
    def __init__(self):
        self.templates = {
            "ICML": "\\documentclass{article}\n\\usepackage{icml2025}",
            "ICLR": "\\documentclass{article}\n\\usepackage{iclr2025}",
            "NeurIPS": "\\documentclass{article}\n\\usepackage{neurips_2025}"
        }
    def generate_paper_latex(self, paper_content):
        venue = paper_content.get("metadata", {}).get("venue", "ICML")
        template = self.templates.get(venue, self.templates["ICML"])
        latex = f"{template}\n\n\\title{{{paper_content.get('title', 'Untitled')}}}\n\n"
        latex += "\\begin{document}\n\\maketitle\n\n"
        latex += "\\begin{abstract}\n"
        latex += paper_content.get("abstract", "Abstract content missing.")
        latex += "\n\\end{abstract}\n\n"
        for section, content in paper_content.get("sections", {}).items():
            if section != "references":
                latex += f"\\section{{{section.title()}}}\n"
                latex += f"{content}\n\n"
        latex += "\\section{References}\n"
        latex += "\\bibliography{references}\n"
        latex += "\\bibliographystyle{plain}\n"
        latex += "\\end{document}"
        return latex