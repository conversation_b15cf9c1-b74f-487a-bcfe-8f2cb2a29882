"""
Automatic Paper Revision Engine

实现基于专家评审结果的自动论文修订系统
"""

import os
import sys
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass
import logging
import dataclasses

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from agents.agent_manager import AgentManager
from paper_generation.review_system.multi_expert_review_system import (
    PaperReviewResult, RevisionPlan, ExpertReview
)


@dataclass
class RevisionTask:
    """修订任务"""
    task_id: str
    section: str
    priority: str
    original_content: str
    revision_type: str  # rewrite, enhance, restructure, add_content
    target_improvements: List[str]
    expert_feedback: List[str]
    estimated_effort: str


@dataclass
class RevisionResult:
    """修订结果"""
    task_id: str
    success: bool
    revised_content: str
    changes_made: List[str]
    quality_improvement: float
    revision_rationale: str
    warnings: List[str]


@dataclasses.dataclass
class PaperRevisionSession:
    """论文修订会话"""
    initial_paper: Dict[str, Any]
    final_paper: Dict[str, Any]
    revision_steps: List[Dict[str, Any]]
    improvement_metrics: Dict[str, float]


class AutoRevisionEngine:
    """自动修订引擎"""
    
    def __init__(self, llm_client: Optional[LLMClient] = None):
        """
        初始化自动修订引擎
        
        Args:
            llm_client: LLM客户端实例
        """
        if llm_client is None:
            self.llm_client = LLMClient()
        else:
            self.llm_client = llm_client
            
        self.agent_manager = AgentManager(self.llm_client)
        self.logger = self._setup_logger()
        
        # 修订策略配置
        self.revision_strategies = {
            'novelty': {
                'target_sections': ['introduction', 'related_work'],
                'revision_type': 'enhance',
                'focus_areas': ['unique contributions', 'comparison with existing work', 'innovation highlighting']
            },
            'technical_quality': {
                'target_sections': ['methodology', 'experiments'],
                'revision_type': 'restructure',
                'focus_areas': ['experimental design', 'validation methods', 'technical rigor']
            },
            'clarity': {
                'target_sections': ['overall'],
                'revision_type': 'rewrite',
                'focus_areas': ['writing flow', 'logical structure', 'readability']
            },
            'significance': {
                'target_sections': ['introduction', 'conclusion'],
                'revision_type': 'enhance',
                'focus_areas': ['impact articulation', 'broader implications', 'future directions']
            },
            'reproducibility': {
                'target_sections': ['methodology', 'experiments'],
                'revision_type': 'add_content',
                'focus_areas': ['implementation details', 'parameter settings', 'code availability']
            }
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('AutoRevisionEngine')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def execute_paper_revision(self, paper_content: Dict[str, Any], 
                              review_result: PaperReviewResult,
                              max_iterations: int = 3) -> PaperRevisionSession:
        """
        执行论文自动修订
        
        Args:
            paper_content: 原始论文内容
            review_result: 专家评审结果
            max_iterations: 最大修订轮次
            
        Returns:
            PaperRevisionSession: 修订会话结果
        """
        print(f"\\n🔧 开始自动论文修订流程")
        print(f"📊 原始评分: {review_result.overall_score:.2f}/10")
        print(f"🔄 最大修订轮次: {max_iterations}")
        
        session_id = f"revision_{int(time.time())}"
        current_paper = paper_content.copy()
        all_revision_tasks = []
        all_revision_results = []
        
        for iteration in range(max_iterations):
            print(f"\\n🔄 修订轮次 {iteration + 1}/{max_iterations}")
            
            # 第1步: 生成修订任务
            print("  📋 生成修订任务")
            revision_tasks = self._generate_revision_tasks(current_paper, review_result)
            
            if not revision_tasks:
                print("  ✅ 无需进一步修订")
                break
            
            print(f"  📝 生成了 {len(revision_tasks)} 个修订任务")
            all_revision_tasks.extend(revision_tasks)
            
            # 第2步: 执行修订任务
            print("  🚀 执行修订任务")
            revision_results = self._execute_revision_tasks(current_paper, revision_tasks)
            all_revision_results.extend(revision_results)
            
            # 第3步: 应用修订结果
            print("  ✅ 应用修订结果")
            current_paper = self._apply_revision_results(current_paper, revision_results)
            
            # 第4步: 评估改进效果
            print("  📈 评估改进效果")
            improvement = self._assess_revision_quality(revision_results)
            print(f"    质量改进: {improvement:.2f}")
            
            # 如果改进很小，可以提前结束
            if improvement < 0.1:
                print("  ⏹️ 改进幅度较小，结束修订")
                break
        
        # 计算最终改进指标
        print("\\n📊 计算最终改进指标")
        improvement_metrics = self._calculate_improvement_metrics(
            paper_content, current_paper, all_revision_results
        )
        
        session = PaperRevisionSession(
            initial_paper=paper_content,
            final_paper=current_paper,
            revision_steps=[{"section": task.section, "action": task.revision_type, "status": "completed"} for task in all_revision_tasks],
            improvement_metrics=improvement_metrics
        )
        
        print(f"\\n✅ 自动修订完成")
        print(f"📝 总修订任务: {len(all_revision_tasks)}")
        print(f"✅ 成功修订: {sum(1 for r in all_revision_results if r.success)}")
        print(f"📈 质量提升: {improvement_metrics.get('overall_improvement', 0):.2f}")
        
        return session
    
    def _generate_revision_tasks(self, paper_content: Dict[str, Any], 
                                review_result: PaperReviewResult) -> List[RevisionTask]:
        """生成修订任务"""
        revision_tasks = []
        
        # 基于专家评审的修订计划生成任务
        for i, plan in enumerate(review_result.revision_plans):
            task = self._create_revision_task_from_plan(paper_content, plan, i)
            if task:
                revision_tasks.append(task)
        
        # 基于低分标准生成额外任务
        for review in review_result.expert_reviews:
            for criterion, score in review.criterion_scores.items():
                if score < 6.0 and criterion in self.revision_strategies:
                    additional_task = self._create_revision_task_from_criterion(
                        paper_content, criterion, review, len(revision_tasks)
                    )
                    if additional_task:
                        revision_tasks.append(additional_task)
        
        # 按优先级排序
        priority_order = {'High': 0, 'Medium': 1, 'Low': 2}
        revision_tasks.sort(key=lambda x: priority_order.get(x.priority, 3))
        
        return revision_tasks[:10]  # 限制任务数量
    
    def _create_revision_task_from_plan(self, paper_content: Dict[str, Any], 
                                       plan: RevisionPlan, task_index: int) -> Optional[RevisionTask]:
        """从修订计划创建修订任务"""
        section = plan.section
        sections = paper_content.get('sections', {})
        
        if section == 'overall':
            # 整体修订，选择最需要改进的部分
            target_section = self._find_weakest_section(paper_content)
            original_content = sections.get(target_section, '')
        else:
            original_content = sections.get(section, '')
        
        if not original_content:
            return None
        
        return RevisionTask(
            task_id=f"task_{task_index}",
            section=section if section != 'overall' else target_section,
            priority=plan.priority,
            original_content=original_content,
            revision_type='enhance',  # 默认类型
            target_improvements=[plan.suggested_changes],
            expert_feedback=[plan.issue_description],
            estimated_effort=plan.estimated_effort
        )
    
    def _create_revision_task_from_criterion(self, paper_content: Dict[str, Any],
                                           criterion: str, review: ExpertReview,
                                           task_index: int) -> Optional[RevisionTask]:
        """从评审标准创建修订任务"""
        strategy = self.revision_strategies.get(criterion, {})
        if not strategy:
            return None
        
        target_sections = strategy['target_sections']
        revision_type = strategy['revision_type']
        focus_areas = strategy['focus_areas']
        
        # 选择目标部分
        sections = paper_content.get('sections', {})
        target_section = None
        
        for section in target_sections:
            if section in sections and sections[section]:
                target_section = section
                break
        
        if not target_section or target_section not in sections:
            return None
        
        return RevisionTask(
            task_id=f"criterion_task_{task_index}",
            section=target_section,
            priority='Medium',
            original_content=sections[target_section],
            revision_type=revision_type,
            target_improvements=focus_areas,
            expert_feedback=review.improvement_suggestions[:3],  # 前3个建议
            estimated_effort='Medium'
        )
    
    def _find_weakest_section(self, paper_content: Dict[str, Any]) -> str:
        """找到最薄弱的部分"""
        sections = paper_content.get('sections', {})
        
        # 简单启发式：选择内容最少的关键部分
        key_sections = ['introduction', 'methodology', 'experiments', 'results', 'conclusion']
        
        min_length = float('inf')
        weakest_section = 'introduction'
        
        for section in key_sections:
            if section in sections:
                length = len(sections[section])
                if length < min_length:
                    min_length = length
                    weakest_section = section
        
        return weakest_section
    
    def _execute_revision_tasks(self, paper_content: Dict[str, Any], 
                               revision_tasks: List[RevisionTask]) -> List[RevisionResult]:
        """执行修订任务"""
        revision_results = []
        
        for task in revision_tasks:
            print(f"    🔧 执行任务: {task.task_id} ({task.section})")
            
            try:
                result = self._execute_single_revision_task(paper_content, task)
                revision_results.append(result)
                
                if result.success:
                    print(f"      ✅ 修订成功，质量提升: {result.quality_improvement:.2f}")
                else:
                    print(f"      ❌ 修订失败")
                    
            except Exception as e:
                print(f"      ⚠️ 修订出错: {e}")
                revision_results.append(RevisionResult(
                    task_id=task.task_id,
                    success=False,
                    revised_content=task.original_content,
                    changes_made=[],
                    quality_improvement=0.0,
                    revision_rationale=f"Error: {e}",
                    warnings=[str(e)]
                ))
        
        return revision_results
    
    def _execute_single_revision_task(self, paper_content: Dict[str, Any], 
                                     task: RevisionTask) -> RevisionResult:
        """执行单个修订任务"""
        
        # 构建修订提示词
        revision_prompt = self._build_revision_prompt(paper_content, task)
        
        try:
            # 尝试使用专门的代理
            if task.section in ['methodology', 'experiments']:
                agent = self.agent_manager.get_agent('experiment_design')
            elif task.section in ['introduction', 'related_work']:
                agent = self.agent_manager.get_agent('ai_technology')
            elif task.section in ['results', 'analysis']:
                agent = self.agent_manager.get_agent('data_analysis')
            else:
                agent = self.agent_manager.get_agent('paper_writing')
            
            if agent:
                response = agent.analyze({
                    "input_text": revision_prompt,
                    "analysis_type": "paper_revision"
                })
                
                if response and hasattr(response, 'content') and response.content:
                    return self._parse_revision_result(response.content, task)
            
            # 备用LLM修订
            response = self.llm_client.get_response(revision_prompt)
            response_text = response[0] if isinstance(response, tuple) else response
            
            return self._parse_revision_result(response_text, task)
            
        except Exception as e:
            return RevisionResult(
                task_id=task.task_id,
                success=False,
                revised_content=task.original_content,
                changes_made=[],
                quality_improvement=0.0,
                revision_rationale=f"Revision failed: {e}",
                warnings=[str(e)]
            )
    
    def _build_revision_prompt(self, paper_content: Dict[str, Any], 
                              task: RevisionTask) -> str:
        """
        Build revision prompt for a paper section
        
        Args:
            paper_content: Paper content dictionary
            task: Revision task information
            
        Returns:
            Revision prompt string
        """
        # Get relevant context
        context_info = self._get_revision_context(paper_content, task)
        
        # Determine instruction based on revision type
        instruction = ""
        if task.revision_type == RevisionType.CONTENT_ENHANCEMENT:
            instruction = "Enhance the content quality while maintaining the core ideas. Add relevant details, clarify concepts, and strengthen arguments."
        elif task.revision_type == RevisionType.CLARITY_IMPROVEMENT:
            instruction = "Improve the clarity of the writing. Simplify complex sentences, define technical terms, and enhance the logical flow."
        elif task.revision_type == RevisionType.TECHNICAL_CORRECTION:
            instruction = "Fix technical errors and inaccuracies. Ensure methodological correctness and appropriate use of terminology."
        elif task.revision_type == RevisionType.STRUCTURE_REORGANIZATION:
            instruction = "Reorganize the structure to improve logical flow. Reorder paragraphs, add transitions, and ensure coherent progression of ideas."
        elif task.revision_type == RevisionType.TONE_ADJUSTMENT:
            instruction = "Adjust the tone to be more appropriate for academic writing. Maintain formality, objectivity, and scholarly voice."
        
        prompt = f"""
        You are an expert academic writer tasked with revising a research paper section.

        Context:
        {context_info}

        Section to Revise: {task.section}
        Revision Type: {task.revision_type}
        Priority: {task.priority}

        Current Content:
        {task.original_content}

        Expert Feedback:
        {chr(10).join(f"- {feedback}" for feedback in task.expert_feedback)}

        Target Improvements:
        {chr(10).join(f"- {improvement}" for improvement in task.target_improvements)}

        Instructions:
        {instruction}

        Please provide your revision in the following JSON format:
        {{
            "revised_content": "<improved section content>",
            "changes_made": ["change 1", "change 2", "change 3"],
            "quality_improvement": <0.0-2.0 estimated improvement score>,
            "revision_rationale": "<explanation of changes made>",
            "warnings": ["warning 1 if any"]
        }}

        Requirements:
        1. Maintain academic tone and style
        2. Ensure logical flow and coherence
        3. Preserve essential technical content
        4. Improve clarity and readability
        5. Address the specific expert feedback
        6. Keep appropriate section length (aim for similar or slightly longer than original)
        """
        
        return prompt
    
    def _parse_revision_result(self, response_content: str, 
                              task: RevisionTask) -> RevisionResult:
        """解析修订结果"""
        try:
            # 尝试解析JSON
            json_start = response_content.find('{')
            json_end = response_content.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_text = response_content[json_start:json_end]
                revision_data = json.loads(json_text)
                
                return RevisionResult(
                    task_id=task.task_id,
                    success=True,
                    revised_content=revision_data.get('revised_content', task.original_content),
                    changes_made=revision_data.get('changes_made', []),
                    quality_improvement=float(revision_data.get('quality_improvement', 0.5)),
                    revision_rationale=revision_data.get('revision_rationale', 'Content revised'),
                    warnings=revision_data.get('warnings', [])
                )
            else:
                # 如果没有JSON格式，将整个响应作为修订内容
                return RevisionResult(
                    task_id=task.task_id,
                    success=True,
                    revised_content=response_content.strip(),
                    changes_made=["Content improved"],
                    quality_improvement=0.5,
                    revision_rationale="Direct content revision",
                    warnings=["No structured output format"]
                )
                
        except json.JSONDecodeError:
            return RevisionResult(
                task_id=task.task_id,
                success=False,
                revised_content=task.original_content,
                changes_made=[],
                quality_improvement=0.0,
                revision_rationale="Failed to parse revision result",
                warnings=["JSON parsing failed"]
            )
    
    def _apply_revision_results(self, paper_content: Dict[str, Any], 
                               revision_results: List[RevisionResult]) -> Dict[str, Any]:
        """应用修订结果到论文内容"""
        updated_paper = paper_content.copy()
        sections = updated_paper.get('sections', {}).copy()
        
        for result in revision_results:
            if result.success and result.quality_improvement > 0.2:  # 只应用有明显改进的修订
                # 找到对应的任务以获取部分信息
                task_section = None
                for task in getattr(self, '_current_tasks', []):
                    if task.task_id == result.task_id:
                        task_section = task.section
                        break
                
                if task_section and task_section in sections:
                    sections[task_section] = result.revised_content
                    print(f"      ✅ 应用修订: {task_section}")
        
        updated_paper['sections'] = sections
        return updated_paper
    
    def _assess_revision_quality(self, revision_results: List[RevisionResult]) -> float:
        """评估修订质量"""
        if not revision_results:
            return 0.0
        
        successful_revisions = [r for r in revision_results if r.success]
        if not successful_revisions:
            return 0.0
        
        total_improvement = sum(r.quality_improvement for r in successful_revisions)
        avg_improvement = total_improvement / len(successful_revisions)
        
        return avg_improvement
    
    def _calculate_improvement_metrics(self, original_paper: Dict[str, Any],
                                     final_paper: Dict[str, Any],
                                     revision_results: List[RevisionResult]) -> Dict[str, float]:
        """计算改进指标"""
        metrics = {}
        
        # 成功修订率
        total_tasks = len(revision_results)
        successful_tasks = sum(1 for r in revision_results if r.success)
        metrics['success_rate'] = successful_tasks / total_tasks if total_tasks > 0 else 0.0
        
        # 平均质量改进
        successful_revisions = [r for r in revision_results if r.success]
        if successful_revisions:
            metrics['avg_quality_improvement'] = sum(r.quality_improvement for r in successful_revisions) / len(successful_revisions)
        else:
            metrics['avg_quality_improvement'] = 0.0
        
        # 内容长度变化
        original_length = sum(len(content) for content in original_paper.get('sections', {}).values())
        final_length = sum(len(content) for content in final_paper.get('sections', {}).values())
        metrics['content_expansion'] = (final_length - original_length) / original_length if original_length > 0 else 0.0
        
        # 总体改进估计
        metrics['overall_improvement'] = (
            metrics['success_rate'] * 0.3 + 
            metrics['avg_quality_improvement'] * 0.7
        )
        
        return metrics


def test_auto_revision_engine():
    """测试自动修订引擎"""
    print("🧪 测试自动修订引擎")
    
    # 创建测试论文和评审结果（简化版）
    from paper_generation.review_system.multi_expert_review_system import (
        RevisionPlan, PaperReviewResult, ExpertReview
    )
    
    test_paper = {
        'title': 'Brain-Inspired Adaptive Learning Networks',
        'sections': {
            'introduction': 'This paper presents a new approach...',
            'methodology': 'We propose a novel architecture...',
            'experiments': 'We conducted experiments on...',
            'results': 'Our method achieves...',
            'conclusion': 'In conclusion...'
        }
    }
    
    # 创建模拟评审结果
    mock_review = PaperReviewResult(
        paper_id="test_paper",
        overall_score=5.5,
        consensus_level=0.8,
        expert_reviews=[],
        revision_plans=[
            RevisionPlan(
                priority="High",
                section="introduction",
                issue_description="Novelty not clearly articulated",
                suggested_changes="Better highlight unique contributions",
                rationale="Low novelty scores",
                estimated_effort="Medium"
            )
        ],
        final_recommendation="Revision Required",
        review_summary="Paper needs improvement",
        quality_metrics={}
    )
    
    # 初始化修订引擎
    revision_engine = AutoRevisionEngine()
    
    # 执行修订
    revision_session = revision_engine.execute_paper_revision(test_paper, mock_review)
    
    print(f"\\n📊 修订结果:")
    print(f"修订任务数: {len(revision_session.revision_steps)}")
    print(f"成功修订: {sum(1 for r in revision_session.revision_steps if r['status'] == 'completed')}")
    print(f"质量提升: {revision_session.improvement_metrics.get('overall_improvement', 0):.2f}")
    
    return revision_session


if __name__ == "__main__":
    test_auto_revision_engine()
