"""
Agent系统与论文质量优化器集成测试
测试现有Agent系统与新开发的论文优化模块的协同效果
"""

import asyncio
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入Agent系统
from agents.agent_manager import AgentManager
from agents.base_agent import AgentTask, AgentResponse

# 导入论文质量优化系统
from paper_generation.paper_quality_optimizer import PaperQualityOptimizer
from paper_generation.latex_format_expert import LaTeXFormatExpert
from paper_generation.enhanced_citation_manager import EnhancedCitationManager
from paper_generation.multi_expert_review_system import MultiExpertReviewSystem

# 导入核心组件
from core.llm_client import LLMClient
from core.hybrid_model_client import HybridModelClient

class IntegratedSystemTest:
    """集成系统测试"""
    
    def __init__(self):
        self.test_results = {
            'start_time': datetime.now(),
            'agent_system_tests': [],
            'optimization_system_tests': [],
            'integration_tests': [],
            'performance_metrics': {},
            'recommendations': []
        }
        
        # 初始化客户端
        self.llm_client = None
        self.hybrid_client = None
        self.agent_manager = None
        self.paper_optimizer = None
        
        print("🚀 开始集成系统测试初始化...")
        self._initialize_systems()
    
    def _initialize_systems(self):
        """初始化所有系统"""
        try:
            # 初始化LLM客户端
            print("🔧 初始化LLM客户端...")
            try:
                self.llm_client = LLMClient()
                print("✅ 标准LLM客户端初始化成功")
            except Exception as e:
                print(f"⚠️  标准LLM客户端初始化失败: {e}")
            
            # 初始化混合模型客户端
            print("🔧 初始化混合模型客户端...")
            try:
                self.hybrid_client = HybridModelClient()
                print("✅ 混合模型客户端初始化成功")
            except Exception as e:
                print(f"⚠️  混合模型客户端初始化失败: {e}")
            
            # 初始化Agent管理器
            if self.llm_client:
                print("🤖 初始化Agent管理器...")
                try:
                    self.agent_manager = AgentManager(self.llm_client)
                    print("✅ Agent管理器初始化成功")
                except Exception as e:
                    print(f"⚠️  Agent管理器初始化失败: {e}")
            
            # 初始化论文优化器
            print("📝 初始化论文质量优化器...")
            try:
                self.paper_optimizer = PaperQualityOptimizer(self.hybrid_client)
                print("✅ 论文质量优化器初始化成功")
            except Exception as e:
                print(f"⚠️  论文质量优化器初始化失败: {e}")
            
            # 检查系统就绪状态
            ready_systems = []
            if self.llm_client: ready_systems.append("LLM客户端")
            if self.hybrid_client: ready_systems.append("混合模型客户端")
            if self.agent_manager: ready_systems.append("Agent管理器")
            if self.paper_optimizer: ready_systems.append("论文优化器")
            
            print(f"📊 系统就绪状态: {len(ready_systems)}/4")
            for system in ready_systems:
                print(f"   ✅ {system}")
                
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
    
    async def test_agent_system(self):
        """测试Agent系统"""
        print("\n🤖 测试Agent系统功能...")
        print("="*50)
        
        if not self.agent_manager:
            print("❌ Agent管理器未初始化，跳过测试")
            return
        
        # 测试1: 检查已注册的专家
        print("📋 测试1: 检查已注册的专家")
        agents_info = self.agent_manager.list_agents()
        print(f"   已注册专家: {len(agents_info)}")
        for agent_id, info in agents_info.items():
            print(f"   - {agent_id}: {info.get('agent_type', 'Unknown')}")
        
        agent_test_result = {
            'test_name': 'Agent系统基础功能',
            'registered_agents': len(agents_info),
            'agents_details': agents_info,
            'passed': len(agents_info) > 0
        }
        
        # 测试2: 创建和执行任务
        print("\n📋 测试2: 创建和执行任务")
        try:
            # 创建一个简单的分析任务
            test_task = self.agent_manager.create_task(
                task_type="research_analysis",
                input_data={
                    "research_topic": "神经网络可塑性在深度学习中的应用",
                    "analysis_focus": "技术可行性分析"
                },
                requirements=["技术评估", "创新性分析"]
            )
            
            print(f"   ✅ 任务创建成功: {test_task.task_id}")
            
            # 自动分配任务
            print("   🎯 自动分配任务...")
            results = self.agent_manager.auto_assign_task(test_task)
            
            print(f"   📊 任务执行结果: {len(results)} 个专家响应")
            for agent_id, response in results.items():
                if response:
                    print(f"      - {agent_id}: 置信度 {response.confidence:.2f}")
                    # 显示响应摘要
                    content_preview = response.content[:100] + "..." if len(response.content) > 100 else response.content
                    print(f"        内容预览: {content_preview}")
            
            agent_test_result['task_execution'] = {
                'task_created': True,
                'responses_received': len(results),
                'average_confidence': sum(r.confidence for r in results.values() if r) / max(len(results), 1)
            }
            
        except Exception as e:
            print(f"   ❌ 任务执行失败: {e}")
            agent_test_result['task_execution'] = {
                'task_created': False,
                'error': str(e)
            }
        
        # 测试3: 协作分析
        print("\n🤝 测试3: 多专家协作分析")
        try:
            collaboration_task = self.agent_manager.create_task(
                task_type="collaborative_analysis",
                input_data={
                    "research_topic": "脑启发人工智能算法优化",
                    "collaboration_focus": "跨领域专家协作"
                }
            )
            
            # 执行协作分析
            collaboration_result = self.agent_manager.collaborative_analysis(
                collaboration_task,
                agent_ids=None  # 使用所有可用专家
            )
            
            if collaboration_result:
                print(f"   ✅ 协作分析完成")
                print(f"   📊 融合结果质量: {collaboration_result.get('fusion_quality', 'N/A')}")
                print(f"   🤝 专家共识度: {collaboration_result.get('consensus_level', 'N/A')}")
                
                agent_test_result['collaboration'] = {
                    'completed': True,
                    'fusion_quality': collaboration_result.get('fusion_quality', 0),
                    'consensus_level': collaboration_result.get('consensus_level', 0)
                }
            else:
                print("   ❌ 协作分析失败")
                agent_test_result['collaboration'] = {'completed': False}
                
        except Exception as e:
            print(f"   ❌ 协作分析异常: {e}")
            agent_test_result['collaboration'] = {'completed': False, 'error': str(e)}
        
        # 记录测试结果
        self.test_results['agent_system_tests'].append(agent_test_result)
        
        print(f"\n📊 Agent系统测试完成")
        print(f"   总体评估: {'✅ 通过' if agent_test_result['passed'] else '❌ 失败'}")
    
    async def test_optimization_system(self):
        """测试论文优化系统"""
        print("\n📝 测试论文优化系统功能...")
        print("="*50)
        
        if not self.paper_optimizer:
            print("❌ 论文优化器未初始化，跳过测试")
            return
        
        # 测试论文内容
        test_paper_content = """
\\documentclass{article}
\\begin{document}
\\title{Neural Plasticity-Inspired Deep Learning Architecture}
\\author{Test Author}
\\maketitle

\\begin{abstract}
This paper presents a novel approach to deep learning inspired by neural plasticity mechanisms. 
We propose a hybrid architecture that combines traditional neural networks with biologically-inspired 
plasticity rules to improve learning efficiency and adaptability.
\\end{abstract}

\\section{Introduction}
Deep learning has achieved remarkable success in various domains, but current architectures 
lack the adaptability and efficiency of biological neural networks. 
This work addresses this gap by incorporating neural plasticity principles.

\\section{Methodology}
We propose a neural plasticity-inspired architecture that incorporates:
1. Synaptic plasticity mechanisms
2. Homeostatic regulation
3. Structural plasticity adaptation

\\section{Experiments}
We evaluate our approach on several benchmarks including CIFAR-10, ImageNet, and custom datasets.
The results demonstrate significant improvements in learning efficiency and adaptability.

\\section{Conclusion}
Our results show that incorporating neural plasticity mechanisms can significantly improve 
deep learning performance while maintaining computational efficiency.

\\end{document}
"""
        
        test_metadata = {
            'title': 'Neural Plasticity-Inspired Deep Learning Architecture',
            'abstract': 'This paper presents a novel approach to deep learning inspired by neural plasticity mechanisms.',
            'authors': ['Test Author'],
            'keywords': ['neural plasticity', 'deep learning', 'biologically-inspired AI']
        }
        
        optimization_test_result = {
            'test_name': '论文优化系统',
            'components_tested': [],
            'overall_success': False
        }
        
        # 测试1: LaTeX格式专家
        print("🔧 测试1: LaTeX格式专家")
        try:
            latex_expert = LaTeXFormatExpert(self.hybrid_client)
            latex_result = await latex_expert.optimize_latex_format(test_paper_content, "ICML")
            
            if latex_result.get('success', False):
                print(f"   ✅ LaTeX优化成功，质量分数: {latex_result.get('quality_score', 0):.1f}/10")
                optimization_test_result['components_tested'].append({
                    'component': 'LaTeX格式专家',
                    'success': True,
                    'quality_score': latex_result.get('quality_score', 0)
                })
            else:
                print(f"   ❌ LaTeX优化失败")
                optimization_test_result['components_tested'].append({
                    'component': 'LaTeX格式专家',
                    'success': False
                })
                
        except Exception as e:
            print(f"   ❌ LaTeX专家测试异常: {e}")
            optimization_test_result['components_tested'].append({
                'component': 'LaTeX格式专家',
                'success': False,
                'error': str(e)
            })
        
        # 测试2: 引用管理器
        print("\n📚 测试2: 引用管理器")
        try:
            citation_manager = EnhancedCitationManager(self.hybrid_client)
            citation_result = await citation_manager.enhance_citations(test_paper_content, test_metadata)
            
            if citation_result.get('success', False):
                print(f"   ✅ 引用增强成功，引用数量: {citation_result.get('citation_count', 0)}")
                print(f"   📊 引用质量: {citation_result.get('quality_score', 0):.1f}/10")
                optimization_test_result['components_tested'].append({
                    'component': '引用管理器',
                    'success': True,
                    'citation_count': citation_result.get('citation_count', 0),
                    'quality_score': citation_result.get('quality_score', 0)
                })
            else:
                print(f"   ❌ 引用增强失败")
                optimization_test_result['components_tested'].append({
                    'component': '引用管理器',
                    'success': False
                })
                
        except Exception as e:
            print(f"   ❌ 引用管理器测试异常: {e}")
            optimization_test_result['components_tested'].append({
                'component': '引用管理器',
                'success': False,
                'error': str(e)
            })
        
        # 测试3: 多专家评审系统
        print("\n🔍 测试3: 多专家评审系统")
        try:
            review_system = MultiExpertReviewSystem(self.hybrid_client)
            review_result = await review_system.conduct_review(test_paper_content, test_metadata)
            
            if review_result:
                print(f"   ✅ 多专家评审完成")
                print(f"   📊 共识分数: {review_result.consensus_score:.1f}/10")
                print(f"   👥 专家数量: {len(review_result.reviews)}")
                print(f"   🔧 关键问题: {len(review_result.key_issues)}")
                
                optimization_test_result['components_tested'].append({
                    'component': '多专家评审系统',
                    'success': True,
                    'consensus_score': review_result.consensus_score,
                    'expert_count': len(review_result.reviews),
                    'issues_count': len(review_result.key_issues)
                })
            else:
                print(f"   ❌ 多专家评审失败")
                optimization_test_result['components_tested'].append({
                    'component': '多专家评审系统',
                    'success': False
                })
                
        except Exception as e:
            print(f"   ❌ 多专家评审测试异常: {e}")
            optimization_test_result['components_tested'].append({
                'component': '多专家评审系统',
                'success': False,
                'error': str(e)
            })
        
        # 测试4: 综合优化器
        print("\n🚀 测试4: 综合优化器")
        try:
            optimization_result = await self.paper_optimizer.optimize_paper(
                test_paper_content,
                test_metadata,
                output_dir="output/integration_test",
                target_venue="ICML"
            )
            
            if optimization_result.get('success', False):
                print(f"   ✅ 综合优化成功")
                print(f"   📊 最终质量: {optimization_result.get('final_quality', 0):.1f}/10")
                print(f"   📈 总改进: {optimization_result.get('total_improvement', 0):+.1f}")
                
                optimization_test_result['components_tested'].append({
                    'component': '综合优化器',
                    'success': True,
                    'final_quality': optimization_result.get('final_quality', 0),
                    'total_improvement': optimization_result.get('total_improvement', 0)
                })
                
                optimization_test_result['overall_success'] = True
                
            else:
                print(f"   ❌ 综合优化失败")
                optimization_test_result['components_tested'].append({
                    'component': '综合优化器',
                    'success': False
                })
                
        except Exception as e:
            print(f"   ❌ 综合优化测试异常: {e}")
            optimization_test_result['components_tested'].append({
                'component': '综合优化器',
                'success': False,
                'error': str(e)
            })
        
        # 记录测试结果
        self.test_results['optimization_system_tests'].append(optimization_test_result)
        
        successful_components = sum(1 for comp in optimization_test_result['components_tested'] if comp.get('success', False))
        total_components = len(optimization_test_result['components_tested'])
        
        print(f"\n📊 论文优化系统测试完成")
        print(f"   成功组件: {successful_components}/{total_components}")
        print(f"   总体评估: {'✅ 通过' if optimization_test_result['overall_success'] else '❌ 需要改进'}")
    
    async def test_system_integration(self):
        """测试系统集成效果"""
        print("\n🔗 测试系统集成效果...")
        print("="*50)
        
        if not (self.agent_manager and self.paper_optimizer):
            print("❌ 系统未完全初始化，跳过集成测试")
            return
        
        integration_test_result = {
            'test_name': '系统集成测试',
            'scenarios_tested': [],
            'integration_success': False
        }
        
        # 集成场景1: Agent系统辅助论文优化
        print("🔄 集成场景1: Agent系统辅助论文优化")
        try:
            # 使用Agent系统进行初步分析
            analysis_task = self.agent_manager.create_task(
                task_type="paper_analysis",
                input_data={
                    "paper_title": "Neural Plasticity-Inspired Deep Learning Architecture",
                    "research_domain": "deep learning",
                    "analysis_focus": "技术创新点和实验设计"
                }
            )
            
            # 获取多专家分析结果
            agent_results = self.agent_manager.auto_assign_task(analysis_task)
            
            if agent_results:
                print(f"   ✅ Agent分析完成: {len(agent_results)} 个专家响应")
                
                # 提取分析结果用于优化
                analysis_insights = []
                for agent_id, response in agent_results.items():
                    if response and response.confidence > 0.7:
                        analysis_insights.append({
                            'expert': agent_id,
                            'insight': response.content[:200],
                            'confidence': response.confidence
                        })
                
                print(f"   📊 高质量洞察: {len(analysis_insights)} 条")
                
                integration_test_result['scenarios_tested'].append({
                    'scenario': 'Agent辅助论文优化',
                    'success': True,
                    'expert_responses': len(agent_results),
                    'quality_insights': len(analysis_insights)
                })
            else:
                print("   ❌ Agent分析失败")
                integration_test_result['scenarios_tested'].append({
                    'scenario': 'Agent辅助论文优化',
                    'success': False
                })
                
        except Exception as e:
            print(f"   ❌ 集成场景1异常: {e}")
            integration_test_result['scenarios_tested'].append({
                'scenario': 'Agent辅助论文优化',
                'success': False,
                'error': str(e)
            })
        
        # 集成场景2: 优化器反馈给Agent系统
        print("\n🔄 集成场景2: 优化器反馈给Agent系统")
        try:
            # 获取优化器的统计信息
            optimizer_stats = self.paper_optimizer.get_optimization_stats()
            
            if optimizer_stats:
                print(f"   ✅ 优化器统计获取成功")
                print(f"   📊 总优化次数: {optimizer_stats.get('total_optimizations', 0)}")
                print(f"   📈 平均改进: {optimizer_stats.get('average_quality_improvement', 0):.2f}")
                
                # 将统计信息反馈给Agent系统进行分析
                if hasattr(self.agent_manager, 'update_system_knowledge'):
                    self.agent_manager.update_system_knowledge('optimization_stats', optimizer_stats)
                
                integration_test_result['scenarios_tested'].append({
                    'scenario': '优化器反馈给Agent系统',
                    'success': True,
                    'stats_available': True
                })
            else:
                print("   ❌ 优化器统计获取失败")
                integration_test_result['scenarios_tested'].append({
                    'scenario': '优化器反馈给Agent系统',
                    'success': False
                })
                
        except Exception as e:
            print(f"   ❌ 集成场景2异常: {e}")
            integration_test_result['scenarios_tested'].append({
                'scenario': '优化器反馈给Agent系统',
                'success': False,
                'error': str(e)
            })
        
        # 评估集成效果
        successful_scenarios = sum(1 for scenario in integration_test_result['scenarios_tested'] if scenario.get('success', False))
        total_scenarios = len(integration_test_result['scenarios_tested'])
        
        integration_test_result['integration_success'] = successful_scenarios >= total_scenarios * 0.5
        
        # 记录测试结果
        self.test_results['integration_tests'].append(integration_test_result)
        
        print(f"\n📊 系统集成测试完成")
        print(f"   成功场景: {successful_scenarios}/{total_scenarios}")
        print(f"   集成评估: {'✅ 成功' if integration_test_result['integration_success'] else '❌ 需要改进'}")
    
    def calculate_performance_metrics(self):
        """计算性能指标"""
        print("\n📊 计算性能指标...")
        
        # Agent系统指标
        agent_metrics = {}
        if self.test_results['agent_system_tests']:
            agent_test = self.test_results['agent_system_tests'][0]
            agent_metrics = {
                'registered_agents': agent_test.get('registered_agents', 0),
                'task_execution_success': agent_test.get('task_execution', {}).get('task_created', False),
                'collaboration_success': agent_test.get('collaboration', {}).get('completed', False),
                'average_confidence': agent_test.get('task_execution', {}).get('average_confidence', 0)
            }
        
        # 优化系统指标
        optimization_metrics = {}
        if self.test_results['optimization_system_tests']:
            opt_test = self.test_results['optimization_system_tests'][0]
            successful_components = sum(1 for comp in opt_test.get('components_tested', []) if comp.get('success', False))
            total_components = len(opt_test.get('components_tested', []))
            
            optimization_metrics = {
                'components_success_rate': successful_components / max(total_components, 1),
                'successful_components': successful_components,
                'total_components': total_components,
                'overall_success': opt_test.get('overall_success', False)
            }
        
        # 集成指标
        integration_metrics = {}
        if self.test_results['integration_tests']:
            int_test = self.test_results['integration_tests'][0]
            successful_scenarios = sum(1 for scenario in int_test.get('scenarios_tested', []) if scenario.get('success', False))
            total_scenarios = len(int_test.get('scenarios_tested', []))
            
            integration_metrics = {
                'scenarios_success_rate': successful_scenarios / max(total_scenarios, 1),
                'successful_scenarios': successful_scenarios,
                'total_scenarios': total_scenarios,
                'integration_success': int_test.get('integration_success', False)
            }
        
        self.test_results['performance_metrics'] = {
            'agent_system': agent_metrics,
            'optimization_system': optimization_metrics,
            'integration': integration_metrics
        }
        
        print(f"   ✅ 性能指标计算完成")
    
    def generate_recommendations(self):
        """生成改进建议"""
        print("\n💡 生成改进建议...")
        
        recommendations = []
        
        # 基于测试结果生成建议
        if self.test_results['performance_metrics']:
            metrics = self.test_results['performance_metrics']
            
            # Agent系统建议
            agent_metrics = metrics.get('agent_system', {})
            if agent_metrics.get('registered_agents', 0) < 3:
                recommendations.append("建议增加更多专家代理，提升分析覆盖度")
            
            if agent_metrics.get('average_confidence', 0) < 0.8:
                recommendations.append("建议优化Agent系统的响应质量，提升置信度")
            
            # 优化系统建议
            opt_metrics = metrics.get('optimization_system', {})
            if opt_metrics.get('components_success_rate', 0) < 0.8:
                recommendations.append("建议检查论文优化系统的组件配置，提升成功率")
            
            # 集成建议
            int_metrics = metrics.get('integration', {})
            if int_metrics.get('scenarios_success_rate', 0) < 0.8:
                recommendations.append("建议加强Agent系统与优化器的集成机制")
            
            # 通用建议
            recommendations.extend([
                "建议增加更多测试场景，覆盖更多使用情况",
                "建议优化错误处理机制，提升系统稳定性",
                "建议添加实时监控和日志记录功能"
            ])
        
        self.test_results['recommendations'] = recommendations
        print(f"   ✅ 生成了 {len(recommendations)} 条改进建议")
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n📋 生成测试报告...")
        
        # 完成测试
        self.test_results['end_time'] = datetime.now()
        self.test_results['duration'] = (
            self.test_results['end_time'] - self.test_results['start_time']
        ).total_seconds()
        
        # 计算总体成功率
        total_tests = 0
        successful_tests = 0
        
        for test_category in ['agent_system_tests', 'optimization_system_tests', 'integration_tests']:
            tests = self.test_results.get(test_category, [])
            total_tests += len(tests)
            for test in tests:
                if test.get('passed', False) or test.get('overall_success', False) or test.get('integration_success', False):
                    successful_tests += 1
        
        success_rate = (successful_tests / max(total_tests, 1)) * 100
        
        print("\n" + "="*60)
        print("🎯 集成系统测试报告")
        print("="*60)
        
        print(f"📊 测试统计:")
        print(f"   测试时长: {self.test_results['duration']:.1f}秒")
        print(f"   总测试数: {total_tests}")
        print(f"   成功测试: {successful_tests}")
        print(f"   成功率: {success_rate:.1f}%")
        
        # 系统状态
        if self.test_results['performance_metrics']:
            print(f"\n📈 系统性能:")
            metrics = self.test_results['performance_metrics']
            
            if 'agent_system' in metrics:
                agent_metrics = metrics['agent_system']
                print(f"   Agent系统: {agent_metrics.get('registered_agents', 0)} 个专家")
                print(f"   平均置信度: {agent_metrics.get('average_confidence', 0):.2f}")
            
            if 'optimization_system' in metrics:
                opt_metrics = metrics['optimization_system']
                print(f"   优化系统: {opt_metrics.get('successful_components', 0)}/{opt_metrics.get('total_components', 0)} 组件成功")
            
            if 'integration' in metrics:
                int_metrics = metrics['integration']
                print(f"   集成效果: {int_metrics.get('successful_scenarios', 0)}/{int_metrics.get('total_scenarios', 0)} 场景成功")
        
        # 改进建议
        if self.test_results['recommendations']:
            print(f"\n💡 改进建议:")
            for i, rec in enumerate(self.test_results['recommendations'], 1):
                print(f"   {i}. {rec}")
        
        # 总体评估
        if success_rate >= 80:
            overall_status = "✅ 优秀"
        elif success_rate >= 60:
            overall_status = "⚠️  良好"
        else:
            overall_status = "❌ 需要改进"
        
        print(f"\n🏆 总体评估: {overall_status}")
        
        # 保存报告
        self.save_test_report()
        
        return self.test_results
    
    def save_test_report(self):
        """保存测试报告"""
        output_dir = Path("output/integration_test_reports")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON报告
        json_file = output_dir / f"integration_test_{timestamp}.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📁 测试报告已保存: {json_file}")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始集成系统全面测试")
        print("="*60)
        
        # 运行各项测试
        await self.test_agent_system()
        await self.test_optimization_system()
        await self.test_system_integration()
        
        # 计算性能指标
        self.calculate_performance_metrics()
        
        # 生成改进建议
        self.generate_recommendations()
        
        # 生成测试报告
        return self.generate_test_report()

async def main():
    """主测试函数"""
    print("🚀 启动集成系统测试")
    
    # 创建测试实例
    test_runner = IntegratedSystemTest()
    
    # 运行所有测试
    test_results = await test_runner.run_all_tests()
    
    print("\n🎉 集成系统测试完成！")
    return test_results

if __name__ == "__main__":
    asyncio.run(main())
