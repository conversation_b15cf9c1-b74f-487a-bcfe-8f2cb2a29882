"""
增强可视化建议生成器 - 集成统一API客户端和多专家协作
根据实验结果生成智能的可视化建议和展示方案
集成多专家协作系统，提供更专业的可视化建议
"""

import os
import sys
import json
import time
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime
from pathlib import Path

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 核心依赖
from core.unified_api_client import UnifiedAPIClient
from reasoning.enhanced_multi_agent_collaborator import EnhancedMultiAgentCollaborator
from reasoning.data_models import (
    ExperimentPlan, ImplementationPlan, VisualizationPlan, 
    VisualizationChart, VISUALIZATION_TEMPLATES, CollaborationSession
)


class EnhancedVisualizationAdvisor:
    """增强可视化建议生成器 - 集成统一API和多专家协作"""
    
    def __init__(self, unified_client: Optional[UnifiedAPIClient] = None):
        """
        初始化增强可视化建议器
        
        Args:
            unified_client: 统一API客户端
        """
        # 初始化客户端
        if unified_client is None:
            unified_client = UnifiedAPIClient()
        
        self.unified_client = unified_client
        
        # 初始化多专家协作器
        try:
            self.collaborator = EnhancedMultiAgentCollaborator(unified_client)
        except ImportError:
            print("⚠️  多专家协作模块未找到，使用基础模式")
            self.collaborator = None
        
        # 可视化工具对比
        self.visualization_tools = {
            "matplotlib": {
                "strengths": ["科学绘图", "统计图表", "学术论文图"],
                "use_cases": ["线图", "散点图", "直方图", "箱线图"],
                "code_template": "import matplotlib.pyplot as plt"
            },
            "seaborn": {
                "strengths": ["统计可视化", "美观默认样式", "快速探索"],
                "use_cases": ["相关性热图", "分布图", "回归图"],
                "code_template": "import seaborn as sns"
            },
            "plotly": {
                "strengths": ["交互式图表", "3D可视化", "动态图表"],
                "use_cases": ["动态图表", "仪表盘", "3D图"],
                "code_template": "import plotly.graph_objects as go"
            },
            "altair": {
                "strengths": ["语法简洁", "数据驱动", "交互性"],
                "use_cases": ["探索性分析", "快速原型", "统计图表"],
                "code_template": "import altair as alt"
            }
        }
        
        # 图表类型与使用场景映射
        self.chart_scenarios = {
            "performance_comparison": {
                "recommended_charts": ["bar", "line", "radar"],
                "description": "性能对比分析"
            },
            "trend_analysis": {
                "recommended_charts": ["line", "area", "multi_line"],
                "description": "趋势分析"
            },
            "distribution_analysis": {
                "recommended_charts": ["histogram", "boxplot", "violin", "kde"],
                "description": "分布分析"
            },
            "correlation_analysis": {
                "recommended_charts": ["heatmap", "scatter", "pair_plot"],
                "description": "相关性分析"
            },
            "classification_results": {
                "recommended_charts": ["confusion_matrix", "roc_curve", "precision_recall"],
                "description": "分类结果展示"
            },
            "regression_results": {
                "recommended_charts": ["scatter", "residual_plot", "prediction_vs_actual"],
                "description": "回归结果展示"
            }
        }
        
        print("✅ 增强可视化建议生成器初始化完成")
    
    def generate_visualization_plan_collaborative(self, 
                                                experiment_results: Dict[str, Any],
                                                research_context: Optional[Dict[str, Any]] = None) -> VisualizationPlan:
        """使用多专家协作生成可视化计划"""
        if self.collaborator:
            print("🤝 启动多专家协作可视化设计...")
            
            # 创建协作会话
            session = self.collaborator.create_collaboration_session(
                research_topic="实验结果可视化设计",
                specific_questions=[
                    "如何选择最合适的图表类型来展示实验结果？",
                    "可视化设计中需要突出哪些关键信息？",
                    "如何确保可视化的清晰度和专业性？"
                ]
            )
            
            # 准备协作输入
            context_info = {
                "experiment_results": experiment_results,
                "research_context": research_context or {},
                "available_tools": list(self.visualization_tools.keys()),
                "chart_scenarios": self.chart_scenarios
            }
            
            # 协作设计可视化
            results = self.collaborator.conduct_multi_round_discussion(
                session=session,
                discussion_points=[
                    "设计专业的实验结果可视化方案",
                    f"实验结果类型：{list(experiment_results.keys()) if isinstance(experiment_results, dict) else '未知'}"
                ]
            )
            
            # 解析协作结果
            if results.success and results.final_consensus:
                try:
                    # 从共识中提取可视化计划
                    consensus_data = results.final_consensus
                    return self._parse_visualization_plan(consensus_data, experiment_results)
                except Exception as e:
                    print(f"⚠️  协作结果解析失败：{e}")
                    return self.generate_visualization_plan_basic(experiment_results, research_context)
            else:
                print("⚠️  多专家协作未达成共识，回退到基础模式")
                return self.generate_visualization_plan_basic(experiment_results, research_context)
        else:
            return self.generate_visualization_plan_basic(experiment_results, research_context)
    
    def generate_visualization_plan_basic(self, 
                                        experiment_results: Dict[str, Any],
                                        research_context: Optional[Dict[str, Any]] = None) -> VisualizationPlan:
        """基础可视化计划生成（不使用多专家协作）"""
        return self.generate_visualization_plan(experiment_results, research_context)
    
    def _parse_visualization_plan(self, 
                                consensus_data: Dict[str, Any], 
                                experiment_results: Dict[str, Any]) -> VisualizationPlan:
        """解析多专家协作结果为可视化计划"""
        try:
            # 从共识数据中提取可视化信息
            charts = []
            
            # 解析推荐的图表
            recommended_charts = consensus_data.get('recommended_charts', [])
            for chart_info in recommended_charts:
                if isinstance(chart_info, dict):
                    chart = VisualizationChart(
                        chart_type=chart_info.get('type', 'line'),
                        title=chart_info.get('title', 'Untitled Chart'),
                        data_source=chart_info.get('data_source', 'experiment_results'),
                        x_axis=chart_info.get('x_axis', 'x'),
                        y_axis=chart_info.get('y_axis', 'y'),
                        description=chart_info.get('description', ''),
                        code_template=chart_info.get('code_template', '')
                    )
                    charts.append(chart)
            
            # 如果没有图表，创建默认图表
            if not charts:
                charts.append(VisualizationChart(
                    chart_type='line',
                    title='Experiment Results Overview',
                    data_source='experiment_results',
                    x_axis='iteration',
                    y_axis='metric',
                    description='Overview of experiment results',
                    code_template=self._generate_basic_chart_code('line')
                ))
            
            return VisualizationPlan(
                experiment_name=experiment_results.get('experiment_name', 'collaborative_visualization'),
                charts=charts,
                recommended_tools=consensus_data.get('recommended_tools', ['matplotlib', 'seaborn']),
                export_formats=consensus_data.get('export_formats', ['png', 'pdf', 'svg']),
                styling_suggestions=consensus_data.get('styling_suggestions', []),
                interactive_features=consensus_data.get('interactive_features', []),
                code_examples=consensus_data.get('code_examples', {})
            )
            
        except Exception as e:
            print(f"⚠️  解析协作结果失败：{e}")
            # 返回基础可视化计划
            return self._create_default_visualization_plan(experiment_results)
    
    def _create_default_visualization_plan(self, experiment_results: Dict[str, Any]) -> VisualizationPlan:
        """创建默认可视化计划"""
        default_chart = VisualizationChart(
            chart_type='line',
            title='Default Results Visualization',
            data_source='experiment_results',
            x_axis='x',
            y_axis='y',
            description='Default visualization for experiment results',
            code_template=self._generate_basic_chart_code('line')
        )
        
        return VisualizationPlan(
            experiment_name=experiment_results.get('experiment_name', 'default_experiment'),
            charts=[default_chart],
            recommended_tools=['matplotlib'],
            export_formats=['png'],
            styling_suggestions=['使用科学论文标准样式'],
            interactive_features=[],
            code_examples={'basic_plot': self._generate_basic_chart_code('line')}
        )
    
    def _generate_basic_chart_code(self, chart_type: str) -> str:
        """生成基础图表代码模板"""
        templates = {
            'line': '''
import matplotlib.pyplot as plt
import numpy as np

# 数据准备
x = np.array([1, 2, 3, 4, 5])
y = np.array([2, 4, 1, 5, 3])

# 创建图表
plt.figure(figsize=(8, 6))
plt.plot(x, y, marker='o')
plt.title('Line Chart')
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.grid(True, alpha=0.3)
plt.show()
''',
            'bar': '''
import matplotlib.pyplot as plt
import numpy as np

# 数据准备
categories = ['A', 'B', 'C', 'D']
values = [23, 17, 35, 29]

# 创建图表
plt.figure(figsize=(8, 6))
plt.bar(categories, values)
plt.title('Bar Chart')
plt.xlabel('Categories')
plt.ylabel('Values')
plt.show()
''',
            'scatter': '''
import matplotlib.pyplot as plt
import numpy as np

# 数据准备
x = np.random.randn(50)
y = np.random.randn(50)

# 创建图表
plt.figure(figsize=(8, 6))
plt.scatter(x, y, alpha=0.6)
plt.title('Scatter Plot')
plt.xlabel('X-axis')
plt.ylabel('Y-axis')
plt.show()
'''
        }
        return templates.get(chart_type, templates['line'])
    
    def generate_visualization_plan(self, 
                                  experiment_results: Dict[str, Any],
                                  research_context: Optional[Dict[str, Any]] = None) -> VisualizationPlan:
        """基础可视化计划生成方法"""
        
        # 分析实验结果类型
        result_type = self._analyze_result_type(experiment_results)
        
        # 构建提示词
        prompt = f"""
作为专业的数据可视化专家，请为以下实验结果设计最佳的可视化方案：

实验结果类型: {result_type}
实验结果数据: {json.dumps(experiment_results, indent=2, ensure_ascii=False)[:1000]}...
研究背景: {research_context or "未提供"}

请生成一个包含以下信息的JSON格式可视化计划：
1. experiment_name: 实验名称
2. charts: 推荐的图表列表，每个图表包含：
   - chart_type: 图表类型 (line/bar/scatter/heatmap/boxplot等)
   - title: 图表标题
   - data_source: 数据来源
   - x_axis: X轴数据
   - y_axis: Y轴数据  
   - description: 图表描述
3. recommended_tools: 推荐的可视化工具 (matplotlib/seaborn/plotly等)
4. export_formats: 导出格式 (png/pdf/svg等)
5. styling_suggestions: 样式建议
6. interactive_features: 交互功能建议

请确保建议专业、实用，适合学术论文展示。

输出格式：
```json
{{
    "experiment_name": "...",
    "charts": [...],
    ...
}}
```
"""
        
        try:
            # 使用统一API客户端生成响应
            response = self.unified_client.get_text_response(
                prompt=prompt,
                model_type="chat",
                temperature=0.7
            )
            
            # 解析响应
            if response and response.content and response.content.strip():
                json_match = self._extract_json_from_response(response.content)
                if json_match:
                    plan_data = json.loads(json_match)
                    return self._create_visualization_plan_from_data(plan_data, experiment_results)
            
            # 如果解析失败，使用默认计划
            print("⚠️ 可视化计划解析失败，使用默认方案")
            return self._create_default_visualization_plan(experiment_results)
            
        except Exception as e:
            print(f"生成可视化计划时出错: {e}")
            return self._create_default_visualization_plan(experiment_results)
    
    def _analyze_result_type(self, experiment_results: Dict[str, Any]) -> str:
        """分析实验结果类型"""
        if 'accuracy' in experiment_results or 'precision' in experiment_results:
            return "classification"
        elif 'mse' in experiment_results or 'rmse' in experiment_results:
            return "regression"
        elif 'loss' in experiment_results:
            return "training_metrics"
        else:
            return "general"
    
    def _extract_json_from_response(self, response: str) -> Optional[str]:
        """从响应中提取JSON内容"""
        import re
        json_match = re.search(r'```json\n(.*?)\n```', response, re.DOTALL)
        if json_match:
            return json_match.group(1)
        
        # 尝试直接解析整个响应
        try:
            json.loads(response.strip())
            return response.strip()
        except json.JSONDecodeError:
            return None
    
    def _create_visualization_plan_from_data(self, 
                                           plan_data: Dict[str, Any], 
                                           experiment_results: Dict[str, Any]) -> VisualizationPlan:
        """从数据创建可视化计划对象"""
        # 解析图表数据
        charts = []
        for chart_data in plan_data.get('charts', []):
            chart = VisualizationChart(
                chart_type=chart_data.get('chart_type', 'line'),
                title=chart_data.get('title', 'Untitled'),
                data_source=chart_data.get('data_source', 'experiment_results'),
                x_axis=chart_data.get('x_axis', 'x'),
                y_axis=chart_data.get('y_axis', 'y'),
                description=chart_data.get('description', ''),
                code_template=chart_data.get('code_template', '')
            )
            charts.append(chart)
        
        return VisualizationPlan(
            experiment_name=plan_data.get('experiment_name', 'visualization'),
            charts=charts,
            recommended_tools=plan_data.get('recommended_tools', ['matplotlib']),
            export_formats=plan_data.get('export_formats', ['png']),
            styling_suggestions=plan_data.get('styling_suggestions', []),
            interactive_features=plan_data.get('interactive_features', []),
            code_examples=plan_data.get('code_examples', {})
        )
    
    def generate_chart_code(self, 
                          chart: VisualizationChart, 
                          data_path: str,
                          tool: str = "matplotlib") -> str:
        """生成具体图表的代码"""
        
        prompt = f"""
作为专业的数据可视化工程师，请为以下图表生成完整的{tool}代码：

图表信息：
- 类型: {chart.chart_type}
- 标题: {chart.title}
- X轴: {chart.x_axis}
- Y轴: {chart.y_axis}
- 描述: {chart.description}
- 数据源: {data_path}

请生成包含以下部分的完整代码：
1. 必要的库导入
2. 数据加载（假设数据在CSV文件中）
3. 数据预处理
4. 图表创建和样式设置
5. 保存和显示

代码要求：
- 使用{tool}库
- 遵循最佳实践
- 包含错误处理
- 适合学术论文使用
- 注释清晰

直接输出可执行的Python代码：
"""
        
        try:
            response = self.unified_client.get_text_response(
                prompt=prompt,
                model_type="chat",
                temperature=0.3
            )
            
            code = response.content if response and response.content else ""
            return code if code else self._generate_fallback_code(chart, tool)
            
        except Exception as e:
            print(f"生成图表代码时出错: {e}")
            return self._generate_fallback_code(chart, tool)
    
    def _generate_fallback_code(self, chart: VisualizationChart, tool: str) -> str:
        """生成备用图表代码"""
        if tool == "matplotlib":
            return f"""
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np

# 加载数据
try:
    data = pd.read_csv('data.csv')
except FileNotFoundError:
    print("数据文件未找到，使用示例数据")
    data = pd.DataFrame({{
        '{chart.x_axis}': np.arange(1, 11),
        '{chart.y_axis}': np.random.randn(10)
    }})

# 创建图表
plt.figure(figsize=(10, 6))
if '{chart.chart_type}' == 'line':
    plt.plot(data['{chart.x_axis}'], data['{chart.y_axis}'], marker='o')
elif '{chart.chart_type}' == 'bar':
    plt.bar(data['{chart.x_axis}'], data['{chart.y_axis}'])
else:
    plt.scatter(data['{chart.x_axis}'], data['{chart.y_axis}'])

plt.title('{chart.title}')
plt.xlabel('{chart.x_axis}')
plt.ylabel('{chart.y_axis}')
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.savefig('{chart.title.lower().replace(" ", "_")}.png', dpi=300, bbox_inches='tight')
plt.show()
"""
        else:
            return f"# {tool} code template for {chart.chart_type} chart"
    
    def create_complete_visualization_suite(self, 
                                          visualization_plan: VisualizationPlan,
                                          output_dir: str,
                                          data_path: str) -> Dict[str, str]:
        """创建完整的可视化代码套件"""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        generated_files = {}
        
        # 为每个图表生成代码文件
        for i, chart in enumerate(visualization_plan.charts):
            for tool in visualization_plan.recommended_tools:
                filename = f"chart_{i+1}_{chart.chart_type}_{tool}.py"
                file_path = output_path / filename
                
                # 生成图表代码
                code = self.generate_chart_code(chart, data_path, tool)
                
                # 保存到文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(code)
                
                generated_files[f"{chart.title}_{tool}"] = str(file_path)
        
        # 生成主运行脚本
        main_script = self._generate_main_visualization_script(visualization_plan)
        main_file_path = output_path / "run_all_visualizations.py"
        with open(main_file_path, 'w', encoding='utf-8') as f:
            f.write(main_script)
        
        generated_files["main_script"] = str(main_file_path)
        
        # 生成README
        readme_content = self._generate_visualization_readme(visualization_plan)
        readme_path = output_path / "README.md"
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        generated_files["readme"] = str(readme_path)
        
        return generated_files
    
    def _generate_main_visualization_script(self, plan: VisualizationPlan) -> str:
        """生成主可视化脚本"""
        return f'''#!/usr/bin/env python3
"""
主可视化脚本 - 运行所有图表生成
实验: {plan.experiment_name}
生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
"""

import os
import subprocess
import sys
from pathlib import Path

def run_visualization_script(script_path):
    """运行单个可视化脚本"""
    try:
        print(f"🎨 运行可视化脚本: {{script_path}}")
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 成功")
        else:
            print(f"❌ 失败: {{result.stderr}}")
    except Exception as e:
        print(f"❌ 运行脚本时出错: {{e}}")

def main():
    """主函数"""
    print("🚀 开始运行所有可视化脚本...")
    
    current_dir = Path(__file__).parent
    
    # 查找所有chart_*.py文件
    chart_scripts = list(current_dir.glob("chart_*.py"))
    
    if not chart_scripts:
        print("⚠️  未找到图表脚本文件")
        return
    
    # 运行每个脚本
    for script in sorted(chart_scripts):
        run_visualization_script(script)
    
    print("✅ 所有可视化脚本运行完成！")

if __name__ == "__main__":
    main()
'''
    
    def _generate_visualization_readme(self, plan: VisualizationPlan) -> str:
        """生成可视化README"""
        return f'''# 可视化方案 - {plan.experiment_name}

## 概述
本目录包含为实验"{plan.experiment_name}"生成的完整可视化方案。

## 推荐图表

{chr(10).join([f"### {i+1}. {chart.title}" + chr(10) + f"- **类型**: {chart.chart_type}" + chr(10) + f"- **描述**: {chart.description}" + chr(10) for i, chart in enumerate(plan.charts)])}

## 推荐工具
{chr(10).join([f"- {tool}" for tool in plan.recommended_tools])}

## 导出格式
{chr(10).join([f"- {fmt}" for fmt in plan.export_formats])}

## 样式建议
{chr(10).join([f"- {suggestion}" for suggestion in plan.styling_suggestions])}

## 使用方法

1. 确保安装了所需的Python包：
```bash
pip install matplotlib seaborn plotly pandas numpy
```

2. 运行所有可视化：
```bash
python run_all_visualizations.py
```

3. 或运行单个图表：
```bash
python chart_1_*.py
```

## 文件说明
- `chart_*.py`: 各个图表的生成脚本
- `run_all_visualizations.py`: 主运行脚本
- `README.md`: 本说明文件

生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
'''


def create_enhanced_visualization_advisor(unified_client: Optional[UnifiedAPIClient] = None) -> EnhancedVisualizationAdvisor:
    """创建增强可视化建议器实例"""
    return EnhancedVisualizationAdvisor(unified_client)


# 使用示例
if __name__ == "__main__":
    from core.unified_api_client import UnifiedAPIClient
    
    # 初始化统一客户端
    unified_client = UnifiedAPIClient()
    
    # 创建增强可视化建议器
    advisor = create_enhanced_visualization_advisor(unified_client)
    
    # 示例实验结果
    sample_results = {
        "experiment_name": "attention_classification",
        "accuracy": 0.92,
        "precision": 0.89,
        "recall": 0.91,
        "f1_score": 0.90,
        "training_loss": [0.8, 0.6, 0.4, 0.3, 0.2],
        "validation_loss": [0.9, 0.7, 0.5, 0.4, 0.3]
    }
    
    # 生成可视化计划（使用多专家协作）
    viz_plan = advisor.generate_visualization_plan_collaborative(sample_results)
    
    # 创建完整可视化套件
    files = advisor.create_complete_visualization_suite(
        visualization_plan=viz_plan,
        output_dir="./visualizations",
        data_path="./results.csv"
    )
    
    print("✅ 增强可视化方案生成完成！")
    print("📁 生成的文件:")
    for name, path in files.items():
        print(f"  - {name}: {path}")
