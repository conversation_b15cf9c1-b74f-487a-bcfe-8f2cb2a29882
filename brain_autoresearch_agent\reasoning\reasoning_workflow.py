"""
实验推理工作流协调器
协调整个推理流程，确保各阶段无缝衔接，集成增强的多专家协作系统
"""

import os
import sys
import json
import time
import uuid
from typing import Dict, List, Any, Optional, Tuple, Callable
from datetime import datetime
from pathlib import Path

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.unified_api_client import UnifiedAPIClient
from core.enhanced_literature_manager import EnhancedLiteratureManager
from agents.agent_manager import AgentManager
from reasoning.enhanced_multi_agent_collaborator import EnhancedMultiAgentCollaborator
from reasoning.data_models import (
    ResearchProblem, ExperimentPlan, ImplementationPlan, 
    VisualizationPlan, ReasoningSession
)
from reasoning.research_question_evaluator import ResearchQuestionEvaluator
from reasoning.hypothesis_experiment_designer import HypothesisExperimentDesigner
from reasoning.implementation_planner import ImplementationPlanner
from reasoning.visualization_advisor import VisualizationAdvisor


class ExperimentReasoningWorkflow:
    """实验推理工作流协调器 - 增强版"""
    
    def __init__(self, unified_client: Optional[UnifiedAPIClient] = None):
        """
        初始化增强工作流协调器
        
        Args:
            unified_client: 统一API客户端
        """
        # 初始化统一客户端
        if unified_client is None:
            unified_client = UnifiedAPIClient()
        
        self.unified_client = unified_client
        
        # 初始化核心组件
        self.literature_manager = EnhancedLiteratureManager(unified_client)
        self.agent_manager = AgentManager(unified_client)
        self.collaborator = EnhancedMultiAgentCollaborator(unified_client)
        
        # 初始化推理组件（需要更新为使用统一客户端）
        self.question_evaluator = ResearchQuestionEvaluator(unified_client)
        self.experiment_designer = HypothesisExperimentDesigner(unified_client)
        self.implementation_planner = ImplementationPlanner(unified_client)
        self.visualization_advisor = VisualizationAdvisor(unified_client)
        
        # 工作流状态
        self.current_session: Optional[ReasoningSession] = None
        self.session_history: List[ReasoningSession] = []
        
        # 增强工作流配置
        self.workflow_config = {
            "evaluation_rounds": 3,
            "collaboration_rounds": 3,
            "auto_save": True,
            "save_directory": "./reasoning_sessions",
            "enable_logging": True,
            "checkpoint_stages": True,
            "enable_multi_agent_collaboration": True,
            "quality_threshold": 0.8,
            "consensus_threshold": 0.75
        }
        
        # 确保保存目录存在
        Path(self.workflow_config["save_directory"]).mkdir(parents=True, exist_ok=True)
        
        print("✅ 增强实验推理工作流协调器初始化完成")
        print(f"   💡 多专家协作: {'启用' if self.workflow_config['enable_multi_agent_collaboration'] else '禁用'}")
    
    def run_enhanced_reasoning_flow(self, 
                                  research_question: str,
                                  hypothesis: List[str],
                                  background: Dict[str, Any] = None,
                                  workflow_context: Dict[str, Any] = None,
                                  target_venue: str = "ICML",
                                  progress_callback: Optional[Callable] = None) -> ReasoningSession:
        """
        运行增强的多专家协作推理流程
        
        Args:
            research_question: 研究问题
            hypothesis: 假设列表
            background: 背景信息
            workflow_context: 从论文提取的工作流上下文
            target_venue: 目标期刊/会议
            progress_callback: 进度回调函数
            
        Returns:
            完整的推理会话记录
        """
        
        print(f"\n🚀 启动增强多专家协作推理流程")
        print(f"📋 研究问题: {research_question}")
        print(f"🎯 目标期刊: {target_venue}")
        print(f"🤖 参与专家: {list(self.agent_manager.agents.keys())}")
        print("=" * 80)
        
        # 创建新的推理会话
        session = self._create_new_session(research_question, hypothesis, background or {})
        self.current_session = session
        
        try:
            # 阶段1：多专家协作评估研究问题
            if progress_callback:
                progress_callback("阶段1: 多专家协作评估研究问题", 0.1)
            
            evaluation_result = self._run_collaborative_evaluation(
                research_question, hypothesis, background or {}
            )
            session.research_problem = evaluation_result
            
            # 阶段2：协作设计实验方案
            if progress_callback:
                progress_callback("阶段2: 协作设计实验方案", 0.3)
            
            experiment_result = self._run_collaborative_experiment_design(
                session.research_problem, workflow_context or {}
            )
            session.experiment_plan = experiment_result
            
            # 阶段3：协作制定实现计划
            if progress_callback:
                progress_callback("阶段3: 协作制定实现计划", 0.6)
            
            implementation_result = self._run_collaborative_implementation_planning(
                session.experiment_plan
            )
            session.implementation_plan = implementation_result
            
            # 阶段4：协作设计可视化方案
            if progress_callback:
                progress_callback("阶段4: 协作设计可视化方案", 0.8)
            
            visualization_result = self._run_collaborative_visualization_planning(
                session.implementation_plan
            )
            session.visualization_plan = visualization_result
            
            # 完成推理流程
            session.status = "completed"
            session.completion_time = datetime.now()
            
            if progress_callback:
                progress_callback("推理流程完成", 1.0)
            
            # 保存会话
            if self.workflow_config["auto_save"]:
                self._save_session(session)
            
            print(f"\n✅ 增强推理流程完成")
            print(f"   📊 会话ID: {session.session_id}")
            print(f"   ⏱️ 总时长: {session.completion_time - session.start_time}")
            
            return session
            
        except Exception as e:
            session.status = "error"
            session.error_message = str(e)
            print(f"\n❌ 推理流程出错: {e}")
            raise
    
    def run_complete_reasoning_flow(self, 
                                  research_question: str,
                                  hypothesis: List[str],
                                  background: Dict[str, Any] = None,
                                  workflow_context: Dict[str, Any] = None,
                                  target_venue: str = "ICML",
                                  progress_callback: Optional[Callable] = None) -> ReasoningSession:
        """
        运行完整推理流程（兼容性方法）
        """
        return self.run_enhanced_reasoning_flow(
            research_question=research_question,
            hypothesis=hypothesis,
            background=background,
            workflow_context=workflow_context,
            target_venue=target_venue,
            progress_callback=progress_callback
        )
    
    def _run_collaborative_evaluation(self, 
                                    research_question: str,
                                    hypothesis: List[str],
                                    background: Dict[str, Any]) -> ResearchProblem:
        """运行协作评估阶段"""
        print("\n📊 阶段1: 多专家协作评估研究问题")
        print("-" * 50)
        
        # 准备讨论要点
        discussion_points = [
            f"研究问题的科学价值和创新性: {research_question}",
            f"假设的合理性和可验证性: {hypothesis}",
            "现有研究的局限性和研究空白",
            "研究的技术可行性和资源需求",
            "预期贡献和影响力评估"
        ]
        
        # 创建协作会话
        collab_session = self.collaborator.create_collaboration_session(
            research_topic=f"研究问题评估: {research_question}",
            specific_questions=discussion_points,
            required_experts=["ai_technology", "data_analysis"]
        )
        
        # 执行多轮讨论
        completed_session = self.collaborator.conduct_multi_round_discussion(
            collab_session, discussion_points
        )
        
        # 基于协作结果生成研究问题
        consensus = completed_session.final_consensus
        
        research_problem = ResearchProblem(
            question=research_question,
            hypothesis=hypothesis,
            background=background,
            novelty_score=consensus.get("novelty_score", 0.8),
            feasibility_score=consensus.get("feasibility_score", 0.7),
            impact_score=consensus.get("impact_score", 0.8),
            evaluation_summary=consensus.get("core_conclusions", "多专家达成评估共识"),
            key_challenges=consensus.get("risks_limitations", []),
            suggested_approaches=consensus.get("next_actions", [])
        )
        
        print(f"   ✅ 评估完成 - 质量得分: {completed_session.quality_score:.2f}")
        return research_problem
    
    def _run_collaborative_experiment_design(self,
                                           research_problem: ResearchProblem,
                                           workflow_context: Dict[str, Any]) -> ExperimentPlan:
        """运行协作实验设计阶段"""
        print("\n🔬 阶段2: 多专家协作设计实验方案")
        print("-" * 50)
        
        # 准备实验设计讨论要点
        discussion_points = [
            f"实验设计原则和方法论: {research_problem.question}",
            "对照组和实验组的设计",
            "关键变量和评价指标的定义",
            "数据收集和分析策略",
            "实验的可重复性和有效性保证"
        ]
        
        # 创建实验设计协作会话
        collab_session = self.collaborator.create_collaboration_session(
            research_topic=f"实验方案设计: {research_problem.question}",
            specific_questions=discussion_points,
            required_experts=["experiment_design", "data_analysis", "ai_technology"]
        )
        
        # 执行协作讨论
        completed_session = self.collaborator.conduct_multi_round_discussion(
            collab_session, discussion_points
        )
        
        # 生成实验计划
        consensus = completed_session.final_consensus
        
        experiment_plan = ExperimentPlan(
            research_question=research_problem.question,
            hypothesis=research_problem.hypothesis,
            methodology=consensus.get("methodology", "协作制定的实验方法"),
            variables={
                "independent": consensus.get("independent_variables", []),
                "dependent": consensus.get("dependent_variables", []),
                "controlled": consensus.get("controlled_variables", [])
            },
            metrics=consensus.get("key_metrics", []),
            baseline_methods=consensus.get("baseline_methods", []),
            evaluation_criteria=consensus.get("evaluation_criteria", []),
            expected_outcomes=consensus.get("expected_outcomes", []),
            collaboration_insights=completed_session.insights
        )
        
        print(f"   ✅ 实验设计完成 - 质量得分: {completed_session.quality_score:.2f}")
        return experiment_plan
    
    def _run_collaborative_implementation_planning(self,
                                                 experiment_plan: ExperimentPlan) -> ImplementationPlan:
        """运行协作实现计划阶段"""
        print("\n⚙️ 阶段3: 多专家协作制定实现计划")
        print("-" * 50)
        
        # 准备实现计划讨论要点
        discussion_points = [
            "技术架构和实现框架选择",
            "关键算法和模块的设计",
            "数据处理和模型训练策略",
            "性能优化和资源管理",
            "测试和验证方案"
        ]
        
        # 创建实现规划协作会话
        collab_session = self.collaborator.create_collaboration_session(
            research_topic=f"实现方案规划: {experiment_plan.research_question}",
            specific_questions=discussion_points,
            required_experts=["ai_technology", "data_analysis"]
        )
        
        # 执行协作讨论
        completed_session = self.collaborator.conduct_multi_round_discussion(
            collab_session, discussion_points
        )
        
        # 生成实现计划
        consensus = completed_session.final_consensus
        
        implementation_plan = ImplementationPlan(
            experiment_plan=experiment_plan,
            architecture=consensus.get("technical_architecture", {}),
            algorithms=consensus.get("key_algorithms", []),
            frameworks=consensus.get("frameworks", []),
            data_pipeline=consensus.get("data_processing", {}),
            training_strategy=consensus.get("training_strategy", {}),
            evaluation_pipeline=consensus.get("evaluation_methods", {}),
            resource_requirements=consensus.get("resource_requirements", {}),
            timeline=consensus.get("implementation_timeline", {}),
            collaboration_insights=completed_session.insights
        )
        
        print(f"   ✅ 实现计划完成 - 质量得分: {completed_session.quality_score:.2f}")
        return implementation_plan
    
    def _run_collaborative_visualization_planning(self,
                                                implementation_plan: ImplementationPlan) -> VisualizationPlan:
        """运行协作可视化规划阶段"""
        print("\n📊 阶段4: 多专家协作设计可视化方案")
        print("-" * 50)
        
        # 准备可视化讨论要点
        discussion_points = [
            "关键结果和发现的可视化策略",
            "图表类型和设计原则",
            "数据展示的清晰度和可读性",
            "学术论文的图表规范",
            "交互式可视化的可能性"
        ]
        
        # 创建可视化协作会话
        collab_session = self.collaborator.create_collaboration_session(
            research_topic=f"可视化方案设计: {implementation_plan.experiment_plan.research_question}",
            specific_questions=discussion_points,
            required_experts=["data_analysis", "paper_writing"]
        )
        
        # 执行协作讨论
        completed_session = self.collaborator.conduct_multi_round_discussion(
            collab_session, discussion_points
        )
        
        # 生成可视化计划
        consensus = completed_session.final_consensus
        
        visualization_plan = VisualizationPlan(
            implementation_plan=implementation_plan,
            chart_types=consensus.get("recommended_charts", []),
            design_principles=consensus.get("design_guidelines", []),
            data_stories=consensus.get("data_narratives", []),
            interactive_elements=consensus.get("interactive_features", []),
            academic_standards=consensus.get("academic_requirements", []),
            accessibility_features=consensus.get("accessibility", []),
            collaboration_insights=completed_session.insights
        )
        
        print(f"   ✅ 可视化设计完成 - 质量得分: {completed_session.quality_score:.2f}")
        return visualization_plan
        
    
    def _create_new_session(self, research_question: str, 
                          hypothesis: List[str], 
                          background: Dict[str, Any]) -> ReasoningSession:
        """创建新的推理会话"""
        
        session_id = f"reasoning_session_{int(time.time())}_{uuid.uuid4().hex[:8]}"
        
        research_problem = ResearchProblem(
            question=research_question,
            hypothesis=hypothesis,
            background=background
        )
        
        session = ReasoningSession(
            session_id=session_id,
            start_time=datetime.now(),
            research_problem=research_problem,
            status="in_progress"
        )
        
        print(f"✅ 创建新的推理会话: {session_id}")
        return session
    
    def _save_session(self, session: ReasoningSession) -> None:
        """保存会话到文件"""
        try:
            file_path = f"{self.workflow_config['save_directory']}/{session.session_id}.json"
            session_data = {
                "session_id": session.session_id,
                "start_time": session.start_time.isoformat(),
                "completion_time": session.completion_time.isoformat() if session.completion_time else None,
                "status": session.status,
                "research_problem": session.research_problem.__dict__ if session.research_problem else None,
                "experiment_plan": session.experiment_plan.__dict__ if session.experiment_plan else None,
                "implementation_plan": session.implementation_plan.__dict__ if session.implementation_plan else None,
                "visualization_plan": session.visualization_plan.__dict__ if session.visualization_plan else None
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 会话已保存: {file_path}")
            
        except Exception as e:
            print(f"❌ 保存会话失败: {e}")
    
    def get_session_summary(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话摘要"""
        session = next((s for s in self.session_history if s.session_id == session_id), None)
        if not session:
            return None
        
        return {
            "session_id": session.session_id,
            "start_time": session.start_time,
            "status": session.status,
            "research_question": session.research_problem.question if session.research_problem else None,
            "completion_stages": [
                "问题评估" if session.research_problem else None,
                "实验设计" if session.experiment_plan else None,
                "实现规划" if session.implementation_plan else None,
                "可视化设计" if session.visualization_plan else None
            ]
        }
    
    def list_recent_sessions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """列出最近的会话"""
        recent_sessions = sorted(
            self.session_history, 
            key=lambda x: x.start_time, 
            reverse=True
        )[:limit]
        
        return [self.get_session_summary(session.session_id) for session in recent_sessions]


# 兼容性方法 - 支持旧版本接口
def create_enhanced_reasoning_workflow(unified_client: Optional[UnifiedAPIClient] = None) -> ExperimentReasoningWorkflow:
    """创建增强推理工作流实例"""
    return ExperimentReasoningWorkflow(unified_client)


if __name__ == "__main__":
    # 演示增强推理工作流
    print("🧠 增强多专家协作推理工作流演示")
    print("=" * 60)
    
    # 创建增强工作流
    unified_client = UnifiedAPIClient()
    workflow = ExperimentReasoningWorkflow(unified_client)
    
    # 示例研究问题
    research_question = "如何设计一个基于脑启发机制的高效神经网络架构？"
    hypothesis = [
        "生物神经网络的稀疏连接可以提高计算效率",
        "模拟神经可塑性机制可以改善学习性能",
        "层级化的信息处理可以增强模型的泛化能力"
    ]
    
    background = {
        "domain": "深度学习与神经科学",
        "previous_work": "现有的神经网络架构主要基于数学优化原理",
        "motivation": "希望通过借鉴生物大脑的工作机制来改进人工神经网络"
    }
    
    print(f"\n📋 研究问题: {research_question}")
    print(f"🔬 假设数量: {len(hypothesis)}")
    print(f"🎯 应用领域: {background['domain']}")
    
    try:
        # 运行增强推理流程
        print("\n🚀 开始运行增强多专家协作推理流程...")
        print("(实际运行需要有效的API连接)")
        
        # 创建会话用于演示
        session = workflow._create_new_session(research_question, hypothesis, background)
        print(f"\n✅ 演示会话创建成功: {session.session_id}")
        
    except Exception as e:
        print(f"\n❌ 演示过程出错: {e}")
        print("💡 请确保API配置正确后再次尝试")

    def _generate_comprehensive_report(self, session: ReasoningSession) -> str:
        """生成综合报告"""
        
        report = f"""
# 实验推理工作流报告

## 基本信息
- **会话ID**: {session.session_id}
- **开始时间**: {session.start_time}
- **完成时间**: {session.completion_time or '进行中'}
- **状态**: {session.status}

## 研究问题
{session.research_problem.question if session.research_problem else '未定义'}

## 完成状态
{chr(10).join(f"- {stage}: {'✅' if status else '❌'}" for stage, status in session.completion_status.items())}
        """
        
        return report.strip()
    
    def _generate_implementation_checklist(self, session: ReasoningSession) -> List[str]:
        """生成实施清单"""
        
        checklist = []
        
        # 基础准备
        checklist.extend([
            "[ ] 环境搭建和依赖安装",
            "[ ] 项目结构创建",
            "[ ] 数据集准备和预处理"
        ])
        
        # 实验相关
        if session.experiment_plan:
            checklist.extend([
                "[ ] 实验变量定义和控制",
                "[ ] 基线方法实现",
                "[ ] 评估指标计算"
            ])
        
        # 实现相关
        if session.implementation_plan:
            for step in session.implementation_plan.steps:
                checklist.append(f"[ ] {step.title}")
        
        # 可视化相关
        if session.visualization_plan:
            checklist.extend([
                "[ ] 可视化工具配置",
                "[ ] 图表模板准备",
                "[ ] 结果展示设计"
            ])
        
        # 最终交付
        checklist.extend([
            "[ ] 实验结果分析",
            "[ ] 论文图表生成",
            "[ ] 代码文档整理",
            "[ ] 结果验证和复现"
        ])
        
        return checklist
    
    def _estimate_implementation_time(self, session: ReasoningSession) -> Dict[str, str]:
        """估算实现时间"""
        
        if not session.implementation_plan:
            return {"总计": "无法估算"}
        
        time_estimation = {}
        total_hours = 0
        
        for step in session.implementation_plan.steps:
            estimated_time = step.estimated_time or "1-2小时"
            time_estimation[step.title] = estimated_time
            
            # 简单解析时间（用于总计）
            if "小时" in estimated_time:
                try:
                    if "-" in estimated_time:
                        hours = sum(map(float, estimated_time.split("小时")[0].split("-"))) / 2
                    else:
                        hours = float(estimated_time.split("小时")[0])
                    total_hours += hours
                except:
                    total_hours += 2  # 默认值
            elif "天" in estimated_time:
                try:
                    if "-" in estimated_time:
                        days = sum(map(float, estimated_time.split("天")[0].split("-"))) / 2
                    else:
                        days = float(estimated_time.split("天")[0])
                    total_hours += days * 8  # 每天8小时
                except:
                    total_hours += 16  # 默认2天
        
        time_estimation["总计"] = f"约{total_hours:.0f}小时 ({total_hours/8:.1f}天)"
        return time_estimation
    
    def _log_stage(self, session: ReasoningSession, stage_name: str, message: str):
        """记录阶段日志"""
        
        log_entry = {
            "stage": stage_name,
            "timestamp": datetime.now().isoformat(),
            "message": message
        }
        session.reasoning_log.append(log_entry)
        
        if self.workflow_config["enable_logging"]:
            print(f"  📝 {message}")
    
    def _log_error(self, session: ReasoningSession, error_message: str):
        """记录错误日志"""
        
        error_entry = {
            "type": "error",
            "timestamp": datetime.now().isoformat(),
            "message": error_message,
            "stage": session.current_stage
        }
        session.reasoning_log.append(error_entry)
        
        print(f"  ❌ 错误: {error_message}")
    
    def _save_session_v2(self, session: ReasoningSession):
        """保存会话到文件 (v2版本避免冲突)"""
        
        if not self.workflow_config["auto_save"]:
            return
        
        try:
            filename = f"{session.session_id}.json"
            filepath = Path(self.workflow_config["save_directory"]) / filename
            session.save_to_file(str(filepath))
            print(f"  💾 会话已保存: {filepath}")
        except Exception as e:
            print(f"  ⚠️ 会话保存失败: {e}")
    
    def load_session(self, session_id: str) -> Optional[ReasoningSession]:
        """从文件加载会话"""
        
        try:
            filepath = Path(self.workflow_config["save_directory"]) / f"{session_id}.json"
            if filepath.exists():
                session = ReasoningSession.load_from_file(str(filepath))
                self.current_session = session
                return session
            else:
                print(f"  ❌ 会话文件不存在: {filepath}")
                return None
        except Exception as e:
            print(f"  ❌ 会话加载失败: {e}")
            return None
    
    def list_sessions(self) -> List[str]:
        """列出所有保存的会话"""
        
        try:
            session_dir = Path(self.workflow_config["save_directory"])
            if session_dir.exists():
                session_files = list(session_dir.glob("reasoning_session_*.json"))
                return [f.stem for f in session_files]
            return []
        except Exception as e:
            print(f"  ❌ 会话列表获取失败: {e}")
            return []
    
    def resume_session(self, session_id: str) -> Optional[ReasoningSession]:
        """恢复未完成的会话"""
        
        session = self.load_session(session_id)
        if session:
            print(f"  🔄 恢复会话: {session_id}")
            print(f"  📊 当前阶段: {session.current_stage}")
            print(f"  ✅ 完成状态: {session.completion_status}")
            return session
        return None
    
    def generate_final_deliverables(self, session: ReasoningSession, 
                                  output_dir: str = "./output") -> Dict[str, str]:
        """生成最终交付物"""
        
        print(f"\n📦 生成最终交付物")
        print("-" * 50)
        
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        deliverables = {}
        
        try:
            # 1. 综合报告
            report = self._generate_comprehensive_report(session)
            report_file = output_path / f"{session.session_id}_report.md"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            deliverables["综合报告"] = str(report_file)
            
            # 2. 实验设计报告
            if session.experiment_plan:
                exp_report = self.experiment_designer.generate_experiment_report(session.experiment_plan)
                exp_file = output_path / f"{session.session_id}_experiment_design.md"
                with open(exp_file, 'w', encoding='utf-8') as f:
                    f.write(exp_report)
                deliverables["实验设计报告"] = str(exp_file)
            
            # 3. 实现指南
            if session.implementation_plan:
                impl_guide = self.implementation_planner.generate_implementation_guide(session.implementation_plan)
                impl_file = output_path / f"{session.session_id}_implementation_guide.md"
                with open(impl_file, 'w', encoding='utf-8') as f:
                    f.write(impl_guide)
                deliverables["实现指南"] = str(impl_file)
            
            # 4. 可视化指南
            if session.visualization_plan:
                viz_guide = self.visualization_advisor.generate_visualization_guide(session.visualization_plan)
                viz_file = output_path / f"{session.session_id}_visualization_guide.md"
                with open(viz_file, 'w', encoding='utf-8') as f:
                    f.write(viz_guide)
                deliverables["可视化指南"] = str(viz_file)
            
            # 5. 代码模板包
            if session.implementation_plan and session.implementation_plan.code_templates:
                code_dir = output_path / f"{session.session_id}_code_templates"
                code_dir.mkdir(exist_ok=True)
                
                for template_name, template_code in session.implementation_plan.code_templates.items():
                    template_file = code_dir / f"{template_name}.py"
                    with open(template_file, 'w', encoding='utf-8') as f:
                        f.write(template_code)
                
                deliverables["代码模板包"] = str(code_dir)
            
            # 6. 完整会话数据
            session_file = output_path / f"{session.session_id}_full_session.json"
            session.save_to_file(str(session_file))
            deliverables["完整会话数据"] = str(session_file)
            
            print(f"  ✅ 交付物生成完成，共{len(deliverables)}项")
            for name, path in deliverables.items():
                print(f"    📄 {name}: {path}")
            
        except Exception as e:
            print(f"  ❌ 交付物生成失败: {e}")
        
        return deliverables


# 测试函数
def test_experiment_reasoning_workflow():
    """测试完整的实验推理工作流"""
    
    print("🧪 测试完整实验推理工作流")
    
    # 创建工作流协调器
    workflow = ExperimentReasoningWorkflow()
    
    # 定义测试参数
    research_question = "如何设计一种基于脑神经可塑性的自适应神经网络架构？"
    hypothesis = [
        "脑神经可塑性机制可以指导神经网络结构的动态调整",
        "自适应架构能够提高学习效率和泛化能力",
        "生物启发的连接调整规则优于传统的梯度优化"
    ]
    background = {
        "domain": "brain-inspired intelligence",
        "related_work": "神经可塑性、自适应网络、生物启发计算",
        "motivation": "现有神经网络结构固定，缺乏生物系统的自适应能力"
    }
    workflow_context = {
        "paper_analysis": "基于已有论文的工作流分析",
        "technical_constraints": "计算资源和时间限制"
    }
    
    # 定义进度回调
    def progress_callback(stage: str, description: str, progress: float):
        print(f"  🔄 {stage}: {description} ({progress*100:.0f}%)")
    
    # 运行完整工作流
    session = workflow.run_complete_reasoning_flow(
        research_question=research_question,
        hypothesis=hypothesis,
        background=background,
        workflow_context=workflow_context,
        target_venue="ICML",
        progress_callback=progress_callback
    )
    
    # 生成最终交付物
    deliverables = workflow.generate_final_deliverables(session)
    
    return session, deliverables


if __name__ == "__main__":
    test_experiment_reasoning_workflow()
