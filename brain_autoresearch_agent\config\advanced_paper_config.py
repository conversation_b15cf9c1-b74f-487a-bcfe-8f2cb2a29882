"""
高级论文生成配置文件

用于自定义论文生成的各种参数和设置
"""

# 论文生成基础配置
PAPER_GENERATION_CONFIG = {
    # LLM 配置
    "llm_settings": {
        "model": "deepseek-chat",  # 支持: gpt-4o, claude-3-5-sonnet, deepseek-chat
        "temperature": 0.7,
        "max_tokens": 4000,
        "timeout": 300  # 秒
    },
    
    # 文献搜索配置
    "literature_config": {
        "max_papers_per_query": 15,
        "include_recent_only": True,
        "years_back": 5,
        "use_hybrid_search": True,
        "semantic_scholar_priority": True,
        "arxiv_fallback": True
    },
    
    # 多专家推理配置
    "reasoning_config": {
        "enable_multi_agent": True,
        "max_reasoning_rounds": 3,
        "consensus_threshold": 0.7,
        "expert_timeout": 120,  # 秒
        "include_visualization_phase": True
    },
    
    # 论文结构配置
    "structure_config": {
        "default_venue": "ICML",
        "include_appendix": True,
        "include_acknowledgments": True,
        "max_references": 50,
        "target_word_count": {
            "abstract": 250,
            "introduction": 1500,
            "related_work": 2000,
            "methodology": 3000,
            "experiments": 2500,
            "conclusion": 800
        }
    },
    
    # 内容生成配置
    "content_config": {
        "writing_style": "academic",  # academic, technical, accessible
        "citation_style": "numbered",  # numbered, author-year
        "include_mathematical_notation": True,
        "include_code_snippets": False,
        "include_algorithm_boxes": True,
        "language": "english"  # english, chinese
    },
    
    # 输出配置
    "output_config": {
        "generate_latex": True,
        "generate_pdf": False,  # 需要LaTeX环境
        "generate_word": False,  # 需要额外库
        "save_intermediate_results": True,
        "output_directory": "output",
        "filename_prefix": "brain_paper"
    }
}

# 不同会议的特定配置
VENUE_SPECIFIC_CONFIG = {
    "ICML": {
        "page_limit": 8,
        "reference_style": "icml",
        "include_broader_impact": True,
        "include_reproducibility": True,
        "format_style": "two_column"
    },
    
    "NeurIPS": {
        "page_limit": 9,
        "reference_style": "neurips", 
        "include_broader_impact": True,
        "include_supplementary": True,
        "format_style": "two_column"
    },
    
    "ICLR": {
        "page_limit": 8,
        "reference_style": "iclr",
        "include_anonymous_submission": True,
        "include_ethics_statement": True,
        "format_style": "single_column"
    },
    
    "AAAI": {
        "page_limit": 7,
        "reference_style": "aaai",
        "include_keywords": True,
        "format_style": "two_column"
    },
    
    "IJCAI": {
        "page_limit": 7,
        "reference_style": "ijcai",
        "include_keywords": True,
        "format_style": "two_column"
    }
}

# 研究领域特定配置
RESEARCH_DOMAIN_CONFIG = {
    "brain_inspired_computing": {
        "keywords": ["neuromorphic", "spiking networks", "plasticity", "bioinspired"],
        "important_venues": ["Nature", "Science", "Neural Networks", "IEEE TNNLS"],
        "key_researchers": ["Geoffrey Hinton", "Yann LeCun", "Andrew Ng"],
        "specialized_databases": ["PubMed", "IEEE Xplore", "ACM Digital Library"]
    },
    
    "machine_learning": {
        "keywords": ["deep learning", "neural networks", "optimization", "generalization"],
        "important_venues": ["ICML", "NeurIPS", "ICLR", "JMLR"],
        "key_researchers": ["Yoshua Bengio", "Geoffrey Hinton", "Yann LeCun"],
        "specialized_databases": ["ArXiv ML", "Papers with Code", "OpenReview"]
    },
    
    "computer_vision": {
        "keywords": ["object detection", "image recognition", "visual learning", "CNNs"],
        "important_venues": ["CVPR", "ICCV", "ECCV", "IEEE TPAMI"],
        "key_researchers": ["Fei-Fei Li", "Alex Krizhevsky", "Ross Girshick"],
        "specialized_databases": ["Computer Vision Foundation", "IEEE Xplore"]
    },
    
    "natural_language_processing": {
        "keywords": ["transformers", "language models", "NLP", "text generation"],
        "important_venues": ["ACL", "EMNLP", "NAACL", "Computational Linguistics"],
        "key_researchers": ["Christopher Manning", "Dan Jurafsky", "Emily Bender"],
        "specialized_databases": ["ACL Anthology", "ArXiv NLP"]
    }
}

# 质量控制配置
QUALITY_CONTROL_CONFIG = {
    "validation_checks": {
        "check_citations": True,
        "check_grammar": True,
        "check_novelty": True,
        "check_technical_accuracy": True,
        "check_reproducibility": True
    },
    
    "metrics": {
        "min_citation_count": 20,
        "max_repetition_ratio": 0.1,
        "min_technical_depth_score": 0.7,
        "min_novelty_score": 0.6,
        "min_clarity_score": 0.8
    },
    
    "review_criteria": {
        "technical_contribution": 0.4,
        "experimental_validation": 0.3,
        "clarity_and_presentation": 0.2,
        "significance_and_impact": 0.1
    }
}

# 专家agent配置
EXPERT_AGENT_CONFIG = {
    "ai_technology_expert": {
        "specialization": ["machine learning", "deep learning", "AI algorithms"],
        "confidence_threshold": 0.7,
        "max_response_length": 2000
    },
    
    "neuroscience_expert": {
        "specialization": ["brain science", "neural networks", "cognitive science"],
        "confidence_threshold": 0.6,
        "max_response_length": 1800
    },
    
    "data_analysis_expert": {
        "specialization": ["statistics", "experimental design", "data visualization"],
        "confidence_threshold": 0.8,
        "max_response_length": 1500
    },
    
    "paper_writing_expert": {
        "specialization": ["academic writing", "structure", "presentation"],
        "confidence_threshold": 0.7,
        "max_response_length": 2500
    },
    
    "experiment_design_expert": {
        "specialization": ["methodology", "validation", "reproducibility"],
        "confidence_threshold": 0.8,
        "max_response_length": 2000
    }
}

# 高级功能配置
ADVANCED_FEATURES_CONFIG = {
    "auto_citation_generation": {
        "enabled": True,
        "style": "ieee",  # ieee, apa, chicago
        "include_doi": True,
        "include_urls": True
    },
    
    "plagiarism_detection": {
        "enabled": False,  # 需要外部API
        "similarity_threshold": 0.15,
        "check_against_databases": ["arxiv", "semantic_scholar"]
    },
    
    "translation_support": {
        "enabled": False,  # 需要翻译API
        "target_languages": ["chinese", "spanish", "french"],
        "preserve_technical_terms": True
    },
    
    "collaborative_features": {
        "version_control": True,
        "multi_user_editing": False,
        "comment_system": True,
        "change_tracking": True
    }
}

# 调试和日志配置
DEBUG_CONFIG = {
    "logging_level": "INFO",  # DEBUG, INFO, WARNING, ERROR
    "save_logs": True,
    "log_directory": "logs",
    "detailed_timing": True,
    "expert_response_logging": True,
    "api_call_logging": True
}

def get_config(config_name: str = "default"):
    """
    获取指定的配置
    
    Args:
        config_name: 配置名称 (default, minimal, comprehensive)
        
    Returns:
        配置字典
    """
    if config_name == "minimal":
        return {
            "llm_settings": {"model": "deepseek-chat", "temperature": 0.5},
            "literature_config": {"max_papers_per_query": 5},
            "reasoning_config": {"enable_multi_agent": False},
            "output_config": {"generate_latex": False}
        }
    
    elif config_name == "comprehensive":
        return {
            **PAPER_GENERATION_CONFIG,
            "literature_config": {
                **PAPER_GENERATION_CONFIG["literature_config"],
                "max_papers_per_query": 25
            },
            "reasoning_config": {
                **PAPER_GENERATION_CONFIG["reasoning_config"],
                "max_reasoning_rounds": 5
            }
        }
    
    else:  # default
        return PAPER_GENERATION_CONFIG

def get_venue_config(venue: str):
    """获取特定会议的配置"""
    return VENUE_SPECIFIC_CONFIG.get(venue.upper(), VENUE_SPECIFIC_CONFIG["ICML"])

def get_domain_config(domain: str):
    """获取特定研究领域的配置"""
    return RESEARCH_DOMAIN_CONFIG.get(domain.lower(), RESEARCH_DOMAIN_CONFIG["machine_learning"])

def validate_config(config: dict):
    """验证配置的有效性"""
    required_keys = ["llm_settings", "literature_config", "output_config"]
    
    for key in required_keys:
        if key not in config:
            raise ValueError(f"Missing required configuration key: {key}")
    
    # 验证模型名称
    valid_models = ["deepseek-chat", "gpt-4o", "claude-3-5-sonnet"]
    if config["llm_settings"]["model"] not in valid_models:
        raise ValueError(f"Invalid model: {config['llm_settings']['model']}")
    
    # 验证温度参数
    temp = config["llm_settings"]["temperature"]
    if not 0 <= temp <= 2:
        raise ValueError(f"Invalid temperature: {temp}")
    
    return True

if __name__ == "__main__":
    # 测试配置
    print("🔧 论文生成配置系统")
    print("=" * 50)
    
    # 显示默认配置
    default_config = get_config("default")
    print(f"📋 默认配置加载成功")
    print(f"   LLM模型: {default_config['llm_settings']['model']}")
    print(f"   最大论文数: {default_config['literature_config']['max_papers_per_query']}")
    print(f"   启用多专家: {default_config['reasoning_config']['enable_multi_agent']}")
    
    # 显示会议配置
    icml_config = get_venue_config("ICML")
    print(f"\n📊 ICML配置:")
    print(f"   页数限制: {icml_config['page_limit']}")
    print(f"   格式样式: {icml_config['format_style']}")
    
    # 验证配置
    try:
        validate_config(default_config)
        print(f"\n✅ 配置验证通过")
    except ValueError as e:
        print(f"\n❌ 配置验证失败: {e}")
    
    print(f"\n🎯 配置系统就绪！")
