"""
直接测试DeepSeek API连接
不通过任何中间层，只使用openai库直接调用
"""

import os
import time
import sys

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️ OpenAI包未安装，无法测试DeepSeek API。安装：pip install openai")
    sys.exit(1)

def test_direct_deepseek():
    """直接测试DeepSeek API连接"""
    # 设置API密钥
    api_key = "***********************************"
    base_url = "https://api.deepseek.com"
    model = "deepseek-chat"
    
    # 创建客户端
    client = OpenAI(
        api_key=api_key,
        base_url=base_url
    )
    
    print(f"📡 直接测试DeepSeek API连接")
    print(f"🔑 API密钥: {api_key[:8]}...{api_key[-4:]}")
    print(f"🌐 基础URL: {base_url}")
    print(f"🤖 模型: {model}")
    
    try:
        # 计时开始
        start_time = time.time()
        
        # 发送请求
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": "Reply with exactly 'Direct DeepSeek API test successful'"}],
            max_tokens=20,
            temperature=0.1
        )
        
        # 提取响应
        content = response.choices[0].message.content
        tokens_used = response.usage.total_tokens if hasattr(response, 'usage') else "未知"
        
        # 计时结束
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ API连接成功!")
        print(f"📄 响应内容: {content}")
        print(f"🔢 使用的token: {tokens_used}")
        print(f"⏱️ 响应时间: {duration:.2f} 秒")
        
        # 判断是否包含预期内容
        success = "test successful" in content.lower()
        print(f"✓ 包含预期内容: {success}")
        
        return success
    
    except Exception as e:
        print(f"\n❌ API连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 DeepSeek API 直接连接测试")
    print("=" * 60)
    
    # 设置或保持环境变量
    os.environ["DEEPSEEK_API_KEY"] = "***********************************"
    os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
    os.environ["MOCK_MODE"] = "false"
    os.environ["ENABLE_MOCK_DATA"] = "false"
    
    test_direct_deepseek() 