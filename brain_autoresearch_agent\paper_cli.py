#!/usr/bin/env python3
"""
脑启发智能AutoResearch Agent命令行界面
用于运行完整研究工作流或单独的阶段
"""

import os
import sys
import json
import argparse
import time
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("paper_cli.log", encoding='utf-8')
    ]
)
logger = logging.getLogger("PaperCLI")

# 导入工作流组件
try:
    from workflow.complete_research_workflow import CompleteResearchWorkflow
    from paper_generation.enhanced_brain_paper_writer import EnhancedBrainPaperWriter
    from paper_generation.paper_quality_optimizer import PaperQualityOptimizer
    from core.unified_api_client import get_unified_client
    from paper_generation.version_management_system import VersionManagementSystem
    from core.research_logger import ResearchLogger
except ImportError as e:
    logger.error(f"导入组件失败: {e}")
    logger.error("请确保在正确的目录下运行此脚本")
    sys.exit(1)

def print_header():
    """打印程序标题"""
    header = """
=====================================================================
 ____             _         _____            _                  _     
|  _ \           (_)       |  __ \          (_)                | |    
| |_) |_ __ __ _ _ _ __    | |__) |___  ___  _ _ __   ___  ___| |_   
|  _ <| '__/ _` | | '_ \   |  _  // _ \/ __|| | '_ \ / _ \/ __| __|  
| |_) | | | (_| | | | | |  | | \ \  __/\__ \| | | | |  __/ (__| |_   
|____/|_|  \__,_|_|_| |_|  |_|  \_\___||___/_|_| |_|\___|\___|\__|  
                                                                    
     ___                   _      _____ _            _   _           
    / _ \                 | |    / ____(_)          | | (_)          
   / /_\ \ __ _  ___ _ __ | |_  | |  __ _  ___ _ __ | |_ _ ___  ___ 
   |  _  |/ _` |/ _ \ '_ \| __| | | |_ | |/ _ \ '_ \| __| / __|/ __|
   | | | | (_| |  __/ | | | |_  | |__| | |  __/ | | | |_| \__ \ (__ 
   \_| |_/\__, |\___|_| |_|\__|  \_____|_|\___|_| |_|\__|_|___/\___|
           __/ |                                                     
          |___/                                                      
=====================================================================
脑启发智能AutoResearch Agent - 自动研究论文生成系统
版本: 1.1.0
=====================================================================
"""
    print(header)

def run_workflow(args):
    """运行完整研究工作流"""
    logger.info(f"启动完整研究工作流: {args.topic}")
    
    print(f"🚀 开始研究工作流: {args.topic}")
    print(f"📁 输出目录: {args.output_dir}")
    
    # 如果未指定配置文件，使用默认配置
    config_path = args.config
    if not config_path:
        config_path = os.path.join(os.path.dirname(__file__), "config", "default_workflow_config.json")
        if os.path.exists(config_path):
            print(f"📋 使用默认配置文件: {config_path}")
    
    # 创建工作流
    workflow = CompleteResearchWorkflow(
        output_dir=args.output_dir,
        config_path=config_path
    )
    
    try:
        # 显示进度条
        print("🔄 工作流程进行中...")
        start_time = time.time()
        
        # 运行工作流
        result = workflow.run_complete_workflow(args.topic)
        
        # 计算总耗时
        elapsed_time = time.time() - start_time
        minutes = int(elapsed_time // 60)
        seconds = int(elapsed_time % 60)
        
        print(f"✅ 工作流成功完成! 总耗时: {minutes}分{seconds}秒")
        
        # 输出研究日志路径
        research_log_path = None
        if hasattr(workflow, 'research_logger') and workflow.research_logger:
            research_log_path = workflow.research_logger.get_log_path()
            print(f"📝 研究日志: {research_log_path}")
        
        # 输出论文信息
        if 'paper_results' in result and result['paper_results']:
            print(f"📄 最终论文: {result['paper_results'].get('final_paper_path', '未生成')}")
            if 'optimization_results' in result['paper_results'] and result['paper_results']['optimization_results']:
                print(f"📊 论文质量评分: {result['paper_results']['optimization_results'].get('final_score', 0):.2f}/10")
        
        # 生成报告
        report_path = os.path.join(args.output_dir, "workflow_report.md")
        report = workflow.generate_report() if hasattr(workflow, 'generate_report') else None
        if report:
            print(f"📋 详细报告: {report_path}")
        
        # 提示研究日志的用途
        if research_log_path:
            print("\n💡 研究日志包含整个研究过程的详细信息，包括:")
            print("   - 文献调研结果和工作流提取")
            print("   - 多专家讨论的内容和假设")
            print("   - 实验设计、代码和可视化方案")
            print("   - 论文生成和评审过程")
        
        return 0
    except Exception as e:
        logger.error(f"工作流运行失败: {e}")
        print(f"❌ 工作流运行失败: {e}")
        return 1

def generate_paper(args):
    """直接生成论文"""
    logger.info(f"直接生成论文: {args.topic}")
    
    print(f"✍️ 开始生成论文: {args.topic}")
    print(f"📁 输出目录: {args.output_dir}")
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 创建研究日志
    research_logger = ResearchLogger(args.output_dir, args.topic)
    print(f"📝 研究日志已创建: {research_logger.get_log_path()}")
    
    # 创建论文生成器
    try:
        paper_writer = EnhancedBrainPaperWriter()
        
        print("🔄 论文生成中...")
        start_time = time.time()
        
        # 生成论文
        paper = paper_writer.generate_complete_paper(
            args.topic, 
            target_venue=args.venue
        )
        
        # 如果生成失败，返回错误
        if 'error' in paper:
            logger.error(f"论文生成失败: {paper['error']}")
            print(f"❌ 论文生成失败: {paper['error']}")
            return 1
        
        # 计算总耗时
        elapsed_time = time.time() - start_time
        minutes = int(elapsed_time // 60)
        seconds = int(elapsed_time % 60)
        
        # 保存论文
        paper_path = os.path.join(args.output_dir, "generated_paper.json")
        with open(paper_path, 'w', encoding='utf-8') as f:
            json.dump(paper, f, indent=2, ensure_ascii=False)
        
        # 保存LaTeX文件
        latex_path = os.path.join(args.output_dir, "generated_paper.tex")
        with open(latex_path, 'w', encoding='utf-8') as f:
            f.write(paper.get('latex_content', ''))
        
        # 记录论文生成到研究日志
        research_logger.log_paper_generation(latex_path, paper)
        
        print(f"✅ 论文生成成功! 总耗时: {minutes}分{seconds}秒")
        print(f"📄 论文标题: {paper.get('title', '未指定')}")
        print(f"📄 JSON文件: {paper_path}")
        print(f"📄 LaTeX文件: {latex_path}")
        print(f"📝 研究日志: {research_logger.get_log_path()}")
        
        return 0
    except Exception as e:
        logger.error(f"论文生成失败: {e}")
        print(f"❌ 论文生成失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

def manage_versions(args):
    """管理论文版本"""
    version_dir = os.path.join(args.output_dir, "versions")
    version_manager = VersionManagementSystem(base_dir=version_dir)
    
    if args.list:
        # 列出所有版本
        versions = version_manager.list_versions()
        
        if not versions:
            print("📂 没有可用的版本")
            return 0
        
        print("\n📋 可用的论文版本:")
        print("=" * 80)
        print(f"{'版本ID':<10} {'时间戳':<20} {'质量评分':<10} {'备注'}")
        print("-" * 80)
        
        for v in versions:
            score = f"{v['quality_score']:.2f}" if v['quality_score'] else "N/A"
            print(f"{v['version_id']:<10} {v['timestamp']:<20} {score:<10} {v['comment']}")
        
        print("=" * 80)
    elif args.export:
        # 导出指定版本
        if not args.version_id:
            print("❌ 缺少版本ID，请使用 --version-id 指定要导出的版本")
            return 1
        
        export_path = os.path.join(args.output_dir, f"exported_{args.version_id}.tex")
        success = version_manager.export_version(args.version_id, export_path)
        
        if success:
            print(f"✅ 版本 {args.version_id} 已导出到: {export_path}")
        else:
            print(f"❌ 导出版本 {args.version_id} 失败")
            return 1
    elif args.compare:
        # 比较两个版本
        if not args.version_id or not args.other_version:
            print("❌ 缺少版本ID，请使用 --version-id 和 --other-version 指定要比较的版本")
            return 1
        
        diff, similarity = version_manager.compare_versions(args.version_id, args.other_version)
        
        print(f"\n📊 版本比较: {args.version_id} vs {args.other_version}")
        print(f"相似度: {similarity:.2f}%")
        
        if args.show_diff:
            print("\n差异:")
            print("=" * 80)
            print(diff[:2000] + ("..." if len(diff) > 2000 else ""))
            print("=" * 80)
    elif args.revert:
        # 恢复到指定版本
        if not args.version_id:
            print("❌ 缺少版本ID，请使用 --version-id 指定要恢复的版本")
            return 1
        
        new_version = version_manager.revert_to_version(args.version_id)
        
        if new_version:
            print(f"✅ 已恢复到版本 {args.version_id}")
            print(f"📝 新版本ID: {new_version.version_id}")
        else:
            print(f"❌ 恢复版本 {args.version_id} 失败")
            return 1
    
    return 0

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="脑启发智能AutoResearch Agent命令行界面")
    
    # 通用参数
    parser.add_argument("--output-dir", type=str, default="output", help="输出目录")
    
    # 子命令
    subparsers = parser.add_subparsers(dest="command", help="子命令")
    
    # 完整工作流子命令
    workflow_parser = subparsers.add_parser("workflow", help="运行完整研究工作流")
    workflow_parser.add_argument("--topic", type=str, required=True, help="研究主题")
    workflow_parser.add_argument("--config", type=str, default=None, help="配置文件路径")
    
    # 仅生成论文子命令
    paper_parser = subparsers.add_parser("paper", help="仅生成论文")
    paper_parser.add_argument("--topic", type=str, required=True, help="研究主题")
    paper_parser.add_argument("--venue", type=str, default="ICML", help="目标会议/期刊")
    paper_parser.add_argument("--optimize", action="store_true", help="是否优化论文质量")
    paper_parser.add_argument("--quality-threshold", type=float, default=7.0, help="质量评分阈值")
    paper_parser.add_argument("--optimization-rounds", type=int, default=3, help="最大优化轮数")
    
    # 版本管理子命令
    version_parser = subparsers.add_parser("versions", help="管理论文版本")
    version_parser.add_argument("--list", action="store_true", help="列出所有版本")
    version_parser.add_argument("--export", action="store_true", help="导出指定版本")
    version_parser.add_argument("--compare", action="store_true", help="比较两个版本")
    version_parser.add_argument("--revert", action="store_true", help="恢复到指定版本")
    version_parser.add_argument("--version-id", type=str, help="版本ID")
    version_parser.add_argument("--other-version", type=str, help="比较时的另一个版本ID")
    version_parser.add_argument("--show-diff", action="store_true", help="是否显示详细差异")
    
    args = parser.parse_args()
    
    # 打印标题
    print_header()
    
    # 执行子命令
    if args.command == "workflow":
        return run_workflow(args)
    elif args.command == "paper":
        return generate_paper(args)
    elif args.command == "versions":
        return manage_versions(args)
    else:
        parser.print_help()
        return 1

if __name__ == "__main__":
    sys.exit(main())
