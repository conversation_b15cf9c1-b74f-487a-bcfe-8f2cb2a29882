"""
Hybrid Multi-Model Client Manager
Integrates DeepSeek (text) + <PERSON><PERSON> (vision) for enhanced paper generation
"""

import os
import json
from typing import Dict, List, Any, Optional, Union
from enum import Enum
from dataclasses import dataclass
from openai import OpenAI


class ModelType(Enum):
    """模型类型枚举"""
    TEXT_GENERATION = "text_generation"
    REASONING = "reasoning" 
    VISION = "vision"
    ACADEMIC_WRITING = "academic_writing"


@dataclass
class ModelConfig:
    """模型配置"""
    provider: str
    model_name: str
    api_key: str
    base_url: str
    temperature: float = 0.7
    max_tokens: int = 4096
    capabilities: List[ModelType] = None


class HybridModelClient:
    """混合模型客户端管理器"""
    
    def __init__(self):
        """初始化混合模型客户端"""
        self.models = {}
        self.clients = {}
        self._setup_models()
        self._initialize_clients()
    
    def _setup_models(self):
        """设置模型配置"""
        # DeepSeek配置
        deepseek_config = ModelConfig(
            provider="deepseek",
            model_name="deepseek-chat",
            api_key="***********************************",
            base_url="https://api.deepseek.com",
            temperature=0.7,
            capabilities=[ModelType.TEXT_GENERATION, ModelType.ACADEMIC_WRITING]
        )
        
        deepseek_reasoning_config = ModelConfig(
            provider="deepseek", 
            model_name="deepseek-reasoner",
            api_key="***********************************",
            base_url="https://api.deepseek.com",
            temperature=0.6,
            capabilities=[ModelType.REASONING]
        )
        
        # Qwen配置
        qwen_config = ModelConfig(
            provider="qwen",
            model_name="qwen-plus",
            api_key="sk-f8559ea97bad4d638416d20db63bc643",
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            temperature=0.7,
            capabilities=[ModelType.TEXT_GENERATION, ModelType.ACADEMIC_WRITING]
        )
        
        qwen_vision_config = ModelConfig(
            provider="qwen",
            model_name="qwen-vl-plus", 
            api_key="sk-f8559ea97bad4d638416d20db63bc643",
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            temperature=0.5,
            capabilities=[ModelType.VISION]
        )
        
        # 注册模型
        self.models = {
            "deepseek_chat": deepseek_config,
            "deepseek_reasoning": deepseek_reasoning_config, 
            "qwen_text": qwen_config,
            "qwen_vision": qwen_vision_config
        }
        
        print(f"✅ 混合模型配置完成:")
        print(f"  🧠 DeepSeek: {len([m for m in self.models.values() if m.provider == 'deepseek'])} 模型")
        print(f"  🌟 Qwen: {len([m for m in self.models.values() if m.provider == 'qwen'])} 模型")
    
    def _initialize_clients(self):
        """初始化API客户端"""
        for model_id, config in self.models.items():
            try:
                client = OpenAI(
                    api_key=config.api_key,
                    base_url=config.base_url
                )
                self.clients[model_id] = client
                print(f"✅ {model_id} 客户端初始化成功")
            except Exception as e:
                print(f"❌ {model_id} 客户端初始化失败: {e}")
    
    def get_best_model_for_task(self, task_type: ModelType) -> str:
        """为特定任务选择最佳模型"""
        task_models = {
            ModelType.TEXT_GENERATION: "deepseek_chat",  # DeepSeek for general text
            ModelType.REASONING: "deepseek_reasoning",   # DeepSeek reasoner for complex analysis
            ModelType.VISION: "qwen_vision",             # Qwen for visual tasks
            ModelType.ACADEMIC_WRITING: "qwen_text"      # Qwen for academic writing (better formatting)
        }
        
        return task_models.get(task_type, "deepseek_chat")
    
    def generate_text(self, prompt: str, task_type: ModelType = ModelType.TEXT_GENERATION,
                     system_message: str = None, **kwargs) -> str:
        """生成文本响应"""
        model_id = self.get_best_model_for_task(task_type)
        client = self.clients.get(model_id)
        config = self.models.get(model_id)
        
        if not client or not config:
            raise ValueError(f"Model {model_id} not available")
        
        # 构建消息
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        messages.append({"role": "user", "content": prompt})
        
        # 使用配置参数
        temperature = kwargs.get('temperature', config.temperature)
        max_tokens = kwargs.get('max_tokens', config.max_tokens)
        
        try:
            response = client.chat.completions.create(
                model=config.model_name,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            print(f"❌ {model_id} 生成失败: {e}")
            # 回退到备用模型
            fallback_id = "deepseek_chat" if model_id != "deepseek_chat" else "qwen_text"
            if fallback_id in self.clients:
                print(f"🔄 回退到 {fallback_id}")
                return self._generate_with_fallback(prompt, system_message, fallback_id)
            raise e
    
    async def generate_async(self, prompt: str, model_type: str = "text",
                           system_message: str = None, **kwargs) -> str:
        """异步生成文本响应"""
        try:
            # 映射模型类型
            task_type_map = {
                "text": ModelType.TEXT_GENERATION,
                "reasoning": ModelType.REASONING,
                "vision": ModelType.VISION,
                "academic": ModelType.ACADEMIC_WRITING
            }
            
            task_type = task_type_map.get(model_type, ModelType.TEXT_GENERATION)
            
            # 移除可能重复的参数
            filtered_kwargs = {k: v for k, v in kwargs.items() if k != 'task_type'}
            
            # 使用同步方法(在实际应用中可以改为真正的异步)
            response = self.generate_text(prompt, task_type, system_message, **filtered_kwargs)
            
            return response
            
        except Exception as e:
            print(f"❌ 异步生成失败: {e}")
            return f"异步生成失败: {str(e)}"
    
    def _generate_with_fallback(self, prompt: str, system_message: str, model_id: str) -> str:
        """使用回退模型生成"""
        client = self.clients[model_id]
        config = self.models[model_id]
        
        messages = []
        if system_message:
            messages.append({"role": "system", "content": system_message})
        messages.append({"role": "user", "content": prompt})
        
        response = client.chat.completions.create(
            model=config.model_name,
            messages=messages,
            temperature=config.temperature
        )
        
        return response.choices[0].message.content
    
    def analyze_visual_layout(self, image_path: str, analysis_prompt: str) -> str:
        """分析视觉布局 (使用Qwen视觉模型)"""
        model_id = "qwen_vision"
        client = self.clients.get(model_id)
        config = self.models.get(model_id)
        
        if not client or not config:
            raise ValueError("Vision model not available")
        
        try:
            # 读取图像
            import base64
            with open(image_path, "rb") as f:
                image_data = base64.b64encode(f.read()).decode()
            
            # 构建视觉消息
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": analysis_prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{image_data}"
                            }
                        }
                    ]
                }
            ]
            
            response = client.chat.completions.create(
                model=config.model_name,
                messages=messages,
                temperature=config.temperature
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            print(f"❌ 视觉分析失败: {e}")
            # 返回基础分析
            return "Visual analysis not available. Please check layout manually."
    
    def batch_generate(self, prompts: List[Dict[str, Any]]) -> List[str]:
        """批量生成"""
        results = []
        
        for prompt_config in prompts:
            prompt = prompt_config.get('prompt')
            task_type = prompt_config.get('task_type', ModelType.TEXT_GENERATION)
            system_message = prompt_config.get('system_message')
            
            try:
                result = self.generate_text(
                    prompt=prompt,
                    task_type=task_type,
                    system_message=system_message
                )
                results.append(result)
            except Exception as e:
                print(f"❌ 批量生成失败: {e}")
                results.append(f"Generation failed: {e}")
        
        return results
    
    def get_model_status(self) -> Dict[str, bool]:
        """获取模型状态"""
        status = {}
        for model_id, client in self.clients.items():
            try:
                # 简单测试
                config = self.models[model_id]
                response = client.chat.completions.create(
                    model=config.model_name,
                    messages=[{"role": "user", "content": "Hello"}],
                    max_tokens=10
                )
                status[model_id] = True
            except:
                status[model_id] = False
        
        return status
    
    def __str__(self):
        """字符串表示"""
        active_models = sum(1 for status in self.get_model_status().values() if status)
        total_models = len(self.models)
        return f"HybridModelClient({active_models}/{total_models} models active)"


# 全局实例
hybrid_client = None

def get_hybrid_client() -> HybridModelClient:
    """获取全局混合客户端实例"""
    global hybrid_client
    if hybrid_client is None:
        hybrid_client = HybridModelClient()
    return hybrid_client


if __name__ == "__main__":
    # 测试混合客户端
    print("🧪 测试混合模型客户端...")
    
    client = HybridModelClient()
    
    # 测试不同任务类型
    test_cases = [
        {
            "task": "学术写作",
            "type": ModelType.ACADEMIC_WRITING,
            "prompt": "Write a brief abstract about neural plasticity-inspired deep learning.",
            "system": "You are an expert academic writer."
        },
        {
            "task": "推理分析", 
            "type": ModelType.REASONING,
            "prompt": "Analyze the potential benefits and challenges of integrating neural plasticity mechanisms into deep learning.",
            "system": "You are a research analyst."
        },
        {
            "task": "文本生成",
            "type": ModelType.TEXT_GENERATION, 
            "prompt": "Explain the concept of synaptic plasticity in simple terms.",
            "system": "You are a helpful assistant."
        }
    ]
    
    for test in test_cases:
        print(f"\\n🔬 测试 {test['task']}...")
        try:
            result = client.generate_text(
                prompt=test['prompt'],
                task_type=test['type'],
                system_message=test['system']
            )
            print(f"✅ {test['task']}: OK ({len(result)} chars)")
            print(f"📄 预览: {result[:100]}...")
        except Exception as e:
            print(f"❌ {test['task']}: 失败 - {e}")
    
    # 显示模型状态
    print(f"\\n📊 模型状态:")
    status = client.get_model_status()
    for model_id, is_active in status.items():
        print(f"  {'✅' if is_active else '❌'} {model_id}")
    
    print(f"\\n🎉 混合客户端测试完成!")
