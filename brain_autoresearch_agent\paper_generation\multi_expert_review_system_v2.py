"""
多专家评审机制升级版 - 第一优先级完善
确保论文质量达到7.5+分数的专业评审系统
包含5个专业评审专家，提供详细的质量评估和改进建议
"""

import asyncio
import json
import re
import statistics
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum

class ReviewCategory(Enum):
    """评审类别"""
    TECHNICAL_QUALITY = "technical_quality"
    WRITING_QUALITY = "writing_quality"
    INNOVATION = "innovation"
    EXPERIMENT_DESIGN = "experiment_design"
    SIGNIFICANCE = "significance"

@dataclass
class ReviewComment:
    """评审意见"""
    category: str
    score: float  # 0-10分
    comment: str
    suggestions: List[str]
    severity: str  # critical, major, minor, suggestion

@dataclass
class ExpertReview:
    """专家评审"""
    expert_name: str
    expert_type: str
    overall_score: float
    comments: List[ReviewComment]
    summary: str
    recommendation: str  # accept, revise, reject
    confidence: float  # 0-1, 评审信心
    strengths: List[str]
    weaknesses: List[str]
    detailed_feedback: str

@dataclass
class ReviewResult:
    """评审结果"""
    paper_title: str
    reviews: List[ExpertReview]
    consensus_score: float
    final_recommendation: str
    key_issues: List[str]
    improvement_suggestions: List[str]
    quality_metrics: Dict[str, float]
    review_summary: str
    review_time: datetime

class MultiExpertReviewSystem:
    """多专家评审系统 - 5专家质量控制"""
    
    def __init__(self, hybrid_client=None):
        self.hybrid_client = hybrid_client
        self.quality_threshold = 7.5
        self.experts = self._initialize_experts()
        
    def _initialize_experts(self) -> Dict[str, Dict]:
        """初始化评审专家"""
        return {
            'technical_quality': {
                'name': '技术质量评审专家',
                'description': '专注于技术方法的科学性、创新性和可行性',
                'expertise': ['算法设计', '理论分析', '技术创新', '方法论'],
                'weight': 0.30
            },
            'experimental_design': {
                'name': '实验设计评审专家',
                'description': '专注于实验设计的合理性和结果的可信度',
                'expertise': ['实验设计', '统计分析', '数据验证', '结果解释'],
                'weight': 0.25
            },
            'innovation_assessment': {
                'name': '创新性评估专家',
                'description': '专注于研究的原创性和新颖性',
                'expertise': ['创新评估', '新颖性分析', '贡献评价', '影响预测'],
                'weight': 0.20
            },
            'writing_quality': {
                'name': '写作质量评审专家',
                'description': '专注于论文的写作质量和表达清晰度',
                'expertise': ['学术写作', '逻辑结构', '语言表达', '可读性'],
                'weight': 0.15
            },
            'domain_significance': {
                'name': '领域重要性评审专家',
                'description': '专注于研究在脑启发智能领域的重要性',
                'expertise': ['领域影响', '理论贡献', '应用价值', '发展前景'],
                'weight': 0.10
            }
        }
    
    async def conduct_review(self, paper_content: str, paper_metadata: Dict) -> ReviewResult:
        """进行多专家评审"""
        print("🔍 启动多专家评审系统")
        print(f"📊 质量目标: {self.quality_threshold}/10")
        print(f"👥 参与专家: {len(self.experts)} 位")
        
        # 并行进行多个专家评审
        review_tasks = []
        for expert_type, expert_info in self.experts.items():
            task = self._conduct_expert_review(expert_type, expert_info, paper_content, paper_metadata)
            review_tasks.append(task)
        
        # 等待所有评审完成
        expert_reviews = await asyncio.gather(*review_tasks)
        
        # 计算共识分数
        consensus_score = self._calculate_consensus_score(expert_reviews)
        
        # 生成最终推荐
        final_recommendation = self._generate_final_recommendation(consensus_score, expert_reviews)
        
        # 提取关键问题
        key_issues = self._extract_key_issues(expert_reviews)
        
        # 生成改进建议
        improvement_suggestions = self._generate_improvement_suggestions(expert_reviews)
        
        # 计算质量指标
        quality_metrics = self._calculate_quality_metrics(expert_reviews)
        
        # 生成评审摘要
        review_summary = self._generate_review_summary(expert_reviews, consensus_score)
        
        result = ReviewResult(
            paper_title=paper_metadata.get('title', '未知标题'),
            reviews=expert_reviews,
            consensus_score=consensus_score,
            final_recommendation=final_recommendation,
            key_issues=key_issues,
            improvement_suggestions=improvement_suggestions,
            quality_metrics=quality_metrics,
            review_summary=review_summary,
            review_time=datetime.now()
        )
        
        print(f"✅ 多专家评审完成")
        print(f"   📊 共识分数: {consensus_score:.2f}/10")
        print(f"   🎯 质量目标: {'达成' if consensus_score >= self.quality_threshold else '未达成'}")
        print(f"   📋 最终推荐: {final_recommendation}")
        
        return result
    
    async def _conduct_expert_review(self, expert_type: str, expert_info: Dict, 
                                   paper_content: str, paper_metadata: Dict) -> ExpertReview:
        """进行单个专家评审"""
        print(f"  🔍 {expert_info['name']} 评审中...")
        
        # 根据专家类型生成专门的评审提示
        review_prompt = self._create_expert_prompt(expert_type, expert_info, paper_content, paper_metadata)
        
        try:
            # 获取专家评审结果
            if self.hybrid_client:
                response = await self.hybrid_client.generate_async(
                    prompt=review_prompt,
                    task_type='academic_review',
                    max_tokens=2000
                )
            else:
                # 模拟评审结果
                response = self._simulate_expert_review(expert_type, expert_info)
            
            # 解析评审结果
            review_result = self._parse_expert_review(response, expert_type, expert_info)
            
            print(f"    ✅ {expert_info['name']} 评审完成 (分数: {review_result.overall_score:.1f}/10)")
            
            return review_result
            
        except Exception as e:
            print(f"    ❌ {expert_info['name']} 评审失败: {e}")
            return self._create_fallback_review(expert_type, expert_info)
    
    def _create_expert_prompt(self, expert_type: str, expert_info: Dict, 
                            paper_content: str, paper_metadata: Dict) -> str:
        """创建专家评审提示"""
        base_prompt = f"""You are a {expert_info['name']} reviewing a paper for a top-tier AI conference.

Paper Title: {paper_metadata.get('title', 'Unknown')}
Paper Abstract: {paper_metadata.get('abstract', 'Unknown')}

Your expertise: {', '.join(expert_info['expertise'])}
Focus: {expert_info['description']}

Paper Content (first 2000 chars):
{paper_content[:2000]}...

Please provide a detailed review focusing on your area of expertise.
"""
        
        if expert_type == 'technical_quality':
            prompt = base_prompt + """
Evaluate the technical aspects:
1. Technical soundness and methodology (0-10)
2. Algorithmic innovation and design (0-10)
3. Theoretical analysis depth (0-10)
4. Technical feasibility (0-10)
5. Method novelty (0-10)

For each aspect, provide:
- Score (0-10)
- Detailed comments
- Specific suggestions for improvement
- Severity level (critical/major/minor/suggestion)

Also provide:
- List of technical strengths
- List of technical weaknesses
- Overall technical quality score (0-10)
- Recommendation (accept/revise/reject)
- Confidence level (0-1)
"""
        
        elif expert_type == 'experimental_design':
            prompt = base_prompt + """
Evaluate the experimental aspects:
1. Experimental design quality (0-10)
2. Dataset adequacy and diversity (0-10)
3. Baseline comparisons fairness (0-10)
4. Statistical analysis validity (0-10)
5. Result reproducibility (0-10)

For each aspect, provide:
- Score (0-10)
- Detailed comments
- Specific suggestions for improvement
- Severity level (critical/major/minor/suggestion)

Also provide:
- List of experimental strengths
- List of experimental weaknesses
- Overall experimental quality score (0-10)
- Recommendation (accept/revise/reject)
- Confidence level (0-1)
"""
        
        elif expert_type == 'innovation_assessment':
            prompt = base_prompt + """
Evaluate the innovation aspects:
1. Originality and novelty (0-10)
2. Contribution significance (0-10)
3. Difference from existing work (0-10)
4. Impact potential (0-10)
5. Creative insights (0-10)

For each aspect, provide:
- Score (0-10)
- Detailed comments
- Specific suggestions for improvement
- Severity level (critical/major/minor/suggestion)

Also provide:
- List of innovation strengths
- List of innovation weaknesses
- Overall innovation score (0-10)
- Recommendation (accept/revise/reject)
- Confidence level (0-1)
"""
        
        elif expert_type == 'writing_quality':
            prompt = base_prompt + """
Evaluate the writing quality:
1. Structure and organization (0-10)
2. Clarity and readability (0-10)
3. Logical flow (0-10)
4. Language precision (0-10)
5. Academic standards (0-10)

For each aspect, provide:
- Score (0-10)
- Detailed comments
- Specific suggestions for improvement
- Severity level (critical/major/minor/suggestion)

Also provide:
- List of writing strengths
- List of writing weaknesses
- Overall writing quality score (0-10)
- Recommendation (accept/revise/reject)
- Confidence level (0-1)
"""
        
        else:  # domain_significance
            prompt = base_prompt + """
Evaluate the domain significance:
1. Relevance to brain-inspired AI (0-10)
2. Theoretical contribution depth (0-10)
3. Practical application value (0-10)
4. Field advancement potential (0-10)
5. Research impact scope (0-10)

For each aspect, provide:
- Score (0-10)
- Detailed comments
- Specific suggestions for improvement
- Severity level (critical/major/minor/suggestion)

Also provide:
- List of domain strengths
- List of domain weaknesses
- Overall domain significance score (0-10)
- Recommendation (accept/revise/reject)
- Confidence level (0-1)
"""
        
        return prompt
    
    def _simulate_expert_review(self, expert_type: str, expert_info: Dict) -> str:
        """模拟专家评审结果"""
        if expert_type == 'technical_quality':
            return """
Technical Quality Review:

1. Technical soundness: 7.2/10 - The methodology is generally sound with some theoretical gaps
2. Algorithmic innovation: 7.5/10 - Novel approach with interesting technical insights
3. Theoretical analysis: 6.8/10 - Adequate but could be more rigorous
4. Technical feasibility: 7.8/10 - Implementation appears feasible
5. Method novelty: 7.4/10 - Good novelty compared to existing methods

Strengths:
- Novel technical approach to brain-inspired AI
- Good algorithmic design
- Feasible implementation strategy

Weaknesses:
- Theoretical analysis needs strengthening
- Some technical details are unclear
- Complexity analysis is missing

Overall Score: 7.3/10
Recommendation: revise
Confidence: 0.85
"""
        
        elif expert_type == 'experimental_design':
            return """
Experimental Design Review:

1. Experimental design: 6.8/10 - Adequate but could be more comprehensive
2. Dataset adequacy: 7.0/10 - Reasonable datasets used
3. Baseline comparisons: 6.5/10 - Limited baseline comparisons
4. Statistical analysis: 7.2/10 - Proper statistical methods used
5. Reproducibility: 6.0/10 - Some implementation details missing

Strengths:
- Multiple datasets used for validation
- Proper statistical significance testing
- Clear experimental protocol

Weaknesses:
- Limited baseline methods compared
- Missing ablation studies
- Reproducibility concerns

Overall Score: 6.7/10
Recommendation: revise
Confidence: 0.80
"""
        
        elif expert_type == 'innovation_assessment':
            return """
Innovation Assessment Review:

1. Originality: 7.8/10 - Novel ideas with good originality
2. Contribution significance: 7.5/10 - Meaningful contributions to the field
3. Difference from existing work: 7.6/10 - Clear differences from prior work
4. Impact potential: 7.2/10 - Good potential for impact
5. Creative insights: 7.4/10 - Interesting and creative approach

Strengths:
- Original approach to brain-inspired intelligence
- Clear contributions to the field
- Creative problem-solving approach

Weaknesses:
- Could better articulate the novelty
- Impact claims need more support
- Related work comparison could be deeper

Overall Score: 7.5/10
Recommendation: accept with minor revisions
Confidence: 0.87
"""
        
        elif expert_type == 'writing_quality':
            return """
Writing Quality Review:

1. Structure and organization: 7.5/10 - Well-organized with clear sections
2. Clarity and readability: 7.2/10 - Generally clear but some complex passages
3. Logical flow: 7.3/10 - Good logical progression
4. Language precision: 7.0/10 - Adequate language with some imprecision
5. Academic standards: 7.4/10 - Meets academic writing standards

Strengths:
- Clear paper structure
- Good use of figures and tables
- Appropriate academic tone

Weaknesses:
- Some sections are dense and hard to follow
- Technical details need clearer explanation
- Minor grammatical issues

Overall Score: 7.3/10
Recommendation: accept with minor revisions
Confidence: 0.82
"""
        
        else:  # domain_significance
            return """
Domain Significance Review:

1. Relevance to brain-inspired AI: 8.0/10 - Highly relevant to the field
2. Theoretical contribution: 7.4/10 - Good theoretical contributions
3. Practical application: 7.6/10 - Strong practical applications
4. Field advancement: 7.3/10 - Advances the field meaningfully
5. Research impact: 7.5/10 - Good potential for research impact

Strengths:
- Highly relevant to brain-inspired intelligence
- Strong theoretical foundations
- Clear practical applications

Weaknesses:
- Could better connect to neuroscience literature
- Application scenarios could be expanded
- Long-term impact unclear

Overall Score: 7.6/10
Recommendation: accept
Confidence: 0.88
"""
    
    def _parse_expert_review(self, response: str, expert_type: str, expert_info: Dict) -> ExpertReview:
        """解析专家评审结果"""
        # 提取分数
        scores = re.findall(r'(\d+\.?\d*)/10', response)
        
        # 提取主要部分
        strengths = self._extract_list_section(response, ['Strengths', 'strengths', '优点'])
        weaknesses = self._extract_list_section(response, ['Weaknesses', 'weaknesses', '缺点'])
        
        # 计算总分
        if len(scores) >= 5:
            criteria_scores = [float(score) for score in scores[:5]]
            overall_score = sum(criteria_scores) / len(criteria_scores)
        else:
            overall_score = 7.0  # 默认分数
        
        # 提取推荐
        recommendation = 'revise'
        if 'accept' in response.lower():
            recommendation = 'accept'
        elif 'reject' in response.lower():
            recommendation = 'reject'
        
        # 提取置信度
        confidence_match = re.search(r'Confidence[:\s]+(\d+\.?\d*)', response)
        confidence = float(confidence_match.group(1)) if confidence_match else 0.8
        
        # 创建评审意见
        comments = []
        for i, score in enumerate(scores[:5]):
            comments.append(ReviewComment(
                category=f"criterion_{i+1}",
                score=float(score),
                comment=f"评分: {score}/10",
                suggestions=["需要改进"],
                severity="minor"
            ))
        
        return ExpertReview(
            expert_name=expert_info['name'],
            expert_type=expert_type,
            overall_score=overall_score,
            comments=comments,
            summary=response[:200] + "..." if len(response) > 200 else response,
            recommendation=recommendation,
            confidence=confidence,
            strengths=strengths,
            weaknesses=weaknesses,
            detailed_feedback=response
        )
    
    def _extract_list_section(self, text: str, keywords: List[str]) -> List[str]:
        """提取列表部分"""
        for keyword in keywords:
            if keyword in text:
                # 找到关键词位置
                start = text.find(keyword)
                if start != -1:
                    # 提取该部分内容
                    section = text[start:start+300]
                    lines = section.split('\n')
                    items = []
                    for line in lines[1:]:  # 跳过标题行
                        line = line.strip()
                        if line and line.startswith(('-', '•', '*')):
                            items.append(line[1:].strip())
                        elif line and len(line) > 10:
                            items.append(line)
                    return items[:3]  # 最多3个项目
        return ['需要进一步分析']
    
    def _create_fallback_review(self, expert_type: str, expert_info: Dict) -> ExpertReview:
        """创建备用评审结果"""
        return ExpertReview(
            expert_name=expert_info['name'],
            expert_type=expert_type,
            overall_score=7.0,
            comments=[],
            summary="评审过程中遇到技术问题，使用默认评分",
            recommendation="revise",
            confidence=0.5,
            strengths=["系统功能正常"],
            weaknesses=["评审过程中遇到问题"],
            detailed_feedback="技术问题导致无法完成详细评审"
        )
    
    def _calculate_consensus_score(self, reviews: List[ExpertReview]) -> float:
        """计算共识分数"""
        if not reviews:
            return 0.0
        
        weighted_sum = 0.0
        total_weight = 0.0
        
        for review in reviews:
            expert_type = review.expert_type
            weight = self.experts[expert_type]['weight']
            weighted_sum += review.overall_score * weight
            total_weight += weight
        
        return weighted_sum / total_weight if total_weight > 0 else 0.0
    
    def _generate_final_recommendation(self, consensus_score: float, reviews: List[ExpertReview]) -> str:
        """生成最终推荐"""
        if consensus_score >= 8.5:
            return "强烈推荐接收 - 论文质量优秀"
        elif consensus_score >= 7.5:
            return "推荐接收 - 论文质量达标"
        elif consensus_score >= 6.5:
            return "有条件接收 - 需要小幅修改"
        elif consensus_score >= 5.5:
            return "重大修改后重审 - 存在重要问题"
        else:
            return "不推荐接收 - 论文质量不足"
    
    def _extract_key_issues(self, reviews: List[ExpertReview]) -> List[str]:
        """提取关键问题"""
        issues = []
        for review in reviews:
            if review.overall_score < 7.0:
                issues.extend(review.weaknesses[:2])  # 取前2个问题
        
        # 去重并限制数量
        unique_issues = list(set(issues))
        return unique_issues[:8]
    
    def _generate_improvement_suggestions(self, reviews: List[ExpertReview]) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        # 根据分数低的方面生成建议
        for review in reviews:
            if review.overall_score < 7.5:
                if review.expert_type == 'technical_quality':
                    suggestions.append("加强技术方法的理论分析和创新性")
                elif review.expert_type == 'experimental_design':
                    suggestions.append("完善实验设计，增加基线对比和消融实验")
                elif review.expert_type == 'innovation_assessment':
                    suggestions.append("强化创新点表述，明确与现有工作的区别")
                elif review.expert_type == 'writing_quality':
                    suggestions.append("改进写作质量，提高表达清晰度")
                elif review.expert_type == 'domain_significance':
                    suggestions.append("加强领域贡献的阐述和应用价值分析")
        
        return suggestions[:6]
    
    def _calculate_quality_metrics(self, reviews: List[ExpertReview]) -> Dict[str, float]:
        """计算质量指标"""
        scores = [review.overall_score for review in reviews]
        
        return {
            'mean_score': statistics.mean(scores),
            'median_score': statistics.median(scores),
            'std_deviation': statistics.stdev(scores) if len(scores) > 1 else 0.0,
            'min_score': min(scores),
            'max_score': max(scores),
            'consensus_level': 1.0 - (statistics.stdev(scores) / 10.0) if len(scores) > 1 else 1.0,
            'quality_threshold_met': statistics.mean(scores) >= self.quality_threshold
        }
    
    def _generate_review_summary(self, reviews: List[ExpertReview], consensus_score: float) -> str:
        """生成评审摘要"""
        summary = f"""
多专家评审摘要：

📊 总体评分: {consensus_score:.2f}/10
👥 参与专家: {len(reviews)} 位
🎯 质量目标: {self.quality_threshold}/10 {'✅ 达成' if consensus_score >= self.quality_threshold else '❌ 未达成'}

各专家评分详情:
"""
        
        for review in reviews:
            summary += f"• {review.expert_name}: {review.overall_score:.2f}/10 ({review.recommendation})\n"
        
        # 添加主要优点
        all_strengths = []
        for review in reviews:
            all_strengths.extend(review.strengths)
        
        if all_strengths:
            summary += f"\n🔍 主要优点:\n"
            for strength in all_strengths[:3]:
                summary += f"• {strength}\n"
        
        # 添加主要问题
        all_weaknesses = []
        for review in reviews:
            all_weaknesses.extend(review.weaknesses)
        
        if all_weaknesses:
            summary += f"\n⚠️ 主要问题:\n"
            for weakness in all_weaknesses[:3]:
                summary += f"• {weakness}\n"
        
        return summary.strip()
    
    def generate_detailed_report(self, review_result: ReviewResult) -> str:
        """生成详细评审报告"""
        report = f"""
# 多专家评审详细报告

## 基本信息
- 论文标题: {review_result.paper_title}
- 评审时间: {review_result.review_time.strftime('%Y-%m-%d %H:%M:%S')}
- 参与专家: {len(review_result.reviews)} 位

## 评审结果
- **共识分数**: {review_result.consensus_score:.2f}/10
- **最终推荐**: {review_result.final_recommendation}
- **质量目标**: {self.quality_threshold}/10 {'✅ 达成' if review_result.consensus_score >= self.quality_threshold else '❌ 未达成'}

## 专家评分详情

| 专家 | 评分 | 推荐 | 置信度 |
|------|------|------|--------|
"""
        
        for review in review_result.reviews:
            report += f"| {review.expert_name} | {review.overall_score:.2f}/10 | {review.recommendation} | {review.confidence:.2f} |\n"
        
        report += f"""
## 质量指标
- 平均分: {review_result.quality_metrics['mean_score']:.2f}
- 中位数: {review_result.quality_metrics['median_score']:.2f}
- 标准差: {review_result.quality_metrics['std_deviation']:.2f}
- 最低分: {review_result.quality_metrics['min_score']:.2f}
- 最高分: {review_result.quality_metrics['max_score']:.2f}
- 共识水平: {review_result.quality_metrics['consensus_level']:.2f}

## 关键问题
"""
        for issue in review_result.key_issues:
            report += f"- {issue}\n"
        
        report += f"""
## 改进建议
"""
        for suggestion in review_result.improvement_suggestions:
            report += f"- {suggestion}\n"
        
        report += f"""
## 详细专家意见

"""
        for review in review_result.reviews:
            report += f"### {review.expert_name}\n"
            report += f"**评分**: {review.overall_score:.2f}/10\n"
            report += f"**推荐**: {review.recommendation}\n"
            report += f"**置信度**: {review.confidence:.2f}\n\n"
            
            if review.strengths:
                report += f"**优点**:\n"
                for strength in review.strengths:
                    report += f"- {strength}\n"
                report += "\n"
            
            if review.weaknesses:
                report += f"**问题**:\n"
                for weakness in review.weaknesses:
                    report += f"- {weakness}\n"
                report += "\n"
        
        report += f"""
## 评审摘要
{review_result.review_summary}
"""
        
        return report
    
    def save_review_result(self, review_result: ReviewResult, filename: str) -> None:
        """保存评审结果"""
        try:
            # 转换为可序列化格式
            data = {
                'paper_title': review_result.paper_title,
                'consensus_score': review_result.consensus_score,
                'final_recommendation': review_result.final_recommendation,
                'key_issues': review_result.key_issues,
                'improvement_suggestions': review_result.improvement_suggestions,
                'quality_metrics': review_result.quality_metrics,
                'review_summary': review_result.review_summary,
                'review_time': review_result.review_time.isoformat(),
                'reviews': []
            }
            
            for review in review_result.reviews:
                data['reviews'].append({
                    'expert_name': review.expert_name,
                    'expert_type': review.expert_type,
                    'overall_score': review.overall_score,
                    'recommendation': review.recommendation,
                    'confidence': review.confidence,
                    'strengths': review.strengths,
                    'weaknesses': review.weaknesses,
                    'detailed_feedback': review.detailed_feedback
                })
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 评审结果已保存到 {filename}")
            
        except Exception as e:
            print(f"❌ 保存评审结果失败: {e}")
    
    def load_review_result(self, filename: str) -> Optional[ReviewResult]:
        """加载评审结果"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            reviews = []
            for review_data in data['reviews']:
                review = ExpertReview(
                    expert_name=review_data['expert_name'],
                    expert_type=review_data['expert_type'],
                    overall_score=review_data['overall_score'],
                    comments=[],  # 简化版本
                    summary=review_data.get('summary', ''),
                    recommendation=review_data['recommendation'],
                    confidence=review_data['confidence'],
                    strengths=review_data['strengths'],
                    weaknesses=review_data['weaknesses'],
                    detailed_feedback=review_data['detailed_feedback']
                )
                reviews.append(review)
            
            result = ReviewResult(
                paper_title=data['paper_title'],
                reviews=reviews,
                consensus_score=data['consensus_score'],
                final_recommendation=data['final_recommendation'],
                key_issues=data['key_issues'],
                improvement_suggestions=data['improvement_suggestions'],
                quality_metrics=data['quality_metrics'],
                review_summary=data['review_summary'],
                review_time=datetime.fromisoformat(data['review_time'])
            )
            
            print(f"✅ 从 {filename} 加载评审结果成功")
            return result
            
        except Exception as e:
            print(f"❌ 加载评审结果失败: {e}")
            return None
