\documentclass{article} % For LaTeX2e
\usepackage{iclr2025,times}

% Optional math commands from https://github.com/goodfeli/dlbook_notation.
\input{math_commands.tex}

\usepackage{hyperref}
\usepackage{url}
\usepackage{graphicx}
\usepackage{subfigure}
\usepackage{booktabs}

% For theorems and such
\usepackage{amsmath}
\usepackage{amssymb}
\usepackage{mathtools}
\usepackage{amsthm}

% Custom
\usepackage{multirow}
\usepackage{color}
\usepackage{colortbl}
\usepackage[capitalize,noabbrev]{cleveref}
\usepackage{xspace}

%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
% THEOREMS
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
\theoremstyle{plain}
\newtheorem{theorem}{Theorem}[section]
\newtheorem{proposition}[theorem]{Proposition}
\newtheorem{lemma}[theorem]{Lemma}
\newtheorem{corollary}[theorem]{Corollary}
\theoremstyle{definition}
\newtheorem{definition}[theorem]{Definition}
\newtheorem{assumption}[theorem]{Assumption}
\theoremstyle{remark}
\newtheorem{remark}[theorem]{Remark}

\graphicspath{{../figures/}} % To reference your generated figures, name the PNGs directly. DO NOT CHANGE THIS.

\begin{filecontents}{references.bib}
@book{goodfellow2016deep,
  title={Deep learning},
  author={Goodfellow, Ian and Bengio, Yoshua and Courville, Aaron and Bengio, Yoshua},
  volume={1},
  year={2016},
  publisher={MIT Press}
}
\end{filecontents}

\title{
%%%%%%%%%TITLE%%%%%%%%%
TITLE HERE
%%%%%%%%%TITLE%%%%%%%%%
}

% Authors must not appear in the submitted version. They should be hidden
% as long as the \iclrfinalcopy macro remains commented out below.
% Non-anonymous submissions will be rejected without review.

\author{Anonymous}

% The \author macro works with any number of authors. There are two commands
% used to separate the names and addresses of multiple authors: \And and \AND.
%
% Using \And between authors leaves it to \LaTeX{} to determine where to break
% the lines. Using \AND forces a linebreak at that point. So, if \LaTeX{}
% puts 3 of 4 authors names on the first line, and the last on the second
% line, try using \AND instead of \And before the third author name.

\newcommand{\fix}{\marginpar{FIX}}
\newcommand{\new}{\marginpar{NEW}}

%\iclrfinalcopy % Uncomment for camera-ready version, but NOT for submission.
\begin{document}


\maketitle

\begin{abstract}
%%%%%%%%%ABSTRACT%%%%%%%%%
ABSTRACT HERE
%%%%%%%%%ABSTRACT%%%%%%%%%
\end{abstract}

\section{Introduction}
\label{sec:intro}
%%%%%%%%%INTRODUCTION%%%%%%%%%
INTRO HERE
%%%%%%%%%INTRODUCTION%%%%%%%%%

\section{Related Work}
\label{sec:related}
%%%%%%%%%RELATED WORK%%%%%%%%%
RELATED WORK HERE
%%%%%%%%%RELATED WORK%%%%%%%%%

\section{Background}
\label{sec:background}
%%%%%%%%%BACKGROUND%%%%%%%%%
BACKGROUND HERE
%%%%%%%%%BACKGROUND%%%%%%%%%

\section{Method}
\label{sec:method}
%%%%%%%%%METHOD%%%%%%%%%
METHOD HERE
%%%%%%%%%METHOD%%%%%%%%%

\section{Experimental Setup}
\label{sec:experimental_setup}
%%%%%%%%%EXPERIMENTAL SETUP%%%%%%%%%
EXPERIMENTAL SETUP HERE
%%%%%%%%%EXPERIMENTAL SETUP%%%%%%%%%

\section{Experiments}
\label{sec:experiments}
%%%%%%%%%EXPERIMENTS%%%%%%%%%
RESULTS HERE

% EXAMPLE FIGURE: REPLACE AND ADD YOUR OWN FIGURES / CAPTIONS
\begin{figure}[h!]
\centering
\includegraphics[width=0.5\textwidth]{example-image-a}
\caption{PLEASE FILL IN CAPTION HERE}
\label{fig:first_figure}
\end{figure}
%%%%%%%%%EXPERIMENTS%%%%%%%%%

\section{Conclusion}
\label{sec:conclusion}
%%%%%%%%%CONCLUSION%%%%%%%%%
CONCLUSIONS HERE
%%%%%%%%%CONCLUSION%%%%%%%%%

\bibliography{iclr2025}
\bibliographystyle{iclr2025}

\appendix

\section*{\LARGE Supplementary Material}
\label{sec:appendix}

%%%%%%%%%APPENDIX%%%%%%%%%
\section{Appendix Section}
APPENDIX TEXT
%%%%%%%%%APPENDIX%%%%%%%%%

\end{document}
