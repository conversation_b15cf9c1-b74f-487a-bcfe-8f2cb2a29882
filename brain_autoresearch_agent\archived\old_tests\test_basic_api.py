"""
简化API连通性测试
仅验证基本API连接，不依赖复杂组件
"""

import sys
import os

def test_basic_api_connection():
    """测试基本API连接"""
    print("🔧 基础API连通性测试")
    print("=" * 50)
    
    # 测试环境变量
    print("🔍 检查API密钥环境变量...")
    apis_available = {}
    
    if os.getenv('DEEPSEEK_API_KEY'):
        apis_available['DeepSeek'] = '✅'
        print("   🔑 DEEPSEEK_API_KEY: 已设置")
    else:
        apis_available['DeepSeek'] = '❌'
        print("   ❌ DEEPSEEK_API_KEY: 未设置")
    
    if os.getenv('OPENAI_API_KEY'):
        apis_available['OpenAI'] = '✅'
        print("   🔑 OPENAI_API_KEY: 已设置")
    else:
        apis_available['OpenAI'] = '❌'
        print("   ❌ OPENAI_API_KEY: 未设置")
        
    if os.getenv('ANTHROPIC_API_KEY'):
        apis_available['Anthropic'] = '✅'
        print("   🔑 ANTHROPIC_API_KEY: 已设置")
    else:
        apis_available['Anthropic'] = '❌'
        print("   ❌ ANTHROPIC_API_KEY: 未设置")
    
    # 测试包可用性
    print("\n📦 检查依赖包...")
    packages_available = {}
    
    try:
        import openai
        packages_available['openai'] = '✅'
        print("   📦 openai: 可用")
    except ImportError:
        packages_available['openai'] = '❌' 
        print("   ❌ openai: 不可用")
    
    try:
        import anthropic
        packages_available['anthropic'] = '✅'
        print("   📦 anthropic: 可用")
    except ImportError:
        packages_available['anthropic'] = '❌'
        print("   ❌ anthropic: 不可用")
    
    # 测试AI Scientist v2可用性
    print("\n🔬 检查AI Scientist v2...")
    try:
        ai_scientist_path = os.path.join(os.path.dirname(__file__), '..', 'AI-Scientist-v2')
        sys.path.append(ai_scientist_path)
        
        from ai_scientist.llm import get_response_from_llm, AVAILABLE_LLMS
        print(f"   ✅ AI Scientist v2: 可用")
        print(f"   📋 支持模型: {AVAILABLE_LLMS}")
    except ImportError as e:
        print(f"   ❌ AI Scientist v2: 不可用 - {e}")
        AVAILABLE_LLMS = []
    
    # 生成测试报告
    print("\n📊 连通性测试总结")
    print("-" * 30)
    
    available_count = sum(1 for status in apis_available.values() if status == '✅')
    total_apis = len(apis_available)
    
    print(f"API密钥: {available_count}/{total_apis} 可用")
    for api, status in apis_available.items():
        print(f"   {api}: {status}")
    
    package_count = sum(1 for status in packages_available.values() if status == '✅')
    total_packages = len(packages_available)
    
    print(f"\n依赖包: {package_count}/{total_packages} 可用")
    for package, status in packages_available.items():
        print(f"   {package}: {status}")
    
    # 推荐配置
    print("\n💡 建议配置:")
    if available_count == 0:
        print("   ⚠️  未检测到API密钥，建议设置至少一个:")
        print("   - set DEEPSEEK_API_KEY=your_key")  
        print("   - set OPENAI_API_KEY=your_key")
        print("   - set ANTHROPIC_API_KEY=your_key")
    
    if package_count < total_packages:
        print("   ⚠️  安装缺失的依赖包:")
        for package, status in packages_available.items():
            if status == '❌':
                print(f"   - pip install {package}")
    
    if available_count > 0:
        print("   ✅ 系统基本可用，可以进行真实API测试")
        return True
    else:
        print("   ❌ 系统不可用，请先配置API密钥")
        return False

def simple_api_test():
    """简单API测试"""
    if not test_basic_api_connection():
        return
    
    print(f"\n🚀 尝试简单API调用...")
    
    # 尝试使用最简单的方式测试API
    try:
        # 如果有DEEPSEEK_API_KEY，优先测试DeepSeek
        if os.getenv('DEEPSEEK_API_KEY'):
            try:
                from openai import OpenAI
                client = OpenAI(
                    api_key=os.getenv('DEEPSEEK_API_KEY'),
                    base_url="https://api.deepseek.com"
                )
                
                response = client.chat.completions.create(
                    model="deepseek-chat",
                    messages=[{"role": "user", "content": "请用一句话介绍人工智能"}],
                    max_tokens=100,
                    temperature=0.7
                )
                
                result = response.choices[0].message.content
                print(f"   ✅ DeepSeek API测试成功")
                print(f"   📝 响应: {result[:100]}...")
                return True
                
            except Exception as e:
                print(f"   ❌ DeepSeek API测试失败: {e}")
        
        # 测试OpenAI API
        if os.getenv('OPENAI_API_KEY'):
            try:
                from openai import OpenAI
                client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
                
                response = client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": "请用一句话介绍人工智能"}],
                    max_tokens=100,
                    temperature=0.7
                )
                
                result = response.choices[0].message.content  
                print(f"   ✅ OpenAI API测试成功")
                print(f"   📝 响应: {result[:100]}...")
                return True
                
            except Exception as e:
                print(f"   ❌ OpenAI API测试失败: {e}")
        
        print("   ⚠️  所有API测试失败或无可用API密钥")
        return False
        
    except Exception as e:
        print(f"   ❌ API测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Brain AutoResearch Agent - 基础连通性测试")
    print("=" * 60)
    
    success = simple_api_test()
    
    print(f"\n📊 测试结果: {'成功' if success else '失败'}")
    if success:
        print("✅ 系统基础功能可用，可以继续进行端到端测试")
    else:
        print("❌ 请先解决API连接问题再进行完整测试")
