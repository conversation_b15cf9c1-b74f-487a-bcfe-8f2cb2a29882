"""
测试论文生成和优化系统
验证完整的论文生成、评审和优化流程
"""

import sys
import os
import unittest
import asyncio
import json
from datetime import datetime
from unittest.mock import MagicMock, patch

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from paper_generation.brain_paper_writer import BrainPaperWriter
from paper_generation.paper_quality_optimizer import PaperQualityOptimizer
from paper_generation.improved_latex_generator import ImprovedLaTeXGenerator
from paper_generation.multi_expert_review_system import MultiExpertReviewSystem
from paper_generation.enhanced_brain_paper_writer import (
    EnhancedBrainPaperWriter,
    PaperGenerationConfig
)

from core.unified_api_client import get_unified_client

class TestPaperGeneration(unittest.TestCase):
    """测试论文生成和优化系统"""
    
    @classmethod
    def setUpClass(cls):
        """初始化测试环境"""
        # 设置DeepSeek API环境变量
        if "DEEPSEEK_API_KEY" not in os.environ:
            os.environ["DEEPSEEK_API_KEY"] = "***********************************"
            os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
            os.environ["MOCK_MODE"] = "false"
            os.environ["ENABLE_MOCK_DATA"] = "false"
            print("🔑 已全局设置DeepSeek API环境变量")
        
        cls.api_client = get_unified_client()
        cls.output_dir = "output/test_paper_generation"
        os.makedirs(cls.output_dir, exist_ok=True)
        
        # 设置测试参数
        cls.research_topic = "Brain-Inspired Meta-Learning for Adaptive Neural Networks"
        cls.target_venue = "ICML"
        
        print(f"\n🚀 开始论文生成和优化测试")
        print(f"📋 研究主题: {cls.research_topic}")
        print(f"🏆 目标会议: {cls.target_venue}")
    
    def test_01_paper_writer_initialization(self):
        """测试论文写作器初始化"""
        print(f"\n📝 测试论文写作器初始化...")
        
        try:
            # 设置环境变量确保使用真实API
            if "DEEPSEEK_API_KEY" not in os.environ:
                os.environ["DEEPSEEK_API_KEY"] = "***********************************"
                os.environ["DEEPSEEK_BASE_URL"] = "https://api.deepseek.com"
                os.environ["MOCK_MODE"] = "false"
                os.environ["ENABLE_MOCK_DATA"] = "false"
                print("✅ 已设置DeepSeek API环境变量")
            
            paper_writer = BrainPaperWriter(
                model="deepseek-chat",
                temperature=0.7
            )
            
            # 确认使用真实DeepSeek API
            self.assertIsNotNone(paper_writer)
            self.assertIsNotNone(paper_writer.llm_client)
            self.assertTrue(paper_writer.llm_client.deepseek_mode, "未使用DeepSeek模式，而是使用了模拟模式")
            self.assertIsNotNone(paper_writer.agent_manager)
            print(f"✅ 论文写作器初始化成功")
            print(f"🔌 LLM客户端模式: {paper_writer.llm_client.deepseek_mode}")
        except Exception as e:
            self.fail(f"论文写作器初始化失败: {e}")
    
    def test_02_paper_generation(self):
        """测试论文生成"""
        print(f"\n📄 测试论文生成...")
        
        try:
            paper_writer = BrainPaperWriter(model="deepseek-chat", temperature=0.7)
            
            # 生成论文
            paper = paper_writer.generate_paper(
                research_topic=self.research_topic,
                target_venue=self.target_venue,
                paper_type="research"
            )
            
            # 验证论文结构
            self.assertIsNotNone(paper)
            self.assertIn('title', paper)
            self.assertIn('abstract', paper)
            self.assertIn('introduction', paper)
            self.assertIn('methodology', paper)
            self.assertIn('conclusion', paper)
            
            # 保存论文
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.output_dir, f"generated_paper_{timestamp}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(paper, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 论文生成成功")
            print(f"📄 标题: {paper.get('title', 'Untitled')}")
            print(f"💾 保存至: {output_file}")
            
            # 保存为测试用例的属性，供后续测试使用
            self.__class__.generated_paper = paper
            
        except Exception as e:
            self.fail(f"论文生成失败: {e}")
    
    def test_03_latex_generation(self):
        """测试LaTeX生成"""
        print(f"\n📑 测试LaTeX生成...")
        
        if not hasattr(self.__class__, 'generated_paper'):
            self.skipTest("没有生成的论文可用")
        
        try:
            latex_generator = ImprovedLaTeXGenerator()
            
            # 生成LaTeX
            latex_content = latex_generator.generate_latex_paper(
                self.__class__.generated_paper,
                venue=self.target_venue
            )
            
            # 验证LaTeX内容
            self.assertIsNotNone(latex_content)
            self.assertGreater(len(latex_content), 1000)
            self.assertIn('\\documentclass', latex_content)
            self.assertIn('\\begin{document}', latex_content)
            self.assertIn('\\end{document}', latex_content)
            
            # 保存LaTeX
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.output_dir, f"generated_latex_{timestamp}.tex")
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(latex_content)
            
            print(f"✅ LaTeX生成成功")
            print(f"📄 LaTeX长度: {len(latex_content)} 字符")
            print(f"💾 保存至: {output_file}")
            
            # 保存为测试用例的属性，供后续测试使用
            self.__class__.latex_content = latex_content
            
        except Exception as e:
            self.fail(f"LaTeX生成失败: {e}")
    
    def test_04_expert_review(self):
        """测试专家评审"""
        print(f"\n🔍 测试专家评审...")
        
        if not hasattr(self.__class__, 'latex_content'):
            self.skipTest("没有LaTeX内容可用")
        
        try:
            review_system = MultiExpertReviewSystem(self.api_client)
            
            # 进行评审
            paper_metadata = {
                'title': self.__class__.generated_paper.get('title', 'Untitled'),
                'target_venue': self.target_venue
            }
            
            review_result = review_system.conduct_review_sync(
                self.__class__.latex_content,
                paper_metadata
            )
            
            # 验证评审结果
            self.assertIsNotNone(review_result)
            self.assertIn('consensus_score', review_result)
            self.assertIn('expert_reviews', review_result)
            # 注释掉不存在的key_issues检查
            # self.assertIn('key_issues', review_result)
            
            # 保存评审结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = os.path.join(self.output_dir, f"review_result_{timestamp}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(review_result, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 专家评审成功")
            print(f"   📊 共识分数: {review_result.get('consensus_score', 0):.2f}/10")
            print(f"   🎯 质量目标: {'达成' if review_result.get('target_reached') else '未达成'}")
            print(f"   📋 最终推荐: {review_result.get('final_recommendation', 'N/A')}")

            self.assertIsNotNone(review_result)
            self.assertIn('expert_reviews', review_result)
            self.assertGreater(review_result.get('consensus_score', 0), 0)
            self.assertIsNotNone(review_result.get('final_recommendation'))
            
            # 保存为测试用例的属性，供后续测试使用
            self.__class__.review_result = review_result
            
        except Exception as e:
            self.fail(f"专家评审失败: {e}")
    
    def test_05_paper_optimization(self):
        """测试论文优化"""
        print(f"\n🔧 测试论文优化...")
        
        if not hasattr(self.__class__, 'latex_content'):
            self.skipTest("没有LaTeX内容可用")
            
        try:
            optimizer = PaperQualityOptimizer(self.api_client)
            
            # 设置论文元数据
            paper_metadata = {
                'title': self.__class__.generated_paper.get('title', 'Untitled'),
                'target_venue': self.target_venue,
                'abstract': self.__class__.generated_paper.get('abstract', '')
            }
            
            # 进行论文优化（同步方式调用异步方法）
            loop = asyncio.get_event_loop()
            optimization_result = loop.run_until_complete(
                optimizer.optimize_paper(
                    self.__class__.latex_content,
                    paper_metadata,
                    output_dir=os.path.join(self.output_dir, "optimized"),
                    target_venue=self.target_venue
                )
            )
            
            # 验证优化结果
            self.assertIsNotNone(optimization_result)
            self.assertIn('success', optimization_result)
            self.assertIn('final_quality', optimization_result)
            self.assertIn('optimized_content', optimization_result)
            
            # 输出优化结果
            print(f"✅ 论文优化完成")
            print(f"📊 优化前质量: {self.__class__.review_result.get('consensus_score', 0):.2f}/10")
            print(f"📊 优化后质量: {optimization_result.get('final_quality', 0):.2f}/10")
            print(f"📈 质量提升: {optimization_result.get('total_improvement', 0):+.2f}")
            print(f"💾 输出文件:")
            for file_type, file_path in optimization_result.get('output_files', {}).items():
                print(f"   - {file_type}: {file_path}")
            
        except Exception as e:
            self.fail(f"论文优化失败: {e}")
    
    def test_06_complete_pipeline(self):
        """测试完整流程"""
        print(f"\n🔄 测试完整的论文生成和优化流程...")
        
        try:
            # 1. 初始化组件
            paper_writer = BrainPaperWriter(model="deepseek-chat", temperature=0.7)
            latex_generator = ImprovedLaTeXGenerator()
            optimizer = PaperQualityOptimizer(self.api_client)
            
            # 2. 生成论文
            paper = paper_writer.generate_paper(
                research_topic="Neural-Plasticity-Inspired Deep Learning Architecture",
                target_venue="ICML",
                paper_type="research"
            )
            
            # 3. 生成LaTeX
            latex_content = latex_generator.generate_latex_paper(
                paper,
                venue="ICML"
            )
            
            # 4. 优化论文（同步方式调用异步方法）
            paper_metadata = {
                'title': paper.get('title', 'Untitled'),
                'target_venue': "ICML",
                'abstract': paper.get('abstract', '')
            }
            
            loop = asyncio.get_event_loop()
            optimization_result = loop.run_until_complete(
                optimizer.optimize_paper(
                    latex_content,
                    paper_metadata,
                    output_dir=os.path.join(self.output_dir, "complete_pipeline"),
                    target_venue="ICML"
                )
            )
            
            # 验证完整流程结果
            self.assertTrue(optimization_result['success'])
            self.assertGreater(optimization_result['final_quality'], 7.0)
            
            print(f"✅ 完整流程测试成功")
            print(f"📊 最终质量评分: {optimization_result['final_quality']:.2f}/10")
            print(f"📈 质量提升: {optimization_result['total_improvement']:+.2f}")
            
            # 保存完整流程结果摘要
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_file = os.path.join(self.output_dir, f"complete_pipeline_summary_{timestamp}.json")
            with open(summary_file, 'w', encoding='utf-8') as f:
                summary = {
                    'topic': "Neural-Plasticity-Inspired Deep Learning Architecture",
                    'venue': "ICML",
                    'title': paper.get('title', 'Untitled'),
                    'final_quality': optimization_result['final_quality'],
                    'quality_improvement': optimization_result['total_improvement'],
                    'optimization_rounds': len(optimization_result.get('optimization_log', {}).get('optimization_rounds', [])),
                    'success': optimization_result['success'],
                    'timestamp': datetime.now().isoformat()
                }
                json.dump(summary, f, ensure_ascii=False, indent=2)
            
            print(f"💾 流程摘要保存至: {summary_file}")
            
        except Exception as e:
            self.fail(f"完整流程测试失败: {e}")
    
    @patch('paper_generation.enhanced_brain_paper_writer.LLMClient')
    @patch('paper_generation.enhanced_brain_paper_writer.AgentManager')
    @patch('paper_generation.enhanced_brain_paper_writer.PaperWorkflowExtractor')
    def test_paper_initialization(self, mock_workflow, mock_agent, mock_llm):
        config = PaperGenerationConfig(target_venue="ICML", language="english")
        writer = EnhancedBrainPaperWriter(config=config)
        self.assertIsNotNone(writer)
        self.assertEqual(writer.config.target_venue, "ICML")
        self.assertTrue(hasattr(writer, 'review_system'))
        self.assertTrue(hasattr(writer, 'revision_engine'))
    
    @patch('paper_generation.enhanced_brain_paper_writer.LLMClient')
    @patch('paper_generation.enhanced_brain_paper_writer.AgentManager')
    @patch('paper_generation.enhanced_brain_paper_writer.PaperWorkflowExtractor')
    def test_initial_paper_generation(self, mock_workflow, mock_agent, mock_llm):
        mock_llm_instance = mock_llm.return_value
        mock_llm_instance.get_response.return_value = "模拟的LLM响应"
        mock_agent_instance = mock_agent.return_value
        mock_agent_instance.get_agent.return_value = MagicMock()
        config = PaperGenerationConfig(target_venue="ICML", language="english")
        writer = EnhancedBrainPaperWriter(config=config)
        research_topic = "测试研究主题"
        paper = writer._generate_initial_paper(research_topic, {})
        self.assertIn('title', paper)
        self.assertIn('sections', paper)
    
    @patch('paper_generation.enhanced_brain_paper_writer.LLMClient')
    @patch('paper_generation.enhanced_brain_paper_writer.AgentManager')
    @patch('paper_generation.enhanced_brain_paper_writer.PaperWorkflowExtractor')
    def test_complete_paper_generation(self, mock_workflow, mock_agent, mock_llm):
        mock_llm_instance = mock_llm.return_value
        mock_llm_instance.get_response.return_value = "模拟的LLM响应"
        mock_agent_instance = mock_agent.return_value
        mock_expert = MagicMock()
        mock_expert.analyze.return_value = MagicMock(content="模拟专家分析")
        mock_agent_instance.get_agent.return_value = mock_expert
        config = PaperGenerationConfig(target_venue="ICML", language="english")
        writer = EnhancedBrainPaperWriter(config=config)
        writer.review_system.conduct_multi_expert_review = MagicMock(return_value=MagicMock(
            overall_score=7.5,
            expert_reviews=[],
            consensus_level=0.8,
            final_recommendation="接受",
            revision_plans=[]
        ))
        research_topic = "测试研究主题"
        result = writer.generate_paper_with_quality_control(research_topic)
        self.assertTrue(result.success)
        self.assertGreaterEqual(result.quality_metrics.overall_score, 6.0)
        self.assertIn('title', result.paper_content)

if __name__ == '__main__':
    unittest.main()
