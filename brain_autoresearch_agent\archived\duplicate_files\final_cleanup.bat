@echo off
echo 第三轮文件精简 - 归档历史输出和会话数据...

REM 移动整个输出目录到archived/
echo 移动整个output目录到archived/...
move output archived\ 2>NUL

REM 移动reasoning_sessions到archived/
echo 移动推理会话数据...
move reasoning_sessions archived\ 2>NUL

REM 移动test_experiments到archived/
echo 移动测试实验数据...
move test_experiments archived\ 2>NUL

REM 移动report目录（如果存在且不是核心的话）
echo 检查并移动report目录...
if exist report (
    move report archived\ 2>NUL
)

REM 归档更多临时文件
echo 归档其他临时文件...
move continue_file_cleanup.bat archived\duplicate_files\ 2>NUL

REM 归档重复的requirements文件
echo 检查requirements文件...
if exist requirements_enhanced.txt (
    move requirements_enhanced.txt archived\duplicate_files\ 2>NUL
)

REM 清理__pycache__目录
echo 清理缓存文件...
rmdir /s /q __pycache__ 2>NUL

echo.
echo 第三轮文件精简完成！
echo 检查当前根目录结构...
dir /b
pause
