"""
本地论文数据库系统

用于存储、索引和检索论文信息和提取的工作流，避免重复检索和处理错误。
"""

import os
import json
import time
import sqlite3
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime


class PaperDatabase:
    """本地论文数据库系统"""
    
    def __init__(self, db_path: str = None):
        """
        初始化论文数据库
        
        Args:
            db_path: 数据库文件路径，默认为当前目录下的paper_database.db
        """
        # 设置数据库路径
        if db_path is None:
            # 创建数据目录
            db_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "data", "paper_db")
            os.makedirs(db_dir, exist_ok=True)
            db_path = os.path.join(db_dir, "paper_database.db")
        
        self.db_path = db_path
        self.cache_dir = os.path.join(os.path.dirname(self.db_path), "paper_cache")
        os.makedirs(self.cache_dir, exist_ok=True)
        
        print(f"📚 初始化论文数据库: {self.db_path}")
        self._init_database()
    
    def _init_database(self):
        """初始化数据库结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建论文表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS papers (
            id TEXT PRIMARY KEY,
            title TEXT,
            abstract TEXT,
            authors TEXT,
            venue TEXT,
            year INTEGER,
            source TEXT,
            url TEXT,
            citation_count INTEGER,
            cache_path TEXT,
            timestamp TIMESTAMP
        )
        ''')
        
        # 创建工作流表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS workflows (
            id TEXT PRIMARY KEY,
            paper_id TEXT,
            datasets TEXT,
            methods TEXT,
            metrics TEXT,
            architectures TEXT,
            platforms TEXT,
            brain_inspiration TEXT,
            cache_path TEXT,
            timestamp TIMESTAMP,
            FOREIGN KEY (paper_id) REFERENCES papers (id)
        )
        ''')
        
        # 创建查询记录表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS queries (
            id TEXT PRIMARY KEY,
            query TEXT,
            paper_ids TEXT,
            timestamp TIMESTAMP
        )
        ''')
        
        conn.commit()
        conn.close()
        
        print("✅ 数据库结构初始化完成")
    
    def _generate_id(self, paper: Dict[str, Any]) -> str:
        """根据论文信息生成唯一ID"""
        # 使用标题、作者、年份生成ID
        title = paper.get('title', '')
        
        # 获取作者
        authors = paper.get('authors', [])
        author_str = ""
        if isinstance(authors, list):
            if authors and isinstance(authors[0], dict) and 'name' in authors[0]:
                author_str = ",".join([a.get('name', '') for a in authors[:3]])
            elif authors and hasattr(authors[0], 'name'):
                author_str = ",".join([a.name for a in authors[:3] if hasattr(a, 'name')])
            else:
                author_str = str(authors)
        
        year = paper.get('year', '')
        
        # 生成哈希
        id_str = f"{title}|{author_str}|{year}"
        return hashlib.md5(id_str.encode()).hexdigest()
    
    def _paper_to_dict(self, paper: Any) -> Dict[str, Any]:
        """将论文对象转换为字典"""
        if isinstance(paper, dict):
            return paper
        
        # 如果是对象，提取属性
        result = {}
        
        # 提取标准属性
        attrs = ['title', 'abstract', 'venue', 'year', 'url', 'source', 'citation_count', 'id']
        for attr in attrs:
            if hasattr(paper, attr):
                result[attr] = getattr(paper, attr)
        
        # 特殊处理authors
        if hasattr(paper, 'authors'):
            authors = getattr(paper, 'authors')
            if isinstance(authors, list):
                if authors and hasattr(authors[0], 'name'):
                    result['authors'] = [{'name': a.name} for a in authors]
                else:
                    result['authors'] = authors
            else:
                result['authors'] = authors
        
        return result
    
    def save_papers(self, papers: List[Any], query: str = None) -> List[str]:
        """
        保存论文信息到数据库
        
        Args:
            papers: 论文信息列表
            query: 查询关键词
        
        Returns:
            论文ID列表
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        paper_ids = []
        
        for paper in papers:
            # 转换为字典格式
            paper_dict = self._paper_to_dict(paper)
            
            # 生成ID
            paper_id = self._generate_id(paper_dict)
            paper_ids.append(paper_id)
            
            # 缓存完整数据
            cache_path = os.path.join(self.cache_dir, f"paper_{paper_id}.json")
            with open(cache_path, 'w', encoding='utf-8') as f:
                json.dump(paper_dict, f, ensure_ascii=False, indent=2)
            
            # 准备数据
            title = paper_dict.get('title', '')
            abstract = paper_dict.get('abstract', '')
            
            # 处理作者
            authors = paper_dict.get('authors', [])
            if isinstance(authors, list):
                if authors and isinstance(authors[0], dict) and 'name' in authors[0]:
                    authors_str = json.dumps([a.get('name', '') for a in authors])
                else:
                    authors_str = json.dumps(authors)
            else:
                authors_str = str(authors)
            
            venue = paper_dict.get('venue', '')
            year = paper_dict.get('year', 0)
            source = paper_dict.get('source', '')
            url = paper_dict.get('url', '')
            citation_count = paper_dict.get('citation_count', 0)
            timestamp = datetime.now().isoformat()
            
            # 插入数据库
            cursor.execute('''
            INSERT OR REPLACE INTO papers 
            (id, title, abstract, authors, venue, year, source, url, citation_count, cache_path, timestamp)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (paper_id, title, abstract, authors_str, venue, year, source, url, citation_count, cache_path, timestamp))
        
        # 如果提供了查询关键词，保存查询记录
        if query and paper_ids:
            query_id = hashlib.md5(query.encode()).hexdigest()
            timestamp = datetime.now().isoformat()
            paper_ids_str = json.dumps(paper_ids)
            
            cursor.execute('''
            INSERT OR REPLACE INTO queries
            (id, query, paper_ids, timestamp)
            VALUES (?, ?, ?, ?)
            ''', (query_id, query, paper_ids_str, timestamp))
        
        conn.commit()
        conn.close()
        
        print(f"✅ 已保存 {len(paper_ids)} 篇论文到数据库")
        return paper_ids
    
    def save_workflow(self, paper_id: str, workflow: Dict[str, Any]) -> str:
        """
        保存工作流信息到数据库
        
        Args:
            paper_id: 论文ID
            workflow: 工作流信息
            
        Returns:
            工作流ID
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 生成工作流ID
        workflow_id = f"{paper_id}_workflow"
        
        # 缓存完整数据
        cache_path = os.path.join(self.cache_dir, f"workflow_{workflow_id}.json")
        with open(cache_path, 'w', encoding='utf-8') as f:
            json.dump(workflow, f, ensure_ascii=False, indent=2)
        
        # 提取关键字段
        datasets = json.dumps(workflow.get('datasets', []))
        methods = json.dumps(workflow.get('methods', []))
        metrics = json.dumps(workflow.get('metrics', []))
        architectures = json.dumps(workflow.get('architectures', []) or workflow.get('network_architectures', []))
        platforms = json.dumps(workflow.get('platforms', []) or workflow.get('platforms_tools', []))
        brain_inspiration = json.dumps(workflow.get('brain_inspiration', []))
        timestamp = datetime.now().isoformat()
        
        # 插入数据库
        cursor.execute('''
        INSERT OR REPLACE INTO workflows
        (id, paper_id, datasets, methods, metrics, architectures, platforms, brain_inspiration, cache_path, timestamp)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (workflow_id, paper_id, datasets, methods, metrics, architectures, platforms, brain_inspiration, cache_path, timestamp))
        
        conn.commit()
        conn.close()
        
        print(f"✅ 已保存论文 {paper_id} 的工作流")
        return workflow_id
    
    def get_papers_by_query(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        根据查询关键词获取论文
        
        Args:
            query: 查询关键词
            limit: 返回结果数量限制
            
        Returns:
            论文信息列表
        """
        # 生成查询ID
        query_id = hashlib.md5(query.encode()).hexdigest()
        
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        cursor = conn.cursor()
        
        # 检查是否有缓存的查询结果
        cursor.execute('SELECT paper_ids FROM queries WHERE id = ?', (query_id,))
        result = cursor.fetchone()
        
        if result:
            # 有缓存结果
            paper_ids = json.loads(result[0])
            papers = []
            
            for paper_id in paper_ids[:limit]:
                # 优先从缓存文件加载完整数据
                cache_path = os.path.join(self.cache_dir, f"paper_{paper_id}.json")
                if os.path.exists(cache_path):
                    with open(cache_path, 'r', encoding='utf-8') as f:
                        papers.append(json.load(f))
                else:
                    # 从数据库加载
                    cursor.execute('SELECT * FROM papers WHERE id = ?', (paper_id,))
                    paper = cursor.fetchone()
                    if paper:
                        paper_dict = dict(paper)
                        # 处理作者
                        if 'authors' in paper_dict:
                            try:
                                paper_dict['authors'] = json.loads(paper_dict['authors'])
                            except:
                                paper_dict['authors'] = []
                        papers.append(paper_dict)
            
            print(f"✅ 从缓存中找到查询 '{query}' 的 {len(papers)} 篇论文")
            conn.close()
            return papers
        
        # 如果没有缓存结果，进行模糊搜索
        cursor.execute('''
        SELECT * FROM papers 
        WHERE title LIKE ? OR abstract LIKE ?
        ORDER BY year DESC, citation_count DESC
        LIMIT ?
        ''', (f'%{query}%', f'%{query}%', limit))
        
        papers = []
        for row in cursor.fetchall():
            paper_dict = dict(row)
            # 处理作者
            if 'authors' in paper_dict:
                try:
                    paper_dict['authors'] = json.loads(paper_dict['authors'])
                except:
                    paper_dict['authors'] = []
            papers.append(paper_dict)
        
        conn.close()
        
        print(f"✅ 从数据库中搜索到 {len(papers)} 篇与 '{query}' 相关的论文")
        return papers
    
    def get_workflow(self, paper_id: str) -> Optional[Dict[str, Any]]:
        """
        获取论文的工作流信息
        
        Args:
            paper_id: 论文ID
            
        Returns:
            工作流信息或None
        """
        workflow_id = f"{paper_id}_workflow"
        
        # 优先从缓存文件加载
        cache_path = os.path.join(self.cache_dir, f"workflow_{workflow_id}.json")
        if os.path.exists(cache_path):
            with open(cache_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        # 从数据库加载
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM workflows WHERE id = ?', (workflow_id,))
        workflow = cursor.fetchone()
        
        conn.close()
        
        if workflow:
            # 转换为字典
            workflow_dict = dict(workflow)
            
            # 解析JSON字段
            for field in ['datasets', 'methods', 'metrics', 'architectures', 'platforms', 'brain_inspiration']:
                if field in workflow_dict:
                    try:
                        workflow_dict[field] = json.loads(workflow_dict[field])
                    except:
                        workflow_dict[field] = []
            
            return workflow_dict
        
        return None
    
    def get_all_workflows(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有工作流信息
        
        Returns:
            以论文ID为键的工作流字典
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('SELECT id, paper_id, datasets, methods, metrics, architectures, platforms, brain_inspiration FROM workflows')
        workflows = {}
        
        for row in cursor.fetchall():
            workflow_dict = dict(row)
            paper_id = workflow_dict.pop('paper_id')
            
            # 解析JSON字段
            for field in ['datasets', 'methods', 'metrics', 'architectures', 'platforms', 'brain_inspiration']:
                if field in workflow_dict:
                    try:
                        workflow_dict[field] = json.loads(workflow_dict[field])
                    except:
                        workflow_dict[field] = []
            
            workflows[paper_id] = workflow_dict
        
        conn.close()
        return workflows
    
    def get_paper_count(self) -> int:
        """获取数据库中论文总数"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM papers')
        count = cursor.fetchone()[0]
        
        conn.close()
        return count
    
    def get_workflow_count(self) -> int:
        """获取数据库中工作流总数"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM workflows')
        count = cursor.fetchone()[0]
        
        conn.close()
        return count
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 论文总数
        cursor.execute('SELECT COUNT(*) FROM papers')
        paper_count = cursor.fetchone()[0]
        
        # 工作流总数
        cursor.execute('SELECT COUNT(*) FROM workflows')
        workflow_count = cursor.fetchone()[0]
        
        # 查询总数
        cursor.execute('SELECT COUNT(*) FROM queries')
        query_count = cursor.fetchone()[0]
        
        # 最常见的数据集
        cursor.execute('SELECT datasets FROM workflows')
        datasets = []
        for row in cursor.fetchall():
            try:
                datasets.extend(json.loads(row[0]))
            except:
                pass
        
        dataset_counts = {}
        for dataset in datasets:
            if dataset in dataset_counts:
                dataset_counts[dataset] += 1
            else:
                dataset_counts[dataset] = 1
        
        top_datasets = sorted(dataset_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        # 最常见的方法
        cursor.execute('SELECT methods FROM workflows')
        methods = []
        for row in cursor.fetchall():
            try:
                methods.extend(json.loads(row[0]))
            except:
                pass
        
        method_counts = {}
        for method in methods:
            if method in method_counts:
                method_counts[method] += 1
            else:
                method_counts[method] = 1
        
        top_methods = sorted(method_counts.items(), key=lambda x: x[1], reverse=True)[:5]
        
        conn.close()
        
        return {
            'paper_count': paper_count,
            'workflow_count': workflow_count,
            'query_count': query_count,
            'top_datasets': top_datasets,
            'top_methods': top_methods,
            'db_path': self.db_path,
            'cache_dir': self.cache_dir
        }
    
    def clear(self):
        """清空数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM workflows')
        cursor.execute('DELETE FROM papers')
        cursor.execute('DELETE FROM queries')
        
        conn.commit()
        conn.close()
        
        print("✅ 已清空数据库")


# 示例用法
if __name__ == '__main__':
    # 创建论文数据库
    db = PaperDatabase()
    
    # 打印统计信息
    stats = db.get_statistics()
    print(f"论文总数: {stats['paper_count']}")
    print(f"工作流总数: {stats['workflow_count']}")
    print(f"查询总数: {stats['query_count']}")
    
    if stats['top_datasets']:
        print("\n最常见的数据集:")
        for dataset, count in stats['top_datasets']:
            print(f"  {dataset}: {count}次")
    
    if stats['top_methods']:
        print("\n最常见的方法:")
        for method, count in stats['top_methods']:
            print(f"  {method}: {count}次") 