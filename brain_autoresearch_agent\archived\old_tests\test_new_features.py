"""
快速功能演示脚本 - 测试新补全的功能
"""

import os
import sys
import asyncio
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.llm_client import LLMClient
from paper_generation.enhanced_brain_paper_writer import (
    EnhancedBrainPaperWriter, PaperGenerationConfig
)
from paper_generation.advanced_brain_paper_writer import (
    AdvancedBrainPaperWriter, AdvancedPaperConfig
)


def test_enhanced_system_methods():
    """测试增强系统的新方法"""
    print("🧪 测试增强系统新方法")
    
    config = PaperGenerationConfig(
        target_venue="ICML",
        paper_type="research",
        enable_multi_expert_review=True,
        enable_auto_revision=True,
        latex_output=False
    )
    
    writer = EnhancedBrainPaperWriter(config=config)
    
    # 测试设置方法
    print("\\n  🔧 测试设置方法:")
    try:
        writer._setup_multi_expert_review()
        writer._setup_auto_revision_engine()
        print("    ✅ 设置方法测试通过")
        return True
    except Exception as e:
        print(f"    ❌ 设置方法测试失败: {e}")
        return False


def test_advanced_system_methods():
    """测试高级系统的新方法"""
    print("\\n🚀 测试高级系统新方法")
    
    config = AdvancedPaperConfig(
        target_venue="ICML",
        paper_type="research",
        enable_detailed_section_generation=True,
        enable_multi_expert_review=True,
        enable_auto_revision=True,
        latex_output=False
    )
    
    writer = AdvancedBrainPaperWriter(config=config)
    
    # 测试详细生成方法
    print("\\n  📝 测试详细生成方法:")
    
    test_context = {
        'research_topic': 'Brain-Inspired Adaptive Learning Networks',
        'target_venue': 'ICML',
        'paper_type': 'research'
    }
    
    try:
        # 测试摘要生成
        abstract = writer._generate_abstract_detailed_with_review(test_context)
        print(f"    ✅ 摘要生成: {len(abstract)} 字符")
        
        # 测试引言生成
        introduction = writer._generate_introduction_detailed_with_novelty(test_context)
        print(f"    ✅ 引言生成: {len(introduction)} 字符")
        
        # 测试方法论生成
        methodology = writer._generate_methodology_detailed_with_validation(test_context)
        print(f"    ✅ 方法论生成: {len(methodology)} 字符")
        
        print("    ✅ 详细生成方法测试通过")
        return True
        
    except Exception as e:
        print(f"    ❌ 详细生成方法测试失败: {e}")
        return False


def test_experiment_data_integration():
    """测试实验数据集成功能"""
    print("\\n🧪 测试实验数据集成功能")
    
    config = AdvancedPaperConfig(
        enable_experiment_integration=True,
        latex_output=False
    )
    
    writer = AdvancedBrainPaperWriter(config=config)
    
    # 模拟实验数据
    experiment_data = {
        'baseline_results': {
            'accuracy': 0.85,
            'f1_score': 0.82,
            'inference_time': 120.5
        },
        'proposed_results': {
            'accuracy': 0.92,
            'f1_score': 0.89,
            'inference_time': 98.2
        },
        'ablation_results': {
            'without_brain_module': {'accuracy': 0.88},
            'without_adaptive_learning': {'accuracy': 0.87}
        },
        'datasets': ['CIFAR-10', 'ImageNet-100', 'MNIST'],
        'metrics': ['accuracy', 'f1_score', 'inference_time']
    }
    
    try:
        integrated_data = writer.load_and_integrate_experiment_data(experiment_data)
        
        print(f"    ✅ 实验数据集成成功")
        print(f"    📊 集成结果:")
        print(f"      - 实验总结: {len(integrated_data.get('experiment_summaries', {}))}")
        print(f"      - 性能指标: {len(integrated_data.get('performance_metrics', {}))}")
        print(f"      - 数据集: {len(integrated_data.get('datasets_used', []))}")
        
        # 检查性能提升计算
        improvements = integrated_data.get('performance_metrics', {}).get('improvements', {})
        if improvements:
            print(f"      - 性能提升:")
            for metric, improvement in improvements.items():
                print(f"        * {metric}: +{improvement:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"    ❌ 实验数据集成失败: {e}")
        return False


def test_comprehensive_quality_control():
    """测试综合质量控制功能"""
    print("\\n🎯 测试综合质量控制功能")
    
    config = AdvancedPaperConfig(
        target_venue="ICML",
        enable_multi_expert_review=True,
        enable_auto_revision=True,
        quality_threshold=6.0,  # 降低阈值便于测试
        max_review_iterations=2,
        latex_output=False
    )
    
    writer = AdvancedBrainPaperWriter(config=config)
    
    research_topic = "Brain-Inspired Adaptive Learning Networks for Computer Vision"
    
    # 模拟实验数据
    experimental_data = {
        'baseline_results': {'accuracy': 0.85},
        'proposed_results': {'accuracy': 0.92},
        'datasets': ['CIFAR-10', 'ImageNet']
    }
    
    quality_requirements = {
        'min_expert_score': 6.0,
        'max_revision_rounds': 2,
        'enable_data_integration': True
    }
    
    try:
        result = writer.generate_paper_with_comprehensive_quality_control(
            research_topic=research_topic,
            experimental_data=experimental_data,
            quality_requirements=quality_requirements
        )
        
        if result['success']:
            print(f"    ✅ 综合质量控制成功")
            print(f"    📊 结果摘要:")
            print(f"      - 最终评分: {result['quality_metrics'].get('overall_score', 0):.2f}/10")
            print(f"      - 完成轮次: {result['quality_metrics'].get('iterations_completed', 0)}")
            print(f"      - 论文标题: {result['paper_content']['title']}")
            print(f"      - 章节数量: {len(result['paper_content']['sections'])}")
            print(f"      - 生成用时: {result['generation_metadata']['generation_time']:.1f}秒")
            return True
        else:
            print(f"    ❌ 综合质量控制失败: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"    ❌ 综合质量控制异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🔧 Brain AutoResearch Agent - 新功能测试")
    print("=" * 60)
    
    tests = [
        ("增强系统方法测试", test_enhanced_system_methods),
        ("高级系统方法测试", test_advanced_system_methods), 
        ("实验数据集成测试", test_experiment_data_integration),
        ("综合质量控制测试", test_comprehensive_quality_control)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\\n📋 运行 {test_name}...")
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {str(e)}")
    
    print(f"\\n📊 功能测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有新功能测试通过！")
        print("\\n🚀 下一步建议:")
        print("1. 运行完整的demo_advanced_paper_generation.py")
        print("2. 使用高级系统生成实际论文")
        print("3. 对比不同系统的生成质量")
    else:
        print("⚠️ 部分功能需要进一步完善。")
        print(f"完成度: {(passed/total)*100:.1f}%")


if __name__ == "__main__":
    main()
