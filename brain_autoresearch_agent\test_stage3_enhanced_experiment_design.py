"""
Stage 3 集成测试 - 实验设计与代码生成
测试增强的实验设计器、代码生成器和可视化建议器
验证多专家协作和统一API客户端集成

测试范围：
1. 增强假设实验设计器测试
2. 增强实验代码生成器测试  
3. 增强可视化建议器测试
4. 端到端集成测试
5. 多专家协作验证
"""

import unittest
import sys
import os
import json
import tempfile
import shutil
from pathlib import Path
from typing import Dict, Any, List, Optional
from unittest.mock import Mock, patch, MagicMock

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 测试目标导入
from reasoning.enhanced_hypothesis_experiment_designer import EnhancedHypothesisExperimentDesigner
from core.experiment_code_generator import EnhancedExperimentCodeGenerator, ExperimentSpecification
from reasoning.enhanced_visualization_advisor import EnhancedVisualizationAdvisor
from core.unified_api_client import UnifiedAPIClient
from reasoning.data_models import (
    ResearchProblem, ExperimentPlan, ImplementationPlan,
    VisualizationPlan, CollaborationSession
)


class TestStage3EnhancedExperimentDesign(unittest.TestCase):
    """Stage 3: 增强实验设计与代码生成测试套件"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        print("=" * 80)
        print("🧪 Stage 3 增强实验设计与代码生成 - 集成测试开始")
        print("=" * 80)
        
        # 创建模拟的统一API客户端
        cls.mock_unified_client = cls._create_comprehensive_mock_client()
        
        # 初始化测试目录
        cls.test_output_dir = tempfile.mkdtemp(prefix="stage3_test_")
        
        # 测试计数器
        cls.passed_tests = 0
        cls.total_tests = 0
    
    @classmethod
    def _create_comprehensive_mock_client(cls):
        """创建完善的Mock客户端"""
        mock_client = Mock()
        
        # 基本响应配置
        mock_client.generate_response = Mock(return_value=cls._get_mock_api_response())
        mock_client.generate_response_with_json = Mock(return_value=(cls._get_mock_api_response(), {"test": "data"}))
        mock_client.available_models = {"text": ["deepseek-chat"], "vision": ["qwen-vl"]}
        
        # 模拟字符串方法 - 解决 len() 问题
        mock_response = cls._get_mock_api_response()
        mock_client.generate_response.return_value = mock_response
        
        # 确保Mock对象支持长度检查和迭代
        mock_client.__len__ = Mock(return_value=len(mock_response) if isinstance(mock_response, str) else 0)
        mock_client.__iter__ = Mock(return_value=iter(mock_response) if isinstance(mock_response, str) else iter([]))
        mock_client.__contains__ = Mock(side_effect=lambda x: x in mock_response if isinstance(mock_response, str) else False)
        
        return mock_client
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        # 清理测试目录
        if os.path.exists(cls.test_output_dir):
            shutil.rmtree(cls.test_output_dir)
        
        # 打印测试摘要
        print("\n" + "=" * 80)
        print(f"🧪 Stage 3 测试完成: {cls.passed_tests}/{cls.total_tests} 通过")
        
        success_rate = (cls.passed_tests / cls.total_tests * 100) if cls.total_tests > 0 else 0
        if success_rate >= 90:
            print(f"✅ Stage 3 测试成功率: {success_rate:.1f}% - 优秀！")
        elif success_rate >= 70:
            print(f"⚠️  Stage 3 测试成功率: {success_rate:.1f}% - 良好")
        else:
            print(f"❌ Stage 3 测试成功率: {success_rate:.1f}% - 需要改进")
        
        print("=" * 80)
    
    def setUp(self):
        """每个测试方法的初始化"""
        TestStage3EnhancedExperimentDesign.total_tests += 1
    
    def tearDown(self):
        """每个测试方法的清理"""
        pass
    
    @staticmethod
    def _get_mock_api_response(response_type: str = "experiment_design") -> str:
        """获取模拟API响应"""
        responses = {
            "experiment_design": '''```json
{
    "experiment_name": "enhanced_attention_classification",
    "title": "Enhanced Attention-based Classification",
    "hypothesis": "Multi-head attention mechanism will improve classification accuracy",
    "methodology": "Comparative study using attention vs baseline models",
    "variables": {
        "independent": ["attention_heads", "learning_rate", "batch_size"],
        "dependent": ["accuracy", "f1_score", "training_time"]
    },
    "success_metrics": ["accuracy > 0.9", "f1_score > 0.85"],
    "baselines": ["ResNet-18", "VGG-16", "Basic CNN"],
    "datasets": ["CIFAR-10", "ImageNet-subset"],
    "implementation_framework": "PyTorch",
    "estimated_duration": "5 days"
}
```''',
            "code_generation": '''```json
{
    "name": "attention_classifier",
    "title": "Attention-based Image Classifier",
    "hypothesis": "Multi-head attention improves classification",
    "framework": "pytorch",
    "experiment_type": "classification",
    "dataset": "cifar10",
    "metrics": ["accuracy", "f1_score", "precision", "recall"],
    "baseline_methods": ["resnet18", "vgg16"],
    "proposed_method": "AttentionNet",
    "code_requirements": ["pytorch", "torchvision", "sklearn"]
}
```''',
            "visualization": '''```json
{
    "experiment_name": "attention_visualization",
    "charts": [
        {
            "chart_type": "line",
            "title": "Training vs Validation Loss",
            "data_source": "training_logs",
            "x_axis": "epoch",
            "y_axis": "loss",
            "description": "Loss curves comparison"
        },
        {
            "chart_type": "bar",
            "title": "Model Performance Comparison",
            "data_source": "results",
            "x_axis": "model",
            "y_axis": "accuracy",
            "description": "Accuracy comparison across models"
        }
    ],
    "recommended_tools": ["matplotlib", "seaborn"],
    "export_formats": ["png", "pdf"],
    "styling_suggestions": ["Use scientific paper style", "Add error bars"],
    "interactive_features": ["Zoom functionality", "Data tooltips"]
}
```'''
        }
        return responses.get(response_type, responses["experiment_design"])
    
    # ===== 增强假设实验设计器测试 =====
    
    def test_enhanced_hypothesis_designer_initialization(self):
        """测试1: 增强假设实验设计器初始化"""
        try:
            designer = EnhancedHypothesisExperimentDesigner(self.mock_unified_client)
            
            # 验证初始化
            self.assertIsNotNone(designer)
            self.assertIsNotNone(designer.unified_client)
            self.assertEqual(designer.unified_client, self.mock_unified_client)
            
            print("✅ 测试1通过: 增强假设实验设计器初始化成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试1失败: 增强假设实验设计器初始化失败 - {e}")
    
    def test_enhanced_experiment_design_collaborative(self):
        """测试2: 多专家协作实验设计"""
        try:
            designer = EnhancedHypothesisExperimentDesigner(self.mock_unified_client)
            
            # 创建研究问题
            research_problem = ResearchProblem(
                title="Attention Mechanism for Classification",
                description="Investigate attention mechanisms for image classification",
                domain="computer_vision",
                background="Attention has shown promise in NLP",
                objectives=["Improve accuracy", "Reduce training time"],
                constraints=["Limited GPU resources"]
            )
            
            # 设计实验（如果协作器可用）
            experiment_design = designer.design_experiment_from_research_problem(research_problem)
            
            # 验证设计
            self.assertIsNotNone(experiment_design)
            self.assertIsInstance(experiment_design, ExperimentPlan)
            self.assertIsNotNone(experiment_design.experiment_name)
            
            print("✅ 测试2通过: 多专家协作实验设计成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试2失败: 多专家协作实验设计失败 - {e}")
    
    def test_experiment_design_components_validation(self):
        """测试3: 实验设计组件验证"""
        try:
            designer = EnhancedHypothesisExperimentDesigner(self.mock_unified_client)
            
            research_problem = ResearchProblem(
                title="Test Problem",
                description="Test description",
                domain="test",
                background="Test background",
                objectives=["Test objective"],
                constraints=[]
            )
            
            experiment_design = designer.design_experiment_from_research_problem(research_problem)
            
            # 验证设计组件
            self.assertTrue(hasattr(experiment_design, 'hypothesis'))
            self.assertTrue(hasattr(experiment_design, 'methodology'))
            self.assertTrue(hasattr(experiment_design, 'variables'))
            self.assertTrue(hasattr(experiment_design, 'success_metrics'))
            
            print("✅ 测试3通过: 实验设计组件验证成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试3失败: 实验设计组件验证失败 - {e}")
    
    # ===== 增强实验代码生成器测试 =====
    
    def test_enhanced_code_generator_initialization(self):
        """测试4: 增强实验代码生成器初始化"""
        try:
            generator = EnhancedExperimentCodeGenerator(self.mock_unified_client)
            
            # 验证初始化
            self.assertIsNotNone(generator)
            self.assertIsNotNone(generator.unified_client)
            self.assertIsNotNone(generator.generators)
            self.assertIn('pytorch', generator.generators)
            
            print("✅ 测试4通过: 增强实验代码生成器初始化成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试4失败: 增强实验代码生成器初始化失败 - {e}")
    
    def test_collaborative_experiment_specification(self):
        """测试5: 多专家协作实验规格生成"""
        try:
            generator = EnhancedExperimentCodeGenerator(self.mock_unified_client)
            
            # 配置模拟响应
            self.mock_unified_client.generate_response.return_value = self._get_mock_api_response("code_generation")
            
            # 生成实验规格
            research_idea = "Novel attention mechanism for image classification"
            spec = generator.generate_experiment_specification_collaborative(research_idea)
            
            # 验证规格
            self.assertIsNotNone(spec)
            self.assertIsInstance(spec, ExperimentSpecification)
            self.assertIsNotNone(spec.name)
            self.assertIsNotNone(spec.framework)
            
            print("✅ 测试5通过: 多专家协作实验规格生成成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试5失败: 多专家协作实验规格生成失败 - {e}")
    
    def test_complete_experiment_code_generation(self):
        """测试6: 完整实验代码生成"""
        try:
            generator = EnhancedExperimentCodeGenerator(self.mock_unified_client)
            
            # 创建测试规格
            spec = ExperimentSpecification(
                name="test_experiment",
                title="Test Experiment",
                hypothesis="Test hypothesis",
                framework="pytorch",
                experiment_type="classification",
                dataset="iris",
                metrics=["accuracy"],
                baseline_methods=["logistic"],
                proposed_method="test_method",
                code_requirements=["pytorch"]
            )
            
            # 生成完整实验
            output_dir = os.path.join(self.test_output_dir, "test_experiment")
            files = generator.generate_complete_experiment(spec, output_dir)
            
            # 验证生成的文件
            self.assertIsInstance(files, dict)
            self.assertTrue(len(files) > 0)
            
            print("✅ 测试6通过: 完整实验代码生成成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试6失败: 完整实验代码生成失败 - {e}")
    
    # ===== 增强可视化建议器测试 =====
    
    def test_enhanced_visualization_advisor_initialization(self):
        """测试7: 增强可视化建议器初始化"""
        try:
            advisor = EnhancedVisualizationAdvisor(self.mock_unified_client)
            
            # 验证初始化
            self.assertIsNotNone(advisor)
            self.assertIsNotNone(advisor.unified_client)
            self.assertIsNotNone(advisor.visualization_tools)
            self.assertIsNotNone(advisor.chart_scenarios)
            
            print("✅ 测试7通过: 增强可视化建议器初始化成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试7失败: 增强可视化建议器初始化失败 - {e}")
    
    def test_collaborative_visualization_plan_generation(self):
        """测试8: 多专家协作可视化计划生成"""
        try:
            advisor = EnhancedVisualizationAdvisor(self.mock_unified_client)
            
            # 配置模拟响应
            self.mock_unified_client.generate_response.return_value = self._get_mock_api_response("visualization")
            
            # 测试实验结果
            experiment_results = {
                "experiment_name": "test_visualization",
                "accuracy": 0.92,
                "precision": 0.89,
                "training_loss": [0.8, 0.6, 0.4, 0.2],
                "validation_loss": [0.9, 0.7, 0.5, 0.3]
            }
            
            # 生成可视化计划
            viz_plan = advisor.generate_visualization_plan_collaborative(experiment_results)
            
            # 验证计划
            self.assertIsNotNone(viz_plan)
            self.assertIsInstance(viz_plan, VisualizationPlan)
            self.assertIsNotNone(viz_plan.experiment_name)
            self.assertTrue(len(viz_plan.charts) > 0)
            
            print("✅ 测试8通过: 多专家协作可视化计划生成成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试8失败: 多专家协作可视化计划生成失败 - {e}")
    
    def test_visualization_code_generation(self):
        """测试9: 可视化代码生成"""
        try:
            advisor = EnhancedVisualizationAdvisor(self.mock_unified_client)
            
            # 配置模拟代码响应
            mock_code = '''
import matplotlib.pyplot as plt
import pandas as pd

data = pd.read_csv('data.csv')
plt.figure(figsize=(10, 6))
plt.plot(data['x'], data['y'])
plt.title('Test Chart')
plt.show()
'''
            self.mock_unified_client.generate_response.return_value = mock_code
            
            # 创建测试图表
            from reasoning.data_models import VisualizationChart
            test_chart = VisualizationChart(
                chart_type='line',
                title='Test Chart',
                data_source='data.csv',
                x_axis='x',
                y_axis='y',
                description='Test chart description'
            )
            
            # 生成图表代码
            code = advisor.generate_chart_code(test_chart, 'data.csv', 'matplotlib')
            
            # 验证代码
            self.assertIsNotNone(code)
            self.assertIn('matplotlib', code)
            
            print("✅ 测试9通过: 可视化代码生成成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试9失败: 可视化代码生成失败 - {e}")
    
    def test_complete_visualization_suite_creation(self):
        """测试10: 完整可视化套件创建"""
        try:
            advisor = EnhancedVisualizationAdvisor(self.mock_unified_client)
            
            # 创建测试可视化计划
            from reasoning.data_models import VisualizationChart, VisualizationPlan
            
            test_charts = [
                VisualizationChart(
                    chart_type='line',
                    title='Training Loss',
                    data_source='training.csv',
                    x_axis='epoch',
                    y_axis='loss',
                    description='Training loss over time'
                ),
                VisualizationChart(
                    chart_type='bar',
                    title='Model Comparison',
                    data_source='results.csv',
                    x_axis='model',
                    y_axis='accuracy',
                    description='Model accuracy comparison'
                )
            ]
            
            test_plan = VisualizationPlan(
                experiment_name='test_suite',
                charts=test_charts,
                recommended_tools=['matplotlib'],
                export_formats=['png'],
                styling_suggestions=['Scientific style'],
                interactive_features=[],
                code_examples={}
            )
            
            # 创建可视化套件
            output_dir = os.path.join(self.test_output_dir, "visualization_suite")
            files = advisor.create_complete_visualization_suite(
                test_plan, output_dir, 'data.csv'
            )
            
            # 验证生成的文件
            self.assertIsInstance(files, dict)
            self.assertTrue(len(files) > 0)
            self.assertIn('main_script', files)
            self.assertIn('readme', files)
            
            print("✅ 测试10通过: 完整可视化套件创建成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试10失败: 完整可视化套件创建失败 - {e}")
    
    # ===== 端到端集成测试 =====
    
    def test_end_to_end_experiment_pipeline(self):
        """测试11: 端到端实验流程"""
        try:
            # 初始化所有组件
            designer = EnhancedHypothesisExperimentDesigner(self.mock_unified_client)
            generator = EnhancedExperimentCodeGenerator(self.mock_unified_client)
            advisor = EnhancedVisualizationAdvisor(self.mock_unified_client)
            
            # 配置不同的模拟响应
            def mock_response_side_effect(prompt, agent_type=None, task_type=None, **kwargs):
                if task_type == "experiment_design":
                    return self._get_mock_api_response("experiment_design")
                elif task_type == "experiment_design" or "实验规格" in prompt:
                    return self._get_mock_api_response("code_generation")
                elif task_type == "visualization_design":
                    return self._get_mock_api_response("visualization")
                else:
                    return "Mock response"
            
            self.mock_unified_client.generate_response.side_effect = mock_response_side_effect
            
            # 步骤1: 设计实验
            research_problem = ResearchProblem(
                title="End-to-End Test Problem",
                description="Test the complete pipeline",
                domain="test",
                background="Test background",
                objectives=["Test pipeline"],
                constraints=[]
            )
            
            experiment_design = designer.design_experiment_from_research_problem(research_problem)
            self.assertIsNotNone(experiment_design)
            
            # 步骤2: 生成代码
            research_idea = "End-to-end pipeline test"
            experiment_spec = generator.generate_experiment_specification_collaborative(research_idea)
            self.assertIsNotNone(experiment_spec)
            
            # 步骤3: 生成可视化
            mock_results = {
                "experiment_name": "e2e_test",
                "accuracy": 0.88,
                "loss": [0.5, 0.3, 0.2]
            }
            viz_plan = advisor.generate_visualization_plan_collaborative(mock_results)
            self.assertIsNotNone(viz_plan)
            
            print("✅ 测试11通过: 端到端实验流程成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试11失败: 端到端实验流程失败 - {e}")
    
    def test_unified_api_integration(self):
        """测试12: 统一API集成验证"""
        try:
            # 验证所有组件都使用统一API客户端
            designer = EnhancedHypothesisExperimentDesigner(self.mock_unified_client)
            generator = EnhancedExperimentCodeGenerator(self.mock_unified_client)
            advisor = EnhancedVisualizationAdvisor(self.mock_unified_client)
            
            # 验证客户端引用
            self.assertEqual(designer.unified_client, self.mock_unified_client)
            self.assertEqual(generator.unified_client, self.mock_unified_client)
            self.assertEqual(advisor.unified_client, self.mock_unified_client)
            
            # 验证API调用能力
            self.assertTrue(hasattr(designer.unified_client, 'generate_response'))
            self.assertTrue(hasattr(generator.unified_client, 'generate_response'))
            self.assertTrue(hasattr(advisor.unified_client, 'generate_response'))
            
            print("✅ 测试12通过: 统一API集成验证成功")
            TestStage3EnhancedExperimentDesign.passed_tests += 1
            
        except Exception as e:
            print(f"❌ 测试12失败: 统一API集成验证失败 - {e}")


def run_stage3_tests():
    """运行Stage 3测试套件"""
    
    print("🚀 启动 Stage 3 增强实验设计与代码生成测试...")
    print("📋 测试范围:")
    print("  1. 增强假设实验设计器 (3个测试)")
    print("  2. 增强实验代码生成器 (3个测试)")  
    print("  3. 增强可视化建议器 (4个测试)")
    print("  4. 端到端集成测试 (2个测试)")
    print("  总计: 12个测试\n")
    
    # 创建测试套件
    loader = unittest.TestLoader()
    suite = loader.loadTestsFromTestCase(TestStage3EnhancedExperimentDesign)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)  # 显示详细输出
    result = runner.run(suite)
    
    return result


if __name__ == "__main__":
    result = run_stage3_tests()
    
    # 退出码
    sys.exit(0 if result.wasSuccessful() else 1)
