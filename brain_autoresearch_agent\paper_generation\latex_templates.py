"""
LaTeX Template Manager for Academic Paper Generation

This module provides LaTeX templates and formatting utilities
for generating professional academic papers in various venues.
"""

import os
from typing import Dict, Any, Optional
from datetime import datetime


class LaTeXTemplateManager:
    """
    Manages LaTeX templates for different academic venues and paper types
    """
    
    def __init__(self):
        self.templates = {
            'ICML': self._get_icml_template(),
            'NeurIPS': self._get_neurips_template(),
            'ICLR': self._get_iclr_template(),
            'AAAI': self._get_aaai_template(),
            'IJCAI': self._get_ijcai_template(),
            'generic': self._get_generic_template()
        }
    
    def generate_latex_paper(self, paper_data: Dict[str, Any], 
                           venue: str = "ICML",
                           include_figures: bool = True,
                           include_tables: bool = True) -> str:
        """
        Generate complete LaTeX document from paper data
        
        Args:
            paper_data: Dictionary containing all paper sections
            venue: Target academic venue
            include_figures: Whether to include figure placeholders
            include_tables: Whether to include table placeholders
            
        Returns:
            Complete LaTeX document as string
        """
        
        template = self.templates.get(venue, self.templates['generic'])
        
        # Extract paper components
        title = paper_data.get('title', 'Untitled Research Paper')
        abstract = paper_data.get('abstract', '')
        introduction = paper_data.get('introduction', '')
        related_work = paper_data.get('related_work', '')
        methodology = paper_data.get('methodology', '')
        experiments = paper_data.get('experiments', '')
        results = paper_data.get('results', '')
        discussion = paper_data.get('discussion', '')
        conclusion = paper_data.get('conclusion', '')
        
        # Generate authors (placeholder)
        authors = self._generate_author_section(paper_data.get('metadata', {}))
        
        # Build complete document
        latex_content = template.format(
            title=self._escape_latex(title),
            authors=authors,
            abstract=self._format_section(abstract),
            introduction=self._format_section(introduction),
            related_work=self._format_section(related_work),
            methodology=self._format_section(methodology),
            experiments=self._format_section(experiments),
            results=self._format_section(results),
            discussion=self._format_section(discussion),
            conclusion=self._format_section(conclusion),
            references=self._generate_references_section(paper_data),
            figures=self._generate_figure_placeholders() if include_figures else "",
            tables=self._generate_table_placeholders() if include_tables else "",
            date=datetime.now().strftime("%B %d, %Y")
        )
        
        return latex_content
    
    def _get_icml_template(self) -> str:
        """ICML conference template"""
        return """
\\documentclass{{icml2025}}

% Package imports
\\usepackage{{amsmath}}
\\usepackage{{amssymb}}
\\usepackage{{amsfonts}}
\\usepackage{{graphicx}}
\\usepackage{{algorithm}}
\\usepackage{{algorithmic}}
\\usepackage{{booktabs}}
\\usepackage{{xcolor}}
\\usepackage{{natbib}}
\\usepackage{{url}}

% Paper title
\\icmltitle{{{title}}}

% Authors and affiliations
{authors}

\\begin{{document}}

\\twocolumn[
\\icmltitleabstract{{
\\begin{{abstract}}
{abstract}
\\end{{abstract}}
}}
]

\\section{{Introduction}}
{introduction}

\\section{{Related Work}}
{related_work}

\\section{{Methodology}}
{methodology}

\\section{{Experiments}}
{experiments}

\\section{{Results}}
{results}

\\section{{Discussion}}
{discussion}

\\section{{Conclusion}}
{conclusion}

{figures}

{tables}

{references}

\\end{{document}}
"""
    
    def _get_neurips_template(self) -> str:
        """NeurIPS conference template"""
        return """
\\documentclass{{article}}

% NeurIPS style packages
\\usepackage[preprint]{{neurips_2024}}
\\usepackage[utf8]{{inputenc}}
\\usepackage[T1]{{fontenc}}
\\usepackage{{hyperref}}
\\usepackage{{url}}
\\usepackage{{amsfonts}}
\\usepackage{{nicefrac}}
\\usepackage{{microtype}}
\\usepackage{{xcolor}}
\\usepackage{{graphicx}}
\\usepackage{{booktabs}}
\\usepackage{{algorithm}}
\\usepackage{{algorithmic}}
\\usepackage{{natbib}}

\\title{{{title}}}

{authors}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
{abstract}
\\end{{abstract}}

\\section{{Introduction}}
{introduction}

\\section{{Related Work}}
{related_work}

\\section{{Methodology}}
{methodology}

\\section{{Experiments}}
{experiments}

\\section{{Results}}
{results}

\\section{{Discussion}}
{discussion}

\\section{{Conclusion}}
{conclusion}

{figures}

{tables}

{references}

\\end{{document}}
"""
    
    def _get_iclr_template(self) -> str:
        """ICLR conference template"""
        return """
\\documentclass{{article}}

% ICLR style
\\usepackage[final]{{iclr2025_conference}}
\\usepackage[utf8]{{inputenc}}
\\usepackage[T1]{{fontenc}}
\\usepackage{{hyperref}}
\\usepackage{{url}}
\\usepackage{{amsfonts}}
\\usepackage{{amsmath}}
\\usepackage{{amssymb}}
\\usepackage{{graphicx}}
\\usepackage{{booktabs}}
\\usepackage{{algorithm}}
\\usepackage{{algorithmic}}
\\usepackage{{natbib}}

\\title{{{title}}}

{authors}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
{abstract}
\\end{{abstract}}

\\section{{Introduction}}
{introduction}

\\section{{Related Work}}
{related_work}

\\section{{Methodology}}
{methodology}

\\section{{Experiments}}
{experiments}

\\section{{Results}}
{results}

\\section{{Discussion}}
{discussion}

\\section{{Conclusion}}
{conclusion}

{figures}

{tables}

{references}

\\end{{document}}
"""
    
    def _get_aaai_template(self) -> str:
        """AAAI conference template"""
        return """
\\documentclass[letterpaper]{{article}}

% AAAI style
\\usepackage{{aaai25}}
\\usepackage{{times}}
\\usepackage{{helvet}}
\\usepackage{{courier}}
\\usepackage[hyphens]{{url}}
\\usepackage{{graphicx}}
\\usepackage{{amsmath}}
\\usepackage{{amssymb}}
\\usepackage{{booktabs}}
\\usepackage{{algorithm}}
\\usepackage{{algorithmic}}
\\usepackage{{natbib}}

\\title{{{title}}}

{authors}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
{abstract}
\\end{{abstract}}

\\section{{Introduction}}
{introduction}

\\section{{Related Work}}
{related_work}

\\section{{Methodology}}
{methodology}

\\section{{Experiments}}
{experiments}

\\section{{Results}}
{results}

\\section{{Discussion}}
{discussion}

\\section{{Conclusion}}
{conclusion}

{figures}

{tables}

{references}

\\end{{document}}
"""
    
    def _get_ijcai_template(self) -> str:
        """IJCAI conference template"""
        return """
\\documentclass{{article}}

% IJCAI style
\\usepackage{{ijcai25}}
\\usepackage{{times}}
\\usepackage{{soul}}
\\usepackage{{url}}
\\usepackage[hidelinks]{{hyperref}}
\\usepackage[utf8]{{inputenc}}
\\usepackage[small]{{caption}}
\\usepackage{{graphicx}}
\\usepackage{{amsmath}}
\\usepackage{{amssymb}}
\\usepackage{{booktabs}}
\\usepackage{{algorithm}}
\\usepackage{{algorithmic}}
\\usepackage{{natbib}}

\\title{{{title}}}

{authors}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
{abstract}
\\end{{abstract}}

\\section{{Introduction}}
{introduction}

\\section{{Related Work}}
{related_work}

\\section{{Methodology}}
{methodology}

\\section{{Experiments}}
{experiments}

\\section{{Results}}
{results}

\\section{{Discussion}}
{discussion}

\\section{{Conclusion}}
{conclusion}

{figures}

{tables}

{references}

\\end{{document}}
"""
    
    def _get_generic_template(self) -> str:
        """Generic academic paper template"""
        return """
\\documentclass[11pt,a4paper]{{article}}

% Standard packages
\\usepackage[utf8]{{inputenc}}
\\usepackage[T1]{{fontenc}}
\\usepackage{{amsmath}}
\\usepackage{{amssymb}}
\\usepackage{{amsfonts}}
\\usepackage{{graphicx}}
\\usepackage{{booktabs}}
\\usepackage{{algorithm}}
\\usepackage{{algorithmic}}
\\usepackage{{natbib}}
\\usepackage{{hyperref}}
\\usepackage{{geometry}}
\\usepackage{{setspace}}

% Page setup
\\geometry{{margin=1in}}
\\onehalfspacing

\\title{{{title}}}

{authors}

\\date{{{date}}}

\\begin{{document}}

\\maketitle

\\begin{{abstract}}
{abstract}
\\end{{abstract}}

\\section{{Introduction}}
{introduction}

\\section{{Related Work}}
{related_work}

\\section{{Methodology}}
{methodology}

\\section{{Experiments}}
{experiments}

\\section{{Results}}
{results}

\\section{{Discussion}}
{discussion}

\\section{{Conclusion}}
{conclusion}

{figures}

{tables}

{references}

\\end{{document}}
"""
    
    def _generate_author_section(self, metadata: Dict[str, Any]) -> str:
        """Generate author section for LaTeX"""
        # Placeholder authors - in real implementation, this would be configurable
        return """
\\author{{
    Research Team\\\\
    Brain-Inspired Intelligence Lab\\\\
    University of Advanced AI\\\\
    \\texttt{{<EMAIL>}}
}}"""
    
    def _format_section(self, content: str) -> str:
        """Format section content for LaTeX"""
        if not content:
            return "% Content to be added\\n"
        
        # Basic formatting - escape special characters and format
        formatted = self._escape_latex(content)
        
        # Add basic paragraph breaks
        formatted = formatted.replace('\\n\\n', '\\n\\n\\n')
        
        return formatted
    
    def _escape_latex(self, text: str) -> str:
        """Escape special LaTeX characters"""
        if not text:
            return ""
        
        # Basic LaTeX escaping
        replacements = {
            '&': '\\&',
            '%': '\\%',
            '$': '\\$',
            '#': '\\#',
            '^': '\\textasciicircum{}',
            '_': '\\_',
            '{': '\\{',
            '}': '\\}',
            '~': '\\textasciitilde{}',
            '\\': '\\textbackslash{}'
        }
        
        for char, replacement in replacements.items():
            text = text.replace(char, replacement)
        
        return text
    
    def _generate_references_section(self, paper_data: Dict[str, Any]) -> str:
        """Generate references section"""
        return """
\\bibliographystyle{{natbib}}
\\bibliography{{references}}

% Note: References will be automatically generated based on citations
% Add your .bib file with relevant citations for the paper
"""
    
    def _generate_figure_placeholders(self) -> str:
        """Generate figure placeholders"""
        return """
% Figure placeholders - replace with actual figures

\\begin{{figure}}[h]
\\centering
\\includegraphics[width=0.8\\textwidth]{{figures/architecture_diagram.pdf}}
\\caption{{Brain-inspired architecture overview showing the key components and information flow.}}
\\label{{fig:architecture}}
\\end{{figure}}

\\begin{{figure}}[h]
\\centering
\\includegraphics[width=0.7\\textwidth]{{figures/performance_comparison.pdf}}
\\caption{{Performance comparison with baseline methods across different datasets.}}
\\label{{fig:performance}}
\\end{{figure}}

\\begin{{figure}}[h]
\\centering
\\includegraphics[width=0.9\\textwidth]{{figures/ablation_study.pdf}}
\\caption{{Ablation study results showing the contribution of different components.}}
\\label{{fig:ablation}}
\\end{{figure}}
"""
    
    def _generate_table_placeholders(self) -> str:
        """Generate table placeholders"""
        return """
% Table placeholders - replace with actual data

\\begin{{table}}[h]
\\centering
\\caption{{Performance comparison on benchmark datasets}}
\\label{{tab:performance}}
\\begin{{tabular}}{{lcccc}}
\\toprule
Method & Dataset 1 & Dataset 2 & Dataset 3 & Average \\\\
\\midrule
Baseline 1 & 85.2 & 78.9 & 82.1 & 82.1 \\\\
Baseline 2 & 87.1 & 80.3 & 84.2 & 83.9 \\\\
Our Method & \\textbf{{91.4}} & \\textbf{{85.7}} & \\textbf{{88.9}} & \\textbf{{88.7}} \\\\
\\bottomrule
\\end{{tabular}}
\\end{{table}}

\\begin{{table}}[h]
\\centering
\\caption{{Ablation study results}}
\\label{{tab:ablation}}
\\begin{{tabular}}{{lccc}}
\\toprule
Component & Accuracy & Speed & Memory \\\\
\\midrule
Full Model & 91.4 & 1.2s & 512MB \\\\
w/o Component A & 89.1 & 1.0s & 480MB \\\\
w/o Component B & 87.8 & 1.1s & 490MB \\\\
w/o Component C & 85.3 & 0.9s & 450MB \\\\
\\bottomrule
\\end{{tabular}}
\\end{{table}}

\\begin{{table}}[h]
\\centering
\\caption{{Computational complexity comparison}}
\\label{{tab:complexity}}
\\begin{{tabular}}{{lcc}}
\\toprule
Method & Time Complexity & Space Complexity \\\\
\\midrule
Method 1 & $O(n^2)$ & $O(n)$ \\\\
Method 2 & $O(n \\log n)$ & $O(n)$ \\\\
Our Method & $O(n)$ & $O(1)$ \\\\
\\bottomrule
\\end{{tabular}}
\\end{{table}}
"""
    
    def save_latex_file(self, latex_content: str, filename: str, 
                       output_dir: str = "output") -> str:
        """
        Save LaTeX content to file
        
        Args:
            latex_content: Complete LaTeX document
            filename: Output filename (without extension)
            output_dir: Output directory
            
        Returns:
            Full path to saved file
        """
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Ensure .tex extension
        if not filename.endswith('.tex'):
            filename += '.tex'
        
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(latex_content)
        
        return filepath
    
    def get_supported_venues(self) -> list:
        """Get list of supported venues"""
        return list(self.templates.keys())
    
    def generate_paper_latex(self, paper_data: Dict[str, Any], venue: str = "ICML") -> str:
        """
        Alias for generate_latex_paper to maintain compatibility
        """
        return self.generate_latex_paper(paper_data, venue)


# Utility functions for LaTeX processing

def clean_latex_content(content: str) -> str:
    """Clean and validate LaTeX content"""
    # Remove extra whitespace
    content = '\\n'.join(line.strip() for line in content.split('\\n') if line.strip())
    
    # Basic validation - check for balanced braces
    open_braces = content.count('{')
    close_braces = content.count('}')
    
    if open_braces != close_braces:
        print(f"Warning: Unbalanced braces detected ({open_braces} open, {close_braces} close)")
    
    return content


def generate_bibliography_file(citations: list, filename: str = "references.bib") -> str:
    """
    Generate a .bib file from citation data
    
    Args:
        citations: List of citation dictionaries
        filename: Output filename for .bib file
        
    Returns:
        Path to generated .bib file
    """
    bib_content = "% Automatically generated bibliography\\n\\n"
    
    for i, citation in enumerate(citations):
        # Generate BibTeX entry
        entry_type = citation.get('type', 'article')
        entry_key = citation.get('key', f'ref{i+1}')
        
        bib_content += f"@{entry_type}{{{entry_key},\\n"
        
        for field, value in citation.items():
            if field not in ['type', 'key']:
                bib_content += f"  {field} = {{{value}}},\\n"
        
        bib_content += "}\\n\\n"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(bib_content)
    
    return filename


if __name__ == "__main__":
    # Example usage
    template_manager = LaTeXTemplateManager()
    
    # Sample paper data
    sample_paper = {
        'title': 'Brain-Inspired Visual Recognition: A Novel Approach',
        'abstract': 'This paper presents a novel brain-inspired approach to visual recognition...',
        'introduction': 'Introduction content here...',
        'related_work': 'Related work content here...',
        'methodology': 'Methodology content here...',
        'experiments': 'Experiments content here...',
        'results': 'Results content here...',
        'discussion': 'Discussion content here...',
        'conclusion': 'Conclusion content here...',
        'metadata': {'venue': 'ICML'}
    }
    
    # Generate LaTeX
    latex_content = template_manager.generate_latex_paper(sample_paper, venue='ICML')
    
    # Save to file
    output_path = template_manager.save_latex_file(
        latex_content, 
        'brain_inspired_paper', 
        'output'
    )
    
    print(f"LaTeX paper generated: {output_path}")
    print(f"Supported venues: {template_manager.get_supported_venues()}")
