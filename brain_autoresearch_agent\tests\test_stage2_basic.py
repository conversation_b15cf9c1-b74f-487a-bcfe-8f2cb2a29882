"""
脑启发智能AutoResearch Agent - 阶段2测试脚本
测试多专家代理系统的基础功能
"""

import os
import sys
import time
import json
sys.path.append('.')

def main():
    """运行阶段2基础测试"""
    print("🧠 脑启发智能AutoResearch Agent - 阶段2基础测试")
    print("=" * 80)
    print(f"⏰ 测试开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置API密钥
    api_key = "sk-1b1d72e2e10643029de548b655e1f93e"
    os.environ["DEEPSEEK_API_KEY"] = api_key
    
    # 测试结果记录
    test_results = {
        "base_agent": False,
        "ai_expert": <PERSON>alse,
        "agent_manager": False,
        "single_task": False,
        "collaboration": False
    }
    
    print("\n" + "🤖 阶段2多专家代理系统测试".center(80, "="))
    
    # 1. 测试基础代理类
    print("\n📋 1. 基础代理类测试")
    print("-" * 60)
    test_results["base_agent"] = test_base_agent(api_key)
    
    # 2. 测试AI技术专家
    print("\n📋 2. AI技术专家测试")
    print("-" * 60)
    test_results["ai_expert"] = test_ai_technology_expert(api_key)
    
    # 3. 测试代理管理器
    print("\n📋 3. 代理管理器测试")
    print("-" * 60)
    test_results["agent_manager"] = test_agent_manager(api_key)
    
    # 4. 测试单任务处理
    print("\n📋 4. 单任务处理测试")
    print("-" * 60)
    test_results["single_task"] = test_single_task_processing(api_key)
    
    # 5. 测试多专家协作
    print("\n📋 5. 多专家协作测试")
    print("-" * 60)
    test_results["collaboration"] = test_expert_collaboration(api_key)
    
    # 生成测试报告
    generate_stage2_report(test_results)
    
    return test_results

def test_base_agent(api_key):
    """测试基础代理类"""
    try:
        from core.llm_client import LLMClient
        from agents.base_agent import BaseAgent, AgentResponse, AgentTask
        
        # 创建简单的测试代理
        class TestAgent(BaseAgent):
            def _build_system_prompt(self):
                return "You are a test agent."
            
            def analyze(self, input_data):
                return AgentResponse(
                    agent_type=self.agent_type,
                    content="测试分析完成",
                    confidence=0.8,
                    reasoning="这是一个测试分析",
                    metadata={"test": True},
                    timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
                )
        
        # 创建LLM客户端和测试代理
        llm_client = LLMClient(model="deepseek-chat", api_key=api_key)
        test_agent = TestAgent("测试代理", llm_client, "测试专业")
        
        print("✅ 基础代理类初始化成功")
        print(f"   代理类型: {test_agent.agent_type}")
        print(f"   专业领域: {test_agent.specialization}")
        
        # 测试状态获取
        status = test_agent.get_status()
        print(f"   代理状态: {status}")
        
        # 测试任务处理
        test_task = AgentTask(
            task_id="test_001",
            task_type="test",
            input_data={"test_data": "hello"}
        )
        
        response = test_agent.process_task(test_task)
        
        if response and response.confidence > 0.5:
            print("✅ 基础代理任务处理成功")
            print(f"   响应内容: {response.content}")
            print(f"   置信度: {response.confidence}")
            return True
        else:
            print("❌ 基础代理任务处理失败")
            return False
            
    except Exception as e:
        print(f"❌ 基础代理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_technology_expert(api_key):
    """测试AI技术专家"""
    try:
        from core.llm_client import LLMClient
        from agents.expert_agents.ai_technology_expert import AITechnologyExpert
        from agents.base_agent import AgentTask
        
        # 创建LLM客户端和AI专家
        llm_client = LLMClient(model="deepseek-chat", temperature=0.2, api_key=api_key)
        ai_expert = AITechnologyExpert(llm_client)
        
        print("✅ AI技术专家初始化成功")
        print(f"   专业领域: {ai_expert.specialization}")
        print(f"   核心能力: {ai_expert.capabilities}")
        
        # 测试研究主题分析
        test_task = AgentTask(
            task_id="ai_test_001",
            task_type="research_analysis",
            input_data={
                "research_topic": "基于脉冲神经网络的视觉注意力机制",
                "requirements": ["技术可行性分析", "实现路径规划"]
            }
        )
        
        print("🔍 执行AI技术可行性分析...")
        response = ai_expert.process_task(test_task)
        
        if response and response.confidence > 0.7:
            print("✅ AI技术专家分析成功")
            print(f"   分析结果: {response.content}")
            print(f"   置信度: {response.confidence}")
            
            # 验证分析结果的结构
            if 'technical_analysis' in response.metadata or 'feasibility_analysis' in response.metadata:
                print("✅ 分析结果结构完整")
                return True
            else:
                print("⚠️ 分析结果结构不完整")
                return False
        else:
            print("❌ AI技术专家分析失败")
            print(f"   响应: {response.content if response else 'None'}")
            return False
            
    except Exception as e:
        print(f"❌ AI技术专家测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_manager(api_key):
    """测试代理管理器"""
    try:
        from core.llm_client import LLMClient
        from agents.agent_manager import AgentManager
        
        # 创建LLM客户端和代理管理器
        llm_client = LLMClient(model="deepseek-chat", api_key=api_key)
        manager = AgentManager(llm_client)
        
        print("✅ 代理管理器初始化成功")
        
        # 检查已注册的代理
        agents = manager.list_agents()
        print(f"   已注册代理数量: {len(agents)}")
        print(f"   代理列表: {list(agents.keys())}")
        
        # 测试系统状态
        status = manager.get_system_status()
        print(f"   系统状态: 代理数量={status['agent_manager']['registered_agents']}")
        
        if len(agents) > 0:
            print("✅ 代理管理器功能正常")
            return True
        else:
            print("❌ 代理管理器没有可用代理")
            return False
            
    except Exception as e:
        print(f"❌ 代理管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_single_task_processing(api_key):
    """测试单任务处理流程"""
    try:
        from core.llm_client import LLMClient
        from agents.agent_manager import AgentManager
        
        # 创建代理管理器
        llm_client = LLMClient(model="deepseek-chat", api_key=api_key)
        manager = AgentManager(llm_client)
        
        print("🎯 测试单任务分配和处理...")
        
        # 创建测试任务
        task = manager.create_task(
            task_type="AI技术分析",
            input_data={
                "research_topic": "神经网络压缩技术在边缘计算中的应用",
                "constraints": {
                    "latency": "< 100ms",
                    "memory": "< 50MB",
                    "accuracy": "> 90%"
                }
            },
            requirements=["技术可行性", "性能预测", "实现建议"],
            priority=3
        )
        
        print(f"✅ 任务创建成功: {task.task_id}")
        
        # 自动分配任务
        results = manager.auto_assign_task(task)
        
        if results:
            print(f"✅ 任务自动分配成功，{len(results)}个代理参与")
            
            for agent_id, response in results.items():
                print(f"   {agent_id}: 置信度={response.confidence:.2f}")
                
            # 检查结果质量
            avg_confidence = sum(r.confidence for r in results.values()) / len(results)
            if avg_confidence > 0.7:
                print(f"✅ 任务处理质量良好，平均置信度: {avg_confidence:.2f}")
                return True
            else:
                print(f"⚠️ 任务处理质量一般，平均置信度: {avg_confidence:.2f}")
                return False
        else:
            print("❌ 任务自动分配失败")
            return False
            
    except Exception as e:
        print(f"❌ 单任务处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_expert_collaboration(api_key):
    """测试多专家协作"""
    try:
        from core.llm_client import LLMClient
        from agents.agent_manager import AgentManager
        
        # 创建代理管理器
        llm_client = LLMClient(model="deepseek-chat", api_key=api_key)
        manager = AgentManager(llm_client)
        
        print("🤝 测试多专家协作分析...")
        
        # 创建复杂的协作任务
        task = manager.create_task(
            task_type="综合研究分析",
            input_data={
                "research_topic": "脑启发的多模态学习系统",
                "paper_content": """
                本文提出了一种新型的脑启发多模态学习架构，
                结合了视觉皮层的层次化处理机制和海马体的记忆整合功能。
                我们在ImageNet和COCO数据集上进行了实验，
                使用PyTorch框架实现，并与Transformer基线进行比较。
                实验结果显示在准确率和计算效率方面都有显著提升。
                """,
                "requirements": [
                    "技术创新性评估",
                    "实现复杂度分析", 
                    "应用前景预测"
                ]
            },
            priority=5
        )
        
        print(f"✅ 协作任务创建成功: {task.task_id}")
        
        # 执行协作分析
        collaboration_result = manager.collaborative_analysis(task)
        
        if collaboration_result and "error" not in collaboration_result:
            print("✅ 多专家协作分析成功")
            
            summary = collaboration_result.get("collaboration_summary", {})
            print(f"   参与专家: {summary.get('participating_experts', 0)}")
            print(f"   协作对数: {summary.get('collaboration_pairs', 0)}")
            print(f"   整体置信度: {summary.get('overall_confidence', 0)}")
            
            # 检查协作质量
            consensus_level = collaboration_result.get("consensus_level", 0)
            print(f"   专家共识水平: {consensus_level}")
            
            if consensus_level > 0.7:
                print("✅ 专家协作达成良好共识")
                return True
            else:
                print("⚠️ 专家协作共识水平一般")
                return True  # 仍然算成功，因为协作流程正常
        else:
            print("❌ 多专家协作分析失败")
            print(f"   错误信息: {collaboration_result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 专家协作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_stage2_report(test_results):
    """生成阶段2测试报告"""
    print("\n" + "📊 阶段2基础测试报告".center(80, "="))
    
    total_tests = len(test_results)
    passed_tests = sum(test_results.values())
    success_rate = passed_tests / total_tests * 100
    
    print(f"\n📈 总体测试结果:")
    print(f"   总测试项目: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    print(f"\n📋 详细测试结果:")
    test_names = {
        "base_agent": "基础代理类",
        "ai_expert": "AI技术专家",
        "agent_manager": "代理管理器",
        "single_task": "单任务处理",
        "collaboration": "多专家协作"
    }
    
    for key, name in test_names.items():
        status = "✅ 通过" if test_results[key] else "❌ 失败"
        print(f"   {status} {name}")
    
    print(f"\n🎯 阶段2进展评估:")
    if success_rate >= 90:
        print("🎉 阶段2基础功能完美实现！")
        print("✅ 可以开始扩展更多专家代理")
    elif success_rate >= 80:
        print("🎉 阶段2基础功能基本完成！")
        print("✅ 核心多专家系统已就绪")
    elif success_rate >= 60:
        print("⚠️ 阶段2基础功能部分完成")
        print("🔧 需要修复部分问题后继续")
    else:
        print("❌ 阶段2基础功能存在重要问题")
        print("🔧 需要重点解决核心功能问题")
    
    print(f"\n🚀 下一步建议:")
    if success_rate >= 80:
        print("   1. 扩展更多专家代理类型")
        print("   2. 完善推理引擎和知识融合")
        print("   3. 优化协作策略和性能")
    else:
        print("   1. 修复失败的测试项目")
        print("   2. 完善基础代理功能")
        print("   3. 调试协作和管理机制")
    
    print(f"\n⏰ 测试完成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)

if __name__ == "__main__":
    try:
        test_results = main()
        
        # 保存测试结果
        with open("stage2_test_results.json", "w", encoding="utf-8") as f:
            json.dump({
                "timestamp": time.strftime('%Y-%m-%d %H:%M:%S'),
                "stage": "阶段2基础测试",
                "results": test_results,
                "success_rate": sum(test_results.values()) / len(test_results) * 100
            }, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 测试结果已保存到: stage2_test_results.json")
        
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生严重错误: {e}")
        import traceback
        traceback.print_exc()
