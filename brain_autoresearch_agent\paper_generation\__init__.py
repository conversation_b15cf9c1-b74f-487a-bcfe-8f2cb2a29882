"""
Paper Generation Module for Brain-Inspired Intelligence Research

This module provides automated academic paper writing capabilities
specifically designed for brain-inspired intelligence research.

Key Components:
- BrainPaperWriter: Main paper generation orchestrator
- LaTeX template integration for professional formatting
- Multi-expert collaboration for comprehensive content
- Literature review automation using Semantic Scholar
- Advanced reasoning for paper structure design
- Professional quality optimization systems
"""

from .brain_paper_writer import BrainPaperWriter
from .improved_latex_generator import ImprovedLaTeXGenerator
from .literature_manager import LiteratureManager
from .latex_format_expert import LaTeXFormatExpert
from .enhanced_citation_manager import EnhancedCitationManager
from .multi_expert_review_system import MultiExpertReviewSystem
from .paper_quality_optimizer import PaperQualityOptimizer

__all__ = [
    'BrainPaperWriter',
    'ImprovedLaTeXGenerator', 
    'LiteratureManager',
    'LaTeXFormatExpert',
    'EnhancedCitationManager',
    'MultiExpertReviewSystem',
    'PaperQualityOptimizer'
]

__version__ = "2.0.0"
__author__ = "Brain-Inspired Intelligence Research Team"
