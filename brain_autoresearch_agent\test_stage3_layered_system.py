#!/usr/bin/env python3
"""
Stage 3 分层测试系统
分为Mock模式测试和真实API模式测试，确保完整的系统验证
"""

import os
import sys
import unittest
import tempfile
import shutil
from unittest.mock import Mock, patch
from typing import Dict, Any, List

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.unified_api_client import UnifiedAPIClient
from agents.agent_manager import AgentManager
from reasoning.enhanced_hypothesis_experiment_designer import EnhancedHypothesisExperimentDesigner
from core.experiment_code_generator import EnhancedExperimentCodeGenerator
from reasoning.enhanced_visualization_advisor import EnhancedVisualizationAdvisor
from reasoning.data_models import ResearchProblem, VisualizationChart, VisualizationPlan

class TestStage3LayeredSystem(unittest.TestCase):
    """Stage 3 分层测试系统"""
    
    @classmethod
    def setUpClass(cls):
        """测试类初始化"""
        print("🚀 启动 Stage 3 分层测试系统...")
        print("📋 测试模式: Mock模式 + 真实API模式")
        
        # 初始化测试目录
        cls.test_output_dir = tempfile.mkdtemp(prefix="stage3_layered_test_")
        
        # 测试计数器
        cls.passed_mock_tests = 0
        cls.passed_api_tests = 0
        cls.total_tests = 6  # 3个核心测试 x 2种模式
    
    @classmethod
    def tearDownClass(cls):
        """测试类清理"""
        # 清理测试目录
        if os.path.exists(cls.test_output_dir):
            shutil.rmtree(cls.test_output_dir)
        
        # 打印测试摘要
        print("\n" + "=" * 80)
        print(f"🧪 Stage 3 分层测试完成")
        print(f"✅ Mock模式通过: {cls.passed_mock_tests}/3")
        print(f"✅ 真实API模式通过: {cls.passed_api_tests}/3") 
        print(f"📊 总体通过率: {(cls.passed_mock_tests + cls.passed_api_tests)}/{cls.total_tests} ({((cls.passed_mock_tests + cls.passed_api_tests)/cls.total_tests)*100:.1f}%)")
        print("=" * 80)
    
    def _create_comprehensive_mock_client(self) -> Mock:
        """创建完善的Mock客户端"""
        mock_client = Mock()
        
        # 基本响应配置
        mock_response = self._get_mock_api_response()
        mock_client.generate_response = Mock(return_value=mock_response)
        mock_client.generate_response_with_json = Mock(return_value=(mock_response, {"test": "data"}))
        mock_client.available_models = {"text": ["deepseek-chat"], "vision": ["qwen-vl"]}
        
        # 解决Mock对象的len(), iter(), contains()问题
        mock_client.__len__ = Mock(return_value=len(mock_response))
        mock_client.__iter__ = Mock(return_value=iter(mock_response))
        mock_client.__contains__ = Mock(side_effect=lambda x: x in mock_response)
        
        return mock_client
    
    def _get_mock_api_response(self, response_type: str = "general") -> str:
        """获取模拟API响应"""
        responses = {
            "general": """
            {
                "experiment_design": {
                    "hypothesis": "Attention mechanisms can improve image classification",
                    "variables": ["attention_type", "learning_rate"],
                    "metrics": ["accuracy", "f1_score"],
                    "datasets": ["CIFAR-10", "ImageNet"]
                },
                "code_structure": {
                    "framework": "PyTorch",
                    "main_modules": ["attention_layer", "classifier", "trainer"],
                    "dependencies": ["torch", "torchvision", "numpy"]
                },
                "visualization": {
                    "charts": ["training_curves", "attention_heatmap"],
                    "tools": ["matplotlib", "seaborn"]
                }
            }
            """,
            "experiment": """
            基于注意力机制的实验设计建议：
            1. 使用多头注意力机制
            2. 对比不同注意力类型的效果
            3. 在CIFAR-10数据集上进行验证
            """,
            "visualization": """
            可视化建议：
            1. 绘制训练损失曲线
            2. 展示注意力权重热图
            3. 对比不同模型的性能指标
            """
        }
        return responses.get(response_type, responses["general"])
    
    def _create_real_api_client(self) -> UnifiedAPIClient:
        """创建真实API客户端（如果配置存在）"""
        try:
            api_client = UnifiedAPIClient()
            # 测试连接 - 使用正确的参数名
            response = api_client.get_text_response("测试连接", model_type="default")
            if response and response.content and len(response.content.strip()) > 0:
                return api_client
            else:
                self.skipTest("真实API客户端连接失败")
        except Exception as e:
            self.skipTest(f"真实API客户端初始化失败: {e}")
    
    # =============================================================================
    # Mock模式测试
    # =============================================================================
    
    def test_01_mock_hypothesis_designer(self):
        """测试1: Mock模式 - 假设实验设计器"""
        try:
            print("\n🧪 测试1: Mock模式 - 假设实验设计器")
            
            # 使用Mock客户端
            mock_client = self._create_comprehensive_mock_client()
            designer = EnhancedHypothesisExperimentDesigner(mock_client)
            
            # 创建测试研究问题
            research_problem = ResearchProblem(
                title="Attention for Classification",
                description="Test attention mechanisms"
            )
            
            # 设计实验
            experiment_design = designer.design_experiment_from_research_problem(research_problem)
            
            # 验证结果
            self.assertIsNotNone(experiment_design)
            print("✅ Mock模式假设实验设计器测试通过")
            TestStage3LayeredSystem.passed_mock_tests += 1
            
        except Exception as e:
            print(f"❌ Mock模式假设实验设计器测试失败: {e}")
            self.fail(f"Mock模式测试失败: {e}")
    
    def test_02_mock_code_generator(self):
        """测试2: Mock模式 - 代码生成器"""
        try:
            print("\n🧪 测试2: Mock模式 - 代码生成器")
            
            # 使用Mock客户端
            mock_client = self._create_comprehensive_mock_client()
            generator = EnhancedExperimentCodeGenerator(mock_client)
            
            # 生成实验规格并生成代码
            research_idea = "Test attention mechanism"
            
            # 先生成实验规格
            spec = generator.generate_experiment_specification_collaborative(research_idea)
            
            # 使用正确的方法名生成完整实验代码
            output_dir = os.path.join(self.test_output_dir, "test_code")
            code_files = generator.generate_complete_experiment(spec, output_dir)
            
            # 验证结果
            self.assertIsInstance(code_files, dict)
            self.assertGreater(len(code_files), 0)
            print("✅ Mock模式代码生成器测试通过")
            TestStage3LayeredSystem.passed_mock_tests += 1
            
        except Exception as e:
            print(f"❌ Mock模式代码生成器测试失败: {e}")
            self.fail(f"Mock模式测试失败: {e}")
    
    def test_03_mock_visualization_advisor(self):
        """测试3: Mock模式 - 可视化顾问"""
        try:
            print("\n🧪 测试3: Mock模式 - 可视化顾问")
            
            # 使用Mock客户端
            mock_client = self._create_comprehensive_mock_client()
            advisor = EnhancedVisualizationAdvisor(mock_client)
            
            # 创建测试实验结果
            experiment_results = {
                "accuracy": 0.95,
                "loss": [0.5, 0.3, 0.1],
                "epochs": [1, 2, 3]
            }
            
            # 生成基础可视化计划
            viz_plan = advisor.generate_visualization_plan_basic(experiment_results)
            
            # 验证结果
            self.assertIsNotNone(viz_plan)
            print("✅ Mock模式可视化顾问测试通过")
            TestStage3LayeredSystem.passed_mock_tests += 1
            
        except Exception as e:
            print(f"❌ Mock模式可视化顾问测试失败: {e}")
            self.fail(f"Mock模式测试失败: {e}")
    
    # =============================================================================
    # 真实API模式测试
    # =============================================================================
    
    def test_04_real_api_hypothesis_designer(self):
        """测试4: 真实API模式 - 假设实验设计器"""
        try:
            print("\n🧪 测试4: 真实API模式 - 假设实验设计器")
            
            # 使用真实API客户端
            api_client = self._create_real_api_client()
            designer = EnhancedHypothesisExperimentDesigner(api_client)
            
            # 创建测试研究问题
            research_problem = ResearchProblem(
                title="Attention Mechanism Optimization",
                description="Optimize attention mechanisms for image classification tasks",
                question="How can attention mechanisms be optimized?",
                hypothesis=["Multi-head attention improves performance"],
                background={"domain": "computer_vision", "context": "image classification"}
            )
            
            # 设计实验
            experiment_design = designer.design_experiment_from_research_problem(research_problem)
            
            # 验证结果
            self.assertIsNotNone(experiment_design)
            print("✅ 真实API模式假设实验设计器测试通过")
            TestStage3LayeredSystem.passed_api_tests += 1
            
        except Exception as e:
            if "skipTest" in str(e):
                self.skipTest(str(e))
            else:
                print(f"❌ 真实API模式假设实验设计器测试失败: {e}")
                self.fail(f"真实API模式测试失败: {e}")
    
    def test_05_real_api_code_generator(self):
        """测试5: 真实API模式 - 代码生成器"""
        try:
            print("\n🧪 测试5: 真实API模式 - 代码生成器")
            
            # 使用真实API客户端
            api_client = self._create_real_api_client()
            generator = EnhancedExperimentCodeGenerator(api_client)
            
            # 生成完整实验代码
            research_idea = "Multi-head attention mechanism for image classification"
            
            # 先生成实验规格
            spec = generator.generate_experiment_specification_collaborative(research_idea)
            
            # 生成完整实验代码
            output_dir = os.path.join(self.test_output_dir, "real_api_code")  
            code_files = generator.generate_complete_experiment(spec, output_dir)
            
            # 验证结果
            self.assertIsInstance(code_files, dict)
            self.assertGreater(len(code_files), 0)
            print("✅ 真实API模式代码生成器测试通过")
            TestStage3LayeredSystem.passed_api_tests += 1
            
        except Exception as e:
            if "skipTest" in str(e):
                self.skipTest(str(e))
            else:
                print(f"❌ 真实API模式代码生成器测试失败: {e}")
                self.fail(f"真实API模式测试失败: {e}")
    
    def test_06_real_api_visualization_advisor(self):
        """测试6: 真实API模式 - 可视化顾问"""
        try:
            print("\n🧪 测试6: 真实API模式 - 可视化顾问")
            
            # 使用真实API客户端  
            api_client = self._create_real_api_client()
            advisor = EnhancedVisualizationAdvisor(api_client)
            
            # 创建测试实验结果
            experiment_results = {
                "experiment_name": "attention_classification",
                "accuracy": 0.92,
                "precision": 0.89,
                "recall": 0.91,
                "training_loss": [0.8, 0.6, 0.4, 0.2, 0.1],
                "validation_loss": [0.9, 0.7, 0.5, 0.3, 0.2],
                "attention_weights": [[0.2, 0.3, 0.5], [0.1, 0.4, 0.5]]
            }
            
            # 生成基础可视化计划
            viz_plan = advisor.generate_visualization_plan_basic(experiment_results)
            
            # 验证结果
            self.assertIsNotNone(viz_plan)
            print("✅ 真实API模式可视化顾问测试通过")
            TestStage3LayeredSystem.passed_api_tests += 1
            
        except Exception as e:
            if "skipTest" in str(e):
                self.skipTest(str(e))
            else:
                print(f"❌ 真实API模式可视化顾问测试失败: {e}")
                self.fail(f"真实API模式测试失败: {e}")

if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
