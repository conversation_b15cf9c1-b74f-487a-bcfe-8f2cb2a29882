#!/usr/bin/env python3
"""
Stage 3 快速验证测试 - 验证核心组件能否正常工作
"""

import sys
import os

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def test_stage3_components():
    """快速测试Stage 3的核心组件"""
    print("🧪 Stage 3 快速验证开始...")
    
    success_count = 0
    total_tests = 6
    
    # 1. 测试统一API客户端导入
    try:
        from core.unified_api_client import UnifiedAPIClient
        print("✅ 1/6: 统一API客户端导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 1/6: 统一API客户端导入失败 - {e}")
    
    # 2. 测试增强多专家协作器导入
    try:
        from reasoning.enhanced_multi_agent_collaborator import EnhancedMultiAgentCollaborator
        print("✅ 2/6: 增强多专家协作器导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 2/6: 增强多专家协作器导入失败 - {e}")
    
    # 3. 测试增强实验设计器导入
    try:
        from reasoning.enhanced_hypothesis_experiment_designer import EnhancedHypothesisExperimentDesigner
        print("✅ 3/6: 增强实验设计器导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 3/6: 增强实验设计器导入失败 - {e}")
    
    # 4. 测试增强代码生成器导入
    try:
        from core.experiment_code_generator import EnhancedExperimentCodeGenerator
        print("✅ 4/6: 增强代码生成器导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 4/6: 增强代码生成器导入失败 - {e}")
    
    # 5. 测试增强可视化顾问导入
    try:
        from reasoning.enhanced_visualization_advisor import EnhancedVisualizationAdvisor
        print("✅ 5/6: 增强可视化顾问导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 5/6: 增强可视化顾问导入失败 - {e}")
    
    # 6. 测试数据模型完整性
    try:
        from reasoning.data_models import (
            CollaborationSession, DiscussionRound, CollaborationResult,
            ExperimentPlan, VisualizationPlan
        )
        from core.experiment_code_generator import ExperimentSpecification
        print("✅ 6/6: 数据模型完整性验证成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 6/6: 数据模型完整性验证失败 - {e}")
    
    # 总结
    print(f"\n📊 Stage 3 快速验证结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 Stage 3 所有组件导入成功！系统准备就绪")
        
        # 尝试简单的初始化测试
        try:
            print("\n🔧 尝试初始化核心组件...")
            from unittest.mock import Mock
            
            mock_client = Mock()
            mock_client.generate_response.return_value = "Mock response"
            
            # 初始化测试
            designer = EnhancedHypothesisExperimentDesigner(mock_client)
            generator = EnhancedExperimentCodeGenerator(mock_client)
            advisor = EnhancedVisualizationAdvisor(mock_client)
            
            print("✅ 核心组件初始化成功")
            print("\n🚀 Stage 3 系统完全就绪！可以进行完整测试")
            return True
            
        except Exception as e:
            print(f"⚠️  组件初始化遇到问题: {e}")
            print("但导入测试全部通过，系统基本就绪")
            return True
    else:
        print("❌ 存在导入问题，需要修复")
        return False

if __name__ == "__main__":
    success = test_stage3_components()
    sys.exit(0 if success else 1)
