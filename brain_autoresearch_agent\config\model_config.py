"""
模型配置管理
处理不同LLM客户端的配置和兼容性
"""

import os
from typing import Dict, List, Optional

# 支持的模型映射
MODEL_MAPPINGS = {
    'deepseek-chat': {
        'preferred_client': 'deepseek',
        'fallback_models': ['gpt-4o-mini', 'claude-3-5-sonnet-20241022'],
        'description': 'DeepSeek Chat Model'
    },
    'gpt-4o-mini': {
        'preferred_client': 'openai', 
        'fallback_models': ['claude-3-5-sonnet-20241022'],
        'description': 'OpenAI GPT-4o Mini'
    },
    'claude-3-5-sonnet-20241022': {
        'preferred_client': 'anthropic',
        'fallback_models': ['gpt-4o-mini'],
        'description': 'Anthropic Claude 3.5 Sonnet'
    }
}

# 默认模型优先级
DEFAULT_MODEL_PRIORITY = [
    'gpt-4o-mini',
    'claude-3-5-sonnet-20241022', 
    'claude-3-5-sonnet-20240620'
]


class ModelConfigManager:
    """模型配置管理器"""
    
    def __init__(self):
        self.available_models = []
        self.selected_model = None
        self.client_type = None
        
    def detect_available_models(self, ai_scientist_models: List[str] = None) -> List[str]:
        """
        检测可用的模型
        
        Args:
            ai_scientist_models: AI Scientist支持的模型列表
            
        Returns:
            可用模型列表
        """
        available = []
        
        # 检查AI Scientist支持的模型
        if ai_scientist_models:
            available.extend(ai_scientist_models)
        
        # 检查环境变量中的API密钥
        if os.getenv('DEEPSEEK_API_KEY'):
            available.append('deepseek-chat')
            
        if os.getenv('OPENAI_API_KEY'):
            available.extend(['gpt-4o', 'gpt-4o-mini'])
            
        if os.getenv('ANTHROPIC_API_KEY'):
            available.extend(['claude-3-5-sonnet-20241022', 'claude-3-5-sonnet-20240620'])
        
        self.available_models = list(set(available))
        return self.available_models
    
    def select_best_model(self, requested_model: str = None) -> Dict[str, str]:
        """
        选择最佳可用模型
        
        Args:
            requested_model: 用户请求的模型
            
        Returns:
            模型选择结果
        """
        result = {
            'selected_model': None,
            'client_type': 'mock',
            'status': 'fallback',
            'message': ''
        }
        
        # 如果请求的模型可用
        if requested_model and requested_model in self.available_models:
            result.update({
                'selected_model': requested_model,
                'client_type': self._get_client_type(requested_model),
                'status': 'exact_match',
                'message': f'✅ 使用请求的模型: {requested_model}'
            })
            return result
        
        # 查找最佳替代模型
        for priority_model in DEFAULT_MODEL_PRIORITY:
            if priority_model in self.available_models:
                result.update({
                    'selected_model': priority_model,
                    'client_type': self._get_client_type(priority_model),
                    'status': 'alternative',
                    'message': f'🔄 使用替代模型: {priority_model} (请求的 {requested_model} 不可用)'
                })
                return result
        
        # 如果有任何可用模型
        if self.available_models:
            selected = self.available_models[0]
            result.update({
                'selected_model': selected,
                'client_type': self._get_client_type(selected),
                'status': 'available',
                'message': f'🔄 使用可用模型: {selected}'
            })
            return result
        
        # 完全没有可用模型，使用模拟模式
        result.update({
            'selected_model': 'mock-model',
            'client_type': 'mock',
            'status': 'mock',
            'message': '⚠️ 无可用模型，使用模拟模式'
        })
        
        return result
    
    def _get_client_type(self, model: str) -> str:
        """获取模型对应的客户端类型"""
        if model in MODEL_MAPPINGS:
            return MODEL_MAPPINGS[model]['preferred_client']
        elif 'deepseek' in model.lower():
            return 'deepseek'
        elif 'gpt' in model.lower() or 'openai' in model.lower():
            return 'openai'
        elif 'claude' in model.lower():
            return 'anthropic'
        else:
            return 'ai_scientist'
    
    def get_model_info(self, model: str) -> Dict[str, str]:
        """获取模型信息"""
        return MODEL_MAPPINGS.get(model, {
            'preferred_client': 'unknown',
            'fallback_models': [],
            'description': f'Unknown model: {model}'
        })


def create_model_config(config_path: Optional[str] = None) -> ModelConfigManager:
    """
    创建智能模型配置管理器
    
    Args:
        config_path: 可选的配置文件路径
        
    Returns:
        ModelConfigManager实例
    """
    return ModelConfigManager()


def get_model_config(requested_model: str = 'deepseek-chat', 
                    ai_scientist_models: List[str] = None) -> Dict[str, str]:
    """
    获取模型配置（保持向后兼容）
    
    Args:
        requested_model: 请求的模型名称
        ai_scientist_models: AI Scientist支持的模型列表
        
    Returns:
        模型配置结果
    """
    config_manager = ModelConfigManager()
    config_manager.detect_available_models(ai_scientist_models)
    return config_manager.select_best_model(requested_model)
