"""
系统诊断和问题修复方案
"""

import os
import sys

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)


def diagnose_system_issues():
    """诊断当前系统存在的问题"""
    
    print("🔍 系统诊断报告")
    print("=" * 80)
    
    issues = []
    recommendations = []
    
    # 1. 检查LLM模式
    print("\n📋 1. LLM客户端状态检查")
    try:
        from core.llm_client import LLMClient
        client = LLMClient()
        
        if hasattr(client, 'deepseek_mode') and client.deepseek_mode:
            print("✅ DeepSeek API模式：真实API调用")
        elif hasattr(client, 'ai_scientist_mode') and client.ai_scientist_mode:
            print("✅ AI Scientist模式：真实API调用")  
        else:
            print("⚠️ 模拟模式：使用预设响应")
            issues.append("LLM处于模拟模式，未进行真实API调用")
            recommendations.append("配置DEEPSEEK_API_KEY环境变量或其他API密钥")
    except Exception as e:
        print(f"❌ LLM客户端检查失败: {e}")
        issues.append(f"LLM客户端初始化错误: {e}")
    
    # 2. 检查专家代理
    print("\n📋 2. 专家代理状态检查")
    try:
        from agents.agent_manager import AgentManager
        from core.llm_client import LLMClient
        
        client = LLMClient()
        manager = AgentManager(client)
        
        print(f"📊 当前注册的专家: {list(manager.agents.keys())}")
        
        # 检查应该有的专家
        expected_experts = [
            'ai_technology', 'neuroscience', 'data_analysis', 
            'experiment_design', 'paper_writing'
        ]
        
        missing_experts = [expert for expert in expected_experts if expert not in manager.agents]
        if missing_experts:
            print(f"⚠️ 缺失的专家: {missing_experts}")
            issues.append(f"专家代理不完整，缺失: {missing_experts}")
            recommendations.append("注册所有专业领域专家代理")
        else:
            print("✅ 所有专家代理已注册")
            
    except Exception as e:
        print(f"❌ 专家代理检查失败: {e}")
        issues.append(f"专家代理系统错误: {e}")
    
    # 3. 检查交付物质量
    print("\n📋 3. 交付物质量检查")
    try:
        from pathlib import Path
        output_dir = Path("./output")
        
        if output_dir.exists():
            session_files = list(output_dir.glob("reasoning_session_*_full_session.json"))
            if session_files:
                latest_session = max(session_files, key=os.path.getctime)
                print(f"📄 最新会话文件: {latest_session.name}")
                
                # 检查文件大小和内容
                file_size = latest_session.stat().st_size
                print(f"📏 文件大小: {file_size} 字节")
                
                if file_size < 1000:  # 小于1KB
                    issues.append("会话文件过小，可能内容不完整")
                    recommendations.append("检查推理流程是否正常执行")
                
                # 检查是否有实际的专家意见
                import json
                with open(latest_session, 'r', encoding='utf-8') as f:
                    session_data = json.load(f)
                
                expert_opinions = session_data.get('research_problem', {}).get('expert_opinions', [])
                if not expert_opinions:
                    issues.append("缺乏真实的专家意见，可能使用模拟数据")
                    recommendations.append("确保专家代理正常工作并生成真实评估")
                else:
                    print(f"✅ 包含 {len(expert_opinions)} 个专家意见")
            else:
                issues.append("没有找到推理会话文件")
        else:
            issues.append("输出目录不存在")
    except Exception as e:
        print(f"❌ 交付物检查失败: {e}")
        issues.append(f"交付物检查错误: {e}")
    
    # 4. 检查API配置
    print("\n📋 4. API配置检查")
    api_keys = [
        ("DEEPSEEK_API_KEY", "DeepSeek API"),
        ("OPENAI_API_KEY", "OpenAI API"),
        ("ANTHROPIC_API_KEY", "Anthropic API")
    ]
    
    configured_apis = 0
    for env_var, api_name in api_keys:
        if os.getenv(env_var):
            print(f"✅ {api_name}: 已配置")
            configured_apis += 1
        else:
            print(f"❌ {api_name}: 未配置")
    
    if configured_apis == 0:
        issues.append("未配置任何API密钥")
        recommendations.append("至少配置一个API密钥以启用真实LLM调用")
    
    # 总结
    print(f"\n📊 诊断总结")
    print("-" * 50)
    print(f"🔴 发现问题数量: {len(issues)}")
    print(f"💡 改进建议数量: {len(recommendations)}")
    
    if issues:
        print(f"\n🚨 主要问题:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
    
    if recommendations:
        print(f"\n💡 改进建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")
    
    return issues, recommendations


def create_enhanced_agent_manager():
    """创建增强的代理管理器，注册所有专家"""
    
    print("\n🔧 创建增强代理管理器")
    print("-" * 50)
    
    try:
        from agents.agent_manager import AgentManager
        from agents.expert_agents.ai_technology_expert import AITechnologyExpert
        from agents.expert_agents.neuroscience_expert import NeuroscienceExpert
        from agents.expert_agents.data_analysis_expert import DataAnalysisExpert
        from agents.expert_agents.experiment_design_expert import ExperimentDesignExpert
        from agents.expert_agents.paper_writing_expert import PaperWritingExpert
        from core.llm_client import LLMClient
        
        # 初始化LLM客户端
        llm_client = LLMClient()
        
        # 创建代理管理器（不使用默认初始化）
        manager = AgentManager(llm_client)
        manager.agents.clear()  # 清空默认注册的代理
        
        # 注册所有专家
        experts = [
            ("ai_technology", AITechnologyExpert),
            ("neuroscience", NeuroscienceExpert),
            ("data_analysis", DataAnalysisExpert),
            ("experiment_design", ExperimentDesignExpert),
            ("paper_writing", PaperWritingExpert)
        ]
        
        successfully_registered = []
        failed_registrations = []
        
        for expert_id, expert_class in experts:
            try:
                expert = expert_class(llm_client)
                manager.register_agent(expert_id, expert)
                successfully_registered.append(expert_id)
            except Exception as e:
                print(f"❌ {expert_id} 注册失败: {e}")
                failed_registrations.append((expert_id, str(e)))
        
        print(f"\n✅ 成功注册专家: {successfully_registered}")
        if failed_registrations:
            print(f"❌ 注册失败: {[f[0] for f in failed_registrations]}")
        
        return manager, successfully_registered, failed_registrations
        
    except Exception as e:
        print(f"❌ 增强代理管理器创建失败: {e}")
        return None, [], [(f"system_error", str(e))]


def test_real_llm_calls():
    """测试真实的LLM调用"""
    
    print("\n🧪 测试真实LLM调用")
    print("-" * 50)
    
    try:
        from core.llm_client import LLMClient
        
        # 检查不同的初始化方式
        test_cases = [
            ("默认初始化", {}),
            ("DeepSeek Chat", {"model": "deepseek-chat"}),
            ("DeepSeek Reasoner", {"model": "deepseek-reasoner"}),
            ("GPT-4o Mini", {"model": "gpt-4o-mini"}),
        ]
        
        for test_name, kwargs in test_cases:
            print(f"\n🔍 测试: {test_name}")
            try:
                client = LLMClient(**kwargs)
                
                # 检查模式
                if hasattr(client, 'deepseek_mode') and client.deepseek_mode:
                    print(f"  ✅ DeepSeek API模式")
                    api_status = "真实API"
                elif hasattr(client, 'ai_scientist_mode') and client.ai_scientist_mode:
                    print(f"  ✅ AI Scientist模式") 
                    api_status = "真实API"
                else:
                    print(f"  ⚠️ 模拟模式")
                    api_status = "模拟"
                
                # 尝试简单调用
                response = client.get_response("Hello, test message")
                response_text = response[0] if isinstance(response, tuple) else response
                print(f"  📝 响应长度: {len(response_text)} 字符")
                print(f"  🎯 API状态: {api_status}")
                
            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
        
    except Exception as e:
        print(f"❌ LLM测试失败: {e}")


def create_realistic_demo():
    """创建真实场景演示"""
    
    print("\n🎯 创建真实场景演示")
    print("-" * 50)
    
    # 具体的脑启发智能研究问题
    research_scenarios = [
        {
            "question": "如何设计基于海马体记忆机制的持续学习神经网络？",
            "hypothesis": [
                "海马体的双重编码机制可以解决神经网络的灾难性遗忘问题",
                "快速学习系统和慢速学习系统的协同可以实现更好的知识保持",
                "模式分离和模式完成机制能够提高新旧知识的整合效果"
            ],
            "background": {
                "domain": "continual learning",
                "motivation": "现有神经网络在学习新任务时会忘记旧任务",
                "related_work": ["Elastic Weight Consolidation", "Progressive Neural Networks", "Memory Replay"],
                "biological_basis": "海马体CA3-CA1回路的记忆编码和检索机制"
            }
        },
        {
            "question": "基于前额叶皮层工作记忆的注意力机制如何改进Transformer？",
            "hypothesis": [
                "工作记忆的有限容量和动态更新机制可以提高注意力效率",
                "前额叶的抑制控制机制能够减少注意力的计算复杂度",
                "多模态工作记忆整合可以增强跨模态推理能力"
            ],
            "background": {
                "domain": "attention mechanisms",
                "motivation": "Transformer的注意力机制计算复杂度过高",
                "related_work": ["Sparse Attention", "Linear Attention", "Memory-efficient Transformers"],
                "biological_basis": "前额叶皮层的工作记忆网络和认知控制"
            }
        }
    ]
    
    return research_scenarios


if __name__ == "__main__":
    # 运行系统诊断
    issues, recommendations = diagnose_system_issues()
    
    # 创建增强代理管理器
    enhanced_manager, success_experts, failed_experts = create_enhanced_agent_manager()
    
    # 测试真实LLM调用
    test_real_llm_calls()
    
    # 提供解决方案
    print(f"\n🎯 解决方案建议")
    print("=" * 80)
    
    print(f"\n1. **配置真实API密钥**")
    print(f"   - 获取DeepSeek API密钥: https://platform.deepseek.com/")
    print(f"   - 设置环境变量: set DEEPSEEK_API_KEY=your_key_here")
    print(f"   - 或者配置OpenAI/Anthropic API密钥")
    
    print(f"\n2. **启用完整专家团队**")
    print(f"   - 已检测到5个专家代理模块")
    print(f"   - 需要修复代理注册逻辑以包含所有专家")
    
    print(f"\n3. **使用真实研究场景**") 
    scenarios = create_realistic_demo()
    print(f"   - 提供了{len(scenarios)}个真实的脑启发智能研究场景")
    print(f"   - 包含具体的生物学基础和技术细节")
    
    print(f"\n4. **验证交付物质量**")
    print(f"   - 启用真实API后重新运行推理流程")
    print(f"   - 检查专家意见的多样性和深度")
    print(f"   - 验证实验设计的可执行性")
    
    print(f"\n🎊 诊断完成！按照建议修复后可获得高质量的真实输出。")
