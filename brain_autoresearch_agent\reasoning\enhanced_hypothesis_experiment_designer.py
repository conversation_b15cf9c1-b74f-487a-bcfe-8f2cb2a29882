"""
增强假设到实验设计器
集成多专家协作，基于文献分析生成高质量实验设计方案
"""

import os
import sys
import json
import time
import logging
import uuid
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.unified_api_client import UnifiedAPIClient
from reasoning.data_models import ResearchProblem, ExperimentPlan
from agents.agent_manager import AgentManager


class EnhancedHypothesisExperimentDesigner:
    """增强假设到实验设计器"""
    
    def __init__(self, unified_client=None):
        """
        Initialize the enhanced hypothesis experiment designer
        
        Args:
            unified_client: Unified API client for LLM interaction
        """
        self.unified_client = unified_client
        self.logger = logging.getLogger(self.__class__.__name__)
        self.agent_manager = AgentManager(unified_client)
        
        # 实验设计原则
        self.design_principles = {
            "validity": "确保实验能够有效验证假设",
            "reliability": "保证实验结果的可重现性",
            "controllability": "控制无关变量的影响",
            "measurability": "选择可量化的评估指标",
            "feasibility": "考虑实际实施的可行性"
        }
    
    def design_experiment(self, research_problem: ResearchProblem, papers=None) -> ExperimentPlan:
        """
        设计实验方案
        
        Args:
            research_problem: 研究问题
            papers: 可选，相关论文列表，用于参考实验设计
            
        Returns:
            ExperimentPlan: 实验计划，永不为None
        """
        print(f"🔬 开始设计实验方案...")
        # 1. 生成假设
        hypothesis = self._generate_hypothesis(research_problem)
        if not hypothesis:
            print("❌ Hypothesis generation failed, using default hypothesis.")
            hypothesis = f"The proposed method will outperform baseline approaches on {research_problem.question}"  # fallback
        # 2. 设计实验变量
        variables = self._design_experiment_variables(research_problem, hypothesis)
        # 3. 选择评估指标
        metrics = self._select_evaluation_metrics(research_problem, "experimental")
        # 4. 创建实验计划
        try:
            experiment_plan = ExperimentPlan(
                research_problem=research_problem,
                research_question=research_problem.question,
                hypothesis=[hypothesis] if isinstance(hypothesis, str) else hypothesis,
                variables=variables[:3] if variables else [],  # limit variable count
                metrics=metrics[:5] if metrics else [],  # limit metric count
                methodology="controlled_experiment",
                expected_outcomes=["Demonstrate the effectiveness of the proposed method."],
                evaluation_criteria={"significance_test": "p<0.05"}
            )
            print(f"✅ Experiment design completed: {len(variables)} variables, {len(metrics)} metrics.")
            return experiment_plan
        except Exception as e:
            print(f"❌ ExperimentPlan construction failed: {str(e)}. Using default fallback.")
            # fallback default plan
            return ExperimentPlan(
                research_problem=research_problem,
                research_question=research_problem.question,
                hypothesis=[hypothesis] if isinstance(hypothesis, str) else hypothesis,
                variables=[
                    {"name": "method_type", "type": "independent"},
                    {"name": "performance", "type": "dependent"}
                ],
                metrics=["accuracy", "efficiency"],
                methodology="controlled_experimental_comparison",
                expected_outcomes=["Improved performance over baselines"],
                evaluation_criteria={"significance_test": "p<0.05"}
            )
    
    def _generate_hypothesis(self, research_problem: ResearchProblem) -> str:
        """Generate a research hypothesis in English."""
        try:
            if hasattr(research_problem, 'background') and research_problem.background:
                if isinstance(research_problem.background, dict):
                    background = research_problem.background.get("description", "AI science research")
                else:
                    background = str(research_problem.background)
            else:
                background = "AI science research"
        except (AttributeError, TypeError):
            background = "AI science research"

        hypothesis_prompt = (
            "You are an expert AI scientist. Based on the following research problem, generate a clear, testable hypothesis. "
            "The hypothesis should be specific, measurable, and experimentally verifiable. "
            "Include clear success/failure criteria. "
            f"\nResearch Question: {research_problem.question}"
            f"\nBackground: {background}"
            "\nReturn only the hypothesis statement in English."
        )
        try:
            print("   🧠 Generating research hypothesis...")
            response = self.unified_client.get_text_response(
                prompt=hypothesis_prompt,
                model_type="chat",
                temperature=0.3,
                max_tokens=400
            )
            
            # 处理不同类型的响应
            if hasattr(response, 'success') and hasattr(response, 'content'):
                # APIResponse对象
                if response.success and response.content:
                    print("   ✅ Hypothesis generated.")
                    return response.content.strip()
            elif isinstance(response, str):
                # 直接返回字符串
                print("   ✅ Hypothesis generated.")
                return response.strip()
            elif isinstance(response, dict) and 'content' in response:
                # 返回字典包含content
                print("   ✅ Hypothesis generated.")
                return response['content'].strip()
            
            print("   ⚠️ Response format not recognized, using default hypothesis.")
            
        except Exception as e:
            print(f"   ❌ Hypothesis generation failed: {str(e)}")
        print("   📋 Using default hypothesis.")
        return f"The proposed method will outperform baseline approaches on {research_problem.question}"
    
    def _design_experiment_variables(self, 
                                   research_problem: ResearchProblem,
                                   hypothesis: str) -> List[Dict[str, Any]]:
        """设计实验变量"""
        
        # 安全地获取background
        try:
            if hasattr(research_problem, 'background') and research_problem.background:
                if isinstance(research_problem.background, dict):
                    domain = research_problem.background.get("domain", "machine learning")
                else:
                    domain = "machine learning"
            else:
                domain = "machine learning"
        except (AttributeError, TypeError):
            domain = "machine learning"
        
        variable_design_prompt = f"""
Design experiment variables for the following research:

Domain: {domain}
Research Question: {research_problem.question}
Hypothesis: {hypothesis}

Design independent and dependent variables:
1. Independent variables (controllable factors)
2. Dependent variables (measurements)
3. Control variables (kept constant)

Output format: {{"variables": [{{"name": "var_name", "type": "independent|dependent|control", "description": "description", "possible_values": "range_or_options"}}]}}
"""
        
        try:
            print("   🔧 正在设计实验变量...")
            response = self.unified_client.get_text_response(
                prompt=variable_design_prompt,
                model_type="chat",
                temperature=0.4,
                max_tokens=800
            )
            
            # 处理不同类型的响应
            content = None
            if hasattr(response, 'success') and hasattr(response, 'content'):
                # APIResponse对象
                if response.success:
                    content = response.content
            elif isinstance(response, str):
                # 直接返回字符串
                content = response
            elif isinstance(response, dict) and 'content' in response:
                # 返回字典包含content
                content = response['content']
                
            if content:
                print("   ✅ 变量设计完成")
                variables = self.unified_client.extract_json(content)
                if isinstance(variables, list):
                    return variables
                elif isinstance(variables, dict) and variables.get("variables"):
                    return variables["variables"]
            
        except Exception as e:
            print(f"   ❌ 变量设计失败，使用默认设计: {e}")
        
        print("   📋 使用默认变量设计")
        # 默认变量设计
        return [
            {
                "name": "proposed_method",
                "type": "independent", 
                "description": "新提出的方法",
                "possible_values": "method_variants"
            },
            {
                "name": "baseline_method",
                "type": "control",
                "description": "基准方法",
                "possible_values": "standard_baselines"
            },
            {
                "name": "performance_score",
                "type": "dependent",
                "description": "性能评估指标",
                "possible_values": "continuous",
                "measurement_method": "quantitative_analysis"
            }
        ]
    
    def _select_evaluation_metrics(self, 
                                 research_problem: ResearchProblem,
                                 experiment_type: str) -> List[str]:
        """选择评估指标"""
        
        # 安全地获取domain，处理background可能是字符串的情况
        try:
            if hasattr(research_problem, 'background') and research_problem.background:
                if isinstance(research_problem.background, dict):
                    domain = research_problem.background.get("domain", "machine learning")
                else:
                    domain = "machine learning"
            else:
                domain = "machine learning"
        except (AttributeError, TypeError):
            domain = "machine learning"
        
        metrics_prompt = f"""
Select appropriate evaluation metrics for the following research:

Domain: {domain}
Research Question: {research_problem.question}
Experiment Type: {experiment_type}

Recommend the most suitable evaluation metrics considering:
1. Accuracy metrics (accuracy, precision, recall, F1-score)
2. Efficiency metrics (training time, inference speed, memory usage)
3. Quality metrics (robustness, generalization, interpretability)
4. Domain-specific metrics

Output format: {{"metrics": ["metric1", "metric2", ...], "reasoning": "selection_reasoning"}}
"""
        
        try:
            print("   📊 正在选择评估指标...")
            response = self.unified_client.get_text_response(
                prompt=metrics_prompt,
                model_type="chat",
                temperature=0.2,
                max_tokens=500
            )
            
            # 处理不同类型的响应
            content = None
            if hasattr(response, 'success') and hasattr(response, 'content'):
                # APIResponse对象
                if response.success:
                    content = response.content
            elif isinstance(response, str):
                # 直接返回字符串
                content = response
            elif isinstance(response, dict) and 'content' in response:
                # 返回字典包含content
                content = response['content']
                
            if content:
                print("   ✅ 评估指标选择完成")
                result = self.unified_client.extract_json(content)
                if result and result.get("metrics"):
                    return result["metrics"]
            
        except Exception as e:
            print(f"   ❌ 指标选择失败，使用默认指标: {e}")
        
        print("   📋 使用默认评估指标")
        # 默认指标
        return [
            "accuracy",
            "f1_score", 
            "training_time",
            "memory_usage",
            "generalization_score"
        ]
    
    def generate_implementation_plan(self, research_problem, experiment_plan) -> Dict[str, Any]:
        """
        Generate detailed implementation plan for experiment
        
        Args:
            research_problem: Research problem definition
            experiment_plan: Experiment plan
            
        Returns:
            Implementation plan details including frameworks, steps, and resource requirements
        """
        print(f"🔧 Generating implementation plan...")
        try:
            # Get AI technology expert for implementation details
            ai_expert = self.agent_manager.get_agent('ai_technology')
            
            # Prepare prompt
            problem_str = research_problem.question if hasattr(research_problem, 'question') else str(research_problem)
            
            # Extract experiment details
            hypothesis = ""
            if hasattr(experiment_plan, 'hypothesis'):
                if isinstance(experiment_plan.hypothesis, list):
                    hypothesis = "; ".join(experiment_plan.hypothesis[:2])
                else:
                    hypothesis = str(experiment_plan.hypothesis)
            
            # Format methodologies
            methodology = ""
            if hasattr(experiment_plan, 'methodology'):
                methodology = experiment_plan.methodology
            
            # Prepare implementation prompt
            prompt = f"""
            Create a detailed implementation plan for the following experiment:
            
            RESEARCH PROBLEM: {problem_str}
            HYPOTHESIS: {hypothesis}
            METHODOLOGY: {methodology}
            
            Please provide a structured implementation plan including:
            1. Recommended frameworks and libraries
            2. Step-by-step implementation instructions
            3. Resource requirements (compute, memory, storage, time)
            4. Technical considerations
            
            Format your response as a JSON with the following structure:
            {{
                "frameworks": ["framework1", "framework2"],
                "implementation_steps": [
                    {{"step": 1, "description": "step description"}},
                    {{"step": 2, "description": "step description"}}
                ],
                "resources": {{
                    "compute": "description",
                    "memory": "description",
                    "storage": "description",
                    "time": "description"
                }},
                "technical_considerations": ["consideration1", "consideration2"]
            }}
            """
            
            if ai_expert:
                # Get implementation plan from AI expert
                response = ai_expert.analyze({
                    "input_text": prompt,
                    "analysis_type": "implementation_planning"
                })
                
                # Extract response content
                if hasattr(response, 'content'):
                    content = response.content
                else:
                    content = str(response)
                
                # Try to extract JSON
                try:
                    # Find JSON pattern
                    import re
                    json_match = re.search(r'\{[\s\S]*\}', content)
                    if json_match:
                        plan_json = json.loads(json_match.group(0))
                        return plan_json
                except:
                    pass
            
            # Fallback: Create a generic implementation plan
            return self._create_default_implementation_plan(research_problem, experiment_plan)
            
        except Exception as e:
            print(f"❌ Failed to generate implementation plan: {e}")
            return self._create_default_implementation_plan(research_problem, experiment_plan)
    
    def _create_default_implementation_plan(self, research_problem, experiment_plan) -> Dict[str, Any]:
        """Create a default implementation plan if the AI expert fails"""
        return {
            "frameworks": ["PyTorch", "NumPy", "scikit-learn"],
            "implementation_steps": [
                {"step": 1, "description": "Set up the development environment"},
                {"step": 2, "description": "Implement data preprocessing pipeline"},
                {"step": 3, "description": "Develop the core algorithm/model"},
                {"step": 4, "description": "Implement evaluation metrics"},
                {"step": 5, "description": "Train and validate the model"},
                {"step": 6, "description": "Analyze and visualize results"}
            ],
            "resources": {
                "compute": "Standard GPU workstation (NVIDIA RTX 3080 or equivalent)",
                "memory": "32GB RAM minimum",
                "storage": "100GB for dataset and model checkpoints",
                "time": "Approximately 2-3 weeks for implementation and testing"
            },
            "technical_considerations": [
                "Ensure reproducibility by setting random seeds",
                "Implement proper logging and checkpointing",
                "Use version control for code management",
                "Consider parallelization for efficiency"
            ]
        }
    
    def generate_experiment_code(self, experiment_plan, implementation_plan=None) -> Dict[str, Any]:
        """
        Generate code structure for experiment implementation
        
        Args:
            experiment_plan: Experiment plan details
            implementation_plan: Optional implementation plan details
            
        Returns:
            Code structure information including files and sample code
        """
        print(f"💻 Generating experiment code structure...")
        try:
            # Determine which framework to use
            frameworks = ["PyTorch"]  # Default
            if implementation_plan and "frameworks" in implementation_plan:
                frameworks = implementation_plan["frameworks"]
            
            framework = frameworks[0] if frameworks else "PyTorch"
            
            # Get AI expert for code generation
            ai_expert = self.agent_manager.get_agent('ai_technology')
            
            if ai_expert:
                # Prepare prompt for code generation
                prompt = f"""
                Generate a code structure for implementing experiments with {framework}.
                
                The experiment is about:
                {experiment_plan.research_question if hasattr(experiment_plan, 'research_question') else 'advanced AI research'}
                
                Please provide:
                1. List of necessary files
                2. Sample code snippets for key components
                3. Main entry point structure
                
                Format your response as a JSON with:
                {{
                    "files": [
                        {{"filename": "file1.py", "description": "description"}},
                        {{"filename": "file2.py", "description": "description"}}
                    ],
                    "code_samples": {{
                        "model_definition": "code here",
                        "training_loop": "code here",
                        "evaluation": "code here"
                    }},
                    "entry_point": "command to run experiment"
                }}
                """
                
                # Get implementation code from AI expert
                response = ai_expert.analyze({
                    "input_text": prompt,
                    "analysis_type": "code_generation"
                })
                
                # Extract response content
                if hasattr(response, 'content'):
                    content = response.content
                else:
                    content = str(response)
                
                # Try to extract JSON
                try:
                    import re
                    json_match = re.search(r'\{[\s\S]*\}', content)
                    if json_match:
                        code_json = json.loads(json_match.group(0))
                        return code_json
                except:
                    pass
            
            # Fallback: Create generic code structure
            return self._create_default_code_structure(framework)
            
        except Exception as e:
            print(f"❌ Failed to generate experiment code: {e}")
            return self._create_default_code_structure("PyTorch")
    
    def _create_default_code_structure(self, framework: str) -> Dict[str, Any]:
        """Create a default code structure if the AI expert fails"""
        if framework.lower() == "pytorch":
            return {
                "files": [
                    {"filename": "main.py", "description": "Main entry point for experiment execution"},
                    {"filename": "model.py", "description": "Neural network model definitions"},
                    {"filename": "data.py", "description": "Data loading and preprocessing utilities"},
                    {"filename": "train.py", "description": "Training and evaluation functions"},
                    {"filename": "utils.py", "description": "Helper functions and utilities"},
                    {"filename": "config.py", "description": "Configuration parameters"},
                    {"filename": "visualization.py", "description": "Visualization utilities"}
                ],
                "code_samples": {
                    "model_definition": "import torch\nimport torch.nn as nn\n\nclass ExperimentModel(nn.Module):\n    def __init__(self):\n        super().__init__()\n        # Model architecture\n        self.layers = nn.Sequential(\n            nn.Linear(input_dim, hidden_dim),\n            nn.ReLU(),\n            nn.Linear(hidden_dim, output_dim)\n        )\n    \n    def forward(self, x):\n        return self.layers(x)",
                    
                    "training_loop": "def train(model, dataloader, optimizer, criterion):\n    model.train()\n    for inputs, targets in dataloader:\n        optimizer.zero_grad()\n        outputs = model(inputs)\n        loss = criterion(outputs, targets)\n        loss.backward()\n        optimizer.step()\n    return loss.item()",
                    
                    "evaluation": "def evaluate(model, dataloader, metric_fn):\n    model.eval()\n    results = []\n    with torch.no_grad():\n        for inputs, targets in dataloader:\n            outputs = model(inputs)\n            results.append(metric_fn(outputs, targets))\n    return sum(results) / len(results)"
                },
                "entry_point": "python main.py --config config.json"
            }
        else:
            # Generic structure for other frameworks
            return {
                "files": [
                    {"filename": "main.py", "description": "Main entry point"},
                    {"filename": "model.py", "description": "Model definitions"},
                    {"filename": "data.py", "description": "Data handling"},
                    {"filename": "train.py", "description": "Training functions"},
                    {"filename": "evaluate.py", "description": "Evaluation code"}
                ],
                "code_samples": {
                    "model_definition": "# Model definition code\n# Placeholder for framework-specific code",
                    "data_loading": "# Data loading code\n# Placeholder for framework-specific code",
                    "training": "# Training code\n# Placeholder for framework-specific code"
                },
                "entry_point": "python main.py"
            }
    
    def generate_visualization_plan(self, research_problem=None, experiment_plan=None, implementation_plan=None) -> Dict[str, Any]:
        """
        Generate visualization plan for experiment results
        
        Args:
            research_problem: Research problem details
            experiment_plan: Experiment plan details
            implementation_plan: Implementation plan details
            
        Returns:
            Visualization plan with chart types and tool recommendations
        """
        print(f"📊 Generating visualization plan...")
        try:
            # Get data analysis expert for visualization recommendations
            data_expert = self.agent_manager.get_agent('data_analysis')
            
            # Extract experiment details
            problem_str = ""
            if research_problem:
                if hasattr(research_problem, 'question'):
                    problem_str = research_problem.question
                else:
                    problem_str = str(research_problem)
            
            metrics = []
            if experiment_plan and hasattr(experiment_plan, 'metrics'):
                metrics = experiment_plan.metrics
            
            # Prepare prompt for visualization plan
            prompt = f"""
            Create a comprehensive visualization plan for presenting results from:
            
            RESEARCH PROBLEM: {problem_str}
            METRICS: {metrics}
            
            Please provide:
            1. Types of visualizations to use
            2. Recommended visualization tools
            3. Presentation tips
            
            Format your response as a JSON with:
            {{
                "visualizations": [
                    {{"type": "chart_type", "description": "what to visualize"}},
                    {{"type": "chart_type", "description": "what to visualize"}}
                ],
                "recommended_tools": ["tool1", "tool2"],
                "presentation_tips": ["tip1", "tip2"]
            }}
            """
            
            if data_expert:
                # Get visualization plan from data expert
                response = data_expert.analyze({
                    "input_text": prompt,
                    "analysis_type": "visualization_planning"
                })
                
                # Extract response content
                if hasattr(response, 'content'):
                    content = response.content
                else:
                    content = str(response)
                
                # Try to extract JSON
                try:
                    import re
                    json_match = re.search(r'\{[\s\S]*\}', content)
                    if json_match:
                        plan_json = json.loads(json_match.group(0))
                        return plan_json
                except:
                    pass
            
            # Fallback: Create a generic visualization plan
            return self._create_default_visualization_plan(metrics)
            
        except Exception as e:
            print(f"❌ Failed to generate visualization plan: {e}")
            return self._create_default_visualization_plan()
    
    def _create_default_visualization_plan(self, metrics=None) -> Dict[str, Any]:
        """Create a default visualization plan if the data expert fails"""
        if not metrics:
            metrics = ["accuracy", "efficiency"]
            
        return {
            "visualizations": [
                {"type": "line_chart", "description": "Show performance metrics over time/epochs"},
                {"type": "bar_chart", "description": "Compare performance across different models/methods"},
                {"type": "scatter_plot", "description": "Visualize relationships between variables"},
                {"type": "heatmap", "description": "Show correlation between different factors"},
                {"type": "box_plot", "description": "Illustrate statistical distribution of results"}
            ],
            "recommended_tools": [
                "Matplotlib", 
                "Seaborn", 
                "Plotly",
                "Bokeh"
            ],
            "presentation_tips": [
                "Use consistent color schemes across all visualizations",
                "Include error bars or confidence intervals where applicable",
                "Label axes clearly and use appropriate scales",
                "Create a visual hierarchy to guide attention to key results",
                "Consider accessibility by using colorblind-friendly palettes"
            ]
        }
    
    def generate_experimental_design(self, research_problem: ResearchProblem) -> Dict[str, Any]:
        """
        生成实验设计方案 (兼容旧接口)
        
        Args:
            research_problem: 研究问题
            
        Returns:
            Dict[str, Any]: 实验设计方案
        """
        experiment_plan = self.design_experiment(research_problem)
        
        if experiment_plan:
            return {
                "hypothesis": experiment_plan.hypothesis,
                "methodology": experiment_plan.methodology,
                "variables": experiment_plan.variables,
                "metrics": experiment_plan.metrics,
                "expected_outcomes": experiment_plan.expected_outcomes,
                "evaluation_criteria": experiment_plan.evaluation_criteria
            }
        else:
            # 返回默认设计
            return {
                "hypothesis": f"The proposed approach will improve upon existing methods for {research_problem.question}",
                "methodology": "controlled_experimental_comparison",
                "variables": [
                    {"name": "method_type", "type": "independent"},
                    {"name": "performance", "type": "dependent"}
                ],
                "metrics": ["accuracy", "efficiency"],
                "expected_outcomes": ["Improved performance over baselines"],
                "evaluation_criteria": {"significance_test": "p<0.05"}
            }