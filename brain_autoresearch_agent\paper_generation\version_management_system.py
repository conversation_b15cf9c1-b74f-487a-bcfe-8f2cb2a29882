"""
论文版本管理系统 - 跟踪和管理论文的多个版本
"""

import os
import json
import shutil
import difflib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import hashlib


class PaperVersion:
    """论文版本类"""
    
    def __init__(
        self,
        version_id: str,
        timestamp: str,
        latex_content: str,
        metadata: Dict[str, Any],
        file_path: Optional[str] = None,
        quality_score: Optional[float] = None,
        comment: Optional[str] = None
    ):
        """
        初始化论文版本
        
        Args:
            version_id: 版本ID
            timestamp: 时间戳
            latex_content: LaTeX内容
            metadata: 元数据
            file_path: 文件路径
            quality_score: 质量评分
            comment: 版本备注
        """
        self.version_id = version_id
        self.timestamp = timestamp
        self.latex_content = latex_content
        self.metadata = metadata
        self.file_path = file_path
        self.quality_score = quality_score
        self.comment = comment or f"版本 {version_id}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "version_id": self.version_id,
            "timestamp": self.timestamp,
            "file_path": self.file_path,
            "quality_score": self.quality_score,
            "comment": self.comment,
            "metadata": self.metadata,
            "content_hash": hashlib.md5(self.latex_content.encode()).hexdigest()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any], content: str) -> 'PaperVersion':
        """从字典创建版本对象"""
        return cls(
            version_id=data["version_id"],
            timestamp=data["timestamp"],
            latex_content=content,
            metadata=data["metadata"],
            file_path=data["file_path"],
            quality_score=data["quality_score"],
            comment=data["comment"]
        )


class VersionManagementSystem:
    """论文版本管理系统"""
    
    def __init__(self, base_dir: str = "output/versions"):
        """
        初始化版本管理系统
        
        Args:
            base_dir: 版本存储目录
        """
        self.base_dir = base_dir
        os.makedirs(self.base_dir, exist_ok=True)
        self.versions_file = os.path.join(self.base_dir, "versions_index.json")
        self.versions = self._load_versions()
    
    def _load_versions(self) -> Dict[str, Dict[str, Any]]:
        """加载版本信息"""
        if os.path.exists(self.versions_file):
            try:
                with open(self.versions_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ 版本文件加载失败: {e}")
                return {}
        return {}
    
    def _save_versions(self) -> None:
        """保存版本信息"""
        try:
            with open(self.versions_file, "w", encoding="utf-8") as f:
                json.dump(self.versions, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ 版本信息保存失败: {e}")
    
    def create_version(
        self,
        latex_content: str,
        metadata: Dict[str, Any],
        quality_score: Optional[float] = None,
        comment: Optional[str] = None
    ) -> PaperVersion:
        """
        创建新版本
        
        Args:
            latex_content: LaTeX内容
            metadata: 元数据
            quality_score: 质量评分
            comment: 版本备注
            
        Returns:
            新版本对象
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        version_id = f"v{len(self.versions) + 1:03d}"
        
        # 为版本创建目录
        version_dir = os.path.join(self.base_dir, version_id)
        os.makedirs(version_dir, exist_ok=True)
        
        # 保存LaTeX内容
        file_name = f"paper_{timestamp}.tex"
        file_path = os.path.join(version_dir, file_name)
        
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(latex_content)
        
        # 创建版本对象
        version = PaperVersion(
            version_id=version_id,
            timestamp=timestamp,
            latex_content=latex_content,
            metadata=metadata,
            file_path=file_path,
            quality_score=quality_score,
            comment=comment
        )
        
        # 更新版本索引
        self.versions[version_id] = version.to_dict()
        self._save_versions()
        
        print(f"✅ 已创建新版本 {version_id} ({timestamp})")
        if quality_score is not None:
            print(f"   📊 质量评分: {quality_score}")
        
        return version
    
    def get_version(self, version_id: str) -> Optional[PaperVersion]:
        """
        获取指定版本
        
        Args:
            version_id: 版本ID
            
        Returns:
            版本对象，如果未找到则返回None
        """
        if version_id not in self.versions:
            print(f"⚠️ 未找到版本 {version_id}")
            return None
        
        version_data = self.versions[version_id]
        file_path = version_data["file_path"]
        
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            
            return PaperVersion.from_dict(version_data, content)
        except Exception as e:
            print(f"⚠️ 加载版本 {version_id} 失败: {e}")
            return None
    
    def get_latest_version(self) -> Optional[PaperVersion]:
        """
        获取最新版本
        
        Returns:
            最新版本对象，如果没有版本则返回None
        """
        if not self.versions:
            print("⚠️ 没有可用的版本")
            return None
        
        # 按版本ID排序并获取最新的
        latest_id = max(self.versions.keys())
        return self.get_version(latest_id)
    
    def get_best_version(self) -> Optional[PaperVersion]:
        """
        获取质量最高的版本
        
        Returns:
            质量最高的版本对象，如果没有版本则返回None
        """
        if not self.versions:
            print("⚠️ 没有可用的版本")
            return None
        
        # 找到质量评分最高的版本
        best_id = max(
            self.versions.keys(),
            key=lambda vid: self.versions[vid].get("quality_score", 0) or 0
        )
        return self.get_version(best_id)
    
    def list_versions(self) -> List[Dict[str, Any]]:
        """
        列出所有版本
        
        Returns:
            版本信息列表
        """
        return [
            {
                "version_id": vid,
                "timestamp": data["timestamp"],
                "quality_score": data["quality_score"],
                "comment": data["comment"]
            }
            for vid, data in sorted(self.versions.items())
        ]
    
    def compare_versions(self, version_id1: str, version_id2: str) -> Tuple[str, float]:
        """
        比较两个版本的差异
        
        Args:
            version_id1: 第一个版本ID
            version_id2: 第二个版本ID
            
        Returns:
            (差异文本, 相似度百分比)
        """
        v1 = self.get_version(version_id1)
        v2 = self.get_version(version_id2)
        
        if not v1 or not v2:
            return "", 0.0
        
        # 分割为行
        v1_lines = v1.latex_content.splitlines()
        v2_lines = v2.latex_content.splitlines()
        
        # 计算差异
        differ = difflib.Differ()
        diff = list(differ.compare(v1_lines, v2_lines))
        
        # 计算相似度
        matcher = difflib.SequenceMatcher(None, v1.latex_content, v2.latex_content)
        similarity = matcher.ratio() * 100.0
        
        return "\n".join(diff), similarity
    
    def revert_to_version(self, version_id: str) -> Optional[PaperVersion]:
        """
        恢复到指定版本
        
        Args:
            version_id: 版本ID
            
        Returns:
            恢复的版本对象，如果未找到则返回None
        """
        version = self.get_version(version_id)
        if not version:
            return None
        
        # 创建新版本作为恢复点
        comment = f"恢复自版本 {version_id}"
        new_version = self.create_version(
            latex_content=version.latex_content,
            metadata=version.metadata,
            quality_score=version.quality_score,
            comment=comment
        )
        
        return new_version
    
    def export_version(self, version_id: str, export_path: str) -> bool:
        """
        导出指定版本
        
        Args:
            version_id: 版本ID
            export_path: 导出路径
            
        Returns:
            是否成功导出
        """
        version = self.get_version(version_id)
        if not version:
            return False
        
        try:
            # 创建导出目录
            export_dir = os.path.dirname(export_path)
            os.makedirs(export_dir, exist_ok=True)
            
            # 导出LaTeX文件
            with open(export_path, "w", encoding="utf-8") as f:
                f.write(version.latex_content)
            
            # 导出元数据
            metadata_path = os.path.splitext(export_path)[0] + "_metadata.json"
            with open(metadata_path, "w", encoding="utf-8") as f:
                json.dump(version.metadata, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 已导出版本 {version_id} 到 {export_path}")
            return True
        except Exception as e:
            print(f"⚠️ 导出版本 {version_id} 失败: {e}")
            return False 