"""
Enhanced Literature Manager

提供增强的文献管理功能，支持多源搜索，智能排序和去重，并集成本地论文数据库。
"""

import os
import json
import asyncio
import logging
import re
import time
from typing import List, Dict, Any, Optional, Union, Tuple
from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import dataclass
from datetime import datetime

# 添加项目路径
import sys
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from core.unified_api_client import UnifiedAPIClient
from core.semantic_scholar_tool import SemanticScholarTool
from core.arxiv_tool import ArxivTool
from core.crossref_tool import CrossrefTool
from core.paper_workflow import PaperWorkflowExtractor, PaperWorkflow

# 导入论文数据库
from core.paper_database import PaperDatabase

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PaperInfo:
    """论文信息数据结构"""
    title: str
    abstract: str
    authors: List[str]
    year: Optional[int]
    venue: str
    url: str
    citation_count: Optional[int]
    source: str  # 来源：semantic_scholar, arxiv, crossref
    paper_id: str
    keywords: List[str] = None
    doi: str = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为可序列化的字典格式"""
        return {
            'title': self.title,
            'abstract': self.abstract,
            'authors': self.authors,
            'year': self.year,
            'venue': self.venue,
            'url': self.url,
            'citation_count': self.citation_count,
            'source': self.source,
            'paper_id': self.paper_id,
            'keywords': self.keywords if self.keywords else [],
            'doi': self.doi
        }
    
@dataclass
class LiteratureSearchResult:
    """文献搜索结果"""
    query: str
    total_papers: int
    papers: List[PaperInfo]
    search_time: float
    sources_used: List[str]
    workflow_extractions: Optional[List[PaperWorkflow]] = None

class EnhancedLiteratureManager:
    """增强文献管理系统"""
    
    def __init__(self, unified_client=None, db_path: str = None):
        # 设置日志
        self.logger = logging.getLogger('EnhancedLiteratureManager')
        self.logger.setLevel(logging.INFO)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        # 统一API客户端
        self.unified_client = unified_client
        
        # 初始化搜索工具
        self.semantic_scholar = SemanticScholarTool()
        self.arxiv = ArxivTool()
        self.crossref = CrossrefTool()
        
        # 文献搜索配置
        self.semantic_scholar_limit = 10
        self.arxiv_limit = 10
        self.crossref_limit = 10
        
        self.brain_inspired_keywords = [
            "brain-inspired", "neuromorphic", "neural computation", 
            "cognitive architecture", "neuroscience-inspired AI"
        ]
        
        # 初始化论文数据库
        self.paper_db = PaperDatabase(db_path)
        
        # 显示数据库统计信息
        stats = self.paper_db.get_statistics()
        print(f"📚 论文数据库: {stats['paper_count']}篇论文, {stats['workflow_count']}个工作流")
    
    def search_literature(self, query: str, max_papers: int = 20, search_depth: str = "standard") -> List[Dict[str, Any]]:
        """
        Search for literature across multiple sources
        
        Args:
            query: Search query
            max_papers: Maximum number of papers to return
            search_depth: Search depth (standard, deep, shallow)
            
        Returns:
            List of paper information dictionaries
        """
        self.logger.info(f"Searching for literature: {query}")
        
        try:
            # Determine search parameters based on depth
            if search_depth == "deep":
                ss_limit = max(20, max_papers // 2)
                arxiv_limit = max(10, max_papers // 3)
                crossref_limit = max(10, max_papers // 3)
            elif search_depth == "shallow":
                ss_limit = max(10, max_papers // 2)
                arxiv_limit = max(5, max_papers // 4)
                crossref_limit = max(5, max_papers // 4)
            else:  # standard
                ss_limit = max(15, max_papers // 2)
                arxiv_limit = max(7, max_papers // 4)
                crossref_limit = max(7, max_papers // 4)
            
            # Search across all sources
            results = []
            sources_used = []
            # Semantic Scholar search
            ss_results = self._search_semantic_scholar(query)
            if ss_results:
                print(f"✅ Semantic Scholar返回 {len(ss_results)} 篇论文")
                results.extend(ss_results)
                sources_used.append('semantic_scholar')
            # ArXiv search
            arxiv_results = self._search_arxiv(query)
            if arxiv_results:
                print(f"✅ ArXiv返回 {len(arxiv_results)} 篇论文")
                results.extend(arxiv_results)
                sources_used.append('arxiv')
            # Crossref search
            crossref_results = self._search_crossref(query)
            if crossref_results:
                print(f"✅ Crossref返回 {len(crossref_results)} 篇论文")
                results.extend(crossref_results)
                sources_used.append('crossref')
            # 如果所有API都失败，才用模拟数据
            if not results:
                print("⚠️ 所有API检索失败，使用模拟数据")
                # 合并所有mock数据
                mock_results = []
                mock_results.extend(self._search_semantic_scholar(query))
                mock_results.extend(self._search_arxiv(query))
                mock_results.extend(self._search_crossref(query))
                results = mock_results
            # Process and deduplicate results
            processed_results = self._process_results(results, query)
            print(f"📚 处理后得到 {len(processed_results)} 篇有效论文")
            # Limit results
            final_results = processed_results[:max_papers]
            return final_results
        except Exception as e:
            self.logger.error(f"Literature search failed: {e}")
            return []
    
    def _process_results(self, results: List[PaperInfo], query: str) -> List[PaperInfo]:
        """
        Process search results - deduplicate and sort by relevance
        
        Args:
            results: List of paper information objects
            query: Original search query for relevance sorting
            
        Returns:
            Processed list of papers
        """
        if not results:
            return []
        
        print(f"🔍 处理搜索结果: {len(results)}篇论文")
        
        # Step 1: Deduplicate
        deduplicated = self._deduplicate_papers(results)
        print(f"✅ 去重后: {len(deduplicated)}篇论文")
        
        # Step 2: Sort by relevance
        sorted_results = self._sort_papers_by_relevance(deduplicated, query)
        print(f"✅ 按相关性排序完成")
        
        # Extract basic metadata for display
        for i, paper in enumerate(sorted_results[:3], 1):
            print(f"  📄 论文{i}: {paper.title} ({paper.year or 'N/A'}) - {paper.venue}")
        
        if len(sorted_results) > 3:
            print(f"  ... 及其他 {len(sorted_results)-3} 篇论文")
        
        return sorted_results
    
    def _enhance_query(self, query: str) -> str:
        """增强查询关键词以聚焦脑启发智能"""
        # 如果查询中已经包含脑启发相关关键词，则不添加
        query_lower = query.lower()
        for keyword in self.brain_inspired_keywords:
            if keyword.lower() in query_lower:
                return query
        
        # 添加脑启发关键词
        enhanced = f"{query} brain-inspired artificial intelligence"
        return enhanced
    
    def _search_semantic_scholar(self, query: str) -> List[PaperInfo]:
        """搜索Semantic Scholar"""
        try:
            results = self.semantic_scholar.search_papers(
                query=query,
                max_results=self.semantic_scholar_limit
            )
            
            # 如果API请求失败，尝试使用模拟数据
            if not results:
                print("⚠️ API请求失败，使用模拟数据")
                results = self.semantic_scholar.mock_search_papers(
                    query=query,
                    max_results=self.semantic_scholar_limit
                )
            
            return self._convert_semantic_scholar_results(results)
        except Exception as e:
            self.logger.error(f"Semantic Scholar搜索失败: {e}")
            print(f"❌ Semantic Scholar搜索失败: {e}")
            
            # 返回模拟数据
            try:
                mock_results = self.semantic_scholar.mock_search_papers(
                    query=query,
                    max_results=self.semantic_scholar_limit
                )
                return self._convert_semantic_scholar_results(mock_results)
            except Exception as mock_error:
                print(f"❌ 生成模拟数据也失败了: {mock_error}")
                return []
    
    def _search_arxiv(self, query: str) -> List[PaperInfo]:
        """搜索ArXiv"""
        try:
            results = self.arxiv.search_papers(
                query=query,
                max_results=self.arxiv_limit
            )
            
            # 如果API请求失败，尝试使用模拟数据
            if not results:
                print("⚠️ ArXiv API请求失败，使用模拟数据")
                results = self.arxiv.mock_search_papers(
                    query=query,
                    max_results=self.arxiv_limit
                )
                
            return self._convert_arxiv_results(results)
        except Exception as e:
            self.logger.error(f"ArXiv搜索失败: {e}")
            print(f"❌ ArXiv搜索失败: {e}")
            
            # 返回模拟数据
            try:
                mock_results = self.arxiv.mock_search_papers(
                    query=query,
                    max_results=self.arxiv_limit
                )
                return self._convert_arxiv_results(mock_results)
            except Exception as mock_error:
                print(f"❌ 生成模拟数据也失败了: {mock_error}")
                return []
    
    def _search_crossref(self, query: str) -> List[PaperInfo]:
        """搜索Crossref"""
        try:
            results = self.crossref.search_papers(
                query=query,
                max_results=self.crossref_limit
            )
            
            # 如果API请求失败，尝试使用模拟数据
            if not results:
                print("⚠️ Crossref API请求失败，使用模拟数据")
                results = self.crossref.mock_search_papers(
                    query=query,
                    max_results=self.crossref_limit
                )
                
            return self._convert_crossref_results(results)
        except Exception as e:
            self.logger.error(f"Crossref搜索失败: {e}")
            print(f"⚠️ Crossref request failed: {e}")
            
            # 返回模拟数据
            try:
                mock_results = self.crossref.mock_search_papers(
                    query=query,
                    max_results=self.crossref_limit
                )
                return self._convert_crossref_results(mock_results)
            except Exception as mock_error:
                print(f"❌ 生成模拟数据也失败了: {mock_error}")
                return []
    
    def _convert_semantic_scholar_results(self, results: List[Dict]) -> List[PaperInfo]:
        """转换Semantic Scholar结果"""
        papers = []
        for item in results:
            try:
                paper = PaperInfo(
                    title=item.get('title', ''),
                    abstract=item.get('abstract', ''),
                    authors=[author.get('name', '') for author in item.get('authors', [])],
                    year=item.get('year'),
                    venue=item.get('venue', ''),
                    url=item.get('url', ''),
                    citation_count=item.get('citationCount'),
                    source='semantic_scholar',
                    paper_id=item.get('paperId', ''),
                    doi=item.get('doi')
                )
                papers.append(paper)
            except Exception as e:
                self.logger.warning(f"转换Semantic Scholar结果失败: {e}")
        return papers
    
    def _convert_arxiv_results(self, results: List[Dict]) -> List[PaperInfo]:
        """转换ArXiv结果"""
        papers = []
        for item in results:
            try:
                paper = PaperInfo(
                    title=item.get('title', ''),
                    abstract=item.get('abstract', ''),
                    authors=[author.get('name', '') for author in item.get('authors', [])],
                    year=self._extract_year_from_date(item.get('publicationDate')),
                    venue='arXiv',
                    url=item.get('url', ''),
                    citation_count=item.get('citationCount'),
                    source='arxiv',
                    paper_id=item.get('paperId', ''),
                    doi=item.get('doi')
                )
                papers.append(paper)
            except Exception as e:
                self.logger.warning(f"转换ArXiv结果失败: {e}")
        return papers
    
    def _convert_crossref_results(self, results: List[Dict]) -> List[PaperInfo]:
        """转换Crossref结果"""
        papers = []
        for item in results:
            try:
                # 处理不同的作者格式
                authors = []
                if 'authors' in item:
                    if isinstance(item['authors'], list):
                        authors = [author.get('name', '') for author in item['authors']]
                    else:
                        authors = [item['authors']]
                
                paper = PaperInfo(
                    title=item.get('title', ''),
                    abstract=item.get('abstract', ''),
                    authors=authors,
                    year=item.get('year'),
                    venue=item.get('venue', ''),
                    url=item.get('url', ''),
                    citation_count=item.get('citationCount'),
                    source='crossref',
                    paper_id=item.get('doi', ''),
                    doi=item.get('doi')
                )
                papers.append(paper)
            except Exception as e:
                self.logger.warning(f"转换Crossref结果失败: {e}")
        return papers
    
    def _extract_year_from_date(self, date_str: str) -> Optional[int]:
        """从日期字符串提取年份"""
        if not date_str:
            return None
        try:
            return int(date_str[:4])
        except:
            return None
    
    def _deduplicate_papers(self, papers: List[PaperInfo]) -> List[PaperInfo]:
        """根据标题去重论文"""
        seen_titles = set()
        unique_papers = []
        
        for paper in papers:
            # 标准化标题用于比较
            normalized_title = paper.title.lower().strip()
            if normalized_title not in seen_titles and normalized_title:
                seen_titles.add(normalized_title)
                unique_papers.append(paper)
        
        self.logger.info(f"去重: {len(papers)} → {len(unique_papers)} 篇论文")
        return unique_papers
    
    def _sort_papers_by_relevance(self, papers: List[PaperInfo], query: str) -> List[PaperInfo]:
        """按相关性排序论文"""
        # 简单的相关性评分：基于标题中的查询词匹配和引用数
        def relevance_score(paper: PaperInfo) -> float:
            score = 0.0
            
            # 标题匹配评分
            query_words = query.lower().split()
            title_words = paper.title.lower().split()
            
            for query_word in query_words:
                if query_word in title_words:
                    score += 2.0
                elif any(query_word in word for word in title_words):
                    score += 1.0
            
            # 摘要匹配评分
            if paper.abstract:
                abstract_lower = paper.abstract.lower()
                for query_word in query_words:
                    if query_word in abstract_lower:
                        score += 0.5
            
            # 引用数评分（归一化）
            if paper.citation_count and paper.citation_count > 0:
                score += min(paper.citation_count / 100, 5.0)
            
            # 年份评分（偏好近期论文）
            if paper.year:
                current_year = datetime.now().year
                year_diff = current_year - paper.year
                if year_diff <= 5:
                    score += 2.0
                elif year_diff <= 10:
                    score += 1.0
            
            return score
        
        return sorted(papers, key=relevance_score, reverse=True)
    
    def _extract_workflows_batch(self, papers: List[PaperInfo]) -> List[PaperWorkflow]:
        """批量提取论文工作流"""
        workflows = []
        
        for i, paper in enumerate(papers, 1):
            self.logger.info(f"🔍 提取工作流 {i}/{len(papers)}: {paper.title[:50]}...")
            
            try:
                # 使用标题和摘要进行工作流提取
                content = f"Title: {paper.title}\n\nAbstract: {paper.abstract}"
                workflow = self.workflow_extractor.extract_workflow(
                    paper_text=content,
                    paper_title=paper.title
                )
                workflows.append(workflow)
                
                # 添加延迟以避免API限制
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"工作流提取失败 ({paper.title[:30]}...): {e}")
                # 创建空的工作流对象
                empty_workflow = PaperWorkflow(
                    title=paper.title,
                    abstract=paper.abstract,
                    datasets=[],
                    network_architectures=[],
                    platforms_tools=[],
                    research_methods=[],
                    evaluation_metrics=[],
                    brain_inspiration=[],
                    ai_techniques=[]
                )
                workflows.append(empty_workflow)
        
        self.logger.info(f"✅ 工作流提取完成: {len(workflows)} 篇论文")
        return workflows
    
    def save_search_results(
        self, 
        results: LiteratureSearchResult, 
        filepath: str = None
    ) -> str:
        """保存搜索结果到文件"""
        if filepath is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"literature_search_{timestamp}.json"
        
        # 转换为可序列化的格式
        data = {
            "search_info": {
                "query": results.query,
                "total_papers": results.total_papers,
                "search_time": results.search_time,
                "sources_used": results.sources_used,
                "timestamp": datetime.now().isoformat()
            },
            "papers": [
                {
                    "title": paper.title,
                    "abstract": paper.abstract,
                    "authors": paper.authors,
                    "year": paper.year,
                    "venue": paper.venue,
                    "url": paper.url,
                    "citation_count": paper.citation_count,
                    "source": paper.source,
                    "paper_id": paper.paper_id,
                    "doi": paper.doi
                }
                for paper in results.papers
            ]
        }
        
        # 如果有工作流提取结果，也保存
        if results.workflow_extractions:
            data["workflows"] = [
                {
                    "title": wf.title,
                    "abstract": wf.abstract,
                    "datasets": wf.datasets,
                    "network_architectures": wf.network_architectures,
                    "platforms_tools": wf.platforms_tools,
                    "research_methods": wf.research_methods,
                    "evaluation_metrics": wf.evaluation_metrics,
                    "brain_inspiration": wf.brain_inspiration,
                    "ai_techniques": wf.ai_techniques
                }
                for wf in results.workflow_extractions
            ]
        
        # 保存到文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📄 搜索结果已保存至: {filepath}")
        return filepath

    def extract_workflow(self, paper_text: str, paper_title: str = "") -> Dict[str, Any]:
        """
        从论文文本中提取工作流
        
        Args:
            paper_text: 论文文本内容
            paper_title: 论文标题（用于识别）
            
        Returns:
            工作流信息字典
        """
        # 生成论文ID（基于标题的哈希值）
        paper_id = None
        if paper_title:
            paper_dict = {'title': paper_title}
            paper_id = self.paper_db._generate_id(paper_dict)
            
            # 检查数据库是否已有此论文的工作流
            if paper_id:
                existing_workflow = self.paper_db.get_workflow(paper_id)
                if existing_workflow:
                    print(f"✅ 从数据库获取工作流: {paper_title[:40]}...")
                    return existing_workflow
        
        # 如果数据库中没有，执行提取
        print(f"🔍 正在提取论文工作流: {paper_title[:40]}...")
        
        workflow = {}
        
        if self.unified_client:
            try:
                prompt = self._create_workflow_extraction_prompt(paper_text, paper_title)
                response = self.unified_client.get_text_response(prompt)
                print(f"✅ API响应获取成功")
                
                # 解析JSON
                try:
                    workflow = self._extract_json_from_response(response)
                    print(f"✅ JSON解析成功")
                    print(f"    ✅ Workflow extracted successfully")
                    
                    # 打印关键信息
                    self._print_workflow_summary(workflow)
                    
                    # 如果有论文ID，保存工作流到数据库
                    if paper_id:
                        self.paper_db.save_workflow(paper_id, workflow)
                        
                except Exception as e:
                    print(f"⚠️ JSON提取失败: {e}")
                    workflow = self._create_default_workflow(paper_title)
            except Exception as e:
                print(f"❌ 工作流提取API调用失败: {e}")
                workflow = self._create_default_workflow(paper_title)
        else:
            print("⚠️ 未提供UnifiedClient，使用默认工作流")
            workflow = self._create_default_workflow(paper_title)
        
        return workflow
    
    def extract_workflows_from_papers(self, papers: List[Any]) -> Dict[str, Dict[str, Any]]:
        """
        批量从论文中提取工作流
        
        Args:
            papers: 论文对象列表
            
        Returns:
            以论文标题为键的工作流字典
        """
        workflows = {}
        
        for paper in papers:
            title = self._get_paper_title(paper)
            abstract = self._get_paper_abstract(paper)
            
            # 创建论文ID
            paper_dict = {'title': title}
            paper_id = self.paper_db._generate_id(paper_dict)
            
            # 检查数据库是否已有工作流
            existing_workflow = None
            if paper_id:
                existing_workflow = self.paper_db.get_workflow(paper_id)
            
            if existing_workflow:
                print(f"  ✓ 从数据库获取工作流: {title[:60]}...")
                workflow = existing_workflow
            else:
                # 提取工作流
                paper_text = abstract or title
                workflow = self.extract_workflow(paper_text, title)
                
                # 保存到数据库
                if paper_id and workflow:
                    self.paper_db.save_workflow(paper_id, workflow)
            
            workflows[title] = workflow
        
        return workflows
        
    def get_db_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        return self.paper_db.get_statistics()
    
    def _create_workflow_extraction_prompt(self, paper_text: str, paper_title: str) -> str:
        """创建工作流提取提示词"""
        return f"""
        Extract structured research workflow information from this paper:
        
        TITLE: {paper_title}
        TEXT: {paper_text[:2000]}...
        
        Please extract and organize the following information:
        
        1. Datasets used
        2. Network architectures or models
        3. Research methods
        4. Evaluation metrics
        5. Platforms and tools
        6. Brain-inspired principles or concepts
        7. AI techniques
        
        Format your response as a properly formatted JSON object with these fields:
        {{
            "datasets": ["dataset1", "dataset2", ...],
            "network_architectures": ["architecture1", "architecture2", ...],
            "methods": ["method1", "method2", ...],
            "metrics": ["metric1", "metric2", ...],
            "platforms_tools": ["platform1", "tool1", ...],
            "brain_inspiration": ["principle1", "concept1", ...],
            "ai_techniques": ["technique1", "technique2", ...]
        }}
        
        If a category has no identifiable elements, provide an empty array for that field.
        """
    
    def _extract_json_from_response(self, response: str) -> Dict[str, Any]:
        """从响应中提取JSON"""
        # 使用统一API客户端的JSON提取
        if hasattr(self.unified_client, 'extract_json'):
            extracted = self.unified_client.extract_json(response)
            if extracted:
                return extracted
        
        # 备用方法：手动解析
        try:
            # 尝试查找JSON格式内容
            import re
            json_pattern = r'```json\s*(.*?)```'
            json_match = re.search(json_pattern, response, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(1)
                return json.loads(json_str)
                
            # 如果没有找到带有```json```标记的内容，尝试查找任何JSON对象
            json_pattern = r'({[\s\S]*?})'
            json_match = re.search(json_pattern, response, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(1)
                return json.loads(json_str)
                
            # 如果仍未找到，尝试加载整个响应
            return json.loads(response)
                
        except Exception as e:
            print(f"手动JSON提取失败: {e}")
            return {}
            
    def _print_workflow_summary(self, workflow: Dict[str, Any]) -> None:
        """打印工作流摘要信息"""
        print(f"    📊 Workflow details:")
        
        # 检查是否有数据集
        datasets = workflow.get('datasets', [])
        if datasets:
            print(f"      • Datasets: {', '.join(datasets[:3])}" + ("..." if len(datasets) > 3 else ""))
            
        # 检查是否有网络架构
        architectures = workflow.get('network_architectures', [])
        if architectures:
            print(f"      • Network architectures: {', '.join(architectures[:3])}" + ("..." if len(architectures) > 3 else ""))
            
        # 检查是否有研究方法
        methods = workflow.get('methods', [])
        if methods:
            print(f"      • Research methods: {', '.join(methods[:3])}" + ("..." if len(methods) > 3 else ""))
            
        # 检查是否有脑启发概念
        brain_inspiration = workflow.get('brain_inspiration', [])
        if brain_inspiration:
            print(f"      • Brain inspiration: {', '.join(brain_inspiration[:3])}" + ("..." if len(brain_inspiration) > 3 else ""))
    
    def _create_default_workflow(self, paper_title: str) -> Dict[str, Any]:
        """创建默认工作流"""
        # 从标题提取关键词
        title_words = paper_title.lower().split()
        
        # 推断可能的数据集
        datasets = []
        for dataset in ["mnist", "cifar", "imagenet", "coco", "voc"]:
            if dataset in title_words:
                datasets.append(dataset.upper())
        
        # 推断可能的方法
        methods = []
        for method in ["cnn", "rnn", "lstm", "transformer", "gan"]:
            if method in title_words:
                methods.append(method.upper())
        
        # 创建默认工作流
        workflow = {
            "datasets": datasets,
            "network_architectures": ["Neural Networks"] if "neural" in paper_title.lower() else [],
            "methods": methods or ["machine learning"],
            "metrics": [],
            "platforms_tools": [],
            "brain_inspiration": ["bio-plausible mechanisms"] if "brain" in paper_title.lower() else [],
            "ai_techniques": []
        }
        
        return workflow
        
    def _get_paper_title(self, paper: Any) -> str:
        """获取论文标题"""
        if isinstance(paper, dict):
            return paper.get('title', '')
        elif hasattr(paper, 'title'):
            return paper.title if paper.title else ''
        return ''
        
    def _get_paper_abstract(self, paper: Any) -> str:
        """获取论文摘要"""
        if isinstance(paper, dict):
            return paper.get('abstract', '')
        elif hasattr(paper, 'abstract'):
            return paper.abstract if paper.abstract else ''
        return ''


if __name__ == "__main__":
    # 测试增强文献管理系统
    print("🧪 增强文献管理系统测试")
    print("=" * 50)
    
    # 创建文献管理器
    manager = EnhancedLiteratureManager()
    
    # 测试搜索
    query = "energy-efficient neuromorphic computing"
    print(f"🔍 测试查询: {query}")
    
    results = manager.search_literature(
        query=query,
        max_papers=20,
        sources=['semantic_scholar', 'arxiv'],
        extract_workflows=False  # 先不提取工作流以节省时间
    )
    
    print(f"\n📊 搜索结果:")
    print(f"  查询: {results.query}")
    print(f"  论文数量: {results.total_papers}")
    print(f"  搜索时间: {results.search_time:.2f} 秒")
    print(f"  使用源: {results.sources_used}")
    
    # 显示前几篇论文
    print(f"\n📚 论文列表 (前5篇):")
    for i, paper in enumerate(results.papers[:5], 1):
        print(f"  {i}. {paper.title[:60]}...")
        print(f"     作者: {', '.join(paper.authors[:3])}...")
        print(f"     来源: {paper.source} | 年份: {paper.year}")
        print(f"     引用: {paper.citation_count or 'N/A'}")
        print()
