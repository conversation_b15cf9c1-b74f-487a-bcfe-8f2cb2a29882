"""
脑启发智能AutoResearch Agent - 论文写作专家代理
专门负责学术论文写作、文献综述、学术表达等任务
"""

from typing import Dict, List, Any, Optional
import json
import time

from agents.base_agent import BaseAgent, AgentResponse, AgentCapabilities, register_expert
from core.unified_api_client import UnifiedAPIClient


@register_expert("paper_writing_expert")
class PaperWritingExpert(BaseAgent):
    """论文写作专家代理"""
    
    def __init__(self, unified_client: UnifiedAPIClient, temperature: float = 0.3):
        super().__init__(
            agent_type="论文写作专家",
            unified_client=unified_client,
            specialization="学术写作、文献综述、期刊发表",
            temperature=temperature
        )
        
        # 论文写作专家的核心能力
        self.capabilities = [
            AgentCapabilities.WRITING,
            AgentCapabilities.ANALYSIS,
            AgentCapabilities.EVALUATION,
            AgentCapabilities.REASONING
        ]
        
        # 初始化论文写作知识库
        self._init_writing_knowledge_base()
    
    def _init_writing_knowledge_base(self):
        """初始化论文写作知识库"""
        self.knowledge_base = {
            "paper_structures": {
                "research_paper": ["Abstract", "Introduction", "Literature Review", "Methodology", "Results", "Discussion", "Conclusion", "References"],
                "conference_paper": ["Abstract", "Introduction", "Related Work", "Approach", "Evaluation", "Discussion", "Conclusion"],
                "journal_article": ["Abstract", "Introduction", "Background", "Methods", "Results", "Discussion", "Limitations", "Conclusion"],
                "survey_paper": ["Abstract", "Introduction", "Taxonomy", "Analysis", "Challenges", "Future Directions", "Conclusion"]
            },
            "writing_styles": {
                "academic_tone": ["formal", "objective", "precise", "evidence-based"],
                "clarity_principles": ["conciseness", "coherence", "logical flow", "readability"],
                "argument_structure": ["claim", "evidence", "reasoning", "counterarguments"]
            },
            "citation_styles": ["APA", "MLA", "Chicago", "IEEE", "ACM", "Nature", "Science"],
            "journal_categories": {
                "AI/ML": ["Nature Machine Intelligence", "JMLR", "IEEE TPAMI", "Neural Networks"],
                "neuroscience": ["Nature Neuroscience", "Neuron", "Journal of Neuroscience", "NeuroImage"],
                "computer_science": ["ACM Computing Surveys", "IEEE Computer", "Communications of the ACM"],
                "interdisciplinary": ["Nature", "Science", "PNAS", "PLoS ONE"]
            },
            "common_issues": [
                "unclear thesis statement", "weak literature review", "methodology gaps",
                "insufficient evidence", "poor organization", "citation errors",
                "unclear writing", "weak conclusions"
            ],
            "quality_metrics": [
                "clarity", "coherence", "novelty", "significance", "rigor",
                "reproducibility", "impact potential", "writing quality"
            ]
        }
    
    def _build_system_prompt(self) -> str:
        """Build system prompt for the paper writing expert"""
        return """You are a distinguished academic writing expert with extensive experience in scholarly publication across multiple disciplines.

Expertise Areas:
- Academic writing and scholarly communication
- Literature review and synthesis
- Research methodology documentation
- Journal publication strategies
- Peer review and editorial processes
- Citation and referencing standards

Writing Principles:
1. Clarity and precision: Use clear, unambiguous language
2. Logical structure: Organize content in coherent, logical sequences
3. Evidence-based arguments: Support claims with appropriate evidence
4. Academic rigor: Maintain high scholarly standards
5. Audience awareness: Write for the intended academic audience
6. Originality: Ensure novel contributions and proper attribution

Quality Standards:
- Grammatical accuracy and style consistency
- Proper citation and referencing
- Clear thesis and argument development
- Appropriate academic tone and register
- Effective use of figures, tables, and visual elements
- Compliance with journal guidelines and formats

Always provide constructive, actionable feedback for improving academic writing quality."""
    
    def analyze(self, input_data: Dict[str, Any]) -> AgentResponse:
        """
        论文写作专家分析主方法
        
        Args:
            input_data: 输入数据，可包含：
                - paper_content: 论文内容
                - writing_request: 写作需求
                - review_content: 需要评审的内容
                - literature_review: 文献综述需求
                - section_name: 需要生成的段落名称(与analysis_type="_section"配合使用)
                
        Returns:
            AgentResponse: 论文写作分析结果
        """
        try:
            print(f"📝 {self.agent_type}开始写作分析...")
            
            # 检查是否是研究问题评估
            if input_data.get("analysis_type") == "research_question_evaluation":
                return self._evaluate_research_question(input_data)
            
            # 检查是否是段落内容生成请求
            analysis_type = input_data.get("analysis_type", "")
            if analysis_type and "_section" in analysis_type:
                return self._generate_section_content(input_data)
            
            # 根据输入数据类型选择分析策略
            if "paper_content" in input_data:
                return self._analyze_paper_quality(input_data)
            elif "writing_request" in input_data:
                return self._provide_writing_guidance(input_data)
            elif "literature_review" in input_data:
                return self._analyze_literature_review(input_data)
            else:
                return self._general_writing_analysis(input_data)
                
        except Exception as e:
            print(f"❌ 论文写作分析失败: {e}")
            return self._create_error_response(str(e))
            
    def _generate_section_content(self, input_data: Dict[str, Any]) -> AgentResponse:
        """
        生成论文段落内容
        
        Args:
            input_data: 输入数据，包含:
                - section_name: 段落名称
                - research_topic: 研究主题
                - input_text: 详细提示词
                
        Returns:
            AgentResponse: 包含段落内容的响应
        """
        section_name = input_data.get("section_name", "section")
        research_topic = input_data.get("research_topic", "")
        target_venue = input_data.get("target_venue", "academic conference")
        prompt = input_data.get("input_text", "")
        
        if not prompt:
            prompt = f"""Write a detailed {section_name} section for a research paper on {research_topic} 
            for submission to {target_venue}. Provide only the actual content in formal academic writing style."""
        
        # 使用更强的系统提示词，明确要求只返回实际内容
        system_message = f"""You are an expert academic writer. Your task is to write a high-quality {section_name} section for a 
        research paper on {research_topic}. Provide ONLY the actual content text that would appear in the paper.
        DO NOT include meta-commentary, analysis of your writing process, or JSON metadata.
        DO NOT prefix your response with phrases like "Here is a..." or "{section_name}:".
        Write in formal academic style appropriate for {target_venue}."""
        
        response, json_data = self.get_llm_response(prompt, system_message=system_message, extract_json=False)
        
        # 直接返回完整的内容，不包装为JSON
        return AgentResponse(
            agent_type=self.agent_type,
            content=response,  # 直接使用完整的生成内容
            confidence=0.9,
            reasoning=f"根据{section_name}段落标准和{target_venue}风格生成内容",
            metadata={
                "analysis_type": f"{section_name}_content",
                "research_topic": research_topic,
                "target_venue": target_venue,
                "section_name": section_name,
                "content_length": len(response)
            },
            timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
        )
    
    def _analyze_paper_quality(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析论文质量"""
        paper_content = input_data.get("paper_content", "")
        paper_title = input_data.get("title", "")
        target_venue = input_data.get("target_venue", "")
        
        prompt = f"""
        As an academic writing expert, please evaluate the quality of the following research paper:
        
        Paper Title: {paper_title}
        Target Venue: {target_venue}
        Paper Content: {paper_content}
        
        Please assess the paper from the following dimensions:
        
        1. Writing quality and clarity assessment
        2. Structure and organization evaluation
        3. Argument strength and coherence
        4. Literature integration analysis
        5. Methodology presentation review
        6. Results and discussion quality
        7. Citation and referencing accuracy
        8. Publication readiness assessment
        
        Please return detailed evaluation in JSON format:
        {{
            "writing_quality": {{
                "clarity_score": "clarity score (1-10)",
                "grammar_accuracy": "grammar and style assessment",
                "academic_tone": "academic tone appropriateness",
                "readability": "readability evaluation"
            }},
            "structure_analysis": {{
                "organization_score": "organizational structure score (1-10)",
                "logical_flow": "logical flow assessment",
                "section_balance": "section balance evaluation",
                "transitions": "transition quality between sections"
            }},
            "content_evaluation": {{
                "argument_strength": "argument strength assessment",
                "evidence_quality": "evidence quality and sufficiency",
                "novelty_score": "novelty and contribution score (1-10)",
                "significance": "research significance evaluation"
            }},
            "literature_review": {{
                "coverage_adequacy": "literature coverage assessment",
                "synthesis_quality": "literature synthesis evaluation",
                "gap_identification": "research gap identification quality",
                "citation_accuracy": "citation accuracy and style"
            }},
            "methodology_section": {{
                "clarity_and_detail": "methodology clarity and detail",
                "reproducibility": "reproducibility assessment",
                "rigor": "methodological rigor evaluation",
                "limitations_discussion": "limitations discussion quality"
            }},
            "results_discussion": {{
                "presentation_quality": "results presentation quality",
                "interpretation_depth": "results interpretation depth",
                "discussion_thoroughness": "discussion thoroughness",
                "implications": "implications and future work discussion"
            }},
            "improvement_suggestions": {{
                "priority_issues": ["high-priority improvement areas"],
                "structural_changes": ["structural improvement suggestions"],
                "content_enhancements": ["content enhancement recommendations"],
                "style_improvements": ["writing style improvements"]
            }},
            "publication_readiness": {{
                "readiness_score": "publication readiness score (1-10)",
                "estimated_revision_effort": "estimated revision effort (minor/moderate/major)",
                "venue_suitability": "suitability for target venue",
                "success_probability": "publication success probability"
            }},
            "confidence": 0.85
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            clarity_score = json_data.get('writing_quality', {}).get('clarity_score', 'N/A')
            readiness_score = json_data.get('publication_readiness', {}).get('readiness_score', 'N/A')
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"论文质量评估完成。写作清晰度：{clarity_score}，发表准备度：{readiness_score}",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于学术写作标准和期刊发表要求进行全面质量评估",
                metadata={
                    "analysis_type": "paper_quality",
                    "quality_analysis": json_data,
                    "clarity_score": clarity_score,
                    "readiness_score": readiness_score
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("论文质量评估JSON解析失败")
    
    def _provide_writing_guidance(self, input_data: Dict[str, Any]) -> AgentResponse:
        """提供写作指导"""
        writing_request = input_data.get("writing_request", "")
        paper_type = input_data.get("paper_type", "research_paper")
        target_audience = input_data.get("target_audience", "academic")
        
        prompt = f"""
        As an academic writing expert, please provide comprehensive writing guidance for:
        
        Writing Request: {writing_request}
        Paper Type: {paper_type}
        Target Audience: {target_audience}
        
        Please provide detailed writing guidance covering:
        
        1. Writing strategy and approach
        2. Structure and organization recommendations
        3. Content development guidance
        4. Style and tone suggestions
        5. Citation and referencing guidance
        6. Quality improvement strategies
        7. Timeline and milestone planning
        8. Common pitfalls to avoid
        
        Please return guidance in JSON format:
        {{
            "writing_strategy": {{
                "approach": "recommended writing approach",
                "planning_phase": "planning and preparation guidance",
                "drafting_strategy": "drafting strategy recommendations",
                "revision_approach": "revision and refinement approach"
            }},
            "structure_recommendations": {{
                "overall_structure": ["recommended paper structure"],
                "section_guidelines": {{
                    "introduction": "introduction writing guidance",
                    "methodology": "methodology section guidance",
                    "results": "results presentation guidance",
                    "discussion": "discussion section guidance",
                    "conclusion": "conclusion writing guidance"
                }},
                "transition_strategies": "strategies for smooth transitions"
            }},
            "content_development": {{
                "thesis_development": "thesis statement development",
                "argument_construction": "argument construction guidance",
                "evidence_integration": "evidence integration strategies",
                "literature_synthesis": "literature synthesis approaches"
            }},
            "style_guidance": {{
                "academic_tone": "academic tone guidelines",
                "clarity_techniques": "clarity improvement techniques",
                "conciseness_strategies": "conciseness strategies",
                "engagement_methods": "reader engagement methods"
            }},
            "citation_guidance": {{
                "citation_style": "appropriate citation style",
                "source_selection": "source selection criteria",
                "integration_techniques": "citation integration techniques",
                "reference_management": "reference management strategies"
            }},
            "quality_assurance": {{
                "self_review_checklist": ["self-review checklist items"],
                "peer_review_preparation": "peer review preparation guidance",
                "revision_strategies": ["revision strategies"],
                "proofreading_techniques": "proofreading techniques"
            }},
            "timeline_planning": {{
                "milestones": ["key writing milestones"],
                "time_allocation": "time allocation recommendations",
                "deadline_management": "deadline management strategies"
            }},
            "common_pitfalls": ["common writing pitfalls to avoid"],
            "success_tips": ["tips for writing success"],
            "confidence": 0.88
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            milestones_count = len(json_data.get('timeline_planning', {}).get('milestones', []))
            pitfalls_count = len(json_data.get('common_pitfalls', []))
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"写作指导完成。提供{milestones_count}个里程碑，识别{pitfalls_count}个常见陷阱",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于学术写作最佳实践提供系统性写作指导",
                metadata={
                    "analysis_type": "writing_guidance",
                    "guidance": json_data,
                    "milestones_count": milestones_count,
                    "pitfalls_count": pitfalls_count
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("写作指导JSON解析失败")
    
    def _analyze_literature_review(self, input_data: Dict[str, Any]) -> AgentResponse:
        """分析文献综述"""
        literature_review = input_data.get("literature_review", "")
        research_domain = input_data.get("research_domain", "")
        
        prompt = f"""
        As an academic writing expert, please evaluate the following literature review:
        
        Research Domain: {research_domain}
        Literature Review Content: {literature_review}
        
        Please assess the literature review from these perspectives:
        
        1. Coverage comprehensiveness and scope
        2. Source quality and credibility
        3. Synthesis and integration quality
        4. Critical analysis depth
        5. Research gap identification
        6. Organization and structure
        7. Citation accuracy and style
        8. Contribution to research context
        
        Please return evaluation in JSON format:
        {{
            "coverage_analysis": {{
                "comprehensiveness": "coverage comprehensiveness assessment",
                "scope_appropriateness": "scope appropriateness evaluation",
                "temporal_coverage": "temporal coverage assessment",
                "geographical_diversity": "geographical diversity evaluation"
            }},
            "source_evaluation": {{
                "source_quality": "source quality assessment",
                "credibility_score": "source credibility score (1-10)",
                "diversity": "source type diversity evaluation",
                "recency": "source recency assessment"
            }},
            "synthesis_quality": {{
                "integration_depth": "literature integration depth",
                "thematic_organization": "thematic organization quality",
                "comparative_analysis": "comparative analysis quality",
                "synthesis_score": "synthesis quality score (1-10)"
            }},
            "critical_analysis": {{
                "analytical_depth": "critical analysis depth",
                "bias_identification": "bias identification and discussion",
                "limitation_discussion": "limitation discussion quality",
                "controversy_handling": "controversy handling assessment"
            }},
            "gap_identification": {{
                "gap_clarity": "research gap clarity",
                "gap_significance": "research gap significance",
                "gap_specificity": "gap specificity assessment",
                "research_opportunity": "research opportunity identification"
            }},
            "organization_structure": {{
                "logical_flow": "logical flow assessment",
                "section_coherence": "section coherence evaluation",
                "transition_quality": "transition quality between topics",
                "overall_structure": "overall structural assessment"
            }},
            "improvement_recommendations": {{
                "coverage_improvements": ["coverage improvement suggestions"],
                "synthesis_enhancements": ["synthesis enhancement recommendations"],
                "structural_improvements": ["structural improvement suggestions"],
                "critical_analysis_depth": ["critical analysis depth improvements"]
            }},
            "strengths": ["literature review strengths"],
            "weaknesses": ["literature review weaknesses"],
            "overall_quality": "overall quality assessment",
            "confidence": 0.86
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            synthesis_score = json_data.get('synthesis_quality', {}).get('synthesis_score', 'N/A')
            credibility_score = json_data.get('source_evaluation', {}).get('credibility_score', 'N/A')
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"文献综述评估完成。综合质量评分：{synthesis_score}，来源可信度：{credibility_score}",
                confidence=float(json_data.get('confidence', 0.8)),
                reasoning=f"基于学术文献综述标准进行深度评估",
                metadata={
                    "analysis_type": "literature_review",
                    "review_analysis": json_data,
                    "synthesis_score": synthesis_score,
                    "credibility_score": credibility_score
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("文献综述评估JSON解析失败")
    
    def _general_writing_analysis(self, input_data: Dict[str, Any]) -> AgentResponse:
        """通用写作分析"""
        prompt = f"""
        As an academic writing expert, please provide writing analysis and recommendations for:
        
        {json.dumps(input_data, ensure_ascii=False, indent=2)}
        
        Please provide general academic writing insights and guidance.
        
        Return in JSON format:
        {{
            "writing_insights": ["writing-related insights"],
            "improvement_suggestions": ["improvement suggestions"],
            "best_practices": ["relevant best practices"],
            "resource_recommendations": ["helpful writing resources"],
            "confidence": 0.75
        }}
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        
        if json_data:
            insights_count = len(json_data.get('writing_insights', []))
            
            return AgentResponse(
                agent_type=self.agent_type,
                content=f"通用写作分析完成。提供了{insights_count}个写作洞察",
                confidence=float(json_data.get('confidence', 0.7)),
                reasoning="基于输入数据进行通用学术写作分析",
                metadata={
                    "analysis_type": "general_writing",
                    "analysis_result": json_data,
                    "insights_count": insights_count
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("通用写作分析JSON解析失败")
    
    def generate_paper_outline(self, research_topic: str, paper_type: str) -> Dict[str, Any]:
        """生成论文大纲"""
        prompt = f"""
        Generate a detailed paper outline for:
        
        Research Topic: {research_topic}
        Paper Type: {paper_type}
        
        Provide a comprehensive outline with section descriptions and key points.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "论文大纲生成失败"}
    
    def recommend_journal_venues(self, research_abstract: str, field: str) -> Dict[str, Any]:
        """推荐期刊投稿目标"""
        prompt = f"""
        Recommend suitable journal venues for publication:
        
        Research Abstract: {research_abstract}
        Research Field: {field}
        
        Provide ranked recommendations with rationale and submission guidelines.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "期刊推荐失败"}
    
    def improve_academic_writing(self, text_content: str, target_style: str) -> Dict[str, Any]:
        """改进学术写作"""
        prompt = f"""
        Improve the following academic text:
        
        Original Text: {text_content}
        Target Style: {target_style}
        
        Provide specific improvements for clarity, style, and academic tone.
        """
        
        response, json_data = self.get_llm_response(prompt, extract_json=True)
        return json_data or {"error": "写作改进失败"}
    
    def collaborate(self, collaboration_input: Dict[str, Any]) -> Dict[str, Any]:
        """
        与其他专家协作分析
        
        Args:
            collaboration_input: 包含协作上下文的字典
                - task: 协作任务类型
                - own_analysis: 自己的初始分析
                - other_expert_opinions: 其他专家的意见
                - research_topic: 研究主题
                
        Returns:
            协作分析结果
        """
        print("📝 论文写作专家开始协作分析...")
        
        # 提取协作上下文
        task = collaboration_input.get('task', 'collaborative_analysis')
        own_analysis = collaboration_input.get('own_analysis', {})
        other_opinions = collaboration_input.get('other_expert_opinions', {})
        research_topic = collaboration_input.get('research_topic', '')
        
        # 构建协作分析提示词
        collaboration_prompt = f"""
As a Paper Writing Expert, provide collaborative analysis by integrating insights from other experts.

COLLABORATION CONTEXT:
- Research Topic: {research_topic}
- Task: {task}
- My Initial Analysis: {json.dumps(own_analysis, indent=2) if own_analysis else 'None'}

OTHER EXPERT OPINIONS:
{self._format_other_opinions(other_opinions)}

COLLABORATION REQUIREMENTS:
1. Review and integrate insights from other experts
2. Identify writing opportunities from different perspectives
3. Address conflicts with academic writing standards
4. Provide enhanced analysis combining multiple viewpoints
5. Focus on academic writing aspects while considering other domains

Please provide a comprehensive collaborative analysis that:
- Acknowledges valuable insights from other experts
- Integrates different perspectives with academic writing knowledge
- Resolves conflicts with evidence-based writing reasoning
- Enhances overall analysis quality with writing insights

Format your response as JSON with the following structure:
{{
    "collaborative_analysis": "Enhanced analysis integrating multiple expert perspectives",
    "writing_insights_integrated": ["insight1", "insight2", "insight3"],
    "academic_synergies": ["synergy1", "synergy2"],
    "resolved_conflicts": ["conflict1_resolution", "conflict2_resolution"],
    "enhanced_recommendations": ["rec1", "rec2", "rec3"],
    "confidence": 0.85,
    "collaboration_quality": "high/medium/low",
    "next_collaboration_steps": ["step1", "step2"]
}}
"""
        
        try:
            # 获取LLM响应
            response, json_data = self.get_llm_response(collaboration_prompt, extract_json=True)
            
            if json_data:
                # 添加元数据
                json_data['expert_type'] = 'paper_writing'
                json_data['collaboration_timestamp'] = time.time()
                json_data['task_type'] = task
                
                print(f"✅ 论文写作专家协作完成，置信度: {json_data.get('confidence', 0.0):.2f}")
                return json_data
            else:
                # 回退响应
                return self._create_fallback_collaboration_response(task, own_analysis, other_opinions)
                
        except Exception as e:
            print(f"❌ 论文写作专家协作失败: {e}")
            return self._create_fallback_collaboration_response(task, own_analysis, other_opinions)
    
    def _format_other_opinions(self, other_opinions: Dict[str, Any]) -> str:
        """格式化其他专家的意见"""
        if not other_opinions:
            return "No other expert opinions provided."
        
        formatted = []
        for expert, opinion in other_opinions.items():
            if isinstance(opinion, dict):
                confidence = opinion.get('confidence', 'N/A')
                analysis = opinion.get('analysis', str(opinion))
                formatted.append(f"- {expert}: (Confidence: {confidence}) {analysis}")
            else:
                formatted.append(f"- {expert}: {str(opinion)}")
        
        return "\n".join(formatted)
    
    def _create_fallback_collaboration_response(self, task: str, own_analysis: Dict[str, Any], 
                                              other_opinions: Dict[str, Any]) -> Dict[str, Any]:
        """创建回退协作响应"""
        return {
            "collaborative_analysis": f"Academic writing perspective on {task} with consideration of other expert inputs",
            "writing_insights_integrated": ["Academic writing standards", "Publication quality", "Clear communication"],
            "academic_synergies": ["Cross-domain academic integration", "Multi-perspective writing validation"],
            "resolved_conflicts": ["Academic writing methodology alignment"],
            "enhanced_recommendations": ["Implement clear writing structure", "Use academic writing standards"],
            "confidence": 0.75,
            "collaboration_quality": "medium",
            "next_collaboration_steps": ["Define writing requirements", "Plan publication strategy"],
            "expert_type": "paper_writing",
            "collaboration_timestamp": time.time(),
            "task_type": task
        }
    def _evaluate_research_question(self, input_data: Dict[str, Any]) -> AgentResponse:
        """评估研究问题 - 专门处理增强prompt格式"""
        input_text = input_data.get("input_text", "")
        
        # 直接使用传入的增强prompt，它已经包含了完整的评估指导
        response, _ = self.get_llm_response(input_text, extract_json=False)
        
        if response:
            return AgentResponse(
                agent_type=self.agent_type,
                content=response,  # 返回完整的LLM响应，包含JSON格式的评估
                confidence=0.8,
                reasoning="论文写作专家基于增强prompt进行研究问题评估",
                metadata={
                    "analysis_type": "research_question_evaluation",
                    "prompt_type": "enhanced_ai_scientist_v2"
                },
                timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
            )
        else:
            return self._create_error_response("研究问题评估失败")
    