{"stage": "Stage 1 - Literature Workflow Infrastructure", "timestamp": "2025-07-19T18:26:51.541696", "duration_seconds": 58.90005588531494, "total_tests": 13, "passed_tests": 12, "failed_tests": 1, "success_rate": 92.3076923076923, "detailed_results": {"DeepSeek文本生成": {"success": true, "details": "响应长度: 51 字符"}, "JSON提取功能": {"success": true, "details": "提取数据: {'test': 'success', 'data': ['item1', 'item2'], 'number': 42}"}, "任务模型推荐": {"success": true, "details": "所有任务都有推荐模型"}, "数据集提取": {"success": true, "details": ""}, "网络架构提取": {"success": true, "details": ""}, "平台工具提取": {"success": true, "details": ""}, "研究方法提取": {"success": true, "details": ""}, "评估指标提取": {"success": true, "details": ""}, "脑启发元素提取": {"success": true, "details": ""}, "AI技术提取": {"success": true, "details": ""}, "文献搜索功能": {"success": true, "details": "找到 10 篇论文，耗时 2.43 秒"}, "增强文献管理系统整体测试": {"success": false, "details": "异常: sequence item 0: expected str instance, dict found"}, "集成工作流提取": {"success": true, "details": "成功提取 3 篇论文的工作流"}}}