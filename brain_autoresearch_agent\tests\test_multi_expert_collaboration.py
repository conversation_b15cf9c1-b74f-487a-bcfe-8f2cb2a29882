"""
脑启发智能AutoResearch Agent - 多专家协作演示
详细展示专家间的协作过程和推理链
"""

import os
import sys
import time
import json
sys.path.append('.')

def main():
    """运行多专家协作演示"""
    print("🧠 脑启发智能AutoResearch Agent - 多专家协作演示")
    print("=" * 80)
    print(f"⏰ 演示开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 设置API密钥
    api_key = "sk-1b1d72e2e10643029de548b655e1f93e"
    os.environ["DEEPSEEK_API_KEY"] = api_key
    
    print("\n" + "🤝 多专家协作深度演示".center(80, "="))
    
    # 首先添加第二个专家代理来展示真正的协作
    print("\n📋 1. 创建多专家系统")
    print("-" * 60)
    
    try:
        from core.llm_client import LLMClient
        from agents.agent_manager import AgentManager
        
        # 创建LLM客户端和代理管理器
        llm_client = LLMClient(model="deepseek-chat", api_key=api_key)
        manager = AgentManager(llm_client)
        
        # 先创建一个简单的神经科学专家来演示协作
        print("🧠 正在创建神经科学专家...")
        neuroscience_expert = create_neuroscience_expert(llm_client)
        manager.register_agent("neuroscience", neuroscience_expert)
        
        print(f"✅ 多专家系统创建成功")
        print(f"   已注册专家: {list(manager.agents.keys())}")
        
        # 2. 创建复杂的研究任务
        print("\n📋 2. 创建复杂研究任务")
        print("-" * 60)
        
        task = manager.create_task(
            task_type="跨领域协作研究",
            input_data={
                "research_topic": "基于大脑视觉皮层机制的自注意力神经网络优化",
                "paper_content": """
                本研究提出了一种新型的神经网络注意力机制，灵感来自于灵长类视觉皮层V1和V4区域的神经元连接模式。
                我们发现大脑在处理视觉信息时，会通过层次化的注意力选择机制来提高处理效率。
                
                技术实现方面，我们使用了多头自注意力机制，并结合了生物学上发现的侧抑制和前馈抑制机制。
                在ImageNet-1K数据集上的实验显示，相比标准Transformer，我们的方法在保持相似准确率的同时，
                计算复杂度降低了30%，参数量减少了25%。
                
                神经科学基础：V1区域的方向选择性神经元和V4区域的复杂特征检测为我们的注意力设计提供了理论依据。
                技术创新：引入了生物启发的动态权重调节机制和多尺度特征融合策略。
                """,
                "requirements": [
                    "从AI技术角度评估算法创新性和实现复杂度",
                    "从神经科学角度验证生物学机制的准确性",
                    "分析两个领域的协同效应和潜在改进方向"
                ]
            },
            priority=5
        )
        
        print(f"✅ 复杂研究任务创建完成: {task.task_id}")
        print(f"   研究主题: 基于大脑视觉皮层机制的自注意力神经网络优化")
        print(f"   需求数量: {len(task.requirements)}")
        
        # 3. 执行详细的多专家协作分析
        print("\n📋 3. 执行多专家协作分析")
        print("-" * 60)
        
        collaboration_result = manager.collaborative_analysis(task)
        
        # 4. 分析协作效果
        print("\n📋 4. 协作效果分析")
        print("-" * 60)
        analyze_collaboration_effectiveness(collaboration_result)
        
        # 5. 展示推理链和决策过程
        print("\n📋 5. 推理链和决策过程")
        print("-" * 60)
        show_reasoning_chain(collaboration_result)
        
        return True
        
    except Exception as e:
        print(f"❌ 多专家协作演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_neuroscience_expert(llm_client):
    """创建神经科学专家（简化版用于演示）"""
    from agents.base_agent import BaseAgent, AgentResponse
    import time
    
    class NeuroscienceExpert(BaseAgent):
        """神经科学专家（演示版）"""
        
        def __init__(self, llm_client):
            super().__init__(
                agent_type="神经科学专家",
                llm_client=llm_client,
                specialization="神经科学、脑机制、认知神经科学",
                temperature=0.3
            )
        
        def _build_system_prompt(self):
            return """你是一位资深的神经科学专家，专注于大脑机制、认知神经科学和计算神经科学研究。

专业领域：
- 视觉皮层结构与功能
- 神经网络连接模式
- 注意力的神经机制
- 脑启发计算模型
- 认知功能的神经基础

分析原则：
1. 科学严谨性：基于最新神经科学研究
2. 生物学准确性：确保机制描述符合实际
3. 跨学科视角：连接神经科学与AI技术
4. 实验验证：考虑实验数据的支持
5. 理论创新：识别新的研究方向

输出要求：
- 提供准确的神经科学分析
- 评估生物学机制的合理性
- 建议进一步的神经科学验证
- 以JSON格式输出结构化结果

始终保持科学客观和学术严谨的分析态度。"""
        
        def analyze(self, input_data):
            try:
                paper_content = input_data.get("paper_content", "")
                research_topic = input_data.get("research_topic", "")
                
                prompt = f"""
                作为神经科学专家，请分析以下研究的神经科学基础：
                
                研究主题：{research_topic}
                论文内容：{paper_content}
                
                请从神经科学角度进行深度分析：
                
                1. 涉及的大脑区域和神经机制
                2. 生物学机制的准确性评估
                3. 神经科学理论支持度
                4. 与已知研究的一致性
                5. 神经科学创新点
                6. 需要验证的假设
                7. 建议的神经科学实验
                8. 跨学科协作机会
                
                请以JSON格式返回：
                {{
                    "brain_regions": ["区域1", "区域2"],
                    "neural_mechanisms": ["机制1", "机制2"],
                    "biological_accuracy": "准确性评分(1-10)",
                    "theoretical_support": "理论支持度评估",
                    "innovation_aspects": ["创新点1", "创新点2"],
                    "validation_needed": ["需验证假设1", "需验证假设2"],
                    "suggested_experiments": ["实验1", "实验2"],
                    "collaboration_opportunities": ["协作机会1", "协作机会2"],
                    "confidence": 0.85
                }}
                """
                
                response, json_data = self.get_llm_response(prompt, extract_json=True)
                
                if json_data:
                    return AgentResponse(
                        agent_type=self.agent_type,
                        content=f"神经科学分析完成。涉及{len(json_data.get('brain_regions', []))}个大脑区域，生物学准确性评分：{json_data.get('biological_accuracy', 'N/A')}",
                        confidence=float(json_data.get('confidence', 0.8)),
                        reasoning="基于神经科学理论和实验证据进行综合分析",
                        metadata={
                            "analysis_type": "neuroscience_analysis",
                            "neuroscience_analysis": json_data
                        },
                        timestamp=time.strftime('%Y-%m-%d %H:%M:%S')
                    )
                else:
                    return self._create_error_response("神经科学分析JSON解析失败")
                    
            except Exception as e:
                return self._create_error_response(str(e))
    
    return NeuroscienceExpert(llm_client)

def analyze_collaboration_effectiveness(collaboration_result):
    """分析协作效果"""
    if not collaboration_result or "error" in collaboration_result:
        print("❌ 协作结果无效，无法分析效果")
        return
    
    print("📊 协作效果分析:")
    
    # 分析参与情况
    summary = collaboration_result.get("collaboration_summary", {})
    participants = summary.get("participating_experts", 0)
    pairs = summary.get("collaboration_pairs", 0)
    confidence = summary.get("overall_confidence", 0)
    
    print(f"   👥 参与专家数量: {participants}")
    print(f"   🤝 协作对话数量: {pairs}")
    print(f"   📊 整体置信度: {confidence}")
    
    # 分析共识水平
    consensus = collaboration_result.get("consensus_level", 0)
    print(f"   🎯 专家共识水平: {consensus}")
    
    if consensus >= 0.9:
        print("   🌟 协作效果: 优秀 - 专家意见高度一致")
    elif consensus >= 0.7:
        print("   ✅ 协作效果: 良好 - 专家意见基本一致")
    elif consensus >= 0.5:
        print("   ⚠️ 协作效果: 一般 - 专家意见存在分歧")
    else:
        print("   ❌ 协作效果: 较差 - 专家意见分歧较大")
    
    # 分析知识融合质量
    insights = collaboration_result.get("synthesized_insights", [])
    recommendations = collaboration_result.get("recommendations", [])
    
    print(f"   💡 综合洞察数量: {len(insights)}")
    print(f"   🎯 共识建议数量: {len(recommendations)}")
    
    # 计算协作价值得分
    collaboration_score = calculate_collaboration_score(collaboration_result)
    print(f"   📈 协作价值评分: {collaboration_score}/10")

def calculate_collaboration_score(collaboration_result):
    """计算协作价值评分"""
    score = 0
    
    # 基础分数（参与度）
    summary = collaboration_result.get("collaboration_summary", {})
    participants = summary.get("participating_experts", 0)
    if participants >= 2:
        score += 2
    
    # 协作质量分数
    consensus = collaboration_result.get("consensus_level", 0)
    score += consensus * 3  # 最多3分
    
    # 置信度分数  
    confidence = summary.get("overall_confidence", 0)
    score += confidence * 2  # 最多2分
    
    # 输出质量分数
    insights = collaboration_result.get("synthesized_insights", [])
    recommendations = collaboration_result.get("recommendations", [])
    
    if len(insights) >= 3:
        score += 1.5
    if len(recommendations) >= 2:
        score += 1.5
    
    return round(min(score, 10), 1)

def show_reasoning_chain(collaboration_result):
    """展示推理链和决策过程"""
    if not collaboration_result or "error" in collaboration_result:
        print("❌ 无法展示推理链")
        return
    
    print("🧠 推理链和决策过程:")
    
    # 1. 个体分析阶段
    print("\n🔍 第一阶段: 专家个体分析")
    individual_analyses = collaboration_result.get("individual_analyses", {})
    
    for i, (agent_id, analysis) in enumerate(individual_analyses.items(), 1):
        print(f"   专家{i} - {analysis['agent_type']}:")
        print(f"     💭 分析结果: {analysis['key_content']}")
        print(f"     📊 置信度: {analysis['confidence']}")
    
    # 2. 协作推理阶段
    print(f"\n🤝 第二阶段: 专家协作推理")
    collab_insights = collaboration_result.get("collaboration_insights", [])
    
    if collab_insights:
        for i, insight in enumerate(collab_insights, 1):
            print(f"   协作洞察{i}: {insight}")
    else:
        print("   (本次演示中协作洞察较少，可能是因为只有单一专家或协作数据结构问题)")
    
    # 3. 知识融合阶段
    print(f"\n🧠 第三阶段: 知识融合与共识达成")
    
    consensus = collaboration_result.get("consensus_level", 0)
    print(f"   共识水平: {consensus}")
    
    insights = collaboration_result.get("synthesized_insights", [])
    if insights:
        print("   综合洞察:")
        for i, insight in enumerate(insights[:5], 1):  # 显示前5条
            print(f"     {i}. {insight}")
    
    recommendations = collaboration_result.get("recommendations", [])
    if recommendations:
        print("   最终建议:")
        for i, rec in enumerate(recommendations[:3], 1):  # 显示前3条
            print(f"     {i}. {rec}")
    
    # 4. 决策过程总结
    print(f"\n🎯 决策过程总结:")
    summary = collaboration_result.get("collaboration_summary", {})
    print(f"   决策参与者: {summary.get('participating_experts', 0)}位专家")
    print(f"   推理步骤: 个体分析 → 协作讨论 → 知识融合 → 共识达成")
    print(f"   决策质量: 置信度{summary.get('overall_confidence', 0)} × 共识度{consensus} = {round(summary.get('overall_confidence', 0) * consensus, 2)}")

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print(f"\n🎉 多专家协作演示成功完成!")
        else:
            print(f"\n❌ 多专家协作演示遇到问题")
            
    except KeyboardInterrupt:
        print(f"\n\n⚠️ 演示被用户中断")
    except Exception as e:
        print(f"\n\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
