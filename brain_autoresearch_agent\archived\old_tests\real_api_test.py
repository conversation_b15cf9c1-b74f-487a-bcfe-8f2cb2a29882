"""
真实API测试 - 使用用户的DeepSeek API密钥
验证推理系统在真实API调用下的表现
"""

import os
import sys
import time
import json
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# 设置API密钥
API_KEY = "sk-1b1d72e2e10643029de548b655e1f93e"
os.environ["DEEPSEEK_API_KEY"] = API_KEY


def test_real_api_connection():
    """测试真实API连接"""
    
    print("🔗 测试DeepSeek API连接")
    print("-" * 50)
    
    try:
        from core.llm_client import LLMClient
        
        # 创建DeepSeek客户端
        client = LLMClient(model="deepseek-chat", api_key=API_KEY)
        
        print(f"✅ LLM客户端初始化成功")
        print(f"   模型: {client.model}")
        print(f"   DeepSeek模式: {getattr(client, 'deepseek_mode', False)}")
        print(f"   API密钥: {API_KEY[:8]}...")
        
        # 测试简单调用
        print(f"\n🧪 测试API调用...")
        test_prompt = "请简要介绍一下深度学习的核心概念。"
        
        start_time = time.time()
        response = client.get_response(test_prompt)
        call_time = time.time() - start_time
        
        if response:
            response_text = response[0] if isinstance(response, tuple) else response
            print(f"✅ API调用成功")
            print(f"   响应时间: {call_time:.2f}秒")
            print(f"   响应长度: {len(response_text)} 字符")
            print(f"   响应预览: {response_text[:100]}...")
            return True, client
        else:
            print(f"❌ API调用失败：无响应")
            return False, None
            
    except Exception as e:
        print(f"❌ API连接测试失败: {e}")
        return False, None


def test_real_expert_agents():
    """测试真实专家代理"""
    
    print(f"\n👥 测试真实专家代理")
    print("-" * 50)
    
    try:
        from agents.agent_manager import AgentManager
        from core.llm_client import LLMClient
        
        # 创建LLM客户端和代理管理器
        llm_client = LLMClient(model="deepseek-chat", api_key=API_KEY)
        agent_manager = AgentManager(llm_client)
        
        # 测试AI技术专家
        ai_expert = agent_manager.get_agent("ai_technology")
        if ai_expert:
            print(f"✅ AI技术专家获取成功")
            
            # 测试专家分析
            print(f"   测试专家分析...")
            test_input = {
                "input_text": "请分析一下基于注意力机制的Transformer架构在自然语言处理中的优势和局限性。",
                "analysis_type": "technology_analysis"
            }
            
            start_time = time.time()
            response = ai_expert.analyze(test_input)
            analysis_time = time.time() - start_time
            
            if response and hasattr(response, 'content'):
                print(f"✅ 专家分析完成")
                print(f"   分析时间: {analysis_time:.2f}秒")
                print(f"   置信度: {getattr(response, 'confidence', 'N/A')}")
                print(f"   分析预览: {response.content[:150]}...")
                return True, agent_manager
            else:
                print(f"❌ 专家分析失败")
                return False, None
        else:
            print(f"❌ AI技术专家获取失败")
            return False, None
            
    except Exception as e:
        print(f"❌ 专家代理测试失败: {e}")
        return False, None


def test_real_reasoning_workflow():
    """测试真实推理工作流"""
    
    print(f"\n🧠 测试真实推理工作流")
    print("-" * 50)
    
    try:
        from enhanced_reasoning_demo import EnhancedReasoningWorkflow
        
        # 创建增强工作流（使用真实API）
        workflow = EnhancedReasoningWorkflow(api_key=API_KEY, use_real_api=True)
        
        print(f"✅ 增强推理工作流初始化成功")
        print(f"   专家团队规模: {len(workflow.enhanced_agent_manager.agents)}")
        print(f"   注册的专家: {list(workflow.enhanced_agent_manager.agents.keys())}")
        
        # 真实的脑启发智能研究问题
        research_question = "如何基于视觉皮层V1区的方向选择性机制设计更高效的卷积神经网络？"
        
        hypothesis = [
            "V1区神经元的方向选择性可以指导卷积核的设计和初始化",
            "简单细胞和复杂细胞的层次化处理可以改进CNN的特征提取效率",
            "侧抑制机制能够增强网络的对比度敏感性和噪声鲁棒性"
        ]
        
        background = {
            "domain": "brain-inspired computer vision",
            "biological_basis": "视觉皮层V1区的方向选择性和层次化处理",
            "technical_motivation": "提高CNN的效率和生物合理性",
            "research_gap": "现有CNN缺乏生物视觉系统的精细化特征"
        }
        
        print(f"\n🔬 研究问题: {research_question}")
        print(f"💡 假设数量: {len(hypothesis)}")
        
        # 进度追踪
        progress_log = []
        def track_progress(stage: str, description: str, progress: float):
            progress_info = f"[{progress*100:5.1f}%] {stage}: {description}"
            progress_log.append(progress_info)
            print(f"📊 {progress_info}")
        
        # 运行推理流程
        print(f"\n🚀 开始真实推理流程...")
        start_time = time.time()
        
        session = workflow.run_enhanced_reasoning_flow(
            research_question=research_question,
            hypothesis=hypothesis,
            background=background,
            target_venue="CVPR",
            enable_deep_analysis=True,
            progress_callback=track_progress
        )
        
        total_time = time.time() - start_time
        
        if session:
            print(f"\n🎉 真实推理流程完成!")
            print(f"   总执行时间: {total_time:.2f}秒")
            print(f"   会话ID: {session.session_id}")
            
            # 分析真实结果
            return analyze_real_results(session, progress_log)
        else:
            print(f"❌ 推理流程失败")
            return False, None
            
    except Exception as e:
        print(f"❌ 推理工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None


def analyze_real_results(session, progress_log):
    """分析真实API调用的结果质量"""
    
    print(f"\n📊 真实结果质量分析")
    print("-" * 50)
    
    analysis_report = {
        "session_id": session.session_id,
        "timestamp": datetime.now().isoformat(),
        "api_mode": "real_deepseek_api",
        "quality_metrics": {}
    }
    
    # 1. 研究问题评估质量
    if session.research_problem:
        score = session.research_problem.value_score
        expert_opinions = session.research_problem.expert_opinions
        
        print(f"🎯 研究问题评估:")
        print(f"   综合评分: {score:.2f}/10")
        print(f"   专家意见数量: {len(expert_opinions)}")
        
        # 检查是否是真实专家意见（非默认值）
        is_real_evaluation = score != 5.0 or len(expert_opinions) > 0
        analysis_report["quality_metrics"]["real_evaluation"] = is_real_evaluation
        
        if is_real_evaluation:
            print(f"   ✅ 检测到真实专家评估")
        else:
            print(f"   ⚠️ 可能仍为模拟评估")
    
    # 2. 实验设计质量
    if session.experiment_plan:
        variables = len(session.experiment_plan.variables)
        metrics = len(session.experiment_plan.metrics)
        
        print(f"\n🔬 实验设计:")
        print(f"   实验类型: {session.experiment_plan.experiment_type}")
        print(f"   变量数量: {variables}")
        print(f"   评估指标: {metrics}")
        
        analysis_report["quality_metrics"]["experiment_complexity"] = variables + metrics
    
    # 3. 实现方案质量
    if session.implementation_plan:
        steps = len(session.implementation_plan.steps)
        frameworks = len(session.implementation_plan.frameworks)
        
        print(f"\n⚙️ 实现方案:")
        print(f"   编程语言: {session.implementation_plan.programming_language}")
        print(f"   框架数量: {frameworks}")
        print(f"   实现步骤: {steps}")
        
        analysis_report["quality_metrics"]["implementation_detail"] = steps
    
    # 4. 可视化方案质量  
    if session.visualization_plan:
        charts = len(session.visualization_plan.charts)
        tools = len(session.visualization_plan.recommended_tools)
        
        print(f"\n🎨 可视化方案:")
        print(f"   图表数量: {charts}")
        print(f"   推荐工具: {tools}")
        
        analysis_report["quality_metrics"]["visualization_richness"] = charts + tools
    
    # 5. 整体质量评估
    print(f"\n📈 整体质量评估:")
    
    quality_indicators = []
    if analysis_report["quality_metrics"].get("real_evaluation", False):
        quality_indicators.append("✅ 真实专家评估")
    else:
        quality_indicators.append("⚠️ 疑似模拟评估")
    
    if analysis_report["quality_metrics"].get("experiment_complexity", 0) > 5:
        quality_indicators.append("✅ 详细实验设计")
    else:
        quality_indicators.append("⚠️ 简单实验设计")
    
    if analysis_report["quality_metrics"].get("implementation_detail", 0) > 5:
        quality_indicators.append("✅ 详细实现指导")
    else:
        quality_indicators.append("⚠️ 基础实现指导")
    
    for indicator in quality_indicators:
        print(f"   {indicator}")
    
    # 保存分析报告
    report_path = f"./output/real_api_analysis_{int(time.time())}.json"
    try:
        os.makedirs("./output", exist_ok=True)
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_report, f, ensure_ascii=False, indent=2)
        print(f"\n💾 分析报告已保存: {report_path}")
    except Exception as e:
        print(f"⚠️ 报告保存失败: {e}")
    
    return True, analysis_report


def compare_mock_vs_real():
    """对比模拟模式和真实API的效果差异"""
    
    print(f"\n🔄 对比模拟模式 vs 真实API")
    print("-" * 50)
    
    # 这里可以加载之前的模拟结果进行对比
    print(f"📊 预期差异:")
    print(f"   ⏱️ 响应时间: 模拟<1秒 vs 真实API 5-15秒")
    print(f"   📝 内容质量: 模拟(预设模板) vs 真实API(动态生成)")
    print(f"   🎯 针对性: 模拟(通用) vs 真实API(问题特定)")
    print(f"   💡 创新性: 模拟(有限) vs 真实API(更丰富)")


def main():
    """主测试函数"""
    
    print("🧪 真实API推理系统测试")
    print("=" * 80)
    print(f"🔑 使用API密钥: {API_KEY[:8]}...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = {}
    
    # 1. 测试API连接
    api_success, llm_client = test_real_api_connection()
    test_results["api_connection"] = api_success
    
    if api_success:
        # 2. 测试专家代理
        expert_success, agent_manager = test_real_expert_agents()
        test_results["expert_agents"] = expert_success
        
        # 3. 测试推理工作流
        workflow_success, analysis = test_real_reasoning_workflow()
        test_results["reasoning_workflow"] = workflow_success
        
        # 4. 对比分析
        compare_mock_vs_real()
    
    # 生成最终报告
    print(f"\n📋 测试总结")
    print("=" * 80)
    
    success_count = sum(test_results.values())
    total_count = len(test_results)
    success_rate = success_count / total_count * 100
    
    print(f"🎯 总体成功率: {success_rate:.1f}% ({success_count}/{total_count})")
    
    for test_name, success in test_results.items():
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if success_rate == 100:
        print(f"\n🎉 所有测试通过! 真实API推理系统工作正常!")
        print(f"💡 建议: 可以开始使用真实API进行研究问题分析")
    elif success_rate >= 75:
        print(f"\n✅ 主要功能正常! 部分组件需要优化")
        print(f"💡 建议: 检查失败的组件并进行修复")
    else:
        print(f"\n⚠️ 系统存在问题，需要进一步调试")
        print(f"💡 建议: 检查API配置和依赖模块")
    
    return test_results


if __name__ == "__main__":
    main()
