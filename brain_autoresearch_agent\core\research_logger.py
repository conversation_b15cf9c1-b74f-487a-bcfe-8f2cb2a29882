#!/usr/bin/env python3
"""
研究日志记录器模块
用于记录整个研究过程中的关键步骤、决策和输出
"""

import os
import json
import logging
from datetime import datetime
import time
from typing import Dict, Any, Optional, List, Union

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ResearchLogger")

class ResearchLogger:
    """研究日志记录器，记录研究过程中的各个阶段和结果"""
    
    def __init__(self, output_dir: str, research_topic: str):
        """
        初始化研究日志记录器
        
        Args:
            output_dir: 输出目录
            research_topic: 研究主题
        """
        self.research_topic = research_topic
        self.output_dir = output_dir
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_filename = f"research_log_{timestamp}.json"
        self.log_path = os.path.join(output_dir, self.log_filename)
        
        # 初始化日志数据
        self.log_data = {
            "research_topic": research_topic,
            "timestamp": timestamp,
            "start_time": time.time(),
            "stages": {},
            "metrics": {},
            "paper_generation": None,
        }
        
        # 保存初始日志
        self._save_log()
        logger.info(f"研究日志已初始化: {self.log_path}")
    
    def log_literature_search(self, search_results: Dict[str, Any]) -> None:
        """记录文献检索结果"""
        self.log_data["stages"]["literature_search"] = {
            "timestamp": time.time(),
            "num_papers": len(search_results.get("papers", [])),
            "search_terms": search_results.get("search_terms", []),
            "paper_ids": [p.get("id", "") for p in search_results.get("papers", [])] if isinstance(search_results.get("papers", []), list) else []
        }
        self._save_log()
    
    def log_workflow_extraction(self, workflows: List[Dict[str, Any]]) -> None:
        """记录工作流提取结果"""
        self.log_data["stages"]["workflow_extraction"] = {
            "timestamp": time.time(),
            "num_workflows": len(workflows),
            "workflow_summary": [
                {
                    "paper_id": w.get("paper_id", ""),
                    "datasets": w.get("datasets", []),
                    "methods": w.get("research_methods", [])
                }
                for w in workflows
            ]
        }
        self._save_log()
    
    def log_reasoning_stage(self, research_problem: Dict[str, Any]) -> None:
        """记录推理阶段结果"""
        self.log_data["stages"]["reasoning"] = {
            "timestamp": time.time(),
            "research_questions": research_problem.get("research_questions", []),
            "significance": research_problem.get("significance", ""),
            "novelty": research_problem.get("novelty", "")
        }
        self._save_log()
    
    def log_experiment_design(self, experiment_plan: Dict[str, Any]) -> None:
        """记录实验设计结果"""
        self.log_data["stages"]["experiment_design"] = {
            "timestamp": time.time(),
            "hypothesis": experiment_plan.get("hypothesis", ""),
            "methods": experiment_plan.get("methods", []),
            "datasets": experiment_plan.get("datasets", []),
            "evaluation_metrics": experiment_plan.get("evaluation_metrics", [])
        }
        self._save_log()
    
    def log_implementation_plan(self, implementation_plan: Dict[str, Any]) -> None:
        """记录实现计划结果"""
        self.log_data["stages"]["implementation_plan"] = {
            "timestamp": time.time(),
            "frameworks": implementation_plan.get("frameworks", []),
            "dependencies": implementation_plan.get("dependencies", []),
            "code_structure": implementation_plan.get("code_structure", {})
        }
        self._save_log()
    
    def log_paper_generation(self, paper_path: str, paper_data: Dict[str, Any]) -> None:
        """记录论文生成结果"""
        self.log_data["paper_generation"] = {
            "timestamp": time.time(),
            "paper_path": paper_path,
            "title": paper_data.get("title", ""),
            "abstract": paper_data.get("abstract", ""),
            "sections": [s.get("title", "") for s in paper_data.get("sections", [])]
        }
        self._save_log()
    
    def log_metric(self, metric_name: str, value: Union[float, int, str]) -> None:
        """记录性能指标"""
        self.log_data["metrics"][metric_name] = {
            "timestamp": time.time(),
            "value": value
        }
        self._save_log()
    
    def _save_log(self) -> None:
        """保存日志数据到文件"""
        # 更新总运行时间
        self.log_data["total_runtime"] = time.time() - self.log_data["start_time"]
        
        # 保存到文件
        try:
            # 确保可序列化
            json_data = self._ensure_serializable(self.log_data)
            
            with open(self.log_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"保存研究日志失败: {e}")
    
    def _ensure_serializable(self, obj: Any) -> Any:
        """确保对象可JSON序列化"""
        if isinstance(obj, dict):
            return {k: self._ensure_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._ensure_serializable(i) for i in obj]
        elif hasattr(obj, '__dict__'):
            return self._ensure_serializable(obj.__dict__)
        elif hasattr(obj, 'to_dict') and callable(getattr(obj, 'to_dict')):
            return self._ensure_serializable(obj.to_dict())
        else:
            # 尝试转换为字符串
            try:
                return str(obj)
            except:
                return None
    
    def get_log_path(self) -> str:
        """获取日志文件路径"""
        return self.log_path
    
    def get_summary(self) -> Dict[str, Any]:
        """获取研究过程摘要"""
        summary = {
            "research_topic": self.research_topic,
            "duration_minutes": round((time.time() - self.log_data["start_time"]) / 60, 2),
            "completed_stages": list(self.log_data["stages"].keys()),
            "paper_title": self.log_data.get("paper_generation", {}).get("title", "未生成")
        }
        return summary 