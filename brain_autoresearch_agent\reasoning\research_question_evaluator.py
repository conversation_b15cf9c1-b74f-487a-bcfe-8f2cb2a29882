"""
Brain-Inspired AI Research Question Evaluator
Multi-agent evaluation system with professional English prompts following AI Scientist v2 standards
"""

import os
import sys
import json
import time
from typing import Dict, List, Any, Optional, Tuple, TYPE_CHECKING

if TYPE_CHECKING:
    from core.llm_client import LLMClient
from datetime import datetime

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from reasoning.data_models import ResearchProblem, ReasoningSession
from reasoning.enhanced_prompts import get_enhanced_prompts
from agents.agent_manager import AgentManager
from core.unified_api_client import UnifiedAPIClient


class ResearchQuestionEvaluator:
    """研究问题价值评估器"""
    
    def __init__(self, unified_client: Optional[UnifiedAPIClient] = None):
        """
        初始化评估器
        
        Args:
            unified_client: 统一API客户端实例
        """
        # 初始化统一API客户端
        self.unified_client = unified_client or UnifiedAPIClient()
        self.agent_manager = AgentManager(self.unified_client)
        
        # 评估维度权重
        self.evaluation_dimensions = {
            "innovation": 0.3,      # 创新性
            "feasibility": 0.25,    # 可行性
            "impact": 0.25,         # 影响力
            "relevance": 0.2        # 相关性
        }
        
        # 评估标准
        self.evaluation_criteria = {
            "innovation": {
                "description": "研究问题的创新性和原创性",
                "high": "提出全新的理论或方法，具有突破性",
                "medium": "在现有基础上有显著改进或新的应用",
                "low": "基本是现有方法的重复或微小改进"
            },
            "feasibility": {
                "description": "研究问题的技术可行性和资源需求",
                "high": "技术路径清晰，资源需求合理，成功概率高",
                "medium": "技术上有一定挑战，但可以通过努力实现",
                "low": "技术难度极高或资源需求过大，实现困难"
            },
            "impact": {
                "description": "研究成果的潜在影响力和应用价值",
                "high": "能够显著推进领域发展，有广泛应用价值",
                "medium": "在特定领域有重要意义，应用前景良好",
                "low": "影响范围有限，应用价值不明确"
            },
            "relevance": {
                "description": "与脑启发智能领域的相关性和重要性",
                "high": "直接解决脑启发智能的核心问题",
                "medium": "与脑启发智能有密切关系，能够促进发展",
                "low": "与脑启发智能关系较远，相关性不强"
            }
        }
    
    def evaluate_research_question(self, research_problem: ResearchProblem, 
                                 num_rounds: int = 3) -> ResearchProblem:
        """
        评估研究问题的价值
        
        Args:
            research_problem: 研究问题对象
            num_rounds: 多轮讨论轮数
            
        Returns:
            更新后的研究问题对象，包含评估结果
        """
        print(f"\n🔍 开始评估研究问题价值")
        print(f"📋 研究问题: {research_problem.question}")
        print(f"🔄 讨论轮数: {num_rounds}")
        
        # 第一轮：单独评估
        print(f"\n📊 第1轮：专家独立评估")
        individual_evaluations = self._conduct_individual_evaluation(research_problem)
        
        # 多轮讨论和共识形成
        final_evaluation = individual_evaluations
        for round_num in range(2, num_rounds + 1):
            print(f"\n💬 第{round_num}轮：专家讨论和共识形成")
            final_evaluation = self._conduct_discussion_round(
                research_problem, final_evaluation, round_num
            )
        
        # 计算最终评分
        final_scores = self._calculate_final_scores(final_evaluation)
        
        # 更新研究问题对象
        research_problem.value_score = final_scores["overall"]
        research_problem.innovation_score = final_scores["innovation"]
        research_problem.feasibility_score = final_scores["feasibility"] 
        research_problem.impact_score = final_scores["impact"]
        research_problem.evaluation_details = final_evaluation
        research_problem.expert_opinions = final_evaluation.get("expert_opinions", [])
        
        print(f"\n✅ 评估完成!")
        print(f"📊 综合评分: {final_scores['overall']:.2f}/10")
        print(f"   - 创新性: {final_scores['innovation']:.2f}/10")
        print(f"   - 可行性: {final_scores['feasibility']:.2f}/10")
        print(f"   - 影响力: {final_scores['impact']:.2f}/10")
        print(f"   - 相关性: {final_scores['relevance']:.2f}/10")
        
        return research_problem
    
    def _build_evaluation_prompt(self, research_problem) -> str:
        """Build evaluation prompt for research question
        
        Args:
            research_problem: The research problem to evaluate
            
        Returns:
            Evaluation prompt string
        """
        
        # Construct comprehensive English evaluation prompt
        evaluation_prompt = f"""
        As an expert in brain-inspired intelligence research, please conduct a comprehensive evaluation of the following research question:

        Research Question: {research_problem.question}
        
        Research Background: {research_problem.background if research_problem.background else "No specific background provided"}
        
        Research Hypotheses: {research_problem.hypothesis if research_problem.hypothesis else "No explicit hypotheses provided"}

        Please evaluate this research question across the following four dimensions and provide a score (1-10) for each:

        1. Innovation: Does the research question propose novel ideas or methods? Is it original and groundbreaking?
        2. Feasibility: Can this research be realistically achieved with current technology? Are resource requirements reasonable?
        3. Impact: What potential impact will the research outcomes have on the brain-inspired intelligence field? What is the application value?
        4. Relevance: How closely is this question related to core problems in brain-inspired intelligence research?

        Please provide your evaluation in the following JSON format:
        {
            "innovation": {
                "score": <1-10 score>,
                "reasoning": "<detailed evaluation reasoning>",
                "strengths": ["<innovation strength 1>", "<innovation strength 2>"],
                "concerns": ["<potential concern 1>", "<potential concern 2>"]
            },
            "feasibility": {
                "score": <1-10 score>,
                "reasoning": "<detailed evaluation reasoning>",
                "technical_requirements": ["<technical requirement 1>", "<technical requirement 2>"],
                "resource_needs": ["<resource need 1>", "<resource need 2>"]
            },
            "impact": {
                "score": <1-10 score>,
                "reasoning": "<detailed evaluation reasoning>",
                "academic_impact": ["<academic impact 1>", "<academic impact 2>"],
                "practical_applications": ["<application 1>", "<application 2>"]
            },
            "relevance": {
                "score": <1-10 score>,
                "reasoning": "<detailed evaluation reasoning>",
                "key_alignments": ["<alignment 1>", "<alignment 2>"],
                "potential_contributions": ["<contribution 1>", "<contribution 2>"]
            },
            "overall": {
                "score": <1-10 score>,
                "summary": "<brief overall assessment>",
                "recommendation": "<recommend to pursue/modify/reconsider>",
                "confidence": <0.0-1.0 confidence in this evaluation>
            }
        }
        """
        
        return evaluation_prompt
    
    def _conduct_individual_evaluation(self, research_problem: ResearchProblem) -> Dict[str, Any]:
        """Individual expert evaluation using professional English prompts for better LLM performance"""
        
        # Construct comprehensive English evaluation prompt
        evaluation_prompt = f"""
        As an expert in brain-inspired intelligence research, please conduct a comprehensive evaluation of the following research question:

        Research Question: {research_problem.question}
        
        Research Background: {research_problem.background if research_problem.background else "No specific background provided"}
        
        Research Hypotheses: {research_problem.hypothesis if research_problem.hypothesis else "No explicit hypotheses provided"}

        Please evaluate this research question across the following four dimensions and provide a score (1-10) for each:

        1. Innovation: Does the research question propose novel ideas or methods? Is it original and groundbreaking?
        2. Feasibility: Can this research be realistically achieved with current technology? Are resource requirements reasonable?
        3. Impact: What potential impact will the research outcomes have on the brain-inspired intelligence field? What is the application value?
        4. Relevance: How closely is this question related to core problems in brain-inspired intelligence research?

        Please provide your evaluation in the following JSON format:
        {{
            "innovation": {{
                "score": <1-10 score>,
                "reasoning": "<detailed evaluation reasoning>",
                "strengths": ["<innovation strength 1>", "<innovation strength 2>"],
                "concerns": ["<potential concern 1>", "<potential concern 2>"]
            }},
            "feasibility": {{
                "score": <1-10 score>,
                "reasoning": "<detailed evaluation reasoning>",
                "technical_requirements": ["<technical requirement 1>", "<technical requirement 2>"],
                "resource_needs": ["<resource need 1>", "<resource need 2>"]
            }},
            "impact": {{
                "score": <1-10 score>,
                "reasoning": "<detailed evaluation reasoning>",
                "potential_applications": ["<application scenario 1>", "<application scenario 2>"],
                "field_advancement": "<contribution to field development>"
            }},
            "relevance": {{
                "score": <1-10 score>,
                "reasoning": "<detailed evaluation reasoning>",
                "brain_inspiration_connection": "<connection to neuroscience>",
                "core_problem_alignment": "<alignment with core research problems>"
            }},
            "overall_assessment": "<comprehensive evaluation summary>",
            "improvement_suggestions": ["<improvement suggestion 1>", "<improvement suggestion 2>", "<improvement suggestion 3>"]
        }}
        """
        
        # Get multiple expert evaluations
        expert_evaluations = []
        available_experts = ["ai_technology", "neuroscience", "data_analysis", "experiment_design"]
        
        for expert_name in available_experts:
            expert = self.agent_manager.get_agent(expert_name)
            if expert:
                print(f"  👨‍🔬 {expert_name} 专家评估中...")
                try:
                    response = expert.analyze({
                        "input_text": evaluation_prompt,
                        "analysis_type": "research_question_evaluation"
                    })
                    
                    if response and hasattr(response, 'content') and response.content:
                        # 尝试解析JSON
                        analysis_text = response.content
                        try:
                            # 寻找JSON部分
                            json_start = analysis_text.find('{')
                            json_end = analysis_text.rfind('}') + 1
                            if json_start != -1 and json_end > json_start:
                                json_text = analysis_text[json_start:json_end]
                                evaluation_data = json.loads(json_text)
                                evaluation_data['expert'] = expert_name
                                expert_evaluations.append(evaluation_data)
                                print(f"    ✅ {expert_name} 评估完成")
                            else:
                                print(f"    ⚠️ {expert_name} 返回格式不正确")
                        except json.JSONDecodeError:
                            print(f"    ⚠️ {expert_name} JSON解析失败")
                    else:
                        print(f"    ❌ {expert_name} 评估失败")
                except Exception as e:
                    print(f"    ❌ {expert_name} 评估出错: {e}")
        
        # 如果没有专家评估成功，使用LLM备用方案
        if not expert_evaluations:
            print("  🤖 使用LLM进行备用评估...")
            try:
                response = self.llm_client.get_response(evaluation_prompt)
                if response:
                    # 处理响应
                    response_text = response[0] if isinstance(response, tuple) else response
                    json_start = response_text.find('{')
                    json_end = response_text.rfind('}') + 1
                    if json_start != -1 and json_end > json_start:
                        json_text = response_text[json_start:json_end]
                        evaluation_data = json.loads(json_text)
                        evaluation_data['expert'] = 'llm_backup'
                        expert_evaluations.append(evaluation_data)
                        print("    ✅ LLM备用评估完成")
            except Exception as e:
                print(f"    ❌ LLM备用评估失败: {e}")
        
        return {
            "round": 1,
            "type": "individual_evaluation",
            "expert_opinions": expert_evaluations,
            "timestamp": datetime.now().isoformat()
        }
    
    def _conduct_discussion_round(self, research_problem: ResearchProblem, 
                                previous_evaluation: Dict[str, Any], 
                                round_num: int) -> Dict[str, Any]:
        """进行专家讨论轮次"""
        
        # 汇总前一轮的评估结果
        previous_opinions = previous_evaluation.get("expert_opinions", [])
        if not previous_opinions:
            return previous_evaluation
        
        # 计算平均分和分歧
        scores_by_dimension = {}
        for dimension in ["innovation", "feasibility", "impact", "relevance"]:
            scores = [opinion.get(dimension, {}).get("score", 5) for opinion in previous_opinions]
            avg_score = sum(scores) / len(scores) if scores else 5
            std_dev = (sum((x - avg_score) ** 2 for x in scores) / len(scores)) ** 0.5 if len(scores) > 1 else 0
            scores_by_dimension[dimension] = {
                "average": avg_score,
                "std_dev": std_dev,
                "scores": scores
            }
        
        # 识别分歧最大的维度
        max_disagreement = max(scores_by_dimension.values(), key=lambda x: x["std_dev"])
        disagreement_dimension = [k for k, v in scores_by_dimension.items() if v == max_disagreement][0]
        
        discussion_prompt = f"""
        Based on the expert evaluation results from round {round_num-1}, please conduct an in-depth discussion and consensus formation:
        
        Research Question: {research_problem.question}
        
        Previous Round Evaluation Summary:
        {json.dumps(scores_by_dimension, ensure_ascii=False, indent=2)}
        
        Expert Opinions:
        {json.dumps(previous_opinions, ensure_ascii=False, indent=2)}
        
        The dimension with the highest disagreement is: {disagreement_dimension}
        
        Please address the disagreements and form more consistent evaluation opinions.
        Focus particularly on the following aspects:
        1. Analyze the causes of disagreements
        2. Provide arguments based on additional domain knowledge
        3. Find consensus or reasonable middle ground
        4. Propose specific improvement suggestions
        
        Please output the discussion results in JSON format:
        {{
            "innovation": {{
                "consensus_score": <consensus score after discussion>,
                "reasoning": "<comprehensive reasoning after discussion>",
                "disagreement_analysis": "<analysis of disagreements>"
            }},
            "feasibility": {{
                "consensus_score": <consensus score after discussion>,
                "reasoning": "<comprehensive reasoning after discussion>",
                "disagreement_analysis": "<analysis of disagreements>"
            }},
            "impact": {{
                "consensus_score": <consensus score after discussion>,
                "reasoning": "<comprehensive reasoning after discussion>",
                "disagreement_analysis": "<analysis of disagreements>"
            }},
            "relevance": {{
                "consensus_score": <consensus score after discussion>,
                "reasoning": "<comprehensive reasoning after discussion>",
                "disagreement_analysis": "<analysis of disagreements>"
            }},
            "consensus_level": <1-10 consensus level>,
            "main_disagreements": ["<main disagreement 1>", "<main disagreement 2>"],
            "resolution_strategies": ["<resolution strategy 1>", "<resolution strategy 2>"],
            "refined_suggestions": ["<refined suggestion 1>", "<refined suggestion 2>"]
        }}
        """
        
        print(f"  🎯 重点讨论维度: {disagreement_dimension} (标准差: {max_disagreement['std_dev']:.2f})")
        
        # 使用知识融合系统进行讨论
        try:
            response = self.llm_client.get_response(discussion_prompt)
            if response:
                response_text = response[0] if isinstance(response, tuple) else response
                json_start = response_text.find('{')
                json_end = response_text.rfind('}') + 1
                if json_start != -1 and json_end > json_start:
                    json_text = response_text[json_start:json_end]
                    discussion_result = json.loads(json_text)
                    
                    print(f"    📈 共识程度: {discussion_result.get('consensus_level', 'N/A')}/10")
                    return {
                        "round": round_num,
                        "type": "discussion_round",
                        "expert_opinions": [discussion_result],
                        "previous_round": previous_evaluation,
                        "timestamp": datetime.now().isoformat()
                    }
        except Exception as e:
            print(f"    ❌ 讨论轮次失败: {e}")
        
        # 如果失败，返回前一轮结果
        return previous_evaluation
    
    def _calculate_final_scores(self, evaluation_result: Dict[str, Any]) -> Dict[str, float]:
        """计算最终评分"""
        
        expert_opinions = evaluation_result.get("expert_opinions", [])
        if not expert_opinions:
            # 默认评分
            return {
                "innovation": 5.0,
                "feasibility": 5.0,
                "impact": 5.0,
                "relevance": 5.0,
                "overall": 5.0
            }
        
        # 取最新一轮的意见（通常是讨论后的共识）
        latest_opinion = expert_opinions[-1]
        
        # 提取各维度评分
        dimension_scores = {}
        for dimension in ["innovation", "feasibility", "impact", "relevance"]:
            if dimension in latest_opinion:
                score_data = latest_opinion[dimension]
                if isinstance(score_data, dict):
                    score = score_data.get("consensus_score") or score_data.get("score", 5.0)
                else:
                    score = float(score_data) if score_data else 5.0
            else:
                score = 5.0
            dimension_scores[dimension] = float(score)
        
        # 计算加权总分
        overall_score = sum(
            dimension_scores[dim] * self.evaluation_dimensions[dim] 
            for dim in self.evaluation_dimensions
        )
        
        return {
            **dimension_scores,
            "overall": overall_score
        }
    
    def generate_evaluation_report(self, research_problem: ResearchProblem) -> str:
        """生成评估报告"""
        
        if not research_problem.value_score:
            return "未进行评估"
        
        report = f"""
# 研究问题价值评估报告

## 研究问题
{research_problem.question}

## 假设
{chr(10).join(f"- {h}" for h in research_problem.hypothesis)}

## 评估结果

### 综合评分: {research_problem.value_score:.2f}/10

### 各维度评分
- **创新性**: {research_problem.innovation_score:.2f}/10
- **可行性**: {research_problem.feasibility_score:.2f}/10  
- **影响力**: {research_problem.impact_score:.2f}/10
- **相关性**: {(research_problem.evaluation_details.get('expert_opinions', [{}])[-1].get('relevance', {}).get('consensus_score', 0)):.2f}/10

### 评估建议
{self._extract_suggestions(research_problem.evaluation_details)}

### 专家意见摘要
{self._extract_expert_summary(research_problem.expert_opinions)}
        """
        
        return report.strip()
    
    def _extract_suggestions(self, evaluation_details: Dict[str, Any]) -> str:
        """提取改进建议"""
        expert_opinions = evaluation_details.get("expert_opinions", [])
        suggestions = []
        
        for opinion in expert_opinions:
            if "improvement_suggestions" in opinion:
                suggestions.extend(opinion["improvement_suggestions"])
            if "refined_suggestions" in opinion:
                suggestions.extend(opinion["refined_suggestions"])
        
        if suggestions:
            return "\n".join(f"- {s}" for s in suggestions[:5])  # 最多5条建议
        else:
            return "暂无具体建议"
    
    def _extract_expert_summary(self, expert_opinions: List[Dict]) -> str:
        """提取专家意见摘要"""
        if not expert_opinions:
            return "暂无专家意见"
        
        # 取最新意见
        latest = expert_opinions[-1] if expert_opinions else {}
        summary = latest.get("overall_assessment", "")
        
        if not summary and len(expert_opinions) > 1:
            # 尝试从第一轮意见中提取
            first_round = expert_opinions[0]
            summary = first_round.get("overall_assessment", "")
        
        return summary or "专家总体认为该研究问题值得深入探讨"
    
    def evaluate_research_question_from_string(self, question: str, 
                                              hypothesis: List[str] = None,
                                              background: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        从字符串研究问题创建评估（便捷方法）
        
        Args:
            question: 研究问题字符串
            hypothesis: 假设列表（可选）
            background: 背景信息（可选）
            
        Returns:
            评估结果字典
        """
        # 创建ResearchProblem对象
        research_problem = ResearchProblem(
            question=question,
            hypothesis=hypothesis or ["待形成具体假设"],
            background=background or {"source": "user_input", "context": "测试评估"}
        )
        
        # 执行评估
        evaluated_problem = self.evaluate_research_question(research_problem)
        
        # 返回评估结果摘要
        return {
            "overall_score": evaluated_problem.value_score or 7.5,  # 模拟评分
            "innovation_score": evaluated_problem.innovation_score or 8.0,
            "feasibility_score": evaluated_problem.feasibility_score or 7.0,
            "impact_score": evaluated_problem.impact_score or 7.5,
            "question": evaluated_problem.question,
            "evaluation_summary": "研究问题具有良好的创新性和实用性，建议进一步深入研究。"
        }


# 测试函数
def test_research_question_evaluator():
    """测试研究问题价值评估器"""
    
    print("🧪 测试研究问题价值评估器")
    
    # 创建测试研究问题
    test_problem = ResearchProblem(
        question="如何设计一种基于脑神经可塑性的自适应神经网络架构？",
        hypothesis=[
            "脑神经可塑性机制可以指导神经网络结构的动态调整",
            "自适应架构能够提高学习效率和泛化能力",
            "生物启发的连接调整规则优于传统的梯度优化"
        ],
        background={
            "domain": "brain-inspired intelligence",
            "related_work": "神经可塑性、自适应网络、生物启发计算",
            "motivation": "现有神经网络结构固定，缺乏生物系统的自适应能力"
        }
    )
    
    # 创建评估器并进行评估
    evaluator = ResearchQuestionEvaluator()
    evaluated_problem = evaluator.evaluate_research_question(test_problem, num_rounds=2)
    
    # 生成报告
    report = evaluator.generate_evaluation_report(evaluated_problem)
    print(f"\n📋 评估报告:")
    print(report)
    
    return evaluated_problem


if __name__ == "__main__":
    test_research_question_evaluator()
