"""
快速API测试脚本
"""

import os
from openai import OpenAI

# 设置API密钥
os.environ['DEEPSEEK_API_KEY'] = '***********************************'
os.environ['DEEPSEEK_BASE_URL'] = 'https://api.deepseek.com'

print("🔧 测试DeepSeek API...")

try:
    client = OpenAI(
        api_key=os.environ['DEEPSEEK_API_KEY'],
        base_url=os.environ['DEEPSEEK_BASE_URL']
    )
    
    response = client.chat.completions.create(
        model='deepseek-chat',
        messages=[{'role': 'user', 'content': 'Please briefly introduce artificial intelligence in one sentence.'}],
        max_tokens=100
    )
    
    print("✅ DeepSeek API测试成功")
    print(f"📝 响应: {response.choices[0].message.content}")
    
except Exception as e:
    print(f"❌ DeepSeek API测试失败: {e}")

print("\n🧪 准备端到端系统测试...")
print("💡 API已配置完成，可以运行完整测试")
