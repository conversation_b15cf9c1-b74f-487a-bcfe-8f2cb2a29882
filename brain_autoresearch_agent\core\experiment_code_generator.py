"""
实验代码生成器
负责根据实验设计生成相应的代码
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Union

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ExperimentCodeGenerator:
    """实验代码生成器"""
    
    def __init__(self, api_client=None):
        """
        初始化代码生成器
        
        Args:
            api_client: 统一API客户端
        """
        self.api_client = api_client
        logger.info("✅ 实验代码生成器初始化")
    
    def generate_experiment_code(self, 
                               experiment_plan: Dict[str, Any],
                               framework: str = "pytorch",
                               code_style: str = "academic") -> Dict[str, Any]:
        """
        生成实验代码
        
        Args:
            experiment_plan: 实验计划
            framework: 框架 (pytorch, tensorflow)
            code_style: 代码风格 (academic, industrial)
            
        Returns:
            生成的代码及相关信息
        """
        logger.info(f"🧪 生成{framework}实验代码...")
        
        # 准备提示词
        prompt = self._create_code_generation_prompt(experiment_plan, framework, code_style)
        
        # 调用API生成代码
        if self.api_client:
            try:
                # 使用推理模式生成代码
                response = self.api_client.get_text_response(
                    prompt=prompt,
                    system_message="You are an expert AI researcher and programmer. Your task is to generate high-quality, executable code for machine learning experiments based on the provided experimental design.",
                    model_type="reasoning",
                    temperature=0.2,  # 低温度，保证代码质量
                    max_tokens=4000
                )
                
                # 解析代码和描述
                code, code_description = self._parse_code_response(response.content)
                
                logger.info(f"✅ 代码生成成功，长度: {len(code)} 字符")
                return {
                    'code': code,
                    'code_description': code_description,
                    'framework': framework,
                    'success': True
                }
            except Exception as e:
                logger.error(f"❌ API调用失败: {e}")
                return self._generate_fallback_code(experiment_plan, framework)
        else:
            # 无API客户端，使用备选实现
            return self._generate_fallback_code(experiment_plan, framework)
    
    def _create_code_generation_prompt(self, 
                                   experiment_plan: Dict[str, Any],
                                   framework: str,
                                   code_style: str) -> str:
        """
        Create code generation prompt
        
        Args:
            experiment_plan: Experiment plan
            framework: Framework
            code_style: Code style
            
        Returns:
            Prompt string
        """
        # Extract key information from experiment plan
        research_question = experiment_plan.get('research_question', 'Brain-inspired learning algorithms')
        experiment_description = experiment_plan.get('description', '')
        methods = experiment_plan.get('methods', [])
        datasets = experiment_plan.get('datasets', [])
        metrics = experiment_plan.get('evaluation_metrics', [])
        
        # Build the prompt
        prompt = f"""
Generate complete Python code for a machine learning experiment with the following specifications:

RESEARCH QUESTION:
{research_question}

EXPERIMENT DESCRIPTION:
{experiment_description}

METHOD/ALGORITHM:
{self._format_list(methods)}

DATASET:
{self._format_list(datasets)}

EVALUATION METRICS:
{self._format_list(metrics)}

FRAMEWORK: {framework}
CODING STYLE: {code_style}

Please generate well-structured, executable Python code that:
1. Implements the complete experiment workflow
2. Includes data loading, preprocessing, model implementation, training, and evaluation
3. Uses best practices for the {framework} framework
4. Has clear comments and documentation
5. Includes proper error handling
6. Is modular and maintainable

Along with the code, provide a brief description of the implementation and instructions for running the experiment.

FORMAT YOUR RESPONSE AS:
```python
# Complete code here
```

IMPLEMENTATION DESCRIPTION:
A brief description of the implementation approach, key components, and how to run the code.
"""
        return prompt
    
    def _format_list(self, items: List[Any]) -> str:
        """格式化列表项为字符串"""
        if not items:
            return "Not specified"
        
        if isinstance(items, list):
            return "\n".join(f"- {item}" for item in items)
        return str(items)
    
    def _parse_code_response(self, response: str) -> tuple:
        """
        解析代码响应
        
        Args:
            response: API响应
            
        Returns:
            (代码, 描述)
        """
        # 提取代码块
        import re
        code_match = re.search(r'```python(.*?)```', response, re.DOTALL)
        code = code_match.group(1).strip() if code_match else ""
        
        # 提取描述
        description_match = re.search(r'IMPLEMENTATION DESCRIPTION:(.*?)($|```)', response, re.DOTALL)
        description = description_match.group(1).strip() if description_match else ""
        
        # 如果没有提取到代码，可能是没有使用```python标记
        if not code:
            # 查找任何代码块
            code_match = re.search(r'```(.*?)```', response, re.DOTALL)
            code = code_match.group(1).strip() if code_match else ""
        
        # 如果还是没有代码，将整个响应作为代码
        if not code:
            code = response
            description = "Generated code based on experiment specifications."
        
        return code, description
    
    def _generate_fallback_code(self, 
                              experiment_plan: Dict[str, Any],
                              framework: str = "pytorch") -> Dict[str, Any]:
        """
        生成备选代码
        
        Args:
            experiment_plan: 实验计划
            framework: 框架
            
        Returns:
            生成的代码及相关信息
        """
        logger.warning("⚠️ 使用备选代码生成")
        
        # 提取实验关键信息
        research_question = experiment_plan.get('research_question', 'Brain-inspired learning')
        
        # 根据框架选择不同模板
        if framework.lower() == "pytorch":
            code = self._get_pytorch_template(research_question)
        else:
            code = self._get_tensorflow_template(research_question)
        
        return {
            'code': code,
            'code_description': f"Basic {framework} implementation template for {research_question}",
            'framework': framework,
            'success': True,
            'is_fallback': True
        }
    
    def _get_pytorch_template(self, research_question: str) -> str:
        """获取PyTorch模板代码"""
        return f'''
"""
{research_question} - PyTorch Implementation
"""

import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import torchvision
import torchvision.transforms as transforms
import numpy as np
import matplotlib.pyplot as plt
import time
import os

# Device configuration
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
print(f"Using device: {device}")

# Hyperparameters
batch_size = 128
num_epochs = 10
learning_rate = 0.001

# Data loading and preprocessing
transform = transforms.Compose([
    transforms.ToTensor(),
    transforms.Normalize((0.5, 0.5, 0.5), (0.5, 0.5, 0.5))
])

# Load CIFAR-10 dataset as example
train_dataset = torchvision.datasets.CIFAR10(root='./data', train=True, download=True, transform=transform)
test_dataset = torchvision.datasets.CIFAR10(root='./data', train=False, download=True, transform=transform)

train_loader = torch.utils.data.DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=2)
test_loader = torch.utils.data.DataLoader(test_dataset, batch_size=batch_size, shuffle=False, num_workers=2)

classes = ('plane', 'car', 'bird', 'cat', 'deer', 'dog', 'frog', 'horse', 'ship', 'truck')

# Neural Network Model
class NeuralPlasticityNetwork(nn.Module):
    def __init__(self):
        super(NeuralPlasticityNetwork, self).__init__()
        self.conv1 = nn.Conv2d(3, 32, 3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, 3, padding=1)
        self.pool = nn.MaxPool2d(2, 2)
        self.fc1 = nn.Linear(64 * 8 * 8, 512)
        self.fc2 = nn.Linear(512, 10)
        self.dropout = nn.Dropout(0.25)
        
        # Neural plasticity parameters
        self.plasticity_rate = 0.01
        self.activation_history = None
        
    def forward(self, x):
        x = self.pool(F.relu(self.conv1(x)))
        x = self.pool(F.relu(self.conv2(x)))
        x = x.view(-1, 64 * 8 * 8)
        
        # Store activation history for plasticity
        if self.training and self.activation_history is None:
            self.activation_history = x.detach().mean(0)
        elif self.training:
            self.activation_history = (1 - self.plasticity_rate) * self.activation_history + \
                                     self.plasticity_rate * x.detach().mean(0)
        
        x = F.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.fc2(x)
        return x
    
    def apply_plasticity(self):
        """Apply neural plasticity updates based on activation history"""
        if self.activation_history is not None:
            with torch.no_grad():
                # Example of homeostatic plasticity: normalize weights based on activation
                fc1_norm = self.fc1.weight.norm(dim=1, keepdim=True)
                activation_scale = 1.0 / (1.0 + self.activation_history)
                self.fc1.weight.data *= activation_scale.unsqueeze(0)
                # Restore original norm
                self.fc1.weight.data *= fc1_norm / self.fc1.weight.norm(dim=1, keepdim=True)

model = NeuralPlasticityNetwork().to(device)
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=learning_rate)

# Training function
def train_model():
    model.train()
    total_loss = 0
    
    for batch_idx, (data, targets) in enumerate(train_loader):
        data, targets = data.to(device), targets.to(device)
        
        # Forward pass
        outputs = model(data)
        loss = criterion(outputs, targets)
        
        # Backward and optimize
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        
        # Apply plasticity
        model.apply_plasticity()
        
        total_loss += loss.item()
        
        if (batch_idx + 1) % 100 == 0:
            print(f'Epoch [{epoch+1}/{num_epochs}], Step [{batch_idx+1}/{len(train_loader)}], Loss: {loss.item():.4f}')
    
    return total_loss / len(train_loader)

# Testing function
def test_model():
    model.eval()
    with torch.no_grad():
        correct = 0
        total = 0
        for data, targets in test_loader:
            data, targets = data.to(device), targets.to(device)
            outputs = model(data)
            _, predicted = torch.max(outputs.data, 1)
            total += targets.size(0)
            correct += (predicted == targets).sum().item()
    
    accuracy = 100 * correct / total
    print(f'Test Accuracy: {accuracy:.2f}%')
    return accuracy

# Training loop
start_time = time.time()
train_losses = []
test_accuracies = []

for epoch in range(num_epochs):
    train_loss = train_model()
    train_losses.append(train_loss)
    
    accuracy = test_model()
    test_accuracies.append(accuracy)
    
    print(f'Epoch [{epoch+1}/{num_epochs}], Loss: {train_loss:.4f}, Accuracy: {accuracy:.2f}%')

print(f'Training completed in {time.time() - start_time:.2f} seconds')

# Plot training results
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.plot(train_losses)
plt.xlabel('Epochs')
plt.ylabel('Training Loss')
plt.title('Training Loss over Epochs')

plt.subplot(1, 2, 2)
plt.plot(test_accuracies)
plt.xlabel('Epochs')
plt.ylabel('Test Accuracy (%)')
plt.title('Test Accuracy over Epochs')

plt.tight_layout()
plt.savefig('training_results.png')
plt.show()

# Save the model
torch.save(model.state_dict(), 'neural_plasticity_model.pth')
print("Model saved successfully")
'''
    
    def _get_tensorflow_template(self, research_question: str) -> str:
        """获取TensorFlow模板代码"""
        return f'''
"""
{research_question} - TensorFlow Implementation
"""

import tensorflow as tf
import numpy as np
import matplotlib.pyplot as plt
import time
import os
from tensorflow.keras import layers, models, optimizers
from tensorflow.keras.datasets import cifar10
from tensorflow.keras.utils import to_categorical

print(f"TensorFlow version: {tf.__version__}")

# Check for GPU
print(f"Using GPU: {tf.config.list_physical_devices('GPU')}")

# Hyperparameters
batch_size = 128
num_epochs = 10
learning_rate = 0.001

# Load and preprocess data
(train_images, train_labels), (test_images, test_labels) = cifar10.load_data()

# Normalize pixel values
train_images = train_images.astype('float32') / 255.0
test_images = test_images.astype('float32') / 255.0

# Convert labels to one-hot encoding
train_labels = to_categorical(train_labels, 10)
test_labels = to_categorical(test_labels, 10)

print(f"Training data shape: {train_images.shape}")
print(f"Training labels shape: {train_labels.shape}")

# Create neural plasticity layer
class NeuralPlasticityLayer(layers.Layer):
    def __init__(self, units, plasticity_rate=0.01, **kwargs):
        super(NeuralPlasticityLayer, self).__init__(**kwargs)
        self.units = units
        self.plasticity_rate = plasticity_rate
        
    def build(self, input_shape):
        self.kernel = self.add_weight(
            shape=(input_shape[-1], self.units),
            initializer='glorot_uniform',
            trainable=True,
            name='kernel'
        )
        self.bias = self.add_weight(
            shape=(self.units,),
            initializer='zeros',
            trainable=True,
            name='bias'
        )
        self.activation_history = self.add_weight(
            shape=(input_shape[-1],),
            initializer='zeros',
            trainable=False,
            name='activation_history'
        )
        super(NeuralPlasticityLayer, self).build(input_shape)
    
    def call(self, inputs, training=None):
        # Update activation history during training
        if training:
            activations = tf.reduce_mean(tf.abs(inputs), axis=0)
            self.activation_history.assign(
                (1 - self.plasticity_rate) * self.activation_history + 
                self.plasticity_rate * activations
            )
            
            # Apply plasticity: Adjust weights based on activation history
            activation_scale = 1.0 / (1.0 + self.activation_history)
            adjusted_kernel = self.kernel * tf.expand_dims(activation_scale, axis=1)
            
            # Normalize to keep weight magnitudes stable
            orig_norm = tf.norm(self.kernel, axis=1, keepdims=True)
            new_norm = tf.norm(adjusted_kernel, axis=1, keepdims=True)
            scaling_factor = orig_norm / (new_norm + 1e-8)
            adjusted_kernel = adjusted_kernel * scaling_factor
            
            return tf.matmul(inputs, adjusted_kernel) + self.bias
        else:
            return tf.matmul(inputs, self.kernel) + self.bias
    
    def get_config(self):
        config = super(NeuralPlasticityLayer, self).get_config()
        config.update({
            'units': self.units,
            'plasticity_rate': self.plasticity_rate
        })
        return config

# Build the neural plasticity model
def build_model():
    model = models.Sequential([
        layers.Conv2D(32, (3, 3), activation='relu', padding='same', input_shape=(32, 32, 3)),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        
        layers.Conv2D(64, (3, 3), activation='relu', padding='same'),
        layers.BatchNormalization(),
        layers.MaxPooling2D((2, 2)),
        
        layers.Flatten(),
        NeuralPlasticityLayer(512),
        layers.Activation('relu'),
        layers.Dropout(0.25),
        layers.Dense(10, activation='softmax')
    ])
    
    # Compile model
    model.compile(
        optimizer=optimizers.Adam(learning_rate=learning_rate),
        loss='categorical_crossentropy',
        metrics=['accuracy']
    )
    
    return model

# Create and train model
model = build_model()
model.summary()

# Custom callback to print progress
class TrainingProgressCallback(tf.keras.callbacks.Callback):
    def on_epoch_end(self, epoch, logs=None):
        print(f"Epoch {epoch+1}/{num_epochs} - Loss: {logs['loss']:.4f}, Accuracy: {logs['accuracy']:.4f}, "
              f"Val Loss: {logs['val_loss']:.4f}, Val Accuracy: {logs['val_accuracy']:.4f}")

# Train model
start_time = time.time()
history = model.fit(
    train_images, train_labels,
    batch_size=batch_size,
    epochs=num_epochs,
    validation_data=(test_images, test_labels),
    callbacks=[TrainingProgressCallback()]
)

print(f"Training completed in {time.time() - start_time:.2f} seconds")

# Evaluate model
test_loss, test_acc = model.evaluate(test_images, test_labels)
print(f"Test accuracy: {test_acc:.4f}")

# Plot training results
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.plot(history.history['loss'], label='Training Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.xlabel('Epochs')
plt.ylabel('Loss')
plt.title('Training and Validation Loss')
plt.legend()

plt.subplot(1, 2, 2)
plt.plot(history.history['accuracy'], label='Training Accuracy')
plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
plt.xlabel('Epochs')
plt.ylabel('Accuracy')
plt.title('Training and Validation Accuracy')
plt.legend()

plt.tight_layout()
plt.savefig('tensorflow_training_results.png')
plt.show()

# Save the model
model.save('neural_plasticity_model_tf')
print("Model saved successfully")
'''

# 示例使用
if __name__ == "__main__":
    # 创建示例实验计划
    example_plan = {
        "research_question": "How can neural plasticity principles improve deep learning?",
        "description": "An experiment comparing traditional neural networks with plasticity-enhanced networks",
        "methods": ["Spike-Timing-Dependent Plasticity (STDP)", "Homeostatic Plasticity"],
        "datasets": ["CIFAR-10", "MNIST"],
        "evaluation_metrics": ["Accuracy", "Convergence Speed", "Robustness to Noise"]
    }
    
    # 初始化代码生成器
    generator = ExperimentCodeGenerator()
    
    # 生成代码
    result = generator.generate_experiment_code(example_plan)
    
    print(f"\n生成的代码描述:\n{result['code_description']}")
    print(f"\n代码长度: {len(result['code'])} 字符")
    
    # 保存代码到文件
    with open("example_experiment.py", "w") as f:
        f.write(result["code"])
    print(f"代码已保存到 example_experiment.py")
