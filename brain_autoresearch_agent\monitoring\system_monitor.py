"""
论文生成系统性能监控和质量评估模块

提供全面的性能监控、质量评估和系统优化建议
"""

import time
import json
import logging
import statistics
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import threading
from collections import defaultdict


@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    operation_name: str
    start_time: float
    end_time: float
    duration: float
    success: bool
    error_message: Optional[str] = None
    additional_data: Optional[Dict] = None


@dataclass 
class QualityMetric:
    """质量指标数据类"""
    metric_name: str
    score: float
    max_score: float
    description: str
    details: Optional[Dict] = None


class PerformanceMonitor:
    """
    性能监控器
    
    监控论文生成过程中各个环节的性能指标
    """
    
    def __init__(self):
        self.metrics = []
        self.current_operations = {}
        self.logger = self._setup_logger()
        self._lock = threading.Lock()
        
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('PerformanceMonitor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def start_operation(self, operation_name: str, additional_data: Dict = None):
        """开始监控一个操作"""
        with self._lock:
            operation_id = f"{operation_name}_{time.time()}"
            self.current_operations[operation_id] = {
                'name': operation_name,
                'start_time': time.time(),
                'additional_data': additional_data or {}
            }
            self.logger.debug(f"Started monitoring: {operation_name}")
            return operation_id
    
    def end_operation(self, operation_id: str, success: bool = True, 
                     error_message: str = None, additional_data: Dict = None):
        """结束监控一个操作"""
        with self._lock:
            if operation_id not in self.current_operations:
                self.logger.warning(f"Operation {operation_id} not found")
                return
            
            operation = self.current_operations.pop(operation_id)
            end_time = time.time()
            duration = end_time - operation['start_time']
            
            metric = PerformanceMetric(
                operation_name=operation['name'],
                start_time=operation['start_time'],
                end_time=end_time,
                duration=duration,
                success=success,
                error_message=error_message,
                additional_data={**operation['additional_data'], **(additional_data or {})}
            )
            
            self.metrics.append(metric)
            
            status = "✅" if success else "❌"
            self.logger.info(f"{status} {operation['name']}: {duration:.2f}s")
            
            return metric
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics:
            return {"message": "No metrics available"}
        
        # 按操作类型分组
        operations = defaultdict(list)
        for metric in self.metrics:
            operations[metric.operation_name].append(metric.duration)
        
        summary = {
            'total_operations': len(self.metrics),
            'successful_operations': sum(1 for m in self.metrics if m.success),
            'failed_operations': sum(1 for m in self.metrics if not m.success),
            'total_time': sum(m.duration for m in self.metrics),
            'operations_summary': {}
        }
        
        for op_name, durations in operations.items():
            summary['operations_summary'][op_name] = {
                'count': len(durations),
                'total_time': sum(durations),
                'average_time': statistics.mean(durations),
                'min_time': min(durations),
                'max_time': max(durations),
                'std_dev': statistics.stdev(durations) if len(durations) > 1 else 0
            }
        
        return summary
    
    def get_slowest_operations(self, top_n: int = 5) -> List[PerformanceMetric]:
        """获取最慢的操作"""
        return sorted(self.metrics, key=lambda x: x.duration, reverse=True)[:top_n]
    
    def get_failed_operations(self) -> List[PerformanceMetric]:
        """获取失败的操作"""
        return [m for m in self.metrics if not m.success]


class QualityAssessment:
    """
    质量评估器
    
    评估生成论文的各种质量指标
    """
    
    def __init__(self):
        self.quality_metrics = []
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('QualityAssessment')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def assess_paper_quality(self, paper_data: Dict[str, Any]) -> Dict[str, Any]:
        """评估论文整体质量"""
        self.logger.info("开始论文质量评估...")
        
        # 1. 结构完整性评估
        structure_score = self._assess_structure_completeness(paper_data)
        
        # 2. 内容质量评估
        content_score = self._assess_content_quality(paper_data)
        
        # 3. 文献引用评估
        citation_score = self._assess_citation_quality(paper_data)
        
        # 4. 技术深度评估
        technical_score = self._assess_technical_depth(paper_data)
        
        # 5. 一致性评估
        consistency_score = self._assess_consistency(paper_data)
        
        # 6. 创新性评估
        novelty_score = self._assess_novelty(paper_data)
        
        # 计算总体质量分数
        total_score = (
            structure_score.score * 0.2 +
            content_score.score * 0.25 +
            citation_score.score * 0.15 +
            technical_score.score * 0.2 +
            consistency_score.score * 0.1 +
            novelty_score.score * 0.1
        )
        
        quality_assessment = {
            'overall_score': round(total_score, 2),
            'max_score': 100.0,
            'grade': self._calculate_grade(total_score),
            'detailed_scores': {
                'structure': asdict(structure_score),
                'content': asdict(content_score),
                'citations': asdict(citation_score),
                'technical_depth': asdict(technical_score),
                'consistency': asdict(consistency_score),
                'novelty': asdict(novelty_score)
            },
            'recommendations': self._generate_improvement_recommendations(
                structure_score, content_score, citation_score,
                technical_score, consistency_score, novelty_score
            ),
            'assessment_timestamp': datetime.now().isoformat()
        }
        
        self.logger.info(f"论文质量评估完成，总分: {total_score:.2f}/100")
        return quality_assessment
    
    def _assess_structure_completeness(self, paper_data: Dict) -> QualityMetric:
        """评估论文结构完整性"""
        required_sections = ['abstract', 'introduction', 'methodology', 'conclusion']
        optional_sections = ['related_work', 'experiments', 'results', 'discussion']
        
        sections = paper_data.get('sections', {})
        
        # 检查必需章节
        required_present = sum(1 for section in required_sections if section in sections)
        required_score = (required_present / len(required_sections)) * 60  # 60%权重
        
        # 检查可选章节
        optional_present = sum(1 for section in optional_sections if section in sections)
        optional_score = (optional_present / len(optional_sections)) * 40  # 40%权重
        
        total_score = required_score + optional_score
        
        return QualityMetric(
            metric_name="structure_completeness",
            score=total_score,
            max_score=100.0,
            description="论文结构完整性评估",
            details={
                'required_sections_present': required_present,
                'optional_sections_present': optional_present,
                'total_sections': len(sections),
                'missing_required': [s for s in required_sections if s not in sections]
            }
        )
    
    def _assess_content_quality(self, paper_data: Dict) -> QualityMetric:
        """评估内容质量"""
        sections = paper_data.get('sections', {})
        
        # 检查内容长度适当性
        length_scores = []
        target_lengths = {
            'abstract': (150, 300),
            'introduction': (800, 2000),
            'methodology': (1000, 3000),
            'conclusion': (300, 1000)
        }
        
        for section, (min_len, max_len) in target_lengths.items():
            if section in sections:
                content = str(sections[section])
                length = len(content)
                
                if min_len <= length <= max_len:
                    length_scores.append(100)
                elif length < min_len:
                    length_scores.append(max(0, (length / min_len) * 100))
                else:  # length > max_len
                    length_scores.append(max(0, 100 - ((length - max_len) / max_len) * 50))
        
        # 检查内容非空性
        non_empty_sections = sum(1 for content in sections.values() 
                               if content and len(str(content).strip()) > 10)
        
        avg_length_score = statistics.mean(length_scores) if length_scores else 0
        completeness_score = (non_empty_sections / max(len(sections), 1)) * 100
        
        total_score = (avg_length_score * 0.6 + completeness_score * 0.4)
        
        return QualityMetric(
            metric_name="content_quality",
            score=total_score,
            max_score=100.0,
            description="内容质量评估",
            details={
                'average_length_appropriateness': avg_length_score,
                'content_completeness': completeness_score,
                'non_empty_sections': non_empty_sections
            }
        )
    
    def _assess_citation_quality(self, paper_data: Dict) -> QualityMetric:
        """评估文献引用质量"""
        literature_review = paper_data.get('literature_review', {})
        papers_by_category = literature_review.get('papers_by_category', {})
        
        total_papers = sum(len(papers) for papers in papers_by_category.values())
        
        # 评估引用数量
        if total_papers >= 30:
            quantity_score = 100
        elif total_papers >= 20:
            quantity_score = 80
        elif total_papers >= 10:
            quantity_score = 60
        elif total_papers >= 5:
            quantity_score = 40
        else:
            quantity_score = max(0, total_papers * 8)  # 每篇8分
        
        # 评估引用分布
        categories = len(papers_by_category)
        if categories >= 5:
            diversity_score = 100
        elif categories >= 3:
            diversity_score = 80
        elif categories >= 2:
            diversity_score = 60
        else:
            diversity_score = categories * 30
        
        # 评估引用质量（基于元数据）
        quality_indicators = literature_review.get('metadata', {})
        coverage_quality = quality_indicators.get('coverage_analysis', {}).get('coverage_quality', 'Basic')
        
        if coverage_quality == 'Comprehensive':
            quality_score = 100
        elif coverage_quality == 'Good':
            quality_score = 80
        else:  # Basic
            quality_score = 60
        
        total_score = (quantity_score * 0.4 + diversity_score * 0.3 + quality_score * 0.3)
        
        return QualityMetric(
            metric_name="citation_quality",
            score=total_score,
            max_score=100.0,
            description="文献引用质量评估",
            details={
                'total_papers': total_papers,
                'categories_covered': categories,
                'coverage_quality': coverage_quality,
                'quantity_score': quantity_score,
                'diversity_score': diversity_score
            }
        )
    
    def _assess_technical_depth(self, paper_data: Dict) -> QualityMetric:
        """评估技术深度"""
        # 检查是否包含实验设计
        has_experiments = 'experiments' in paper_data.get('sections', {})
        
        # 检查是否有推理结果
        reasoning_results = paper_data.get('reasoning_results', {})
        has_reasoning = bool(reasoning_results)
        
        # 检查多专家分析
        phase_results = reasoning_results.get('phase_results', {})
        num_phases = len(phase_results)
        
        # 计算技术深度分数
        experiment_score = 40 if has_experiments else 0
        reasoning_score = 30 if has_reasoning else 0
        phase_score = min(30, num_phases * 7.5)  # 每个阶段7.5分，最多30分
        
        total_score = experiment_score + reasoning_score + phase_score
        
        return QualityMetric(
            metric_name="technical_depth",
            score=total_score,
            max_score=100.0,
            description="技术深度评估",
            details={
                'has_experiments': has_experiments,
                'has_multi_agent_reasoning': has_reasoning,
                'reasoning_phases': num_phases,
                'experiment_score': experiment_score,
                'reasoning_score': reasoning_score
            }
        )
    
    def _assess_consistency(self, paper_data: Dict) -> QualityMetric:
        """评估内容一致性"""
        # 简化的一致性检查
        title = paper_data.get('title', '')
        abstract = str(paper_data.get('sections', {}).get('abstract', ''))
        introduction = str(paper_data.get('sections', {}).get('introduction', ''))
        
        # 检查标题关键词在摘要和引言中的出现
        title_words = set(title.lower().split())
        title_words = {word for word in title_words if len(word) > 3}  # 过滤短词
        
        if title_words:
            abstract_coverage = len([word for word in title_words 
                                   if word in abstract.lower()]) / len(title_words)
            intro_coverage = len([word for word in title_words 
                                if word in introduction.lower()]) / len(title_words)
            
            consistency_score = (abstract_coverage + intro_coverage) / 2 * 100
        else:
            consistency_score = 50  # 默认分数
        
        return QualityMetric(
            metric_name="consistency",
            score=consistency_score,
            max_score=100.0,
            description="内容一致性评估",
            details={
                'title_words_count': len(title_words),
                'abstract_coverage': abstract_coverage if title_words else 0,
                'introduction_coverage': intro_coverage if title_words else 0
            }
        )
    
    def _assess_novelty(self, paper_data: Dict) -> QualityMetric:
        """评估创新性"""
        # 基于文献综述的新颖性评估
        literature_review = paper_data.get('literature_review', {})
        
        # 检查是否识别了研究空白
        gap_analysis = literature_review.get('comprehensive_analysis', {}).get('gap_analysis', [])
        gaps_identified = len(gap_analysis)
        
        # 检查研究方向的新颖性指标
        research_landscape = literature_review.get('research_landscape', {})
        trending_keywords = research_landscape.get('trending_keywords', [])
        
        # 简单的新颖性评分
        if gaps_identified >= 5:
            gap_score = 60
        elif gaps_identified >= 3:
            gap_score = 40
        elif gaps_identified >= 1:
            gap_score = 20
        else:
            gap_score = 0
        
        keyword_novelty = min(40, len(trending_keywords) * 4)  # 每个关键词4分，最多40分
        
        total_score = gap_score + keyword_novelty
        
        return QualityMetric(
            metric_name="novelty",
            score=total_score,
            max_score=100.0,
            description="创新性评估",
            details={
                'research_gaps_identified': gaps_identified,
                'trending_keywords_count': len(trending_keywords),
                'gap_score': gap_score,
                'keyword_novelty_score': keyword_novelty
            }
        )
    
    def _calculate_grade(self, score: float) -> str:
        """计算等级"""
        if score >= 90:
            return "A+"
        elif score >= 85:
            return "A"
        elif score >= 80:
            return "A-"
        elif score >= 75:
            return "B+"
        elif score >= 70:
            return "B"
        elif score >= 65:
            return "B-"
        elif score >= 60:
            return "C+"
        elif score >= 55:
            return "C"
        elif score >= 50:
            return "C-"
        else:
            return "F"
    
    def _generate_improvement_recommendations(self, *quality_metrics) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        for metric in quality_metrics:
            if metric.score < 70:  # 需要改进的阈值
                if metric.metric_name == "structure_completeness":
                    recommendations.append("建议完善论文结构，添加缺失的必要章节")
                elif metric.metric_name == "content_quality":
                    recommendations.append("建议增强内容质量，确保各章节内容充实且长度适当")
                elif metric.metric_name == "citation_quality":
                    recommendations.append("建议增加文献引用数量，提高引用的多样性和质量")
                elif metric.metric_name == "technical_depth":
                    recommendations.append("建议增强技术深度，添加实验设计和详细的方法论分析")
                elif metric.metric_name == "consistency":
                    recommendations.append("建议提高内容一致性，确保标题、摘要和正文内容呼应")
                elif metric.metric_name == "novelty":
                    recommendations.append("建议强化创新性，突出研究的新颖之处和独特贡献")
        
        if not recommendations:
            recommendations.append("论文质量整体良好，建议进行最终的格式调整和语言润色")
        
        return recommendations


class SystemMonitor:
    """
    系统监控器
    
    整合性能监控和质量评估，提供全面的系统监控
    """
    
    def __init__(self):
        self.performance_monitor = PerformanceMonitor()
        self.quality_assessment = QualityAssessment()
        self.session_data = {}
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('SystemMonitor')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def start_session(self, session_id: str, metadata: Dict = None):
        """开始监控会话"""
        self.session_data[session_id] = {
            'start_time': datetime.now(),
            'metadata': metadata or {},
            'performance_metrics': [],
            'quality_scores': [],
            'status': 'active'
        }
        self.logger.info(f"开始监控会话: {session_id}")
    
    def end_session(self, session_id: str, paper_data: Dict = None):
        """结束监控会话"""
        if session_id not in self.session_data:
            self.logger.warning(f"会话 {session_id} 不存在")
            return
        
        session = self.session_data[session_id]
        session['end_time'] = datetime.now()
        session['duration'] = (session['end_time'] - session['start_time']).total_seconds()
        session['status'] = 'completed'
        
        # 生成综合报告
        if paper_data:
            session['quality_assessment'] = self.quality_assessment.assess_paper_quality(paper_data)
        
        session['performance_summary'] = self.performance_monitor.get_performance_summary()
        
        self.logger.info(f"会话 {session_id} 监控完成")
        return session
    
    def get_session_report(self, session_id: str) -> Dict[str, Any]:
        """获取会话报告"""
        if session_id not in self.session_data:
            return {"error": f"Session {session_id} not found"}
        
        session = self.session_data[session_id]
        
        return {
            'session_id': session_id,
            'session_info': {
                'start_time': session['start_time'].isoformat(),
                'end_time': session.get('end_time', datetime.now()).isoformat(),
                'duration': session.get('duration', 0),
                'status': session['status'],
                'metadata': session['metadata']
            },
            'performance_summary': session.get('performance_summary', {}),
            'quality_assessment': session.get('quality_assessment', {}),
            'recommendations': self._generate_session_recommendations(session)
        }
    
    def _generate_session_recommendations(self, session: Dict) -> List[str]:
        """生成会话改进建议"""
        recommendations = []
        
        # 基于性能数据的建议
        perf_summary = session.get('performance_summary', {})
        if perf_summary.get('failed_operations', 0) > 0:
            recommendations.append("检测到操作失败，建议检查系统配置和网络连接")
        
        # 基于质量评估的建议
        quality_assessment = session.get('quality_assessment', {})
        if quality_assessment:
            overall_score = quality_assessment.get('overall_score', 0)
            if overall_score < 70:
                recommendations.append("论文质量需要改进，建议查看详细的质量评估报告")
            
            recommendations.extend(quality_assessment.get('recommendations', []))
        
        return recommendations
    
    def save_monitoring_data(self, filepath: str):
        """保存监控数据"""
        monitoring_data = {
            'sessions': {},
            'global_performance': self.performance_monitor.get_performance_summary(),
            'export_timestamp': datetime.now().isoformat()
        }
        
        # 序列化会话数据
        for session_id, session in self.session_data.items():
            serializable_session = {}
            for key, value in session.items():
                if isinstance(value, datetime):
                    serializable_session[key] = value.isoformat()
                else:
                    serializable_session[key] = value
            monitoring_data['sessions'][session_id] = serializable_session
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(monitoring_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"监控数据已保存到: {filepath}")


if __name__ == "__main__":
    # 测试监控系统
    print("📊 论文生成系统监控测试")
    print("=" * 50)
    
    # 创建系统监控器
    monitor = SystemMonitor()
    
    # 模拟监控会话
    session_id = "test_session_001"
    monitor.start_session(session_id, {"test_mode": True})
    
    # 模拟性能监控
    op_id = monitor.performance_monitor.start_operation("literature_search")
    time.sleep(1)  # 模拟操作耗时
    monitor.performance_monitor.end_operation(op_id, success=True)
    
    # 模拟论文数据用于质量评估
    sample_paper = {
        'title': 'Brain-Inspired Adaptive Learning Systems',
        'sections': {
            'abstract': 'This paper presents a novel approach to brain-inspired computing...' * 10,
            'introduction': 'Introduction content here...' * 50,
            'methodology': 'Methodology details...' * 100,
            'conclusion': 'Conclusion remarks...' * 20
        },
        'literature_review': {
            'papers_by_category': {
                'foundational': [{'title': 'Paper 1'}] * 5,
                'recent_advances': [{'title': 'Paper 2'}] * 3
            },
            'metadata': {
                'total_papers_found': 15,
                'coverage_analysis': {'coverage_quality': 'Good'}
            }
        }
    }
    
    # 结束会话并生成报告
    session_report = monitor.end_session(session_id, sample_paper)
    
    print(f"✅ 会话监控完成")
    print(f"📈 性能摘要: {session_report.get('performance_summary', {}).get('total_operations', 0)} 个操作")
    print(f"🎯 质量评分: {session_report.get('quality_assessment', {}).get('overall_score', 0)}")
    
    # 保存监控数据
    monitor.save_monitoring_data("monitoring_data.json")
    print(f"💾 监控数据已保存")
